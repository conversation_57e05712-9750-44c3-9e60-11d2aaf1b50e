version: "3.7"

services:
  api:
    image: "tradardev-phase2-api:latest"
    deploy:
      replicas: 1
      placement:
        constraints: [ node.hostname==thinklabs02 ]
      restart_policy:
        condition: any
    environment:
      PORT: 3000
      NODE_ENV: "production"
      MONGO_URI: "************************************************************************************************************"
    ports:
      - target: 3000
        published: 4124
        mode: host
    volumes:
      - storages:/usr/src/app/storage

volumes:
  storages:
    driver: local
