pipeline {
    agent any
    stages {
        stage('Init') {
            steps {
                echo 'Testing..'
                telegramSend(message: 'Building job: $PROJECT_NAME ... - Link: $BUILD_URL', chatId: -649539527)
            }
        }
        stage ('Deployments') {
            steps {
                echo 'Deploying to Production environment...'
                echo 'Copy project over SSH...'
                sshPublisher(publishers: [
                    sshPublisherDesc(
                        configName: 'swarm2',
                        transfers:
                            [sshTransfer(
                                cleanRemote: false,
                                excludes: '',
                                execCommand: "docker build -t tradardev-phase2-api ./thinklabsdev/tradardev-phase2-apiCI/ \
                                    && docker service rm tradardev-phase2_api || true \
                                    && docker stack deploy -c ./thinklabsdev/tradardev-phase2-apiCI/docker-compose-dev.yml tradardev-phase2 \
                                    && rm -rf ./thinklabsdev/tradardev-phase2-apiCIB \
                                    && mv ./thinklabsdev/tradardev-phase2-apiCI/ ./thinklabsdev/tradardev-phase2-apiCIB",
                                execTimeout: 6000000,
                                flatten: false,
                                makeEmptyDirs: false,
                                noDefaultExcludes: false,
                                patternSeparator: '[, ]+',
                                remoteDirectory: './thinklabsdev/tradardev-phase2-apiCI',
                                remoteDirectorySDF: false,
                                removePrefix: '',
                                sourceFiles: '*, server/'
                            )],
                        usePromotionTimestamp: false,
                        useWorkspaceInPromotion: false,
                        verbose: false
                    )
                ])
                telegramSend(message: 'Build - $PROJECT_NAME – # $BUILD_NUMBER – STATUS: $BUILD_STATUS!', chatId: -649539527)
            }
        }
    }
}
