export const enTranslation = {
  app_name: 'Power line management system',
  userRouter: 'API user',
  error_user_login_username_password_wrong: 'Username or password was wrong',
  error_user_password_wrong: 'Incorrect password!',
  times: 'times',
  attempts_left: 'attempts left',
  enterd_wrong: 'You have entered wrong',
  you_have: 'You have',
  error_user_login_account_locked: 'Account locked, please contact to your admin.',
  email_subject_user_forgot_password: 'Forgot password',
  email_html1_user_forgot_password: 'You have a request to change the password on the Power Transmission Line Management System',
  email_html2_user_forgot_password: 'Please click on the link to change your password',
  error_user_account_is_registered: 'Account is registered',
  ERROR_USER_ACCOUNT_DOES_NOT_EXIST: 'Account does not exist',
  error_user_email_was_registered: 'The Email was registered',
  error_user_email_is_required: 'The Email is required',
  error_user_phone_was_registered: 'The phone was registered',
  error_user_alias_already_exists: '<PERSON>as already exists',
  email_user_create_from: 'Power transmission line management system',
  EMAIL_UNLOCK_ACCOUNT_SUBJECT: 'Unlock account',
  email_user_create_subject: 'Successful account registration',
  email_user_create_html1: 'You have successfully created an account at Power Transmission Line Management System, Account Information:',
  email_user_update_html1: 'You have successfully updated your account at Power Transmission Line Management System, Account Information:',
  email_user_create_html2: 'Full name: ',
  email_user_create_html3: 'Username: ',
  email_user_create_html4: 'Phone: ',
  email_user_create_html5: 'Email: ',
  email_user_create_html6: 'Please login at ',
  error_user_invalid_token: 'Invalid token',
  error_user_invalid_refresh_token: 'Invalid refresh token',
  error_user_invalid_permission: 'Cannot create a new user with over than permission',
  user_domain_account: 'Domain account',
  user_can_not_be_blank: 'can not be blank',
  error_user_no_account_exist: 'No account exists',
  user_account: 'Account',
  user_exist: 'was exist',
  user_delete_insufficient_permissions: 'Insufficient permissions to delete this user',
  user_update_insufficient_permissions: 'Insufficient permissions to update this user',
  email_user_update_subject: 'Successful update account',
  error_old_password_wrong: 'Old password is incorrect',
  error_user_change_message_successful: 'Change password successfully',
  user_password_change: 'Your password has changed',
  hello: 'Hello',
  user_email_password_of: 'Password of: ',
  user_email_password_has_changed: 'has changed',
  user_email_regard: 'Regard',
  user_email_has_registered: 'Email has been registered',
  error_delete_already_operated_location: 'Unable to delete the location that has already been operated',
  setting_code_missing: 'Setting code is missing',
  parent_line_wrong_or_missing: 'Parent line is incorrect or missing',
  cant_delete_assigned_thematic: 'Cannot be delete the assigned thematic',
  device_already_exists: 'Device code already exists, please check and try again',
  status_name: 'Status name',
  status_code: 'Status code',
  already_exists: 'already exists, please check and try again',
  ID_number_already_exists: 'ID number already exists, please check and try again',
  cant_delete_content_with_test_result: 'Cannot delete content with test results',
  cant_delete_content_system_setting: 'Cannot delete content already exists in system setting',
  content_type_cant_empty: 'Content type cannot be empty',
  location_results_available: 'Location check results are available, content type cannot be edited',
  tower_results_available: 'Tower distance check results are available, content type cannot be edited',
  criteria: 'Criteria',
  have_test_result: 'have test result, cannot be delete',
  exist_in_system_setting: 'already exists in system setting, cannot be delete',
  content_name_already_exist: 'Content name already exists, please check and try again',
  delete_unit_child_before_delete_unit_parent: 'Delete the child unit before delete the parent unit',
  cant_delete_unit_assign: 'Cannot delete the unit because the unit has been assigned a job',
  cant_delete_unit_user: 'Cannot delete the unit because the unit still has user',
  unit_code_already_exists: 'Unit code already exists, please check and try again',
  email_already_exists: 'Email already exists, please check and try again',
  have_unexpected_error: 'There was an unexpected error, please contact the administrator',
  drone_code_already_exists: 'Drone code already exists, please check and try again',
  serial_already_exists: 'Serial already exists, please check and try again',
  delete_edit_location_before_delete_construction: 'Please delete or edit the location before delete the passing construction!',
  location_tower_cant_empty: 'Location - Tower distance cannot be empty',
  location_cant_empty: 'Location cannot be empty',
  delete_profile_child_before_delete_profile_parent: 'Delete the child profile before delete the main profile',
  cant_delete_employee_confirm: 'Employee confirmed to work position, cannot be deleted',
  not_confirm_work_position_not_confirm_leave_work_position: 'Not confirmed to work position, not confirmed to leave work position',
  not_adequate_correct_safety_measure: 'Failure to take adequate and correct safety measures, unable to accept work',
  not_take_more_safety_measure: 'Haven\'t take more safety measures, can\'t accept work',
  not_complete_safety_measure_grounding: 'Safety measures and additional grounding have not been completed yet, can\'t accept work',
  employee_not_confirm: 'Employee hasn\'t confirmed yet, can\'t finish work',
  daily_work_not_confirm: 'Daily work has not been confirmed, can\'t finish work',
  handover_not_complete: 'The handover has not been completed, the lock task cannot be confirmed',
  not_complete_power_cut: 'Power cut has not been completed, work cannot be allowed',
  not_complete_grounding: 'Grounding has not been completed, work cannot be allowed',
  not_complete_barriers_signs: 'Barriers - Signs has not been completed, work cannot be allowed',
  not_complete_scope_job: 'Scope of job has not been completed, work cannot be allowed',
  not_complete_warnings_instructions: 'Warnings - Instructionshas not been completed, work cannot be allowed',
  missing_type_of_work: 'Missing type of work',
  missing_leader: 'Missing leader',
  line_hasnt_chosen: 'Line hasn\'t chosen yet',
  tower_hasnt_chosen: 'Tower distance hasn\'t chosen yet',
  location_hasnt_chosen: 'Location hasn\'t chosen yet',
  missing_start_time: 'Missing start time',
  missing_end_time: 'Missing end time',
  start_time_must_before_end_time: 'Start time must before end time',
  exist_process_hasnt_chosen: 'Exist need process hasn\'t chosen yet',
  overdue_task_cant_delivery: 'Overdue task can\'t delivery',
  task_cant_delivery: 'Task can\'t delivery',
  overdue_task_cant_reception: 'Overdue task can\'t reception',
  task_need_delivery: 'Task need delivery',
  leader_can_reception_work: 'Leader can receive work',
  leader_can_refuse_work: 'Leader can refuse work',
  overdue_task_cant_refuse: 'Overdue task can\'t refuse',
  task_need_reception: 'Task need reception',
  leader_can_confirm_work: 'Leader can confirm work',
  leader_reception_task: 'Leader received task',
  task_need_lock: 'Task need lock',
  leader_can_cancel_lock: 'Leader can cancel lock task',
  leader_cancel_lock_task: 'Leader cancel lock task',
  task_need_returned_can_confirm: 'Task need to be return for confirm',
  cant_confirm_lock_not_result: 'Lock task can\'t confirm because task hasn\'t confirmed result',
  test_result_cant_empty_line: 'The test result cannot be empty "Line"',
  test_result_cant_empty_electrical_insulation: 'The test result cannot be empty "Electrical insulation"',
  unconfirm_task_cant_return: 'The task has not been confirmed can\'t be returned',
  location_or_tower_hasnt_chosen: 'Location or tower distance hasn\'t chosen yet',
  both_location_tower_not_empty: 'Both location and tower distance can\'t be empty',
  location_not_belong_line: 'Location is not belong line or not yet created',
  tower_not_belong_line: 'Tower distance is not belong line or not yet created',
  missing_circuit_info_of_location: 'Missing circuit information of location',
  measurer_not_belong_unit_line_management: 'Measurer is not belong of the line management unit or not yet created',
  task_number_incorrect_or_dont_create: 'Task number is incorrect or not yet created',
  evaluation_result_incorrect_or_dont_create: 'Evaluation result is incorrect or not yet created',
  measure_direction_incorrect: 'Measure direction is incorrect',
  missing_wire_info: 'Missing wire information',
  incorrect_or_not_available: 'Incorrect or not available',
  missing_location_info: 'Missing location information',
  area_incorrect_or_dont_create: 'Area is incorrect or not yet created',
  conclude_incorrect_or_dont_create: 'Conclude is incorrect or not yet created',
  missing_tower_info: 'Missing tower distance information',
  solution_incorrect_or_dont_create: 'Solution is incorrect or not yet created',
  file_cant_download: 'File can\'t dowload',
  report: 'Report',
  asset_code_code_existed: 'Asset code already exists, please check and try again',
  group_devices_existed: 'Group of devices already exists, please check and try again',
  delete_device_tool_in_tracking_notebook: 'Please delete device and tool in tracking notebook!',
  field_code_existed: 'Field code already exists, please check and try again',
  field_name_existed: 'Field name already exists, please check and try again',
  missing_unit_construction: 'Missing construction management unit',
  management_unit_incorrect_or_dont_create: 'Nanagement unit is incorrect or not yet created',
  missing_unit: 'Missing unit',
  unit_incorrect_or_dont_create: 'Unit is incorrect or not yet created',
  missing_construction: 'Missing construction',
  construction_incorrect_or_dont_create: 'Construction is incorrect or not yet created',
  missing_unit_management: 'Missing management unit',
  incorrect_or_dont_create: 'Incorrect or not yet created',
  parent_construction_incorrect_or_dont_create: 'Parent construction is incorrect or not yet created',
  parent_line_incorrect_or_dont_create: 'Parent line is incorrect or not yet created',
  missing_line_operation: 'Missing operation line',
  line_operation_incorrect_or_dont_create: 'Operation line is incorrect or not yet created',
  tower_distance_existed: 'Tower distance already exists, please check and try again',
  complete_or_cancel_task_assign_collaborate: 'Please complete or cancel assign task, work sheet of line before change to "Non-Operating" state',
  CONSTRUCTION_CODE_EXIST: 'Construction code {0} already exists, please check and try again',
  POWER_LINE_CODE_EXISTED: 'Power line code already exists, please check and try again',
  POWER_LINE_CODE_EXIST: 'Power line code {0} already exists, please check and try again',
  complete_or_cancel_task_assign_collaborate_before_update: 'Please complete or cancel assign task, work sheet of line before update',
  operation_code_existed: 'Operation code already exists, please check and try again',
  location_code_existed: 'Location code already exists, please check and try again',
  nothing: 'Nothing',
  format_wrong: 'Wrong, correct format is DD/MM/YYYY',
  format_incorrect: 'Format is incorrect',
  notify_user_do_something_work: 'User {0} has just {1} {2} number {3} that you joined',
  notify_user_do_something_jobsheet: 'User {0} has just {1} business number {2} that you joined',
  notify_unit_has_unresolved_exist: 'Unit "{0}" has {1} unresolved existence',
  notify_system_has_ios_code: 'The system has {0} iOS application code left',
  notify_there_are_new_line_problem: 'There are {0} new line problems',
  notify_line_problem_confirm: 'Line problem {0} confirmed',
  notify_line_problem_cancel_confirm: 'Line problem {0} canceled confirm',
  notify_upgraded_new_version_line_flight: 'Line Management and Flight Control has been upgraded to a new version',
  notify_upgraded_new_version_flight: 'Flight Control has been upgraded to a new version',
  notify_upgraded_new_version_line: 'Line Management has been upgraded to a new version',
  notify_default_new_message: 'You have new message',
  DANG_TAO_PHIEU: 'create task',
  HUY_PHIEU: 'cancel task',
  GIAO_PHIEU: 'delivery task',
  TIEP_NHAN: 'receive task',
  TU_CHOI_NHAN: 'reject task',
  KHOA_PHIEU: 'lock task',
  TU_CHOI_KHOA: 'deny lock task',
  XAC_NHAN_KHOA: 'confirm to lock task',
  KIEM_TRA_DINH_KY_NGAY: 'Periodic check of the day',
  KIEM_TRA_DINH_KY_DEM: 'Periodic check of the night',
  KIEM_TRA_SU_CO: 'Check for problems',
  KIEM_TRA_DOT_XUAT: 'Extraordinary examination',
  KIEM_TRA_KY_THUAT: 'Technical check',
  KIEM_TRA_CHUYEN_DE: 'Thematic test',
  CO_KE_HOACH: 'Power grid troubleshooting',
  KHONG_CO_KE_HOACH: 'Maintenance, enhanced maintenance',
  DO_DIEN_TRO_TIEP_DIA: 'Measure ground resistance',
  DO_NHIET_DO_TIEP_XUC: 'Measure contact temperature',
  DO_KHOANG_CACH_PHA_DAT: 'Measure the ground phase distance',
  DO_COROCAM_CACH_DIEN: 'Insulation Corocam measurement',
  DO_NHIET_DO_CACH_DIEN_COMPOSITE: 'Composite insulation temperature measurement',
  DO_CUONG_DO_DIEN_TRUONG: 'Measure electric field intensity',
  DO_KHOANG_CACH_GIAO_CHEO: 'Measure the intersection distance',
  CONG_TAC_PHU_TRO: 'Supplementary task',
  KIEM_TRA: 'Inspection',
  SUA_CHUA_BAO_DUONG: 'Inspection and maintenance',
  DO_THONG_SO: 'Measurement task',
  title_delivered_task: 'Notification just delivered task',
  title_locked_task: 'Notification just locked task',
  title_action_task: 'Notification just {0} task',
  title_unresolved_exist: 'Notification unresolved existence',
  title_iOS_app_code: 'Notification iOS application code',
  title_line_problem: 'Notification line problem',
  title_confirm_line_problem: 'Notification confirm line problem',
  title_cancel_confirm_line_problem: 'Notification cancel confirm line problem',
  title_upgraded_new_version: 'Notification upgraded new version',
  title_notifycation_done_action: 'Notification: {0} have id is {1} just done {2}',
  please_check: 'Please check',
  here: 'here',
  system_has: 'The system has',
  ios_app_code: 'iOS application code',
  DUY_TU_KET_HOP_KHAC_PHUC: 'Combine two types of work',
  SUA_CHUA_VI_TRI: 'Fix location',
  SUA_CHUA_KHOANG_COT: 'Fix distance tower',
  CONG_VIEC_KHAC: 'Other work',
  KINH_DO: 'Longitude',
  VI_DO: 'Latitude',
  MISSING_DATA: 'Missing data',
  IS_NOT_NUMBER: 'Is not number',
  KHOANG_COT: 'Tower span',
  periodic_measure_grounding_resistance_line_content: 'Periodic measure the ground resistance of the line {0} years {1}',
  periodic_measure_tangent_temperature_line_content: 'Periodic contact temperature measurement of the line {0} years {1} times {2}',
  handling_existing_content: 'Content exists at:\r\nLine: {0}\r\nLocation: {1}\r\nTower span: {2}\r\nCategory: {3}',
  periodic_measure_grounding_resistance_line_title: 'Periodic measure ground resistance',
  periodic_measure_tangent_temperature_line_title: 'Periodic contact temperature measurement',
  handling_existing_title: 'Have existing content that needs to be handled',
  bao_cao_phieu_giao_viec: 'Report the assignment sheet of the unit',
  bao_cao_cham_tien_do: 'Report late vote',
  ly_lich_cot: 'Background column, line',
  bieu_mau_dia_phuong: 'The locality has a line going through',
  theo_doi_giao_cheo: 'Crossover tracking table',
  tong_ke_duong_day: 'Total line list',
  so_nhat_ky_van_hanh: 'Operation log book of the power line operation management team',
  theo_doi_hanh_lang_tuyen: 'Corridor tracking table',
  tong_ke_moi_noi: 'Joint inventory table',
  theo_doi_sua_chua_bao_duong: 'Maintenance and repair log book',
  bo_sung_li_do_chua_hoan_thanh: 'Add a reason not completed',
  bo_sung_thong_tin_bay: 'Additional flight information',
  CO_LAP_DUONG_DAY: 'Line isolation',
  KHONG_CO_LAP_DUONG_DAY: 'Do not isolate the line',
  DANH_SACH: 'List',
  THIEU_CACH_DIEN: 'Insulation missing',
  THIEU_DAY_DAN: 'Line missing',
  INVALID_EMAIL: 'Invalid email',
  MSG_XOA_DUONG_DAY: 'Can not delete the power line',
  DA_GIAO_VIEC: 'was assigned a job',
  CO_VI_TRI: 'has location',
  DUONG_DAY_KHONG_DUOC_DE_TRONG: '"Power line" cannot be left blank',

  PHUONG_PHAP_THUC_HIEN_KHONG_DUOC_DE_TRONG: '"Implementation method" cannot be blank',
  VUI_LONG_CAP_NHAT_APP: 'Cannot be done, please update the app to the latest version',
  PHIEN_BAN_APP: 'Current version {0}, latest version {1}',
  nmd_code_already_exist: 'Power station code already exists, please check and try again',
  tba_code_already_exist: 'Substation code already exists, please check and try again',
  split_power_line_success: 'Split power line successfully',
  INVALID_TOKEN: 'Invalid token, please check and try again',

  DATA_NOT_FOUND: 'Data not found',
  THE_PMIS_SERVER_IS_NOT_RESPONDING: 'The PMIS server is not responding',
  NO_PMIS_DATA_AVAILABLE: 'No PMIS data available',

  TBA_CODE: 'Substation code',
  NMD_CODE: 'Power station code',
  ALREADY_EXIST_PLEASE_CHECK_AND_TRY_AGAIN: 'already exists, please check and try again',

  SONG: 'River',
  SUOI: 'Stream',
  DUONG_DAN_SINH: 'Residential road',
  DUONG_BE_TONG: 'Concrete road',
  DUONG_LIEN_THON: 'Inter-village road',
  DUONG_DO_THI: 'Urban road',
  DUONG_QUOC_LO: 'National highway',
  DUONG_CAO_TOC: 'Expressway',
  DUONG_DAY_THONG_TIN: 'Communication line',
  DUONG_DAY_110KV: '110kV transmission line',
  DUONG_DAY_220KV: '220kV transmission line',
  DUONG_DAY_500KV: '500kV transmission line',
  DUONG_DAY_35K: '35kV transmission line',
};
