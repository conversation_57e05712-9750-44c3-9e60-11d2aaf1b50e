import express from 'express';

import { djiMediaFileRouter } from './api/resources/DJICloud/MediaFile/mediaFile.dji.router';
import { djiWaylineRouter } from './api/resources/DJICloud/Wayline/wayline.dji.router';
import { djiStorageRouter } from './api/resources/DJICloud/ObjectStorage/objectStorage.router';

const routerDrone = express.Router();

routerDrone.use('/media/api/v1', djiMediaFileRouter);
routerDrone.use('/wayline/api/v1', djiWaylineRouter);
routerDrone.use('/storage/api/v1', djiStorageRouter);

module.exports = routerDrone;

