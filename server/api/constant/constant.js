export const USER_CODES = {
  SYSTEM_ADMIN: 'SYSTEM_ADMIN',
};

export const ROLE_CODES = {
  CODE_SYSTEM_ADMIN: 'CODE_SYSTEM_ADMIN',
};

export const CAP_DON_VI = {
  TONG_CONG_TY: 'TONG_CONG_TY',
  CONG_TY: 'CONG_TY',
  TRUYEN_TAI_DIEN: 'TRUYEN_TAI_DIEN',
  DOI_TRUYEN_TAI_DIEN: 'DOI_TRUYEN_TAI_DIEN',
};

export const TYPE_DON_VI = {
  TRIAL: 'TRIAL',
  STANDARD: 'STANDARD',
  PRO: 'PRO',
};

export const STORE_DIRS = {
  AVATAR: './storage/avatars',
  CHU_KY: './storage/chu_kys',
  MOBILE_APP: './storage/mobile_app',
  HO_SO_THIET_BI: './storage/ho_so_thiet_bi',
  ANH_VI_TRI: './storage',
  LOGGER_DATA: './storage/logs',
  PHIEU_GIAO_VIEC: './storage/phieugiaoviec',
  IMAGE: './storage/image',
  STORAGE: './storage',
  S3_SERVER_DATA: './storage/s3_server_data',
  DRONE_WAYLINE: './storage/drone_wayline',
  DRONE_MEDIA: './storage/drone_media',
};

export const TEMPLATES_DIRS = {
  TEMPLATES: './server/templates',
  BIEU_MAU: './server/templates/bieumau',
  REPORT: './server/templates/reports',
  CONG_TRINH_XAY_DUNG: './server/templates/congtrinhxaydung',
  DIGITAL_SIGN: './server/templates/digitalSignFile',
  FILE_IMPORT_SAMPLE: './server/templates/fileImportSample',
  OUTPUT_FILE: './server/templates/outputFile',
  PHIEU_DO_THONG_SO: './server/templates/phieudothongso',
  PDF: './server/templates/pdf',
  TONG_KE: './server/templates/tongke',
  LY_LICH_VAN_HANH: './server/templates/lylichvanhanh',
  KHOILUONGQUANLY: './server/templates/khoiluongquanly',
  PHIEUGIAOVIEC: './server/templates/phieugiaoviec',
  RA_SOAT_DU_LIEU: './server/templates/raSoatDuLieu',
};

export const CAP_HO_SO = {
  CAP_1: 'CAP_1',
  CAP_2: 'CAP_2',
  CAP_3: 'CAP_3',
};

export const TEXT_TO_FIND = {
  PHIEU_GIAO_VIEC: 'ký, ghi rõ họ và tên',
  PHIEU_KT_KH: 'ký và ghi rõ họ tên',
  PHIEU_CONG_TAC: 'chữ ký',
};

export const TINH_TRANG_PHIEU = {
  DANG_GIAO_CHAM: { code: 'DANG_GIAO_CHAM', label: 'Đang giao chậm' },
  DA_GIAO_CHAM: { code: 'DA_GIAO_CHAM', label: 'Đã giao chậm' },
  DANG_TIEP_NHAN_CHAM: { code: 'DANG_TIEP_NHAN_CHAM', label: 'Đang tiếp nhận chậm' },
  DA_TIEP_NHAN_CHAM: { code: 'DA_TIEP_NHAN_CHAM', label: 'Đã tiếp nhận chậm' },
  DANG_THUC_HIEN_CHAM: { code: 'DANG_THUC_HIEN_CHAM', label: 'Đang thực hiện chậm' },
  DA_THUC_HIEN_CHAM: { code: 'DA_THUC_HIEN_CHAM', label: 'Đã thực hiện chậm' },
  DANG_XAC_NHAN_KHOA_CHAM: { code: 'DANG_XAC_NHAN_KHOA_CHAM', label: 'Đang xác nhận khóa chậm' },
  DA_XAC_NHAN_KHOA_CHAM: { code: 'DA_XAC_NHAN_KHOA_CHAM', label: 'Đã xác nhận khóa chậm' },
};

export const KET_LUAN = {
  DAT: { label: 'Đạt', value: 'DAT' },
  KHONG_DAT: { label: 'Không đạt', value: 'KHONG_DAT' },
};

export const HUONG_DO = {
  PHIA_NHO: 'Phía cột thứ tự nhỏ',
  PHIA_LON: 'Phía cột thứ tự lớn',
};

export const PDF_WIDTH = 595.273;
export const PDF_HEIGHT = 841.886;

export const EXTENSION_FILE = {
  DOCX: 'docx',
  PDF: 'pdf',
  XLSX: 'xlsx',
};

export const LOAI_DAY = {
  DAY_CAP_QUANG: { label: 'Dây cáp quang', code: 'DAY_CAP_QUANG' },
  DAY_CHONG_SET: { label: 'Dây chống sét', code: 'DAY_CHONG_SET' },
};

export const USER_NAME_ADDON = '1npt\\';

export const GENDER_OPTIONS = {
  MALE: { label: 'Nam', value: 'MALE' },
  FEMALE: { label: 'Nữ', value: 'FEMALE' },
  OTHER: { label: 'Khác', value: 'OTHER' },
};

export const LOAI_DIEU_KIEN_AN_TOAN = {
  CO_LAP_DUONG_DAY: { code: 'CO_LAP_DUONG_DAY', label: 'Cô lập đường dây' },
  KHONG_CO_LAP_DUONG_DAY: { code: 'KHONG_CO_LAP_DUONG_DAY', label: 'Không cô lập đường dây' },
};

export const PHUONG_PHAP_THUC_HIEN = {
  BAY_TU_DONG: 'BAY_TU_DONG',
  BAY_THU_CONG: 'BAY_THU_CONG',
  KIEM_TRA_TRUYEN_THONG: 'KIEM_TRA_TRUYEN_THONG',
};

export const MACH = {
  MACH_DON: 'MACH_DON',
  MACH_KEP: 'MACH_KEP',
};

export const TEMPLATE_TYPE = {
  waypoint: 0,
  mapping2d: 1,
  mapping3d: 2,
  mappingStrip: 3
};
export const MIME_TYPES = {
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.png': 'image/png',
  '.gif': 'image/gif',
  '.webp': 'image/webp',
};

export const RETURN_TYPE = { FULL: 'FULL', ID: 'ID' };

//Dji Cloud
export const DEVICE_DOMAIN = {
  DRONE: 0,
  PAYLOAD: 1,
  REMOTER_CONTROL: 2,
  DOCK: 3,
};
