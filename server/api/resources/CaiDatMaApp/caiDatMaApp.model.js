import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CAI_DAT_APP, USER } from '../../constant/dbCollections';

const schema = new Schema({
  code: { type: String, required: true, validate: /\S+/ },
  url: { type: String, required: true, validate: /\S+/ },
  da_su_dung: { type: Boolean, default: false },
  user_cai_dat: { type: Schema.Types.ObjectId, ref: USER },
  thoi_gian_cai_dat: Date,
  thoi_gian_them: Date,
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

export default mongoose.model(CAI_DAT_APP, schema, CAI_DAT_APP);
