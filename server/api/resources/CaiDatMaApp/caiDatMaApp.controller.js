import * as Service from './caiDatMaApp.service';
import Model from './caiDatMaApp.model';
import User from '../User/user.model';
import * as responseAction from '../../helpers/responseHelper';
import * as responseHelper from '../../helpers/responseHelper';
import exelUtils from '../../utils/exelUtils';
import { trimData } from '../../common/functionCommons';
import queryHelper from '../../helpers/queryHelper';
import * as NotificationService from '../Notification/notification.service';
import { NOTIFICATION_ACTION, NOTIFICATION_TYPE } from '../Notification/notification.constants';
import { CAI_DAT_APP } from '../../constant/dbCollections';
import { extractIds } from '../../utils/dataconverter';
import { sendEmail } from '../../utils/mailHelper';
import { getConfig } from '../../../config/config';

const config = getConfig(process.env.NODE_ENV);

const sortOpts = [];
const populateOpts = [
  {
    path: 'user_cai_dat', select: 'full_name don_vi_id',
    populate: { path: 'don_vi_id', select: 'ten_don_vi' },
  },
];

export async function findOne(req, res) {
  try {
    const data = await Model.findOne({}, { _id: 0 });
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    const setting = await Model.findOne().lean();
    const data = await Model.findOneAndUpdate({ _id: setting._id }, value, { new: true });
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    options.populate = populateOpts;
    options.sort = sortOpts;
    const data = await Model.paginate(criteria, options);
    data.chua_su_dung = await Model.countDocuments(
      {
        $or: [
          { da_su_dung: false },
          { da_su_dung: { $exists: false } },
        ],
        is_deleted: false,
      },
    );
    responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function installLink(req, res) {
  const { t } = req;
  const NUMBER_IOS_CODE = 1000;

  async function sendEmailToUser(userData) {
    if (!userData?.email || !userData?.full_name) return;
    let mailOptions = {
      from: `${t('app_name')} <${config.mail.auth.user}>`, // sender address
      to: userData.email,
      subject: `${t('title_iOS_app_code')}`,
      html: `<h2>${t('hello')} ${userData.full_name}</h2>
              <div>${t('system_have')} <strong>${NUMBER_IOS_CODE}</strong> ${t('ios_app_code')}</div>
              <div>${t('please_check')} <a href="${config.host_admin}/cai-dat" style="font-weight: 700">${t('here')}</a></div>`, // html body
    };

    sendEmail(mailOptions, (err) => {
      if (err) {
        console.log(err);
      } else {
        console.log('send');
      }
    });
  }

  try {
    const installationData = await Model.findOneAndUpdate({ da_su_dung: { $ne: true }, is_deleted: { $ne: true } }, {
      da_su_dung: true,
      user_cai_dat: req.user._id,
      thoi_gian_cai_dat: new Date(),
    });
    const countRemaining = await Model.countDocuments(
      {
        $or: [
          { da_su_dung: false },
          { da_su_dung: { $exists: false } },
        ],
        is_deleted: false,
      },
    );
    if (parseInt(countRemaining) === NUMBER_IOS_CODE) {
      const userNoti = await User.find({ is_system_admin: true, is_deleted: false }).lean();
      const userNotiId = extractIds(userNoti);
      await NotificationService.notification(NOTIFICATION_TYPE.SYSTEM_TO_USER, CAI_DAT_APP,
        { remaining_ios_code: NUMBER_IOS_CODE }, userNotiId, NOTIFICATION_ACTION.UNG_DUNG_IOS,
        null, 'UngDungIos', t);

      userNoti.forEach(user => sendEmailToUser(user));
    }

    return res.redirect(installationData.url);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

async function checkImportByData(t, sheetData) {
  function getSheetByName(sheetData, name) {
    return sheetData.filter((sheet) => sheet.name.toLowerCase().includes(name.toLowerCase()));
  }

  let resultArray = [];
  resultArray = await Service.checkImport(t, getSheetByName(sheetData, 'Sheet1'));
  return resultArray;
}

export async function checkImport(req, res) {

  try {
    const { t } = req;
    let filePath = req.files.file.path;
    const sheetData = await exelUtils.transformFile(filePath);
    const resultArray = await checkImportByData(t, sheetData);
    responseAction.success(res, resultArray);
  } catch (e) {
    responseAction.error(res, e);
  }
}

async function importData(sheetData) {
  sheetData.forEach(sheet => {
    trimData(sheet);
  });

  function getSheetByName(sheetData, sheetName) {
    return sheetData.find((sheet) => sheet.name.toLowerCase().includes(sheetName.toLowerCase()));
  }

  const result = {};
  if (getSheetByName(sheetData, 'Sheet1')) {
    result['Sheet1'] = await Service.importData(getSheetByName(sheetData, 'Sheet1'));
  }
  return result;
}

export async function importOne(req, res) {
  try {
    let filePath = req.files.file.path;
    const sheetDataRaw = await exelUtils.transformFile(filePath);
    const sheetData = sheetDataRaw.map(sheet => trimData(sheet));
    const result = await importData(sheetData);
    responseAction.success(res, result);
  } catch (e) {
    responseAction.error(res, e);
  }
}
