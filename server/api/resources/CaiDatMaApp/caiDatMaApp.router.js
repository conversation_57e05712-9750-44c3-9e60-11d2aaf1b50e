import express from 'express';
import passport from 'passport';
import * as caiDatAppController from './caiDatMaApp.controller';
import { authorizationMiddleware } from '../RBAC/middleware';
import SettingPermission from '../RBAC/permissions/SettingPermission';
import { checkTempFolder, multipartMiddleware } from '../../utils/fileUtils';
import { loggerMiddleware } from '../../logs/middleware';

export const caiDatMaAppRouter = express.Router();
caiDatMaAppRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
caiDatMaAppRouter.post('*', authorizationMiddleware([SettingPermission.CREATE]));
caiDatMaAppRouter.put('*', authorizationMiddleware([SettingPermission.UPDATE]));
caiDatMaAppRouter.delete('*', authorizationMiddleware([SettingPermission.DELETE]));
caiDatMaAppRouter.route('/')
  .get(caiDatAppController.getAll);
caiDatMaAppRouter
  .route('/import')
  .post(passport.authenticate('jwt', { session: false }),
    checkTempFolder, multipartMiddleware, caiDatAppController.importOne);

caiDatMaAppRouter.route('/install-link')
  .get(caiDatAppController.installLink);
caiDatMaAppRouter
  .route('/:id')
  .get(caiDatAppController.findOne);

