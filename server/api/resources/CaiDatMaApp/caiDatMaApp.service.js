import * as ValidatorHelper from '../../helpers/validatorHelper';
import { isValidHttpUrl } from '../../common/functionCommons';
import CAI_DAT_APP from './caiDatMaApp.model';

export function getAll(query, projection = {}) {
  return CAI_DAT_APP.find(query, projection).lean();
}

export function getOne() {
  return CAI_DAT_APP.findOne().lean();
}

export async function updateAll(dataUpdate) {
  for (const row of dataUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await CAI_DAT_APP.findByIdAndUpdate(value._id, value);
  }
}

const Joi = require('joi');

const objSchema = Joi.object({
  api_phan_tich_hinh_anh: Joi.string(),
  api_dataset: Joi.string(),

});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

const Headers = {
  CODE: 'Code',
  DUONG_DAN_DOI_QUA: 'Đường Dẫn Đổi Quà',
};

export function checkImport(t, sheetData) {
  if (!sheetData) return null;

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row) {
    let errors = [];
    if (!row[Headers.CODE]) {
      errors = [...errors, createError(Headers.CODE, t('setting_code_missing'))];
    }
    if (!row[Headers.DUONG_DAN_DOI_QUA] || isValidHttpUrl(row[Headers.DUONG_DAN_DOI_QUA])) {
      errors = [...errors, createError(Headers.DUONG_DAY_CHINH, t('parent_line_wrong_or_missing'))];
    }
    if (errors.length) {
      row['Lỗi'] = errors;
    } else {
      row['Lỗi'] = null;
    }
    return row;
  }

  return sheetData?.map(e => {
    e.rows = e.rows.map(row => validateRow(t, row));
    return e;
  });
}

export async function importData(sheetData) {
  const { rows } = sheetData;

  function convertToDB(row) {
    return {
      code: row[Headers.CODE]?.trim(),
      url: row[Headers.DUONG_DAN_DOI_QUA]?.trim(),
      thoi_gian_them: new Date(),
      is_deleted: false,
    };
  }

  const dataToDB = rows.map(row => convertToDB(row));
  return await CAI_DAT_APP.bulkWrite(
    dataToDB.map((row) =>
      ({
        updateOne: {
          filter: { code: row.code },
          update: { $set: row },
          upsert: true,
        },
      }),
    ),
  );
}
