import mongoose, { Schema } from 'mongoose';
import { NOTIFICATION, USER } from '../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
    user_id: { type: Schema.Types.ObjectId, ref: USER },
    source_type: { type: String },
    payload_type: { type: String },
    type: { type: String },
    status: { type: String },
    content: { type: String },
    payload: Object,
    source_id: { type: Schema.Types.ObjectId },
    title: String,
    is_deleted: { type: Boolean, default: false, select: false },
  },
  {
    timestamps: {
      createdAt: 'thoi_gian_tao',
      updatedAt: 'thoi_gian_cap_nhat',
    },
    collation: { locale: 'vi' },
    versionKey: false,
  });
schema.plugin(mongoosePaginate);

// Optimized indexes for notification queries
// Main compound index for getAll query: user_id + sort by thoi_gian_tao
schema.index({ 
  user_id: 1, 
  thoi_gian_tao: -1 
});

// Index for filtering by payload_type (used in API query params)
schema.index({ 
  payload_type: 1, 
  status: 1, 
  thoi_gian_tao: -1 
});

// Compound index for user-specific filtered queries
schema.index({ 
  user_id: 1, 
  payload_type: 1, 
  status: 1, 
  thoi_gian_tao: -1 
});

// Index for soft delete queries
schema.index({ 
  is_deleted: 1, 
  user_id: 1, 
  thoi_gian_tao: -1 
});

// Index for source-based queries
schema.index({ 
  source_type: 1, 
  source_id: 1 
});
export { schema as DocumentSchema };
export default mongoose.model(NOTIFICATION, schema, NOTIFICATION);
