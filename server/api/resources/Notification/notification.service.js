import * as Validator<PERSON><PERSON>per from '../../helpers/validatorHelper';
import { NOTIFICATION_ACTION, NOTIFICATION_STATUS, NOTIFICATION_TYPE } from './notification.constants';
import NOTIFICATION from './notification.model';
import REMIND from '../Remind/remind.model';
import { Server } from 'socket.io';
import { NOTIFICATION_EVENT } from './notification.event';
import { PHIEU_CONG_TAC, PHIEU_GIAO_VIEC } from '../../constant/dbCollections';
import userService from '../User/user.service';
import remindService from '../Remind/remind.service';
import Expo from 'expo-server-sdk';
import i18next from 'i18next';

const Joi = require('joi');
const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return NOTIFICATION.create(value);
}

export function getAll(query, projection = {}) {
  return NOTIFICATION.find(query, projection).lean();
}

export function getAllByUserId(userId) {
  const apiResponse = NOTIFICATION.find({ user_id: userId, is_deleted: false }).sort('-thoi_gian_tao').lean();
  return apiResponse;
}

export async function updateAll(chiTietUpdate) {
  for (const row of chiTietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await NOTIFICATION.findByIdAndUpdate(value._id, value);
  }
}

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};

function generateContent(type, sourceType, source, action, data, payloadType) {
  if (type === NOTIFICATION_TYPE.USER_TO_USER) {
    switch (payloadType) {
      case PHIEU_GIAO_VIEC:
        // const loaiCongViec = LOAI_CONG_VIEC[data.loai_cong_viec].name;
        // return `Người dùng ${source.full_name} vừa ${action} ${loaiCongViec} số ${data.so_phieu} mà bạn tham gia`;
        return `{"key": "NGUOI_DUNG_THUC_HIEN_CONG_VIEC","fullName":"${source.full_name}","action": "${data.trang_thai_cong_viec}","loaiCongViec": "${data.loai_cong_viec}","soPhieu": "${data.so_phieu}"}`;
      case PHIEU_CONG_TAC:
        // return `Người dùng ${source.full_name} vừa ${action} công tác số ${data.so_phieu} mà bạn tham gia`;
        return `{"key": "NGUOI_DUNG_THUC_HIEN_CONG_TAC","fullName":"${source.full_name}","action": "${data.trang_thai_cong_viec}","soPhieu": "${data.so_phieu}"}`;
      default:
        // return `Bạn có thông báo mới`;
        return `BAN_CO_THONG_BAO_MOI`;
    }
  } else if (type === NOTIFICATION_TYPE.SYSTEM_TO_USER) {
    switch (action) {
      case NOTIFICATION_ACTION.TON_TAI_CHUA_XU_LY:
        // return `Đơn vị "${source.ten_don_vi}" có ${data.so_luong_ton_tai} tồn tại chưa được xử lý`;
        return `{"key": "DON_VI_CO_TON_TAI_CHUA_XU_LY","tenDonVi":"${source.ten_don_vi}","soLuongTonTai": "${data.so_luong_ton_tai}"}`;
      case NOTIFICATION_ACTION.UNG_DUNG_IOS:
        // return `Hệ thống còn ${source.remaining_ios_code} mã code ứng dụng iOS`;
        return `{"key": "HE_THONG_CON_MA_CODE_UNG_DUNG_IOS","maCodeIos":"${source.remaining_ios_code}"}`;
      case NOTIFICATION_ACTION.SU_CO_DUONG_DAY:
        // return `Có ${source.so_luong_su_co} sự cố đường dây mới`;
        // return `CO_SU_CO_DUONG_DAY_MOI`;
        return `{"key": "CO_SU_CO_DUONG_DAY_MOI","soLuongSuCo":"${source.so_luong_su_co}"}`;
      case NOTIFICATION_ACTION.XAC_NHAN_SU_CO_DUONG_DAY:
        // return `Sự cố đường dây ${data?.duong_day_id?.ten_duong_day} đã được xác nhận`;
        // return `SU_CO_DUONG_DAY_DA_XAC_NHAN`;
        return `{"key": "SU_CO_DUONG_DAY_DA_XAC_NHAN","tenDuongDay":"${data?.duong_day_id?.ten_duong_day}"}`;
      case NOTIFICATION_ACTION.HUY_XAC_NHAN_SU_CO_DUONG_DAY:
        // return `Sự cố đường dây ${data?.duong_day_id?.ten_duong_day} đã được hủy xác nhận`;
        // return `SU_CO_DUONG_DAY_DA_HUY_XAC_NHAN`;
        return `{"key": "SU_CO_DUONG_DAY_DA_HUY_XAC_NHAN","tenDuongDay":"${data?.duong_day_id?.ten_duong_day}"}`;
      case NOTIFICATION_ACTION.CAP_NHAT_VERSION_MOI:
        switch (payloadType) {
          case "CapNhatVersion":
            // return `Phần mềm ứng dụng Quản lý đường dây và Điều khiển bay đã được nâng cấp phiên bản mới`;
            return `{"key": "CAP_NHAT_VERSION"}`;
          case "CapNhatVersionDKB":
            // return `Phần mềm ứng dụng Điều khiển bay đã được nâng cấp phiên bản mới`;
            return `{"key": "CAP_NHAT_VERSION_DKB"}`;
          case "CapNhatVersionQLDD":
            // return `Phần mềm ứng dụng Quản lý đường dây đã được nâng cấp phiên bản mới`;
            return `{"key": "CAP_NHAT_VERSION_QLDD"}`;
          default:
            // return `Bạn có thông báo mới`;
            return `{"key": "BAN_CO_THONG_BAO_MOI"}`;
        }
      default:
        return '';
    }
  }
  // return `Bạn có thông báo mới`;
  return `{"key": "BAN_CO_THONG_BAO_MOI"}`;
}

function generateTitle(type, sourceType, source, action, data) {
  if (type === NOTIFICATION_TYPE.USER_TO_USER) {
    switch (action) {
      case NOTIFICATION_ACTION.USER_GIAO_PHIEU:
        // return `Thông báo có phiếu vừa được giao`;
        return `{"key": "THONG_BAO_CO_PHIEU_VUA_DUOC_GIAO"}`;
      case NOTIFICATION_ACTION.USER_KHOA_PHIEU:
        // return `Thông báo có phiếu vừa được khoá`;
        return `{"key": "THONG_BAO_CO_PHIEU_VUA_DUOC_KHOA"}`;
      default:
        // return `Thông báo vừa có phiếu vừa được ${action}`;
        return `{"key": "THONG_BAO_CO_PHIEU_VUA_DUOC_ACTION","action":"${action}"}`;
    }
  } else if (type === NOTIFICATION_TYPE.SYSTEM_TO_USER) {
    switch (action) {
      case NOTIFICATION_ACTION.TON_TAI_CHUA_XU_LY:
        // return `Thông báo tồn tại chưa được xử lý`;
        return `{"key": "THONG_BAO_TON_TAI_CHUA_XU_LY"}`;
      case NOTIFICATION_ACTION.UNG_DUNG_IOS:
        // return `Thông báo mã code ứng dụng iOS`;
        return `{"key": "THONG_BAO_MA_CODE_UNG_DUNG_IOS"}`;
      case NOTIFICATION_ACTION.SU_CO_DUONG_DAY:
        // return `Thông báo sự cố đường dây`;
        return `{"key": "THONG_BAO_SU_CO_DUONG_DAY"}`;

      case NOTIFICATION_ACTION.XAC_NHAN_SU_CO_DUONG_DAY:
        // return `Thông báo xác nhận sự cố đường dây`;
        return `{"key": "THONG_BAO_XAC_NHAN_SU_CO_DUONG_DAY"}`;
      case NOTIFICATION_ACTION.HUY_XAC_NHAN_SU_CO_DUONG_DAY:
        // return `Thông báo hủy xác nhận sự cố đường dây`;
        return `{"key": "THONG_BAO_HUY_XAC_NHAN_SU_CO_DUONG_DAY"}`;
      case NOTIFICATION_ACTION.CAP_NHAT_VERSION_MOI:
        // return `Thông báo cập nhật version`;
        return `{"key": "THONG_BAO_CAP_NHAT_VERSION_MOI"}`;

      default:
        return '';
    }
  }
  // return `Thông báo: ${sourceType} có id là ${source._id} vừa thực hiện việc ${action}`;
  return `{"key": "THONG_BAO_KHAC","sourceType":"${sourceType}","sourceId": "${source._id}","action":"${action}"}`;

}

export async function notification(type, sourceType, source, users, action, data, payloadType, t) {
  try {
    const contentGenerated = generateContent(type, sourceType, source, action, data, payloadType);
    const titleGenerated = generateTitle(type, sourceType, source, action, data, payloadType);
    const newNotificationData = users.map(userId => {
      return {
        user_id: userId,
        source_type: sourceType,
        type: type,
        payload_type: payloadType,
        payload_id: data?._id,
        status: NOTIFICATION_STATUS.SENT,
        content: contentGenerated,
        payload: data,
        title: titleGenerated,
        source_id: source?._id,
      };
    });
    const newNotifications = await NOTIFICATION.create(newNotificationData);
    pushNotification(t, newNotifications, "Notice");
  } catch (e) {
    console.log('e', e);
  }
}

let io;
const socketIdStorage = {};

let allNotification;
let allRemind;
let unReadNotification;
let unReadRemind;

export function initNotificationService(server) {
  io = new Server(server, {
    transports: ['websocket'],
    path: '/socket',
  });
  io.on('connection', (socket) => {
    console.log('a socket connected', socket.id);
    socket.on('disconnect', createHandler(socket, unregisterUserDevice));
    socket.on('user_login_id', createHandler(socket, registerUserDevice));
    socket.on('user_received_notification', createHandler(socket, receivedAllNotification));
    socket.on('user_viewed_all_notifications', createHandler(socket, viewedAllNotification));
    socket.on('user_viewed_one_notification', createHandler(socket, viewedOneNotification));
  });
}

function createHandler(socket, registerUserDevice) {
  return (payload) => {
    return registerUserDevice(socket, payload);
  };
}

async function getCurrentNumberNotifications(user_id, keyType) {
  if (keyType === 'Notice'){
    allNotification = await getAllByUserId(user_id);
    unReadNotification = allNotification.filter(notification => notification.status !== NOTIFICATION_STATUS.VIEWED);
  }
  if (keyType === 'Remind'){
    allRemind = await remindService.getAllByUserId(user_id);
    unReadRemind = allRemind.filter(notification => notification.status !== NOTIFICATION_STATUS.VIEWED);
  }

  return {
    // total: allNotification?.length,
    totalNotice: allNotification?.length,
    totalRemind: allRemind?.length,
    // unread: unReadNotification?.length,
    unreadNotice: unReadNotification?.length,
    unreadRemind: unReadRemind?.length,
  };
}

async function informNotificationCountToAllDevices(user_id, keyType) {
  try {
    emitEventToUser(user_id, NOTIFICATION_EVENT.NOTIFICATION_COUNT, await getCurrentNumberNotifications(user_id, keyType));
  } catch (e) {
    console.log(e);
  }
}

async function informOneNotificationsUpdatedToAllDevices(user_id, notificationUpdated, keyType) {
  await informNotificationCountToAllDevices(user_id, keyType);
  emitEventToUser(user_id, NOTIFICATION_EVENT.NOTIFICATION_UPDATED_ONE, notificationUpdated);
}

async function informAllNotificationsUpdatedToAllDevices(user_id, keyType) {
  await informNotificationCountToAllDevices(user_id, keyType);
  emitEventToUser(user_id, NOTIFICATION_EVENT.NOTIFICATION_UPDATED_ALL);
}

async function informNotificationCountToOneDevice(socket, user_id, keyType) {
  try {
    socket.emit(NOTIFICATION_EVENT.NOTIFICATION_COUNT, await getCurrentNumberNotifications(user_id, keyType));
  } catch (e) {
    console.log(e);
  }
}

async function registerUserDevice(socket, { user_id, keyType }) {
  socket.user_id = user_id;
  if (socketIdStorage[user_id]) {
    socketIdStorage[user_id] = [socket.id, ...socketIdStorage[user_id]];
  } else {
    socketIdStorage[user_id] = [socket.id];
  }
  await informNotificationCountToOneDevice(socket, user_id, keyType);
}

export function unregisterUserDevice(socket) {
  console.log('a socket disconnected');
  const user_id = socket.user_id;
  const socketId = socket.id;
  if (socketIdStorage.hasOwnProperty(socket.user_id)) {
    let arrSocketIO = socketIdStorage[user_id];
    let idx = arrSocketIO.indexOf(socketId);
    if (idx !== -1) {
      arrSocketIO.splice(idx, 1);
    }
    if (arrSocketIO.length === 0) {
      delete socketIdStorage[user_id];
    } else {
      socketIdStorage[user_id] = arrSocketIO;
    }
  }
}

async function receivedAllNotification(socket, { user_id }) {
  try {
    NOTIFICATION.updateMany({ user_id: user_id }, { status: NOTIFICATION_STATUS.RECEIVED });
    await informAllNotificationsUpdatedToAllDevices(user_id);
  } catch (e) {
    console.log(e);
  }
}

async function viewedAllNotification(socket, { user_id, keyType }) {
  try {
    if (keyType === 'Notice') await NOTIFICATION.updateMany({ user_id: user_id }, { status: NOTIFICATION_STATUS.VIEWED });
    if (keyType === 'Remind') await REMIND.updateMany({ user_id: user_id }, { status: NOTIFICATION_STATUS.VIEWED });
    await informAllNotificationsUpdatedToAllDevices(user_id, keyType);
  } catch (e) {
    console.log(e);
  }
}

async function viewedOneNotification(socket, { user_id, _id, keyType }) {
  try {
    let notificationViewed;
    if (keyType === 'Notice'){
       notificationViewed = await NOTIFICATION.findByIdAndUpdate(_id, { status: NOTIFICATION_STATUS.VIEWED }, { new: true }).lean();
       multiLanguageNotify(null, notificationViewed);
    }
    if (keyType === 'Remind'){
      notificationViewed = await REMIND.findByIdAndUpdate(_id, { status: NOTIFICATION_STATUS.VIEWED }, { new: true }).lean();
      remindService.multiLanguageRemind(null, notificationViewed);
    }

    multiLanguageNotify(null, notificationViewed);
    await informOneNotificationsUpdatedToAllDevices(user_id, notificationViewed, keyType);
  } catch (e) {
    console.log(e);
  }
}

function emitEventToUser(user_id, event, payload) {
  socketIdStorage[user_id]?.forEach(socketId => {
    io.sockets.sockets.get(socketId)?.emit(event, payload);
  });
}

export function pushNotification(t, notifications, keyType) {
  if (keyType === 'Notice') {
    notifications.forEach(notification => {
      multiLanguageNotify(t, notification);
      const jsoNotification = JSON.parse(JSON.stringify(notification));
      informNotificationCountToAllDevices(jsoNotification.user_id, keyType);
      emitEventToUser(jsoNotification.user_id, NOTIFICATION_EVENT.NOTIFICATION_NEW, jsoNotification);
      pushNotifyMobile(jsoNotification.user_id, NOTIFICATION_EVENT.NOTIFICATION_NEW, jsoNotification, keyType);
    });
  }

  if (keyType === 'Remind') {
    notifications.forEach(notification => {
      remindService.multiLanguageRemind(t, notification);
      const jsoNotification = JSON.parse(JSON.stringify(notification));
      informNotificationCountToAllDevices(jsoNotification.user_id, keyType);
      emitEventToUser(jsoNotification.user_id, NOTIFICATION_EVENT.REMIND_NEW, jsoNotification);
      pushNotifyMobile(jsoNotification.user_id, NOTIFICATION_EVENT.REMIND_NEW, jsoNotification, keyType);
    });
  }
}

export function multiLanguageNotify(t, noti) {
  let notifyContent = '';
  let notifyContentObject;
  let notifyTitle = '';
  let notifyTitleObject;

  try {
    notifyContentObject = JSON.parse(`${noti.content}`);
  } catch {
    notifyContentObject = null;
  }

  try {
    notifyTitleObject = JSON.parse(`${noti.title}`);
  } catch {
    notifyTitleObject = null;
  }

  if (notifyContentObject?.key) {
    switch (notifyContentObject.key) {
      case "NGUOI_DUNG_THUC_HIEN_CONG_VIEC":
        notifyContent = (t ? t('notify_user_do_something_work') : i18next.t('notify_user_do_something_work')).format(notifyContentObject.fullName, t ? t(notifyContentObject.action) : i18next.t(notifyContentObject.action), t ? t(notifyContentObject.loaiCongViec) : i18next.t(notifyContentObject.loaiCongViec), notifyContentObject.soPhieu);
        break;
      case "NGUOI_DUNG_THUC_HIEN_CONG_TAC":
        notifyContent = (t ? t('notify_user_do_something_jobsheet') : i18next.t('notify_user_do_something_jobsheet')).format(notifyContentObject.fullName, t ? t(notifyContentObject.action) : i18next.t(notifyContentObject.action), notifyContentObject.soPhieu);
        break;
      case "DON_VI_CO_TON_TAI_CHUA_XU_LY":
        notifyContent = (t ? t('notify_unit_has_unresolved_exist') : i18next.t('notify_unit_has_unresolved_exist')).format(notifyContentObject.tenDonVi, notifyContentObject.soLuongTonTai);
        break;
      case "HE_THONG_CON_MA_CODE_UNG_DUNG_IOS":
        notifyContent = (t ? t('notify_system_has_ios_code') : i18next.t('notify_system_has_ios_code')).format(notifyContentObject.maCodeIos);
        break;
      case "CO_SU_CO_DUONG_DAY_MOI":
        notifyContent = (t ? t('notify_there_are_new_line_problem') : i18next.t('notify_there_are_new_line_problem')).format(notifyContentObject.soLuongSuCo);
        break;
      case "SU_CO_DUONG_DAY_DA_XAC_NHAN":
        notifyContent = (t ? t('notify_line_problem_confirm') : i18next.t('notify_line_problem_confirm')).format(notifyContentObject.tenDuongDay);
        break;
      case "SU_CO_DUONG_DAY_DA_HUY_XAC_NHAN":
        notifyContent = (t ? t('notify_line_problem_cancel_confirm') : i18next.t('notify_line_problem_cancel_confirm')).format(notifyContentObject.tenDuongDay);
        break;
      case "CAP_NHAT_VERSION":
        notifyContent = (t ? t('notify_upgraded_new_version_line_flight') : i18next.t('notify_upgraded_new_version_line_flight'));
        break;
      case "CAP_NHAT_VERSION_DKB":
        notifyContent = (t ? t('notify_upgraded_new_version_flight') : i18next.t('notify_upgraded_new_version_flight'));
        break;
      case "CAP_NHAT_VERSION_QLDD":
        notifyContent = (t ? t('notify_upgraded_new_version_line') : i18next.t('notify_upgraded_new_version_line'));
        break;
      case "BAN_CO_THONG_BAO_MOI":
        notifyContent = (t ? t('notify_default_new_message') : i18next.t('notify_default_new_message'));
        break;
      default:
        notifyContent = (t ? t('notify_default_new_message') : i18next.t('notify_default_new_message'));
        break;
    }
  } else {
    notifyContent = noti.content;
  }

  if (notifyTitleObject?.key) {
    switch (notifyTitleObject.key) {
      case "THONG_BAO_CO_PHIEU_VUA_DUOC_GIAO":
        notifyTitle = (t ? t('title_delivered_task') : i18next.t('title_delivered_task'));
        break;
      case "THONG_BAO_CO_PHIEU_VUA_DUOC_KHOA":
        notifyTitle = (t ? t('title_locked_task') : i18next.t('title_locked_task'));
        break;
      case "THONG_BAO_CO_PHIEU_VUA_DUOC_ACTION":
        notifyTitle = (t ? t('title_action_task') : i18next.t('title_action_task')).format(notifyTitleObject.action);
        break;
      case "THONG_BAO_TON_TAI_CHUA_XU_LY":
        notifyTitle = (t ? t('title_unresolved_exist') : i18next.t('title_unresolved_exist'));
      case "THONG_BAO_MA_CODE_UNG_DUNG_IOS":
        notifyTitle = (t ? t('title_iOS_app_code') : i18next.t('title_iOS_app_code'));
        break;
      case "THONG_BAO_SU_CO_DUONG_DAY":
        notifyTitle = (t ? t('title_line_problem') : i18next.t('title_line_problem'));
        break;
      case "THONG_BAO_XAC_NHAN_SU_CO_DUONG_DAY":
        notifyTitle = (t ? t('title_confirm_line_problem') : i18next.t('title_confirm_line_problem'));
        break;
      case "THONG_BAO_HUY_XAC_NHAN_SU_CO_DUONG_DAY":
        notifyTitle = (t ? t('title_cancel_confirm_line_problem') : i18next.t('title_cancel_confirm_line_problem'));
        break;
      case "THONG_BAO_CAP_NHAT_VERSION_MOI":
        notifyTitle = (t ? t('title_upgraded_new_version') : i18next.t('title_upgraded_new_version'));
        break;
      case "THONG_BAO_KHAC":
        notifyTitle = (t ? t('title_notifycation_done_action') : i18next.t('title_notifycation_done_action')).format(notifyTitleObject.sourceType, notifyTitleObject.sourceId, notifyTitleObject.action);
        break;
      default:
        notifyTitle = '';
        break;
    }
  } else {
    notifyTitle = noti.title;
  }

  noti.content = notifyContent;
  noti.title = notifyTitle;
}

export async function pushNotifyMobile(user_id, event, data, keyType) {
  if (keyType === 'Notice') {
    let userTokens = await userService.getTokens(user_id);
    const { totalNotice, unreadNotice } = await getCurrentNumberNotifications(user_id, keyType);
    const dataReduced = {
      ...data,
      payload_id: data.payload?._id,
      payload: { _id: data.payload?._id, loai_cong_viec: data.payload?.loai_cong_viec },
    };
    expoPushNotification(userTokens, data.title, data.content, unreadNotice, dataReduced);
  }
  if (keyType === 'Remind') {
    let userTokens = await userService.getTokens(user_id);
    const { totalRemind, unreadRemind } = await getCurrentNumberNotifications(user_id, keyType);
    const dataReduced = {
      ...data,
      payload_id: data.payload?._id,
      payload: { _id: data.payload?._id, loai_cong_viec: data.payload?.loai_cong_viec },
    };
    expoPushNotification(userTokens, data.title, data.content, unreadRemind, dataReduced);
  }
}

export function expoPushNotification(expoTokens, title, body, badge, data) {
  let expo = new Expo();
  let messages = [];

  for (let pushToken of expoTokens) {
    // Each push token looks like ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]

    // Check that all your push tokens appear to be valid Expo push tokens
    if (!Expo.isExpoPushToken(pushToken)) {
      console.error(`Push token ${pushToken} is not a valid Expo push token`);
      continue;
    }

    // Construct a message (see https://docs.expo.io/versions/latest/guides/push-notifications.html)
    messages.push({
      to: pushToken,
      title: title,
      body: body,
      data: data,
      sound: 'default',
      badge: badge,
      channelId: 'notification',
    });
  }

  // The Expo push notification service accepts batches of notifications so

  // that you don't need to send 1000 requests to send 1000 notifications. We
  // recommend you batch your notifications to reduce the number of requests
  // and to compress them (notifications with similar content will get
  // compressed).
  let chunks = expo.chunkPushNotifications(messages);
  let tickets = [];
  (async () => {
    // Send the chunks to the Expo push notification service. There are
    // different strategies you could use. A simple one is to send one chunk at a
    // time, which nicely spreads the load out over time:
    for (let chunk of chunks) {
      try {
        let ticketChunk = await expo.sendPushNotificationsAsync(chunk);
        // console.log(ticketChunk);
        tickets.push(...ticketChunk);
        // NOTE: If a ticket contains an error code in ticket.details.error, you
        // must handle it appropriately. The error codes are listed in the Expo
        // documentation:
        // https://docs.expo.io/versions/latest/guides/push-notifications#response-format
      } catch (error) {
        // console.error(error);
        if (error.code === 'PUSH_TOO_MANY_EXPERIENCE_IDS') {
          // console.log('lỗi duplicate');
          Object.values(error.details).forEach(tokens => {
            expoPushNotification(tokens, title, body, badge, data);
          });
        }
      }
    }
  })();
}
