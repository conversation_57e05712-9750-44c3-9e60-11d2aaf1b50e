import mongoose from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import {
  CONG_TRINH,
  KHOANG_COT,
  TIEU_CHI,
  TON_TAI_LAM_QUANG,
  VI_TRI,
  KHOANG_NEO, TIEU_CHI_LAM_QUANG,
} from '../../../constant/dbCollections';
import { GIAI_DOAN_GHI_NHAN, TRANG_THAI_TON_TAI } from './tonTaiLamQuang.constant';

const { Schema } = mongoose;

const schema = new Schema({
  cong_trinh_id: { type: Schema.Types.ObjectId, ref: CONG_TRINH },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  khoang_cot_id: { type: Schema.Types.ObjectId, ref: KHOANG_COT },
  khoang_neo_id: { type: Schema.Types.ObjectId, ref: KHOANG_NEO },
  tieu_chi_id: { type: Schema.Types.ObjectId, ref: TIEU_CHI_LAM_QUANG, required: true },
  khoi_luong: { type: String },
  don_vi_tinh: { type: String },
  tieu_chuan_quy_dinh: { type: String },
  ket_qua_nghiem_thu: { type: String },
  giai_doan_ghi_nhan: {
    type: String,
    enum: Object.keys(GIAI_DOAN_GHI_NHAN),
    default: GIAI_DOAN_GHI_NHAN.DONG_DIEN.code,
  },
  trach_nhiem_giai_quyet: { type: String },
  tien_do_giai_quyet: { type: Date },
  ghi_chu: { type: String },
  type_ton_tai: { type: String },
  trang_thai: {
    type: String,
    enum: Object.keys(TRANG_THAI_TON_TAI),
    default: TRANG_THAI_TON_TAI.CHUA_XU_LY.code,
  },
  xac_nhan_don_vi_qlvh: { type: Boolean, default: true },
  xac_nhan_ban_qlda: { type: Boolean, default: true },
  ly_do_tu_choi: { type: String },
  y_kien_ban_a: { type: String },
  y_kien_cua_ptc: { type: String },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});


schema.plugin(mongoosePaginate);
export default mongoose.model(TON_TAI_LAM_QUANG, schema, TON_TAI_LAM_QUANG);
