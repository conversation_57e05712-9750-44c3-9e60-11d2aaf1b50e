import TonTaiLamQuangModel from './tonTaiLamQuang.model';
import * as ValidatorHelper from '../../../helpers/validatorHelper';
import { TRANG_THAI_TON_TAI } from './tonTaiLamQuang.constant';
import { extractKeys } from '../../../utils/dataconverter';
import createBaseService from '../../../base/baseService';

const Joi = require('joi');

const objSchema = Joi.object({
  ket_qua_nghiem_thu: Joi.string().messages(ValidatorHelper.messageDefine('Kết quả nghiệm thu')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}


const baseService = createBaseService(TonTaiLamQuangModel);

export const createMulti = baseService.createMulti;
export const getAll = baseService.getAll;
export const getOne = baseService.getOne;
export const remove = baseService.remove;
export const distinctId = baseService.distinctId;
export const updateByQuery = baseService.updateByQuery;
export const updateByRows = baseService.updateByRows;
export const deleteAll = baseService.deleteAll;
export const updateAll = baseService.updateAll;


export async function updateTrangThai(arrData, statusUpdate) {
  switch (statusUpdate) {
    case TRANG_THAI_TON_TAI.CHUA_XU_LY.code:
      const tonTaiLamQuangIds = extractKeys(arrData, 'ton_tai_lam_quang_id');
      await TonTaiLamQuangModel.updateMany({ _id: { $in: tonTaiLamQuangIds } }, { trang_thai: TRANG_THAI_TON_TAI.CHUA_XU_LY.code });
      break;
    case TRANG_THAI_TON_TAI.DA_XU_LY.code:
      await TonTaiLamQuangModel.updateMany({ _id: { $in: extractKeys(arrData, '_id') } }, { trang_thai: TRANG_THAI_TON_TAI.DA_XU_LY.code });
      break;
    case  TRANG_THAI_TON_TAI.DANG_XU_LY.code:
      await TonTaiLamQuangModel.updateMany({ _id: { $in: extractKeys(arrData, '_id') } }, { trang_thai: TRANG_THAI_TON_TAI.DANG_XU_LY.code });
      break;
    default:
  }

}

