import * as Service from './tonTaiLamQuang.service';
import Model from './tonTaiLamQuang.model';
import * as controllerHelper from '../../../helpers/controllerHelper';


const populateOpts = [
  { path: 'cong_trinh_id' },
  { path: 'ton_tai_lam_quang_id' },
  { path: 'vi_tri_id' },
  { path: 'khoang_cot_id' },
  { path: 'khoang_neo_id' },
  { path: 'tieu_chi_id' },
];
const uniqueOpts = [];

const sortOpts = { 'created_at': -1 };

export const remove = controllerHelper.createRemoveFunction(Model);
export const findOne = controllerHelper.createFindOneFunction(Model);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, null, populateOpts, sortOpts);

