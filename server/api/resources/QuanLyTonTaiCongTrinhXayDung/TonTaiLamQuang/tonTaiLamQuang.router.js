import express from 'express';
import passport from 'passport';
import * as Controller from './tonTaiLamQuang.controller';
import CongTrinhXayDungPermission from '../../RBAC/permissions/CongTrinhXayDungPermission';
import XacNhanLamQuangPermission from '../../RBAC/permissions/XacNhanLamQuangPermission';
import { authorizationMiddleware } from '../../RBAC/middleware';
import { loggerMiddleware } from '../../../logs/middleware';

export const tonTaiLamQuangRouter = express.Router();

tonTaiLamQuangRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
tonTaiLamQuangRouter.post('*', authorizationMiddleware([CongTrinhXayDungPermission.CREATE && XacNhanLamQuangPermission.CREATE]));
tonTaiLamQuangRouter.put('*', authorizationMiddleware([CongTrinhXayDungPermission.UPDATE && XacNhanLamQuangPermission.UPDATE]));
tonTaiLamQuangRouter.get('*', authorizationMiddleware([CongTrinhXayDungPermission.READ && XacNhanLamQuangPermission.READ]));
tonTaiLamQuangRouter.delete('*', authorizationMiddleware([CongTrinhXayDungPermission.DELETE && XacNhanLamQuangPermission.DELETE]));

tonTaiLamQuangRouter
  .route('/')
  .get(Controller.getAll)
  .post(Controller.create);

tonTaiLamQuangRouter
  .route('/:id')
  .get(Controller.findOne)
  .put(Controller.update)
  .delete(Controller.remove);
