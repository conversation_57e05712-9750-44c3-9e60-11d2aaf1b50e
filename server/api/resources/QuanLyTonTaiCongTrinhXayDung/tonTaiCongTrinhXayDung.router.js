import express from 'express';
import passport from 'passport';
import * as Controller from './tonTaiCongTrinhXayDung.controller';

export const tonTaiCongTrinhXayDungRouter = express.Router();

tonTaiCongTrinhXayDungRouter
  .route('/dongdienchuabangiao')
  .get(passport.authenticate('jwt', { session: false }), Controller.getAllCongTrinhDongDienChuaBanGiao);

tonTaiCongTrinhXayDungRouter
  .route('/dongdienchuabangiao/download')
  .get(Controller.downloadCongTrinhDongDienChuaBanGiao);

tonTaiCongTrinhXayDungRouter
  .route('/bangiaochuahettontai')
  .get(passport.authenticate('jwt', { session: false }), Controller.getAllCongTrinhBanGiaoChuaHetTonTai);

tonTaiCongTrinhXayDungRouter
  .route('/bangiaochuahettontai/download')
  .get(Controller.downloadCongTrinhBanGiaoChuaHetTonTai);

tonTaiCongTrinhXayDungRouter
  .route('/congtrinhxulyhettontai')
  .get(Controller.getAllCongTrinhXuLyHetTonTai);

tonTaiCongTrinhXayDungRouter
  .route('/congtrinhxulyhettontai/download')
  .get(Controller.downloadCongTrinhXuLyHetTonTai);

tonTaiCongTrinhXayDungRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), Controller.findOne);

tonTaiCongTrinhXayDungRouter
  .route('/:id')
  .delete(passport.authenticate('jwt', { session: false }), Controller.remove);

tonTaiCongTrinhXayDungRouter
  .route('/tontaidongdien/download')
  .get(Controller.downloadTonTaiDongDien);

tonTaiCongTrinhXayDungRouter
  .route('/bienbanxacnhan/download')
  .get(Controller.downloadBienBanXacNhan);

tonTaiCongTrinhXayDungRouter
  .route('/tontaibangiao/download')
  .get(Controller.downloadTonTaiBanGiao);

tonTaiCongTrinhXayDungRouter
  .route('/phuluctontai/download')
  .get(Controller.downloadPhuLucTonTai);
