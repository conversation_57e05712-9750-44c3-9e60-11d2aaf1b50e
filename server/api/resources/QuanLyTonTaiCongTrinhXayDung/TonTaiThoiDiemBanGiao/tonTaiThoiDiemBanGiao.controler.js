import Model from './tonTaiThoiDiemBanGiao.model';
import queryHelper from '../../../helpers/queryHelper';
import * as responseHelper from '../../../helpers/responseHelper';

export async function getAll(req, res) {
  try {
    let query = queryHelper.extractQueryParam(req);
    let { criteria, options } = query;
    options.populate = [{ path: 'cong_trinh_id' }];

    const data = await Model.find(criteria)
      .populate({ path: 'cong_trinh_id' })
      .sort({ created_at: -1 }).lean();


    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    let data = await Model.findOne({ cong_trinh_id: id }).lean();
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const data = await Model.create(req.body);
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findByIdAndUpdate(id, req.body, { new: true });
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;

    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });

    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}
