import express from 'express';
import passport from 'passport';
import * as tonTaiThoiDiemBanGiaoController from './tonTaiThoiDiemBanGiao.controler';
import CongTrinhXayDungPermission from '../../RBAC/permissions/CongTrinhXayDungPermission';
import { authorizationMiddleware } from '../../RBAC/middleware';
import { loggerMiddleware } from '../../../logs/middleware';

export const tonTaiThoiDiemBanGiaoRouter = express.Router();

tonTaiThoiDiemBanGiaoRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
tonTaiThoiDiemBanGiaoRouter.post('*', authorizationMiddleware([CongTrinhXayDungPermission.CREATE]));
tonTaiThoiDiemBanGiaoRouter.put('*', authorizationMiddleware([CongTrinhXayDungPermission.UPDATE]));
tonTaiThoiDiemBanGiaoRouter.get('*', authorizationMiddleware([CongTrinhXayDungPermission.READ]));
tonTaiThoiDiemBanGiaoRouter.delete('*', authorizationMiddleware([CongTrinhXayDungPermission.DELETE]));

tonTaiThoiDiemBanGiaoRouter
  .route('/')
  .post(tonTaiThoiDiemBanGiaoController.create);

tonTaiThoiDiemBanGiaoRouter
  .route('/:id')
  .get(tonTaiThoiDiemBanGiaoController.findOne)
  .put(tonTaiThoiDiemBanGiaoController.update)
  .delete(tonTaiThoiDiemBanGiaoController.remove);

