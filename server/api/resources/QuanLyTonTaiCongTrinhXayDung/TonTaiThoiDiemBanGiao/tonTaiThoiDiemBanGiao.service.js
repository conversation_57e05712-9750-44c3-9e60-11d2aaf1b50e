import TonTaiThoiDiemBanGiaoModel from './tonTaiThoiDiemBanGiao.model';
import createBaseService from '../../../base/baseService';

const baseService = createBaseService(TonTaiThoiDiemBanGiaoModel);

export const createMulti = baseService.createMulti;
export const getAll = baseService.getAll;
export const getOne = baseService.getOne;
export const remove = baseService.remove;
export const distinctId = baseService.distinctId;
export const updateByQuery = baseService.updateByQuery;
export const updateByRows = baseService.updateByRows;
export const deleteAll = baseService.deleteAll;
export const updateAll = baseService.updateAll;

export async function removeAll(query) {
  return TonTaiThoiDiemBanGiaoModel.updateMany(query, { is_deleted: true });
}
