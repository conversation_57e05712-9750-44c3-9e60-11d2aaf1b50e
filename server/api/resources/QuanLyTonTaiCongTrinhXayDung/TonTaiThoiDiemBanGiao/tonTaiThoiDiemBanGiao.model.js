import mongoose from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import { TON_TAI_THOI_DIEM_BAN_GIAO, CONG_TRINH } from '../../../constant/dbCollections';

const { Schema } = mongoose;


const schema = new Schema({
  cong_trinh_id: { type: Schema.Types.ObjectId, ref: CONG_TRINH },
  date_update: { type: Date },
  kien_nghi: { type: String },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export default mongoose.model(TON_TAI_THOI_DIEM_BAN_GIAO, schema, TON_TAI_THOI_DIEM_BAN_GIAO);
