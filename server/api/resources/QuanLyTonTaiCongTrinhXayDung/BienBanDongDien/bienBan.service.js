import * as ValidatorHelper from '../../../helpers/validatorHelper';
import BienBanModel from './bienBan.model';
import createBaseService from '../../../base/baseService';

const Joi = require('joi');

const objSchema = Joi.object({
  id_nguoi: Joi.string().required().messages(ValidatorHelper.messageDefine('Người tiến hành')),
  id_donvi: Joi.string().required().messages(ValidatorHelper.messageDefine('Đơn vị')),
  trang_thai: Joi.string().messages(ValidatorHelper.messageDefine('<PERSON>àn thành')),
  thoi_gian_thuc_hien: Joi.date().required().messages(ValidatorHelper.messageDefine('Ngày thanh lý')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

const baseService = createBaseService(BienBanModel);

export const createMulti = baseService.createMulti;
export const getAll = baseService.getAll;
export const getOne = baseService.getOne;
export const remove = baseService.remove;
export const distinctId = baseService.distinctId;
export const updateByQuery = baseService.updateByQuery;
export const updateByRows = baseService.updateByRows;
export const deleteAll = baseService.deleteAll;
export const updateAll = baseService.updateAll;
