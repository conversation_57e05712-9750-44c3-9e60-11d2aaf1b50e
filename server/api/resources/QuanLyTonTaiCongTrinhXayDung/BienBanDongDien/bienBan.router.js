import express from 'express';
import passport from 'passport';
import * as bienBanController from './bienBan.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import CongTrinhXayDungPermission from '../../RBAC/permissions/CongTrinhXayDungPermission.js';
import XacNhanLamQuangPermission from '../../RBAC/permissions/XacNhanLamQuangPermission.js';
import BanQuanLyDuAnPermission from '../../RBAC/permissions/BanQuanLyDuAnPermission.js';
import { loggerMiddleware } from '../../../logs/middleware';

export const bienBanRouter = express.Router();

bienBanRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

bienBanRouter.post('*', authorizationMiddleware(
  [CongTrinhXayDungPermission.CREATE && XacNhanLamQuangPermission.CREATE && BanQuanLyDuAnPermission.CREATE],
));
bienBanRouter.get('*', authorizationMiddleware(
  [CongTrinhXayDungPermission.READ && XacNhanLamQuangPermission.READ && BanQuanLyDuAnPermission.READ],
));
bienBanRouter.put('*', authorizationMiddleware(
  [CongTrinhXayDungPermission.UPDATE && XacNhanLamQuangPermission.UPDATE && BanQuanLyDuAnPermission.UPDATE],
));
bienBanRouter.delete('*', authorizationMiddleware(
  [CongTrinhXayDungPermission.DELETE && XacNhanLamQuangPermission.DELETE && BanQuanLyDuAnPermission.DELETE],
));
bienBanRouter
  .route('/')
  .get(bienBanController.getAll)
  .post(bienBanController.create);

bienBanRouter
  .route('/:id')
  .get(bienBanController.findOne)
  .delete(bienBanController.remove)
  .put(bienBanController.update);
