import express from 'express';
import passport from 'passport';
import * as chiTietBienBanController from './chiTietBienBan.controller';

export const chiTietBienBanRouter = express.Router();
chiTietBienBanRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), chiTietBienBanController.getAll)
  .post(passport.authenticate('jwt', { session: false }), chiTietBienBanController.create);

chiTietBienBanRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), chiTietBienBanController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), chiTietBienBanController.remove)
  .put(passport.authenticate('jwt', { session: false }), chiTietBienBanController.update);
