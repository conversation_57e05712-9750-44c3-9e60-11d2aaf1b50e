import mongoose from 'mongoose';
import { BIEN_BAN_LAM_QUANG, CHI_TIET_BIEN_BAN, TON_TAI_LAM_QUANG } from '../../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const { Schema } = mongoose;
const schema = new Schema({
  bien_ban_id: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: BIEN_BAN_LAM_QUANG,
  },
  ton_tai_lam_quang_id: {
    type: mongoose.Schema.Types.ObjectId,
    required: false,
    ref: TON_TAI_LAM_QUANG,
  },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(CHI_TIET_BIEN_BAN, schema, CHI_TIET_BIEN_BAN);
