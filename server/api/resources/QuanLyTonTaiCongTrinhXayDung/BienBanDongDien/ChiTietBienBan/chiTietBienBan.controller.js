import * as Service from './chiTietBienBan.service';
import Model from './chiTietBienBan.model';
import * as controllerHelper from '../../../../helpers/controllerHelper';

const populateOpts = [
  { path: 'bien_ban_id' },
  { path: 'ton_tai_lam_quang_id' },
];
const uniqueOpts = [];

export const remove = controllerHelper.createRemoveFunction(Model);
export const findOne = controllerHelper.createFindOneFunction(Model);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);
export const getAll = controllerHelper.createGetAllFunction(Model);
