import * as ValidatorHelper from '../../../../helpers/validatorHelper';
import CHI_TIET_BIEN_BAN from './chiTietBienBan.model';

const Joi = require('joi');

export async function create(data) {
  return CHI_TIET_BIEN_BAN.create(data);
}

export async function updateAll(arrData) {
  await CHI_TIET_BIEN_BAN.bulkWrite(
    arrData.map((row) =>
      ({
        updateOne: {
          filter: { _id: row._id },
          update: { $set: row },
          upsert: false,
        },
      }),
    ),
  );
}

export function getAll(query) {
  return CHI_TIET_BIEN_BAN.find(query)
    // .populate({ path: 'ton_tai_lam_quang_id', populate: 'vi_tri_id khoang_cot_id khoang_neo_id tieu_chi_id' })
    .lean();
}

export async function removeAll(query) {
  return CHI_TIET_BIEN_BAN.updateMany(query, { is_deleted: true });
}

const objSchema = Joi.object({
  bien_ban_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Biên bản')),
  ton_tai_lam_quang_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Tồn tại Lâm Quảng')),

});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
