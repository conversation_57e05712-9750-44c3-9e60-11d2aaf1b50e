import mongoose from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import { BIEN_BAN_LAM_QUANG, CONG_TRINH } from '../../../constant/dbCollections';
import { XAC_NHAN_XU_LY_TON_TAI } from './bienBan.constant';

const { Schema } = mongoose;

const schema = new Schema({
  cong_trinh_id: { type: Schema.Types.ObjectId, ref: CONG_TRINH },

  dai_dien_ban_qlda: { type: String },
  xac_nhan_ban_qlda: {
    type: String,
    enum: Object.keys(XAC_NHAN_XU_LY_TON_TAI),
    // default: XAC_NHAN_XU_LY_TON_TAI.TU_CHOI.code,
  },
  ly_do_qlvh_tu_choi: { type: String },

  dai_dien_don_vi_qlvh: { type: String },
  xac_nhan_don_vi_qlvh: {
    type: String,
    enum: Object.keys(XAC_NHAN_XU_LY_TON_TAI),
    // default: XAC_NHAN_XU_LY_TON_TAI.TU_CHOI.code,
  },
  ly_do_qlda_tu_choi: { type: String },

  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export default mongoose.model(BIEN_BAN_LAM_QUANG, schema, BIEN_BAN_LAM_QUANG);
