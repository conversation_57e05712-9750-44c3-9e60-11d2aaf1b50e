import * as responseAction from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from './bienBan.service';
import Model from './bienBan.model';
import CommonError from '../../../error/CommonError';
import * as ChiTietBienBanService from './ChiTietBienBan/chiTietBienBan.service';
import * as TonTaiLamQuangService from '../TonTaiLamQuang/tonTaiLamQuang.service';
import { extractKeys } from '../../../utils/dataconverter';
import { TRANG_THAI_TON_TAI } from '../TonTaiLamQuang/tonTaiLamQuang.constant';
import { XAC_NHAN_XU_LY_TON_TAI } from './bienBan.constant';

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOne({ _id: id, is_deleted: false })
      .populate({ path: 'cong_trinh_id' })
      .lean();
    if (!data) {
      return responseAction.error(res, CommonError.NOT_FOUND());
    }
    data.chitiet = await ChiTietBienBanService.getAll({ bien_ban_id: data._id, is_deleted: false })
      .populate({
        path: 'ton_tai_lam_quang_id',
        populate: [
          { path: 'tieu_chi_id' },
          { path: ' vi_tri_id', select: 'ten_vi_tri' },
          { path: ' khoang_cot_id', select: 'ten_khoang_cot' },
          { path: ' khoang_neo_id', select: 'ten_khoang_neo' },
        ],
      }).lean();
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true }).lean();
    if (!data) {
      return responseAction.error(res, CommonError.NOT_FOUND());
    } else {
      await ChiTietBienBanService.removeAll({ bien_ban_id: data._id });
      data.chitiet = await ChiTietBienBanService.getAll({ bien_ban_id: data._id });

      await TonTaiLamQuangService.updateTrangThai(data.chitiet, TRANG_THAI_TON_TAI.CHUA_XU_LY.code);
    }
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findByIdAndUpdate(id, req.body, { new: true }).lean();
    if (!data) {
      return responseAction.error(res, CommonError.NOT_FOUND());
    } else {
      const chitiet = req.body.chitiet || [];
      const chitietUpdate = chitiet.filter(row => row._id);

      await TonTaiLamQuangService.updateTrangThai(chitietUpdate.filter(row => row.is_deleted === true), TRANG_THAI_TON_TAI.CHUA_XU_LY.code);

      await ChiTietBienBanService.updateAll(chitietUpdate);
      let chitietCreate = chitiet.filter(row => !row.hasOwnProperty('_id'));
      chitietCreate.forEach(row => row.bien_ban_id = data._id);
      await ChiTietBienBanService.create(chitietCreate);
      data.chitiet = await ChiTietBienBanService.getAll({ bien_ban_id: data._id, is_deleted: false })
        .populate({
          path: 'ton_tai_lam_quang_id',
          populate: { path: 'tieu_chi_id vi_tri_id khoang_cot_id khoang_neo_id' },
        }).lean();

      if (data.xac_nhan_ban_qlda === XAC_NHAN_XU_LY_TON_TAI.DONG_Y.code && data.xac_nhan_don_vi_qlvh === XAC_NHAN_XU_LY_TON_TAI.DONG_Y.code) {
        await TonTaiLamQuangService.updateTrangThai(extractKeys(data.chitiet, 'ton_tai_lam_quang_id'), TRANG_THAI_TON_TAI.DA_XU_LY.code);
      } else {
        await TonTaiLamQuangService.updateTrangThai(extractKeys(data.chitiet, 'ton_tai_lam_quang_id'), TRANG_THAI_TON_TAI.DANG_XU_LY.code);
      }
    }
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    let data = await Model.create(value);
    if (data && req.body.chitiet && Array.isArray(req.body.chitiet)) {
      data = data.toObject();
      req.body.chitiet.forEach(row => {
        row.bien_ban_id = data._id;
      });
      await ChiTietBienBanService.create(req.body.chitiet);
      data.chitiet = await ChiTietBienBanService.getAll({ bien_ban_id: data._id, is_deleted: false })
        .populate({
          path: 'ton_tai_lam_quang_id',
          populate: { path: 'tieu_chi_id vi_tri_id khoang_cot_id khoang_neo_id' },
        }).lean();

      if (data.xac_nhan_ban_qlda === XAC_NHAN_XU_LY_TON_TAI.DONG_Y.code && data.xac_nhan_don_vi_qlvh === XAC_NHAN_XU_LY_TON_TAI.DONG_Y.code) {
        await TonTaiLamQuangService.updateTrangThai(extractKeys(data.chitiet, 'ton_tai_lam_quang_id'), TRANG_THAI_TON_TAI.DA_XU_LY.code);
      } else {
        await TonTaiLamQuangService.updateTrangThai(extractKeys(data.chitiet, 'ton_tai_lam_quang_id'), TRANG_THAI_TON_TAI.DANG_XU_LY.code);
      }
    }
    return responseAction.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['dai_dien_don_vi_qlvh', 'dai_dien_ban_qlda']);
    const { criteria, options } = query;
    options.populate = [
      { path: 'cong_trinh_id' },
    ];
    const data = await Model.paginate(criteria, options);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}
