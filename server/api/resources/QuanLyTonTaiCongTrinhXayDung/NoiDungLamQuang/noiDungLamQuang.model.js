import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { NOI_DUNG_LAM_QUANG } from '../../../constant/dbCollections';

const schema = new Schema({
  ten_noi_dung: { type: String, required: true, validate: /\S+/, unique: true },
  loai_noi_dung: { type: String, required: false },
  ghi_chu: { type: String },
  is_active: { type: Boolean, default: true },
  is_system: { type: Boolean, default: false },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(NOI_DUNG_LAM_QUANG, schema, NOI_DUNG_LAM_QUANG);
