import express from 'express';
import passport from 'passport';
import * as noiDungLamQuangController from './noiDungLamQuang.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const noiDungLamQuangRouter = express.Router();
noiDungLamQuangRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
noiDungLamQuangRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
noiDungLamQuangRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
noiDungLamQuangRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
noiDungLamQuangRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), noiDungLamQuangController.getAll)
  .post(passport.authenticate('jwt', { session: false }), noiDungLamQuangController.create);

noiDungLamQuangRouter
  .route('/tree')
  .get(passport.authenticate('jwt', { session: false }), noiDungLamQuangController.getAllTree);

noiDungLamQuangRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), noiDungLamQuangController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), noiDungLamQuangController.remove)
  .put(passport.authenticate('jwt', { session: false }), noiDungLamQuangController.update);
