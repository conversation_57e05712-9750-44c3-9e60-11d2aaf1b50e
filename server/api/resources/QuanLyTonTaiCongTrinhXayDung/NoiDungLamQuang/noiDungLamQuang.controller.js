import Model from './noiDungLamQuang.model';
import * as Service from './noiDungLamQuang.service';
import queryHelper from '../../../helpers/queryHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import TieuChiModel from './TieuChiLamQuang/tieuChiLamQuang.model';
import * as responseAction from '../../../helpers/responseHelper';
import * as TieuChiService from './TieuChiLamQuang/tieuChiLamQuang.service';
import { groupBy } from '../../../common/functionCommons';

async function getTieuChiChild(noiDungId) {
  const tieuChiParent = await TieuChiService.getAll({
    noi_dung_kiem_tra_id: noiDungId,
    is_deleted: false,
    tieu_chi_cha_id: { $exists: false },
  });
  const tieuChiChild = await TieuChiService.getAll({
    noi_dung_kiem_tra_id: noiDungId,
    is_deleted: false,
    tieu_chi_cha_id: { $exists: true },
  });

  return tieuChiParent.map(parent => {
    parent.chi_tiet = tieuChiChild.filter(child => child.tieu_chi_cha_id.toString() === parent._id.toString());
    return parent;
  });
}

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id).lean();
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    data.tieu_chi = await getTieuChiChild(id);
    return responseAction.success(res, data);
  } catch (err) {
    return responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;

    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    return responseAction.error(res, err);
  }
}

export async function update(req, res) {

  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);

    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true }).lean();
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    await createOrUpdate(id, value.tieu_chi);

    data.tieu_chi = await getTieuChiChild(id);
    return responseAction.success(res, data);
  } catch (err) {
    return responseAction.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const { t } = req;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);

    const checkUnique = await Service.count({ ten_noi_dung: value.ten_noi_dung });
    if (checkUnique) {
      return responseHelper.error(res, { message: t('content_name_already_exist') }, 404);
    }

    const data = await Model.create(value);
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    await createOrUpdate(data._id, value.tieu_chi);
    data.tieu_chi = await getTieuChiChild(data._id);
    return responseAction.success(res, data);
  } catch (err) {
    // console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_noi_dung']);
    const { criteria, options } = query;
    let getTieuChi = true;
    if (criteria.get_tieu_chi) {
      getTieuChi = true;
      delete criteria.get_tieu_chi;
    }

    options.sort = { thu_tu: 1 };
    const data = await Model.paginate(criteria, options);

    if (getTieuChi && Array.isArray(data.docs)) {
      const noiDungId = data.docs.map(doc => doc._id);
      const tieuChiAll = await TieuChiModel.find({ noi_dung_kiem_tra_id: { $in: noiDungId }, is_deleted: false })
        .populate('noi_dung_kiem_tra_id')
        .populate('tieu_chi_cha_id')
        .lean();

      const tieuChiParent = tieuChiAll.filter(tieuChi => !tieuChi.tieu_chi_cha_id);
      const tieuChiChild = tieuChiAll.filter(tieuChi => !!tieuChi.tieu_chi_cha_id);

      for (let i = 0; i < data.docs.length; i++) {
        const doc = data.docs[i];
        data.docs[i].tieu_chi_id = tieuChiParent
          .filter(tieuChi => tieuChi.noi_dung_kiem_tra_id._id.toString() === doc._id.toString())
          .map(parent => {
            parent.chi_tiet = tieuChiChild.filter(child => child.tieu_chi_cha_id?._id?.toString() === parent._id.toString());
            return parent;
          });
      }
    }

    return responseAction.success(res, data);
  } catch (err) {
    return responseAction.error(res, err);
  }
}

async function createOrUpdate(noiDungId, danhSachTieuChi) {
  if (Array.isArray(danhSachTieuChi)) {
    for (let i = 0; i < danhSachTieuChi.length; i++) {
      const tieuChi = danhSachTieuChi[i];
      if (tieuChi._id) {
        if (tieuChi.delete) {
          await TieuChiModel.findOneAndUpdate({ _id: tieuChi._id }, { is_deleted: true });
        } else {
          await TieuChiModel.findOneAndUpdate({ _id: tieuChi._id }, tieuChi);
          await createOrUpdate(noiDungId, tieuChi.chi_tiet);
        }
      } else {
        // tao moi khi khong co _id
        tieuChi.noi_dung_kiem_tra_id = noiDungId;
        const dataCreated = await TieuChiModel.create(tieuChi);
        if (dataCreated && Array.isArray(tieuChi.chi_tiet)) {
          for (let j = 0; j < tieuChi.chi_tiet.length; j++) {
            tieuChi.chi_tiet[j].tieu_chi_cha_id = dataCreated._id;
            tieuChi.chi_tiet[j].noi_dung_kiem_tra_id = noiDungId;
            await TieuChiModel.create(tieuChi.chi_tiet[j]);
          }
        }
      }
    }
  }
}

export async function getAllTree(req, res) {
  try {

    const data = await Service.getAll().sort({ ten_noi_dung: 1 });

    const allTieuChi = await TieuChiModel.find({ is_deleted: false })
      .populate('noi_dung_kiem_tra_id')
      .populate('tieu_chi_cha_id')
      .lean();

    const tieuChiParent = allTieuChi.filter(tieuChi => !tieuChi.tieu_chi_cha_id);
    const tieuChiChild = allTieuChi.filter(tieuChi => !!tieuChi.tieu_chi_cha_id);
    for (let i = 0; i < data.length; i++) {
      const doc = data[i];
      data[i].tieu_chi_id = tieuChiParent
        .filter(tieuChi => tieuChi.noi_dung_kiem_tra_id?._id.toString() === doc._id.toString())
        .map(parent => {
          parent.chi_tiet = tieuChiChild.filter(child => child.tieu_chi_cha_id?._id?.toString() === parent._id.toString());
          return parent;
        });
    }
    return responseAction.success(res, data);
  } catch (err) {
    return responseAction.error(res, err);
  }
}
