import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { NOI_DUNG_LAM_QUANG, TIEU_CHI_LAM_QUANG } from '../../../../constant/dbCollections';

const schema = new Schema({
  ten_tieu_chi: { type: String, required: true, validate: /\S+/ },
  ghi_chu: { type: String },
  is_deleted: { type: Boolean, default: false, select: false },
  noi_dung_kiem_tra_id: { type: Schema.Types.ObjectId, ref: NOI_DUNG_LAM_QUANG, required: true },
  tieu_chi_cha_id: { type: Schema.Types.ObjectId, ref: TIEU_CHI_LAM_QUANG },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(TIEU_CHI_LAM_QUANG, schema, TIEU_CHI_LAM_QUANG);

