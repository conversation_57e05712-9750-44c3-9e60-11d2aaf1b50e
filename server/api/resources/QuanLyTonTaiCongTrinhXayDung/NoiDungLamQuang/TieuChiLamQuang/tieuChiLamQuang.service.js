import * as ValidatorHelper from '../../../../helpers/validatorHelper';
import TIEU_CHI_LAM_QUANG from './tieuChiLamQuang.model';

export function getAll(query) {
  return TIEU_CHI_LAM_QUANG.find(query).collation({ locale: 'vi' }).lean();
}

const Joi = require('joi');

const objSchema = Joi.object({
  ten_TIEU_CHI_LAM_QUANG: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên tiêu chí')),

  ghi_chu: Joi.string(),
  noi_dung_kiem_tra_id: Joi.string().required().messages(ValidatorHelper.messageDefine('tên nội dung')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
