import express from 'express';
import passport from 'passport';
import * as tieuChiLamQuangController from './tieuChiLamQuang.controller';
import { authorizationMiddleware } from '../../../RBAC/middleware';
import DanhMucPermission from '../../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../../logs/middleware';

export const tieuChiLamQuangRouter = express.Router();
tieuChiLamQuangRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
tieuChiLamQuangRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
tieuChiLamQuangRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
tieuChiLamQuangRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
tieuChiLamQuangRouter
  .route('/')
  .get(tieuChiLamQuangController.getAll)
  .post(tieuChiLamQuangController.create);

tieuChiLamQuangRouter
  .route('/tree')
  .get(tieuChiLamQuangController.getAllTree);

tieuChiLamQuangRouter
  .route('/:id')
  .get(tieuChiLamQuangController.findOne)
  .delete(tieuChiLamQuangController.remove)
  .put(tieuChiLamQuangController.update);
