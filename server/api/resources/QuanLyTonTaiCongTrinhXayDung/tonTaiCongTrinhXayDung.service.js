import ModelBanGiao from './TonTaiThoiDiemBanGiao/tonTaiThoiDiemBanGiao.model';
import { formatDate } from '../../common/formatUTCDateToLocalDate';
import ModelCongTrinh from '../TongKe/CongTrinh/congTrinh.model';
import { extractKeys } from '../../utils/dataconverter';
import moment from 'moment';
import * as TonTaiLamQuangService from './TonTaiLamQuang/tonTaiLamQuang.service';
import ModelBienBan from './BienBanDongDien/bienBan.model';
import ModelChiTietBienBan from './BienBanDongDien/ChiTietBienBan/chiTietBienBan.model';
import ModelPhuLucTonTai from './PhuLucTonTai/phuLucTonTai.model';
import { XAC_NHAN_XU_LY_TON_TAI } from './BienBanDongDien/bienBan.constant';
import { TRANG_THAI_TON_TAI, TYPE_TON_TAI_LAM_QUANG } from './TonTaiLamQuang/tonTaiLamQuang.constant';

export async function updateAll(Model, arrData) {
  await Model.bulkWrite(
    arrData.map((row) =>
      ({
        updateOne: {
          filter: { _id: row._id },
          update: { $set: row },
          upsert: true,
        },
      }),
    ),
  );
}

export async function getDataTonTaiLamQuang(criteria) {

  let dataTonTaiLamQuang = await TonTaiLamQuangService.getAll(criteria)
    .populate({ path: 'cong_trinh_id vi_tri_id khoang_cot_id khoang_neo_id' })
    .populate({ path: 'tieu_chi_id', populate: { path: 'tieu_chi_cha_id' } });

  function groupBy(listData, key) {
    return listData.reduce(function(grouped, element) {
      let valueKey = element[key]._id;
      (grouped[valueKey] = grouped[valueKey] || []).push({ vi_tri: element.vi_tri_id, ...element });
      return grouped;
    }, {});
  }

  let groupByTonTaiLamQuang = groupBy(dataTonTaiLamQuang, 'tieu_chi_id');

  let dataTonTaiLamQuangGroupBy = [];

  dataTonTaiLamQuang.forEach(item => {
    let findTonTaiLamQuang = dataTonTaiLamQuangGroupBy.find(tontai => tontai.tieu_chi_id._id === item.tieu_chi_id._id);
    if (!findTonTaiLamQuang) {
      item.vi_tri_id = groupByTonTaiLamQuang[item.tieu_chi_id._id];
      dataTonTaiLamQuangGroupBy.push(item);
    }
  });

  return dataTonTaiLamQuangGroupBy;
}

function formatViTri(tontai) {
  let resultString;
  tontai.vi_tri_id?.forEach(vitri => {
    resultString ? resultString = [resultString, vitri.vi_tri_id?.ten_vi_tri || vitri.khoang_cot_id?.ten_khoang_cot || vitri.khoang_neo_id?.ten_khoang_neo].join(', ')
      : resultString = vitri.vi_tri_id?.ten_vi_tri || vitri.khoang_cot_id?.ten_khoang_cot || vitri.khoang_neo_id?.ten_khoang_neo;
  });
  return resultString;
}

export async function getDownloadTonTaiDongDien(criteria) {
  let data = await getDataTonTaiLamQuang(criteria);
  let dataRes = {};

  function convertDataToRow(tontai, index) {
    return {
      stt: index + 1,
      ten_tieu_chi: tontai.tieu_chi_id?.ten_tieu_chi,
      vi_tri: formatViTri(tontai),
      tieu_chuan_quy_dinh: tontai.tieu_chuan_quy_dinh,
      ket_qua_nghiem_thu: tontai.ket_qua_nghiem_thu,
      don_vi_tinh: tontai.don_vi_tinh,
      khoi_luong: tontai.khoi_luong,
      tieu_chuan_va_don_vi: tontai?.tieu_chuan_quy_dinh || tontai?.don_vi_tinh,
      ketqua_khoiluong: tontai?.ket_qua_nghiem_thu || tontai?.khoi_luong,
      trach_nhiem_giai_quyet: tontai.trach_nhiem_giai_quyet,
      tien_do_giai_quyet: tontai.tien_do_giai_quyet ? formatDate(tontai.tien_do_giai_quyet) : '',
      ghi_chu: tontai.ghi_chu,
    };
  }

  dataRes.tieu_chuan_quy_dinh = data.flatMap(e => e.type_ton_tai === TYPE_TON_TAI_LAM_QUANG.TIEU_CHUAN_QUY_DINH.code ? [convertDataToRow(e)] : []);
  dataRes.khoi_luong_cong_viec = data.flatMap(e => e.type_ton_tai === TYPE_TON_TAI_LAM_QUANG.KHOI_LUONG_CHUA_HOAN_THANH.code ? [convertDataToRow(e)] : []);

  dataRes.tong = data.map(convertDataToRow);
  dataRes.ten_cong_trinh = data[0].cong_trinh_id.ten_cong_trinh;
  dataRes.nhom_cong_trinh_du_an = data[0].cong_trinh_id.nhom_cong_trinh_du_an;
  dataRes.ngay_cap_nhat = formatDate(moment());

  return dataRes;
}

export async function getDownloadBienBan(criteria) {
  let dataBienBanXacNhan = {};

  let dataBienBan = await ModelBienBan.findOne(criteria)
    .populate({ path: 'cong_trinh_id' }).lean();
  let dataChiTietBienBan = await ModelChiTietBienBan.find({ bien_ban_id: dataBienBan._id }).lean();
  let chiTietBienBanIds = extractKeys(dataChiTietBienBan, 'ton_tai_lam_quang_id');
  let dataTonTaiLamQuang = await getDataTonTaiLamQuang({ _id: { $in: chiTietBienBanIds } });

  function convertBienBanToRow(tontai, index) {
    return {
      stt: index + 1,
      ten_tieu_chi: tontai.tieu_chi_id?.ten_tieu_chi,
      vi_tri: formatViTri(tontai),
      trach_nhiem_giai_quyet: tontai.trach_nhiem_giai_quyet,
      tien_do_giai_quyet: tontai.tien_do_giai_quyet ? formatDate(tontai.tien_do_giai_quyet) : '',
      ghi_chu: tontai.ghi_chu,
    };
  }

  dataBienBanXacNhan.danh_sach_ton_tai = dataTonTaiLamQuang.map(convertBienBanToRow);
  dataBienBanXacNhan.ten_cong_trinh = dataBienBan.cong_trinh_id.ten_cong_trinh;
  dataBienBanXacNhan.ngay_dong_dien = dataBienBan.cong_trinh_id.ngay_dong_dien ? formatDate(dataBienBan.cong_trinh_id.ngay_dong_dien) : '';
  dataBienBanXacNhan.don_vi_quan_ly_du_an = dataBienBan.cong_trinh_id.don_vi_quan_ly_du_an;
  dataBienBanXacNhan.don_vi_quan_ly_van_hanh = dataBienBan.cong_trinh_id.don_vi_quan_ly_van_hanh;
  dataBienBanXacNhan.dai_dien_ban_qlda = dataBienBan.dai_dien_ban_qlda;
  dataBienBanXacNhan.dai_dien_don_vi_qlvh = dataBienBan.dai_dien_don_vi_qlvh;
  dataBienBanXacNhan.xac_nhan_don_vi_qlvh = XAC_NHAN_XU_LY_TON_TAI[dataBienBan.xac_nhan_don_vi_qlvh]?.label || '';
  dataBienBanXacNhan.xac_nhan_ban_qlda = XAC_NHAN_XU_LY_TON_TAI[dataBienBan.xac_nhan_ban_qlda]?.label || '';
  dataBienBanXacNhan.ngay_thang_nam = `ngày ${moment().format('DD')} tháng ${moment().format('MM')} năm ${moment().format('YYYY')}`;

  return dataBienBanXacNhan;
}


export async function getDownloadTonTaiBanGiao(criteria) {
  let dataTonTaiBanGiao = {};

  let { _id, is_deleted, ...queryLamQuang } = criteria;

  let dataBanGiao = await ModelBanGiao.findOne({ _id, is_deleted })
    .populate({ path: 'cong_trinh_id' })
    .lean();

  let dataTonTaiLamQuang = await getDataTonTaiLamQuang({
    cong_trinh_id: dataBanGiao.cong_trinh_id._id,
    ...queryLamQuang,
    trang_thai: { $ne: TRANG_THAI_TON_TAI.DA_XU_LY.code },
    xac_nhan_don_vi_qlvh: true,
  });

  function convertTonTaiBanGiaoToRow(tontai, index) {
    return {
      stt: index + 1,
      ten_tieu_chi: tontai.tieu_chi_id.ten_tieu_chi,
      vi_tri: formatViTri(tontai),
      trach_nhiem_giai_quyet: tontai.trach_nhiem_giai_quyet,
      tien_do_giai_quyet: tontai.tien_do_giai_quyet ? formatDate(tontai.tien_do_giai_quyet) : '',
      ghi_chu: tontai.ghi_chu,
    };
  }

  dataTonTaiBanGiao.danh_sach_ton_tai = dataTonTaiLamQuang.map(convertTonTaiBanGiaoToRow);
  dataTonTaiBanGiao.ten_cong_trinh = dataBanGiao.cong_trinh_id.ten_cong_trinh;
  dataTonTaiBanGiao.ngay_dong_dien = dataBanGiao.cong_trinh_id.ngay_dong_dien ? formatDate(dataBanGiao.cong_trinh_id.ngay_dong_dien) : '';
  dataTonTaiBanGiao.date_update = dataBanGiao.date_update ? formatDate(dataBanGiao.date_update) : '';
  dataTonTaiBanGiao.kien_nghi = dataBanGiao.kien_nghi;
  dataTonTaiBanGiao.ngay_thang_nam = `ngày ${moment().format('DD')} tháng ${moment().format('MM')} năm ${moment().format('YYYY')}`;

  return dataTonTaiBanGiao;
}

export async function getDownloadPhuLucTonTai(criteria) {
  let dataPhuLucTonTai = {};

  let { _id, is_deleted, ...queryLamQuang } = criteria;

  let dataPhuLuc = await ModelPhuLucTonTai.findOne({ _id, is_deleted })
    .populate({ path: 'cong_trinh_id' }).lean();

  let dataTonTaiLamQuang = await getDataTonTaiLamQuang({
    cong_trinh_id: dataPhuLuc.cong_trinh_id._id,
    ...queryLamQuang,
    trang_thai: { $ne: TRANG_THAI_TON_TAI.DA_XU_LY.code },
    xac_nhan_don_vi_qlvh: true,
  });

  function convertTonTaiBanGiaoToRow(tontai, index) {
    return {
      stt: index + 1,
      ten_tieu_chi: tontai.tieu_chi_id.ten_tieu_chi,
      vi_tri: formatViTri(tontai),
      y_kien_ban_a: tontai.y_kien_ban_a,
      y_kien_cua_ptc: tontai.y_kien_cua_ptc,
      trach_nhiem_giai_quyet: tontai.trach_nhiem_giai_quyet,
      tien_do_giai_quyet: tontai.tien_do_giai_quyet ? formatDate(tontai.tien_do_giai_quyet) : '',
      ghi_chu: tontai.ghi_chu,
    };
  }

  dataPhuLucTonTai.danh_sach_ton_tai = dataTonTaiLamQuang.map(convertTonTaiBanGiaoToRow);
  dataPhuLucTonTai.ten_cong_trinh = dataPhuLuc.cong_trinh_id.ten_cong_trinh;
  dataPhuLucTonTai.ngay_dong_dien = dataPhuLuc.cong_trinh_id.ngay_dong_dien ? formatDate(dataPhuLuc.cong_trinh_id.ngay_dong_dien) : '';
  dataPhuLucTonTai.ngay_ban_giao_tai_san = dataPhuLuc.cong_trinh_id.ngay_ban_giao_tai_san ? formatDate(dataPhuLuc.cong_trinh_id.ngay_ban_giao_tai_san) : '';
  dataPhuLucTonTai.ngay_cap_nhat = dataPhuLuc.ngay_cap_nhat ? formatDate(dataPhuLuc.ngay_cap_nhat) : '';
  dataPhuLucTonTai.kien_nghi = dataPhuLuc.kien_nghi;
  dataPhuLucTonTai.ngay_thang_nam = `ngày ${moment().format('DD')} tháng ${moment().format('MM')} năm ${moment().format('YYYY')}`;

  return dataPhuLucTonTai;
}

export async function getDataTonTaiLamQuangGroupByCongTrinhId(criteria) {
  let dataTonTaiLamQuang = await TonTaiLamQuangService.getAll(criteria)
    .populate({ path: 'cong_trinh_id vi_tri_id khoang_cot_id khoang_neo_id' })
    .populate({ path: 'tieu_chi_id', populate: { path: 'tieu_chi_cha_id' } });


  function groupBy(listData, key) {
    return listData.reduce(function(grouped, element) {
      let valueKey = element[key]._id;
      (grouped[valueKey] = grouped[valueKey] || []).push({ ton_tai: element.tieu_chi_id.ten_tieu_chi });
      return grouped;
    }, {});
  }

  let groupByTonTaiLamQuang = groupBy(dataTonTaiLamQuang, 'cong_trinh_id');

  let dataTonTaiLamQuangGroupBy = [];

  dataTonTaiLamQuang.forEach(item => {
    let findTonTaiLamQuang = dataTonTaiLamQuangGroupBy.find(tontai => tontai.cong_trinh_id._id === item.cong_trinh_id._id);
    if (!findTonTaiLamQuang) {
      item.ton_tai = groupByTonTaiLamQuang[item.cong_trinh_id._id];
      dataTonTaiLamQuangGroupBy.push(item);
    }
  });

  return dataTonTaiLamQuangGroupBy;
}

export async function getAllCongTrinhBanGiao(criteria) {
  const allCongTrinh = await ModelCongTrinh.find(criteria).lean();

  const allCongTrinhIds = extractKeys(allCongTrinh, '_id');

  let allTonTaiLamQuangChuaHetTonTai = await getDataTonTaiLamQuangGroupByCongTrinhId({
    cong_trinh_id: { $in: allCongTrinhIds },
    is_deleted: false,
    trang_thai: { $ne: TRANG_THAI_TON_TAI.DA_XU_LY.code },
    xac_nhan_don_vi_qlvh: true,
  });

  allTonTaiLamQuangChuaHetTonTai.forEach(item => {
    let ton_tai_lam_quang = '';
    item.ton_tai.map(tontai => {
      if (!ton_tai_lam_quang) {
        ton_tai_lam_quang += tontai.ton_tai;
      } else {
        if (ton_tai_lam_quang.lastIndexOf(tontai.ton_tai) === -1) {
          ton_tai_lam_quang += `, ${tontai.ton_tai}`;
        }
      }
    });
    item.ton_tai = ton_tai_lam_quang;
  });

  return allTonTaiLamQuangChuaHetTonTai;
}

export async function getDataDownloadCongTrinhBanGiao(criteria) {
  let dataChuaHetTonTai = await getAllCongTrinhBanGiao(criteria);

  function convertDataToRow(congtrinh, index) {
    return {
      stt: index + 1,
      ten_cong_trinh: congtrinh.cong_trinh_id?.ten_cong_trinh,
      nhom_cong_trinh_du_an: congtrinh.cong_trinh_id?.nhom_cong_trinh_du_an,
      don_vi_quan_ly_van_hanh: congtrinh.cong_trinh_id?.don_vi_quan_ly_van_hanh,
      don_vi_quan_ly_du_an: congtrinh.cong_trinh_id?.don_vi_quan_ly_du_an,
      ngay_dong_dien: congtrinh.cong_trinh_id?.ngay_dong_dien ? formatDate(congtrinh.cong_trinh_id?.ngay_dong_dien) : '',
      ngay_ban_giao: congtrinh.cong_trinh_id?.ngay_ban_giao_tai_san ? formatDate(congtrinh.cong_trinh_id?.ngay_ban_giao_tai_san) : '',
      cac_ton_tai: congtrinh.ton_tai,
      thoi_gian_qua_han: congtrinh.thoi_gian_qua_han,
      trach_nhiem_tiep_tuc_giai_quyet: congtrinh.trach_nhiem_giai_quyet,
    };
  }

  return dataChuaHetTonTai.map(convertDataToRow);
}

export async function getDataDownloadCongTrinhHetTonTai(criteria) {
  let dataChuaHetTonTai = await getAllCongTrinhXuLyHetTonTai(criteria);

  function convertDataToRow(congtrinh, index) {
    return {
      stt: index + 1,
      ten_cong_trinh: congtrinh?.ten_cong_trinh,
      nhom_cong_trinh_du_an: congtrinh?.nhom_cong_trinh_du_an,
      don_vi_quan_ly_van_hanh: congtrinh?.don_vi_quan_ly_van_hanh,
      don_vi_quan_ly_du_an: congtrinh?.don_vi_quan_ly_du_an,
      ngay_dong_dien: congtrinh?.ngay_dong_dien ? formatDate(congtrinh?.ngay_dong_dien) : '',
      ngay_ban_giao_tai_san: congtrinh?.ngay_ban_giao_tai_san ? formatDate(congtrinh?.ngay_ban_giao_tai_san) : '',
    };
  }

  return dataChuaHetTonTai.map(convertDataToRow);
}

export async function getAllCongTrinhXuLyHetTonTai(criteria) {

  let allTonTaiLamQuang = await TonTaiLamQuangService.getAll({ xac_nhan_don_vi_qlvh: true, is_deleted: false });

  let mapCongTrinh = {};

  for (let i = 0; i < allTonTaiLamQuang.length; i++) {
    let tonTaiLamQuang = allTonTaiLamQuang[i];
    if (!mapCongTrinh[tonTaiLamQuang.cong_trinh_id]) {
      mapCongTrinh[tonTaiLamQuang.cong_trinh_id] = false;
    }
    if (tonTaiLamQuang.trang_thai !== TRANG_THAI_TON_TAI.DA_XU_LY.code) {
      mapCongTrinh[tonTaiLamQuang.cong_trinh_id] = true;
    }
  }
  let congTrinhIds = [];
  for (let [key, value] of Object.entries(mapCongTrinh)) {
    if (!value) {
      congTrinhIds.push(key);
    }
  }

  return (await ModelCongTrinh.find({ ...criteria, _id: { $in: congTrinhIds } }).lean());
}
