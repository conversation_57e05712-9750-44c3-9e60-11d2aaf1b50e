import mongoose, { Schema } from 'mongoose';
import { ANH_TON_TAI, BIEN_BAN_LAM_QUANG, USER } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';
import { PointSchema } from '../../GeoObjects/GeoObjects.schema';
import { PROCESS_STATUS } from './anhTonTai.constant';

const schema = new Schema({
    bien_ban_id: { type: Schema.Types.ObjectId, ref: BIEN_BAN_LAM_QUANG },
    image_id: { type: String, require: true },
    thumbnail_id: { type: String, require: true },

    device: String,
    thoi_gian_chup: Date,
    location: { type: PointSchema },
    xac_nhan: Boolean,

    process_status: {
      type: String,
      enum: Object.values(PROCESS_STATUS),
      default: PROCESS_STATUS.UNPROCESSING,
    },

    is_deleted: { type: <PERSON><PERSON>an, default: false, select: false },
    nguoi_tao: { type: Schema.Types.ObjectId, ref: USER },
    nguoi_chinh_sua: { type: Schema.Types.ObjectId, ref: USER },
  },
  {
    timestamps: {
      createdAt: 'thoi_gian_tao',
      updatedAt: 'thoi_gian_cap_nhat',
    },
  });
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(ANH_TON_TAI, schema, ANH_TON_TAI);
