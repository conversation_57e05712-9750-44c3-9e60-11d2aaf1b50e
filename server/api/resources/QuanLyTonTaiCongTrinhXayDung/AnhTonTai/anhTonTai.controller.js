import * as Service from './anhTonTai.service';
import Model from './anhTonTai.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import { findOneById } from '../../../helpers/controllerHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import * as fileUtils from '../../../utils/fileUtils';
import * as FileUtil from '../../../utils/fileUtils';
import { deleteFile } from '../../../utils/fileUtils';
import * as ImageUtils from '../../../utils/ImageUtilsGM';
import queryHelper from '../../../helpers/queryHelper';
import CommonError from '../../../error/CommonError';
import path from 'path';

const populateOpts = [
  { path: 'bien_ban_xac_nhan_id' },
];

export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts);

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await findOneById(Model, id, populateOpts, true);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    const image_id = fileUtils.createUniqueFileName(req.files.image.originalFilename);
    const imagePath = await ImageUtils.rotate(image_id, req.files.image.path);
    const thumbnailPath = await ImageUtils.createThumbnail(image_id, imagePath);
    const thumbnail_id = fileUtils.getName(thumbnailPath);
    await fileUtils.createByName(imagePath, image_id);
    await fileUtils.createByName(thumbnailPath, thumbnail_id);

    const body = req.body;
    body.image_id = image_id;
    body.thumbnail_id = thumbnail_id;

    const data = await Model.create(body);
    const createdData = await controllerHelper.findOneById(Model, data._id, populateOpts);

    return responseHelper.success(res, createdData);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const data = await Model.find(criteria)
      .populate(populateOpts);
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function removeMulti(req, res) {
  try {
    const imageRemove = req.body;
    if (!Array.isArray(imageRemove)) {
      return responseHelper.error(res, 404, '');
    }
    const data = await Model.updateMany({ _id: { $in: imageRemove } }, { is_deleted: true }).lean();
    if (data.ok && data.n === data.nModified) {
      const dataImage = await Model.find({ _id: imageRemove }).lean();
      for (let i = 0; i < dataImage.length; i++) {
        const item = dataImage[i];
        deleteFile(path.join(FileUtil.filesDir, item.image_id));
        deleteFile(path.join(FileUtil.filesDir, item.thumbnail_id));
      }
      return responseHelper.success(res, { success: true });
    } else {
      return responseHelper.error(res, 404, '');
    }
  } catch (err) {
    responseHelper.error(res, err);
  }
}
