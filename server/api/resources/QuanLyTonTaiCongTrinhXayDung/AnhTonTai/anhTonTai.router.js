import express from 'express';
import passport from 'passport';
import * as Controller from './anhTonTai.controller';
import { checkTempFolder, multipartMiddleware } from '../../../utils/fileUtils';
import { loggerMiddleware } from '../../../logs/middleware';
import { authorizationMiddleware } from '../../RBAC/middleware';
import CongTrinhXayDungPermission from '../../RBAC/permissions/CongTrinhXayDungPermission';

export const anhTonTaiRouter = express.Router();
anhTonTaiRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
anhTonTaiRouter.post('*', authorizationMiddleware([CongTrinhXayDungPermission.CREATE]));
anhTonTaiRouter.put('*', authorizationMiddleware([CongTrinhXayDungPermission.UPDATE]));
anhTonTaiRouter.delete('*', authorizationMiddleware([CongTrinhXayDungPermission.DELETE]));
anhTonTaiRouter.route('/')
  .get(Controller.getAll)
  .post(checkTempFolder, multipartMiddleware, Controller.create)
  .delete(Controller.removeMulti);

anhTonTaiRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
