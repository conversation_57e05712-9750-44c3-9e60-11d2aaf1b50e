import * as ValidatorHelper from '../../../helpers/validatorHelper';
import ANH_TON_TAI from './anhTonTai.model';
import { PROCESS_STATUS } from './anhTonTai.constant';

export async function processing(image) {
  return ANH_TON_TAI.findByIdAndUpdate(image._id, { process_status: PROCESS_STATUS.PROCESSING });
}

export async function processFail(image) {
  return ANH_TON_TAI.findByIdAndUpdate(image._id, { process_status: PROCESS_STATUS.SUCCESS });
}

export async function processSucess(image) {
  return ANH_TON_TAI.findByIdAndUpdate(image._id, { process_status: PROCESS_STATUS.SUCCESS });
}


const Joi = require('joi');

const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return ANH_TON_TAI.create(value);
}

export function getAll(query, projection = {}) {
  return ANH_TON_TAI.find(query, projection).lean();
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await ANH_TON_TAI.findByIdAndUpdate(value._id, value);
  }
}

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};
