import { generateDocumentModuleCTXD } from '../Report/GenerateFile/generate.controller';
import queryHelper from '../../helpers/queryHelper';
import * as Service from './tonTaiCongTrinhXayDung.service';
import * as BanGiaoService from './TonTaiThoiDiemBanGiao/tonTaiThoiDiemBanGiao.service';
import * as PhuLucTonTaiService from './PhuLucTonTai/phuLucTonTai.service';
import ModelCongTrinh from '../TongKe/CongTrinh/congTrinh.model';
import Model from '../TongKe/CongTrinh/congTrinh.model';
import * as responseAction from '../../helpers/responseHelper';
import * as ViTriService from '../TongKe/ViTri/viTri.service';
import { groupBy } from '../../common/functionCommons';

export async function findOne(req, res) {
  try {
    const { id } = req.query;

    const data = await ModelCongTrinh.findById(id).lean();

    async function getTonTaiBanGiao() {
      data.ton_tai_tai_thoi_diem_ban_giao = await BanGiaoService.getOne({
        cong_trinh_id: data._id,
        is_deleted: false,
      });
    }

    async function getPhuLucTontai() {
      data.phu_luc_ton_tai = await PhuLucTonTaiService.getOne({ cong_trinh_id: data._id, is_deleted: false });
    }

    const allPromises = [
      getTonTaiBanGiao(),
      getPhuLucTontai(),
    ];
    await Promise.all(allPromises);

    if (!data) {
      return responseAction.error(res, '', 404);
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const allViTri = await ViTriService.getAll({ is_deleted: false });
    const groupViTriByCongTrinh = groupBy(allViTri, 'cong_trinh_id');
    if (groupViTriByCongTrinh[id]) {
      return responseAction.error(res, { message: t('delete_edit_location_before_delete_construction') }, 400);
    }
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    const removePromises = [
      BanGiaoService.removeAll({ cong_trinh_id: id }),
    ];
    await Promise.all(removePromises);

    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getAllCongTrinhDongDienChuaBanGiao(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['don_vi_quan_ly_du_an', 'ten_cong_trinh', 'don_vi_quan_ly_van_hanh']);
    const { criteria } = query;
    let data = await Service.getAllCongTrinhBanGiao({
      ...criteria, ngay_ban_giao_tai_san: { $eq: null }, trang_thai_ban_giao: { $ne: true },
    });
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getAllCongTrinhBanGiaoChuaHetTonTai(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['don_vi_quan_ly_du_an', 'ten_cong_trinh', 'don_vi_quan_ly_van_hanh']);
    const { criteria } = query;
    let data = await Service.getAllCongTrinhBanGiao({
      ...criteria, trang_thai_ban_giao: { $ne: true },
    });
    if (!data) {
      return responseAction.error(res, '', 404);
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function downloadCongTrinhXuLyHetTonTai(req, res) {
  const query = queryHelper.extractQueryParam(req, ['don_vi_quan_ly_du_an', 'ten_cong_trinh', 'don_vi_quan_ly_van_hanh']);
  const { criteria } = query;
  let data = await Service.getDataDownloadCongTrinhHetTonTai({
    ...criteria, trang_thai_ban_giao: { $ne: true },
  });
  const fileNameTemp = 'cong_trinh_xu_ly_het_ton_tai.xlsx';
  const fileNameRes = 'Công trình đã xử lý hết tồn tại.xlsx';
  generateDocumentModuleCTXD(res, data, fileNameTemp, fileNameRes);
}

export async function getAllCongTrinhXuLyHetTonTai(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['don_vi_quan_ly_du_an', 'ten_cong_trinh', 'don_vi_quan_ly_van_hanh']);
    const { criteria } = query;
    criteria.trang_thai_ban_giao = { $ne: true };
    const congTrinhGetTonTai = await Service.getAllCongTrinhXuLyHetTonTai(criteria);
    if (!congTrinhGetTonTai) {
      return responseAction.error(res, '', 404);
    }
    return responseAction.success(res, congTrinhGetTonTai);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function downloadCongTrinhDongDienChuaBanGiao(req, res) {
  const query = queryHelper.extractQueryParam(req, ['don_vi_quan_ly_du_an', 'ten_cong_trinh', 'don_vi_quan_ly_van_hanh']);
  const { criteria } = query;
  let data = await Service.getDataDownloadCongTrinhBanGiao({
    ...criteria, ngay_ban_giao_tai_san: { $eq: null }, trang_thai_ban_giao: { $ne: true },
  });
  const fileNameTemp = 'cong_trinh_dong_dien_chua_ban_giao.xlsx';
  const fileNameRes = 'Chi tiết công trình đã đóng điện nhưng chưa bàn giao.xlsx';
  generateDocumentModuleCTXD(res, data, fileNameTemp, fileNameRes);
}

export async function downloadCongTrinhBanGiaoChuaHetTonTai(req, res) {
  const query = queryHelper.extractQueryParam(req, ['don_vi_quan_ly_du_an', 'ten_cong_trinh', 'don_vi_quan_ly_van_hanh']);
  const { criteria } = query;
  let data = await Service.getDataDownloadCongTrinhBanGiao({
    ...criteria, trang_thai_ban_giao: { $ne: true },
  });
  const fileNameTemp = 'cong_trinh_ban_giao_chua_het_ton_tai.xlsx';
  const fileNameRes = 'Chi tiết công trình đã bàn giao nhưng chưa hết tồn tại.xlsx';
  generateDocumentModuleCTXD(res, data, fileNameTemp, fileNameRes);
}

export async function downloadTonTaiDongDien(req, res) {
  const query = queryHelper.extractQueryParam(req);
  let dataTonTaiDongDien = await Service.getDownloadTonTaiDongDien(query.criteria);
  const fileNameTemp = 'ton_tai_thoi_diem_dong_dien.xlsx';
  const fileNameRes = 'Tồn tại ghi nhận tại thời điểm đóng điện.xlsx';
  generateDocumentModuleCTXD(res, dataTonTaiDongDien, fileNameTemp, fileNameRes);
}

export async function downloadBienBanXacNhan(req, res) {
  const query = queryHelper.extractQueryParam(req);
  let dataTonTaiDongDien = await Service.getDownloadBienBan(query.criteria);
  const fileNameTemp = 'bien_ban_xac_nhan_dong_dien.xlsx';
  const fileNameRes = 'Biên bản xác nhận đóng điện.xlsx';
  generateDocumentModuleCTXD(res, dataTonTaiDongDien, fileNameTemp, fileNameRes);
}

export async function downloadTonTaiBanGiao(req, res) {
  const query = queryHelper.extractQueryParam(req);
  let dataTonTaiDongDien = await Service.getDownloadTonTaiBanGiao(query.criteria);
  const fileNameTemp = 'ton_tai_thoi_diem_ban_giao.xlsx';
  const fileNameRes = 'Tồn tại ghi nhận tại thời điểm bàn giao.xlsx';
  generateDocumentModuleCTXD(res, dataTonTaiDongDien, fileNameTemp, fileNameRes);
}

export async function downloadPhuLucTonTai(req, res) {
  const query = queryHelper.extractQueryParam(req);
  let dataTonTaiDongDien = await Service.getDownloadPhuLucTonTai(query.criteria);
  const fileNameTemp = 'phu_luc_ton_tai.xlsx';
  const fileNameRes = 'Mẫu phụ lục tồn tại.xlsx';
  generateDocumentModuleCTXD(res, dataTonTaiDongDien, fileNameTemp, fileNameRes);
}
