import express from 'express';
import passport from 'passport';
import * as phuLucTonTaiController from './phuLucTonTai.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import CongTrinhXayDungPermission from '../../RBAC/permissions/CongTrinhXayDungPermission.js';
import { loggerMiddleware } from '../../../logs/middleware';

export const phuLucTonTaiRouter = express.Router();

phuLucTonTaiRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

phuLucTonTaiRouter.post('*', authorizationMiddleware([CongTrinhXayDungPermission.CREATE]));
phuLucTonTaiRouter.get('*', authorizationMiddleware([CongTrinhXayDungPermission.READ]));
phuLucTonTaiRouter.put('*', authorizationMiddleware([CongTrinhXayDungPermission.UPDATE]));
phuLucTonTaiRouter.delete('*', authorizationMiddleware([CongTrinhXayDungPermission.DELETE]));

phuLucTonTaiRouter
  .route('/')
  .get(phuLucTonTaiController.getAll)
  .post(phuLucTonTaiController.create);

phuLucTonTaiRouter
  .route('/:id')
  .get(phuLucTonTaiController.findOne)
  .put(phuLucTonTaiController.update);
