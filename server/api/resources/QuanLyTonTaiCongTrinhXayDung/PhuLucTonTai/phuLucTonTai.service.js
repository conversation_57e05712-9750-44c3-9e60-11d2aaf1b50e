import * as ValidatorHelper from '../../../helpers/validatorHelper';
import PhuLucTonTaiModel from './phuLucTonTai.model';
import createBaseService from '../../../base/baseService';

const Joi = require('joi');

const objSchema = Joi.object({
  cong_trinh_id: Joi.date().required().messages(ValidatorHelper.messageDefine('Công trình')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}


const baseService = createBaseService(PhuLucTonTaiModel);

export const createMulti = baseService.createMulti;
export const getAll = baseService.getAll;
export const getOne = baseService.getOne;
export const remove = baseService.remove;
export const distinctId = baseService.distinctId;
export const updateByQuery = baseService.updateByQuery;
export const updateByRows = baseService.updateByRows;
export const deleteAll = baseService.deleteAll;
export const updateAll = baseService.updateAll;
