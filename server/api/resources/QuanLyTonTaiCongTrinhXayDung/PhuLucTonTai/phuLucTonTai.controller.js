import * as Service from './phuLucTonTai.service';
import Model from './phuLucTonTai.model';
import * as controllerHelper from '../../../helpers/controllerHelper';

const populateOpts = [
  { path: 'cong_trinh_id' },
];
const uniqueOpts = [];

export const findOne = controllerHelper.createFindOneFunction(Model);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);
export const getAll = controllerHelper.createGetAllFunction(Model);
