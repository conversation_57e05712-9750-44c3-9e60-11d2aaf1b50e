import mongoose from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import { PHU_LUC_TON_TAI, CONG_TRINH } from '../../../constant/dbCollections';

const { Schema } = mongoose;

const schema = new Schema({
  cong_trinh_id: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: CONG_TRINH,
  },
  ngay_cap_nhat: { type: Date },
  kien_nghi: { type: String },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export default mongoose.model(PHU_LUC_TON_TAI, schema, PHU_LUC_TON_TAI);
