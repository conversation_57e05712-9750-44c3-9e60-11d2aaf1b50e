import * as responseAction from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from './fileBienBan.service';
import Model from './fileBienBan.model';
import * as fileUtils from '../../../utils/fileUtils';
import { deleteFile, getFilePath } from '../../../utils/fileUtils';

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id)
      .populate('bien_ban_id').lean();
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseAction.error(res, '', 400);
    }
    if (data.file_id) {
      deleteFile(getFilePath(data.file_id));
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    let { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    let filePath = undefined;
    if (!value.file) {
      filePath = req.files?.file?.path;
      const file_id = fileUtils.createUniqueFileName(filePath);
      await fileUtils.createByName(filePath, file_id);
      value.file_id = file_id;
      value.file_name = req.files?.file?.name;
    } else {
      delete value.file;
    }

    const data = await Model.findOneAndUpdate({ _id: value._id }, value, { new: true });
    if (!data) {
      return responseAction.error(res, '', 404);
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function create(req, res) {

  try {
    let value = req.body;
    let filePath = undefined;
    if (!value.file) {
      filePath = req.files?.file?.path;
      const file_id = fileUtils.createUniqueFileName(filePath);
      await fileUtils.createByName(filePath, file_id);
      value.file_id = file_id;
      value.file_name = req.files?.file?.name;
    } else {
      delete value.file;
    }
    const data = await Model.create(value);
    let dataRtn = await data
      .populate({ path: 'bien_ban_id' }).execPopulate();
    return responseAction.success(res, dataRtn);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    options.populate = [
      { path: 'bien_ban_id' },
    ];
    options.sort = { createdAt: 1 };
    const data = await Model.paginate(criteria, options);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

