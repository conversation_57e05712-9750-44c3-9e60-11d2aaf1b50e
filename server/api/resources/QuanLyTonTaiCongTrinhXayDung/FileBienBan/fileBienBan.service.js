import * as ValidatorHelper from '../../../helpers/validatorHelper';
import FILE_BIEN_BAN from './fileBienBan.model';

const Joi = require('joi');

const objSchema = Joi.object({
  file_name: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên tài liệu')),
  bien_ban_xac_nhan_id: Joi.object().required().messages(ValidatorHelper.messageDefine('Biên bản xác nhận')),
  file_id: Joi.string().messages(ValidatorHelper.messageDefine('File id')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function getAll(query) {
  return FILE_BIEN_BAN.find(query)
    .populate('bien_ban_xac_nhan_id')
    .lean();
}
