import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { BIEN_BAN_LAM_QUANG, FILE_BIEN_BAN } from '../../../constant/dbCollections';

const schema = new Schema({
  file_id: { type: String },
  file_name: { type: String },
  bien_ban_id: { type: Schema.Types.ObjectId, ref: BIEN_BAN_LAM_QUANG },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(FILE_BIEN_BAN, schema, FILE_BIEN_BAN);
