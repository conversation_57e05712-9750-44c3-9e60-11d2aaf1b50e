import express from 'express';
import passport from 'passport';
import * as fileBienBanController from './fileBienBan.controller';
import { checkTempFolder, multipartMiddleware } from '../../../utils/fileUtils';
import { loggerMiddleware } from '../../../logs/middleware';
import { authorizationMiddleware } from '../../RBAC/middleware';
import CongTrinhXayDungPermission from '../../RBAC/permissions/CongTrinhXayDungPermission';

export const fileBienBanRouter = express.Router();

fileBienBanRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
fileBienBanRouter.post('*', authorizationMiddleware([CongTrinhXayDungPermission.CREATE]));
fileBienBanRouter.put('*', authorizationMiddleware([CongTrinhXayDungPermission.UPDATE]));
fileBienBanRouter.delete('*', authorizationMiddleware([CongTrinhXayDungPermission.DELETE]));
fileBienBanRouter.get('*', authorizationMiddleware([CongTrinhXayDungPermission.READ]));

fileBienBanRouter
  .route('/')
  .get(fileBienBanController.getAll)
  .post(checkTempFolder, multipartMiddleware, fileBienBanController.create)
  .put(checkTempFolder, multipartMiddleware, fileBienBanController.update);

fileBienBanRouter
  .route('/:id')
  .get(fileBienBanController.findOne)
  .delete(fileBienBanController.remove);


