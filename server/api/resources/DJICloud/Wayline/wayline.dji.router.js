import express from 'express';
import * as Controller from './wayline.controller';
import { multipartMiddleware } from '../../../utils/fileUtils';
import passport from 'passport';
import { loggerMiddleware } from '../../../logs/middleware';

export const djiWaylineRouter = express.Router();

djiWaylineRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

djiWaylineRouter
  .route('/workspaces/:workspace_id/waylines')
  .get(Controller.getWaylineList);
djiWaylineRouter
  .route('/workspaces/:workspace_id/waylines/:wayline_id/url')
  .get(Controller.getWaylineFileDownloadAddress);
djiWaylineRouter
  .route('/workspaces/:workspace_id/waylines/duplicate-names')
  .get(Controller.getDuplicatedWaylineName);
djiWaylineRouter
  .route('/workspaces/:workspace_id/upload-callback')
  .post(Controller.fileUploadResultReport);
djiWaylineRouter
  .route('/workspaces/:workspace_id/favorites')
  .post(Controller.batchFavoritesWayline);
djiWaylineRouter
  .route('/workspaces/:workspace_id/favorites')
  .delete(Controller.batchUnfavoritesWayline);
djiWaylineRouter
  .route('/workspaces/:workspace_id/waylines/:wayline_id')
  .delete(Controller.deleteByWaylineId);
djiWaylineRouter
  .route('/workspaces/:workspace_id/waylines/file/upload')
  .post(multipartMiddleware, Controller.importKmzFile);