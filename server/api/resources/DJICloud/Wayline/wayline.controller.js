import * as waylineFileService from './wayline.service';
import Model from './wayline.model';
import * as djiResponseHelper from '../../../helpers/djiResponseHelper';
import fs from 'fs';
import JSZip from 'jszip';
import { DOMParser } from '@xmldom/xmldom';
import { createUniqueFileName, deleteFile, getDirPath, getFilePath } from '../../../utils/fileUtils';
import { DEVICE_DOMAIN, STORE_DIRS, TEMPLATE_TYPE } from '../../../constant/constant';
import path from 'path';
import crypto from 'crypto';
import * as storageService from '../ObjectStorage/objectStorage.service';
import { pipeline } from 'stream/promises';
import { Transform } from 'stream';
import * as responseHelper from '../../../helpers/responseHelper';
import * as controllerHelper from '../../../helpers/controllerHelper';
import CommonError from '../../../error/CommonError';

const searchLikes = ['name'];
const populateOpts = [{ path: 'nguoi_tao_id' }, { path: 'don_vi_id' }];
const sortOpts = { created_at: -1 };

export const getAll = controllerHelper.createGetAllFunction(Model, searchLikes, populateOpts, sortOpts, 'don_vi_id');

export async function create(req, res) {
  try {
    const { username, don_vi_id, _id } = req.user;
    const { file } = req.files;

    const filename = file.originalFilename;
    if (!filename || !filename.endsWith('.kmz')) {
      return responseHelper.error(res, 'The file format is incorrect.');
    }

    const fileBuffer = fs.readFileSync(file.path);
    const zip = new JSZip();
    const loadedZip = await zip.loadAsync(fileBuffer);
    const kmlEntry = loadedZip.file('wpmz/template.kml');
    if (!kmlEntry) {
      return responseHelper.error(res, 'Missing template.kml.');
    }

    const kmlString = await kmlEntry.async('string');
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(kmlString, 'application/xml');

    const parseError = xmlDoc.getElementsByTagName('parsererror')[0];
    if (parseError) {
      return responseHelper.error(res, 'Failed to parse KMZ content.');
    }

    const droneNode = xmlDoc.getElementsByTagName('wpml:droneInfo')[0];
    const payloadNode = xmlDoc.getElementsByTagName('wpml:payloadInfo')[0];
    let dronemodelKey = null, payloadModelKeys = null;
    if (droneNode || payloadNode) {
      const type = droneNode.getElementsByTagName('wpml:droneEnumValue')[0]?.textContent;
      const subType = droneNode.getElementsByTagName('wpml:droneSubEnumValue')[0]?.textContent;
      const payloadType = payloadNode.getElementsByTagName('wpml:payloadEnumValue')[0]?.textContent;
      const payloadSubType = payloadNode.getElementsByTagName('wpml:payloadPositionIndex')[0]?.textContent;
      dronemodelKey = `${DEVICE_DOMAIN.DRONE}-${type}-${subType}`;
      payloadModelKeys = [`${DEVICE_DOMAIN.PAYLOAD}-${payloadType}-${payloadSubType}`];
    }

    const templateType = xmlDoc.getElementsByTagName('wpml:templateType')[0]?.textContent;
    const uniqueFileName = createUniqueFileName(filename);

    const query = {
      don_vi_id: don_vi_id,
      nguoi_tao_id: _id,
      user_name: username,
      drone_model_key: dronemodelKey,
      payload_model_keys: payloadModelKeys,
      name: path.basename(uniqueFileName, '.kmz'),
      sign: crypto.createHash('md5').update(fileBuffer).digest('hex'),
      template_types: [TEMPLATE_TYPE[templateType]],
    };

    const filePath = getFilePath(uniqueFileName, getDirPath(don_vi_id.toString(), STORE_DIRS.DRONE_WAYLINE));
    fs.writeFileSync(filePath, fileBuffer);

    const waylineFile = await Model.create(query);
    return responseHelper.success(res, waylineFile);
  } catch (error) {
    return responseHelper.error(res, error);
  }
}

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, waylineFileService, populateOpts);
export const remove = async (req, res) => {
  try {
    const { id } = req.params;
    const { don_vi_id } = req.user;
    const wayline = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!wayline) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    const filePath = getFilePath(`${wayline.name}.kmz`, getDirPath(don_vi_id.toString(), STORE_DIRS.DRONE_WAYLINE));
    deleteFile(filePath);

    return responseHelper.success(res, wayline);
  } catch (err) {
    return responseHelper.error(res, err);
  }
};

export async function getWaylineList(req, res) {
  try {
    const user = req.user;
    const { page, page_size, order_by, drone_model_keys, payload_model_key, template_type } = req.query;

    const criteria = {
      ...(template_type && { template_types: { $in: template_type } }),
      ...(drone_model_keys && { drone_model_key: drone_model_keys }),
      ...(payload_model_key && { payload_model_keys: { $in: Array.isArray(payload_model_key) ? payload_model_key : [payload_model_key] } }),
      don_vi_id: user.don_vi_id,
      is_deleted: false,
    };

    let sort = {};

    if (order_by) {
      let [field, direction] = order_by.trim().split(/\s+/);
      if (field === 'update_time') field = 'updated_at';
      if (field === 'create_time') field = 'created_at';
      sort[field] = direction && direction.toLowerCase() === 'desc' ? -1 : 1;
    } else {
      sort = sortOpts;
    }

    const options = {
      collation: { locale: 'vi' },
      lean: true,
      limit: +page_size,
      page: +page,
      sort: sort,
    };

    const paginate = await Model.paginate(criteria, options);

    const list = paginate.docs.map((item) => ({
      ...item,
      update_time: item.updated_at.getTime(),
      create_time: item.created_at.getTime(),
    }));

    const data = {
      list: list,
      pagination: {
        page: paginate.page,
        total: paginate.totalDocs,
        page_size: paginate.limit,
      },
    };

    return djiResponseHelper.success(res, data);
  } catch (error) {
    return djiResponseHelper.error(res, error);
  }
}

export async function deleteByWaylineId(req, res) {
  try {
    const { wayline_id } = req.params;

    const wayline = await Model.findOneAndUpdate({ _id: wayline_id }, { is_deleted: true }, { new: true });
    if (!wayline) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    const filePath = getFilePath(`${wayline.name}.kmz`, getDirPath(wayline.don_vi_id.toString(), STORE_DIRS.DRONE_WAYLINE));
    deleteFile(filePath);

    return djiResponseHelper.success(res);
  } catch (error) {
    return djiResponseHelper.error(res, error);
  }
}

export async function getWaylineFileDownloadAddress(req, res) {
  try {
    const { wayline_id } = req.params;
    const wayline = await Model.findOne({ _id: wayline_id }).lean();
    if (!wayline) {
      return djiResponseHelper.error(res, 'Wayline file not found.');
    }

    const filePath = getFilePath(`${wayline.name}.kmz`, getDirPath(wayline.don_vi_id.toString(), STORE_DIRS.DRONE_WAYLINE));
    return res.download(filePath, wayline.name);
  } catch (error) {
    return djiResponseHelper.error(res, error);
  }
}

export async function getDuplicatedWaylineName(req, res) {
  try {
    const { name } = req.query;
    const { don_vi_id } = req.user;

    const waylineFiles = await Model.find({ name: { $in: name }, don_vi_id }).lean();
    const namesList = waylineFiles.map(file => file.name);

    return djiResponseHelper.success(res, namesList);
  } catch (error) {
    return djiResponseHelper.error(res, error);
  }
}

export async function fileUploadResultReport(req, res) {
  const { name, object_key, metadata } = req.body;
  const { username, don_vi_id } = req.user;

  try {
    const objectStream = await storageService.getObject(object_key);

    const fileName = `${name}.kmz`;
    const filePath = getFilePath(fileName, getDirPath(don_vi_id.toString(), STORE_DIRS.DRONE_WAYLINE));

    const hash = crypto.createHash('md5');
    const writeStream = fs.createWriteStream(filePath);

    await pipeline(objectStream.Body, new Transform({
        transform(chunk, encoding, callback) {
          hash.update(chunk);
          this.push(chunk);
          callback();
        },
      }),
      writeStream,
    );

    const sign = hash.digest('hex');

    const query = {
      don_vi_id: don_vi_id,
      nguoi_tao_id: req.user._id,
      user_name: username,
      name: name,
      template_types: metadata.template_types,
      payload_model_keys: metadata.payload_model_keys,
      drone_model_key: metadata.drone_model_key,
      sign: sign,
    };

    const wayline = await Model.create(query);

    return djiResponseHelper.success(res, wayline);
  } catch (error) {
    return djiResponseHelper.error(res, error);
  } finally {
    storageService.deleteObject(object_key);
  }
}

export async function batchFavoritesWayline(req, res) {
  try {
    const { id } = req.query;
    const ids = Array.isArray(id) ? id : [id];
    await waylineFileService.markFavorite(ids, true);

    return djiResponseHelper.success(res);
  } catch (error) {
    return djiResponseHelper.error(res, error);
  }
}

export async function batchUnfavoritesWayline(req, res) {
  try {
    const { id } = req.query;
    const ids = Array.isArray(id) ? id : [id];
    await waylineFileService.markFavorite(ids, false);

    return djiResponseHelper.success(res);
  } catch (error) {
    return djiResponseHelper.error(res, error);
  }
}

export async function importKmzFile(req, res) {
  try {
    const { username, don_vi_id, _id } = req.user;
    const { file } = req.files;

    const filename = file.originalFilename;
    if (!filename || !filename.endsWith('.kmz')) {
      return djiResponseHelper.error(res, 'The file format is incorrect.');
    }

    const fileBuffer = fs.readFileSync(file.path);
    const zip = new JSZip();
    const loadedZip = await zip.loadAsync(fileBuffer);
    const kmlEntry = loadedZip.file('wpmz/template.kml');
    if (!kmlEntry) {
      return djiResponseHelper.error(res, 'Missing template.kml.');
    }

    const kmlString = await kmlEntry.async('string');
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(kmlString, 'application/xml');

    const parseError = xmlDoc.getElementsByTagName('parsererror')[0];
    if (parseError) {
      return djiResponseHelper.error(res, 'Failed to parse KMZ content.');
    }

    const droneNode = xmlDoc.getElementsByTagName('wpml:droneInfo')[0];
    const payloadNode = xmlDoc.getElementsByTagName('wpml:payloadInfo')[0];
    let dronemodelKey = null, payloadModelKeys = null;
    if (droneNode || payloadNode) {
      const type = droneNode.getElementsByTagName('wpml:droneEnumValue')[0]?.textContent;
      const subType = droneNode.getElementsByTagName('wpml:droneSubEnumValue')[0]?.textContent;
      const payloadType = payloadNode.getElementsByTagName('wpml:payloadEnumValue')[0]?.textContent;
      const payloadSubType = payloadNode.getElementsByTagName('wpml:payloadPositionIndex')[0]?.textContent;
      dronemodelKey = `${DEVICE_DOMAIN.DRONE}-${type}-${subType}`;
      payloadModelKeys = [`${DEVICE_DOMAIN.PAYLOAD}-${payloadType}-${payloadSubType}`];
    }

    const templateType = xmlDoc.getElementsByTagName('wpml:templateType')[0]?.textContent;
    const uniqueFileName = createUniqueFileName(filename);

    const query = {
      don_vi_id: don_vi_id,
      nguoi_tao_id: _id,
      user_name: username,
      drone_model_key: dronemodelKey,
      payload_model_keys: payloadModelKeys,
      name: path.basename(uniqueFileName, '.kmz'),
      sign: crypto.createHash('md5').update(fileBuffer).digest('hex'),
      template_types: [TEMPLATE_TYPE[templateType]],
    };

    const filePath = getFilePath(uniqueFileName, getDirPath(don_vi_id.toString(), STORE_DIRS.DRONE_WAYLINE));
    fs.writeFileSync(filePath, fileBuffer);

    const waylineFile = await Model.create(query);
    return djiResponseHelper.success(res, waylineFile);
  } catch (error) {
    return djiResponseHelper.error(res, error);
  }
}
