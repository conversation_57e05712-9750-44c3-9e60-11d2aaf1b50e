import mongoose, { Schema } from 'mongoose';
import { DJI_WAYLINE_FILE, DON_VI, USER, DUONG_DAY, VI_TRI, KHOANG_COT } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  name: { type: String },
  drone_model_key: { type: String },
  payload_model_keys: [{ type: String }],
  sign: { type: String },
  favorited: { type: Boolean, default: false },
  template_types: [{ type: Number }],
  user_name: { type: String },
  nguoi_tao_id: { type: Schema.Types.ObjectId, ref: USER },
  duong_day_id: { type: Schema.Types.ObjectId, ref: DUONG_DAY },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  khoang_cot_id: { type: Schema.Types.ObjectId, ref: KHOANG_COT },
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI, required: true },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export default mongoose.model(DJI_WAYLINE_FILE, schema, DJI_WAYLINE_FILE);
