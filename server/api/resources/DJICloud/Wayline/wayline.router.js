import express from 'express';
import * as Controller from './wayline.controller';
import { multipartMiddleware } from '../../../utils/fileUtils';
import passport from 'passport';
import { loggerMiddleware } from '../../../logs/middleware';

export const waylineRouter = express.Router();

waylineRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

waylineRouter
  .route('/')
  .get(Controller.getAll)
  .post(multipartMiddleware, Controller.create);

waylineRouter
  .route('/:wayline_id/download')
  .get(Controller.getWaylineFileDownloadAddress);

waylineRouter
  .route('/:id')
  .get(Controller.findOne)
  .put(Controller.update)
  .delete(Controller.remove);