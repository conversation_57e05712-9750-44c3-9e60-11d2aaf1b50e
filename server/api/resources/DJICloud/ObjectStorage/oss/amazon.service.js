import { DeleteObjectCommand, GetObjectCommand, PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { AssumeRoleCommand, STSClient } from '@aws-sdk/client-sts';
import { getConfig } from '../../../../../config/config';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

const config = getConfig(process.env.NODE_ENV);
const storageConfig = config.oss.aws;

const s3Client = new S3Client({
  endpoint: storageConfig.endpoint,
  region: storageConfig.region,
  credentials: {
    accessKeyId: storageConfig.access_key,
    secretAccessKey: storageConfig.secret_key,
  },
});

async function getCredentials() {
  const sts = new STSClient({
    credentials: {
      accessKeyId: storageConfig.access_key,
      secretAccessKey: storageConfig.secret_key,
    },
    region: storageConfig.region,
  });

  try {
    const { Credentials } = await sts.send(new AssumeRoleCommand({
      RoleArn: storageConfig.role_arn,
      RoleSessionName: storageConfig.role_session_name,
      DurationSeconds: storageConfig.expire,
    }));

    return {
      access_key_id: Credentials.AccessKeyId,
      access_key_secret: Credentials.SecretAccessKey,
      security_token: Credentials.SessionToken,
      expire: (new Date(Credentials.Expiration).getTime() - Date.now()) / 1000,
    };
  } catch (error) {
    console.error('Failed to get credentials', error.message);
    return null;
  }
}

export async function getObjectUrl(objectKey, bucket) {
  const command = new GetObjectCommand({ Bucket: bucket, Key: objectKey });

  return getSignedUrl(s3Client, command, { expiresIn: 3600 }); // Default expiration set to 1 hour
}

export async function deleteObject(objectKey, bucket) {
  return s3Client.send(new DeleteObjectCommand({ Bucket: bucket, Key: objectKey }));
}

export async function getObject(objectKey, bucket) {
  const response = await s3Client.send(new GetObjectCommand({ Bucket: bucket, Key: objectKey }));
  return response;
}

export async function putObject(objectKey, body, bucket) {
  return s3Client.send(new PutObjectCommand({ Bucket: bucket, Key: objectKey, Body: body }));
}

export async function getSTSCredentials() {
  return {
    endpoint: storageConfig.endpoint,
    bucket: config.oss.bucket,
    credentials: await getCredentials(),
    provider: 'aws',
    object_key_prefix: 'wayline',
    region: storageConfig.region,
  };
}
