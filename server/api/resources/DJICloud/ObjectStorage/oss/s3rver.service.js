import { DeleteObjectCommand, GetObjectCommand, PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { getConfig } from '../../../../../config/config';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

const ip = require('ip');
const config = getConfig(process.env.NODE_ENV);
const storageConfig = config.oss.s3rver;

const endpoint = storageConfig.endpoint || `http://${ip.address()}:${storageConfig.port}`;

const s3Client = new S3Client({
  endpoint: endpoint,
  region: storageConfig.region,
  credentials: {
    accessKeyId: storageConfig.access_key,
    secretAccessKey: storageConfig.secret_key,
  },
  forcePathStyle: true,
});

async function getCredentials() {
  return {
    access_key_id: storageConfig.access_key,
    access_key_secret: storageConfig.secret_key,
    security_token: 'IQoJb3JpZ2luX2VjEAMaDmFwLXNvdXRoZWFzdC0xIkgwRgIhAOaAVUYN9at5F03TWRITO7zCzxspXwJ2QipxXFsrfVgnAiEA/k0Dkw7h69W6yHys0/2elSscNIs/kdua0KrHBeH0W3UqlQIILBAAGgw3Mjk3ODAxNDIzMDUiDKwGC84QFHBAcZgtSyryAYJ9SnWlEIr6iq95I6x3kUpzEGIxII0FcqSCRwdgiMR5/zEm97oC4mRtl0sGvT2HsDvnQlPTHwGa5kP8tyWJQc0rLzR2SOpwUp8v1rAUZBXiCLjCZOSGto3LmUznB6r/9+RIdprsJXKgzqLls40UQ70ZFjev2TzasTPWZHSRr54ELGRZJNjSvJ9fSDD6Wppe34sX6O5vNbQTabB50/F2/hTYSDBYni0Z1IGfAotJOLIL8DM2L1zwqn4b/8T7KqIGlHE04Pn+9SMzWf/NseDIOZtpb9g9xj3U8XDOhnf42wFEBa09I/h354Ytft4hmxi+TLvjMNWaiMQGOpwBllcIsl0d911HbLm7Enok0fxk1EjSKvTHORKPMGaEtFjwMrdUGL8FyvJ7KpnHW8u3ay+yMnUOyMPd/IjO/aspUXsZb2pV/hwgjr1hOjywy91Vs03w6fHOSO1kddIin73RCFrPyRdDTF+Lv/6Qm4UK4WFRLdMt5wL/NwAtrxDtFqiNEyNwnqXdtIeie6hbiRJHwFuApGDz8QIwsYU+',
    expire: 3600,
  };
}

export async function getObjectUrl(objectKey, bucket) {
  const command = new GetObjectCommand({ Bucket: bucket, Key: objectKey });
  return getSignedUrl(s3Client, command, { expiresIn: 3600 });
}

export async function deleteObject(objectKey, bucket) {
  return s3Client.send(new DeleteObjectCommand({ Bucket: bucket, Key: objectKey }));
}

export async function getObject(objectKey, bucket) {
  const result = await s3Client.send(new GetObjectCommand({ Bucket: bucket, Key: objectKey, }));
  return result;
}

export async function putObject(objectKey, body, bucket) {
  return s3Client.send(new PutObjectCommand({ Bucket: bucket, Key: objectKey, Body: body }));
}

export async function getSTSCredentials() {
  return {
    endpoint: endpoint,
    bucket: config.oss.bucket,
    credentials: await getCredentials(),
    provider: 'aws',
    object_key_prefix: 'wayline',
    region: storageConfig.region,
  };
}
