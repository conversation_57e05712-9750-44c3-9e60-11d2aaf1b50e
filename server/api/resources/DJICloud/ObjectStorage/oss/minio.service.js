import * as Minio from 'minio'
import { getConfig } from '../../../../../config/config';
import ip from 'ip';
const config = getConfig(process.env.NODE_ENV);
const storageConfig = config.oss.minio;
import axios from 'axios';
import xml2js from 'xml2js';

const minioClient = new Minio.Client({
  endPoint: storageConfig.minio_host,
  port: 9000,
  useSSL: false,
  accessKey: storageConfig.access_key,
  secretKey: storageConfig.secret_key,
})

async function getCredentials(token) {
  try {
    const params = new URLSearchParams({
      Action: 'AssumeRoleWithCustomToken',
      Token: token,
      Version: '2011-06-15',
      DurationSeconds: 86000,
      RoleArn: `arn:minio:iam:::role/idmp-${storageConfig.role_arn}`
    });

    const response = await axios.post(`${storageConfig.minio_sts_url}?${params.toString()}`);

    let resultCredentials = null;
    xml2js.parseString(response.data, { explicitArray: false }, (err, result) => {
      if (err) return;

      const res = result['AssumeRoleWithCustomTokenResponse'];
      const credentials = res['AssumeRoleWithCustomTokenResult']['Credentials'];

      resultCredentials = {
        access_key_id: credentials.AccessKeyId,
        access_key_secret: credentials.SecretAccessKey,
        security_token: credentials.SessionToken,
        expire: (new Date(credentials.Expiration).getTime() - Date.now()) / 1000
      };
    });

    return resultCredentials;
  } catch (error) {
    return null;
  }
}

export function getObjectUrl(objectKey, bucket) {
  return minioClient.presignedGetObject(bucket, objectKey, 3600);
}

export function deleteObject(objectKey, bucket) {
  return minioClient.removeObject(bucket, objectKey);
}

export async function getObject(objectKey, bucket) {
  const stat = await minioClient.statObject(bucket, objectKey);
  const stream = await minioClient.getObject(bucket, objectKey);

  return {
    Body: stream,
    ContentType: stat.metaData['content-type'] || stat.metaData['Content-Type'],
  }
}

export function putObject(objectKey, body, bucket) {
  return minioClient.putObject(bucket, objectKey, body);
}

export async function getSTSCredentials(token) {
  const existsBucket = await minioClient.bucketExists(config.oss.bucket);
  if(!existsBucket) {
    await minioClient.makeBucket(config.oss.bucket, storageConfig.region);
  }

  return {
    endpoint: storageConfig.endpoint || `http://${ip.address()}:8080`,
    bucket: config.oss.bucket,
    credentials: await getCredentials(token),
    provider: 'minio',
    object_key_prefix: 'wayline',
    region: storageConfig.region,
  };
}
