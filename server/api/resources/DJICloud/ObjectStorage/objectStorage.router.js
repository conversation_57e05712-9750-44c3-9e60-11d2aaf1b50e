import express from 'express';
import * as Controller from './objectStorage.controller';
import passport from 'passport';
import { loggerMiddleware } from '../../../logs/middleware';

export const djiStorageRouter = express.Router();

djiStorageRouter
  .route('/credentials')
  .post(Controller.checkCredential);

djiStorageRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

djiStorageRouter
  .route('/workspaces/:workspace_id/sts')
  .post(Controller.getTemporaryCredential);
