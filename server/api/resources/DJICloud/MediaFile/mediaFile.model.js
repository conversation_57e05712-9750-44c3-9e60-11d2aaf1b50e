import mongoose, { Schema } from 'mongoose';
import { ANH_VI_TRI, DJI_MEDIA_FILE, DON_VI, PHIEU_GIAO_VIEC, USER } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  file_name: { type: String },
  file_path: { type: String },
  fingerprint: { type: String },
  tinny_fingerprint: { type: String },
  sub_file_type: { type: Number },
  is_original: { type: Boolean },
  drone: { type: String },
  payload: { type: String },
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI, required: true },
  nguoi_tao_id: { type: Schema.Types.ObjectId, ref: USER },
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  anh_vi_tri_id: { type: Schema.Types.ObjectId, ref: ANH_VI_TRI },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export default mongoose.model(DJI_MEDIA_FILE, schema, DJI_MEDIA_FILE);
