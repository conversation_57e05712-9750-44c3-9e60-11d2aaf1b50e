import express from 'express';
import * as Controller from './mediaFile.controller';
import passport from 'passport';
import { loggerMiddleware } from '../../../logs/middleware';

export const mediaRouter = express.Router();

mediaRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

mediaRouter
  .route('/')
  .get(Controller.getAll);

mediaRouter
  .route('/:file_id/download')
  .get(Controller.getFileUrl);

mediaRouter
  .route('/:id')
  .get(Controller.findOne)
  .put(Controller.update)
  .delete(Controller.remove);
