import express from 'express';
import * as Controller from './mediaFile.controller';
import passport from 'passport';
import { loggerMiddleware } from '../../../logs/middleware';

export const djiMediaFileRouter = express.Router();

djiMediaFileRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

djiMediaFileRouter
  .route('/workspaces/:workspace_id/fast-upload')
  .post(Controller.mediaFastUpload);

djiMediaFileRouter
  .route('/workspaces/:workspace_id/files/tiny-fingerprints')
  .post(Controller.getExistFileTinyFingerprint);

djiMediaFileRouter
  .route('/workspaces/:workspace_id/upload-callback')
  .post(Controller.mediaUploadCallback);

djiMediaFileRouter
  .route('/files/:workspace_id/files')
  .get(Controller.getFilesList);

djiMediaFileRouter
  .route('/files/:workspace_id/file/:file_id/url')
  .get(Controller.getFileUrl);

