import * as ValidatorHelper from '../../helpers/validatorHelper';
import CAI_DAT_PMIS from './caiDatPmis.model';

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return CAI_DAT_PMIS.create(value);
}

export function getConfig() {
  return CAI_DAT_PMIS.findOne().lean();
}

export function count(query) {
  return CAI_DAT_PMIS.countDocuments(query).lean();
}

export function getAll(query) {
  return CAI_DAT_PMIS.find(query).lean();
}

export function getOne(query, projection = {}) {
  return CAI_DAT_PMIS.findOne(query, projection).lean();
}


const Joi = require('joi');

const objSchema = Joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
