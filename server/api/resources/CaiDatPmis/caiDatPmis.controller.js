import * as Service from './caiDatPmis.service';
import Model from './caiDatPmis.model';
import * as responseHelper from '../../helpers/responseHelper';
import CommonError from '../../error/CommonError';

export async function findOne(req, res) {
  try {
    const data = await Model.findOne({}, { _id: 0 }).lean();
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    data.pmis_pdkey = data.pmis_pdkey?.replace(new RegExp('.', 'g'), '*');
    data.pmis_service = data.pmis_service?.replace(new RegExp('.', 'g'), '*');

    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, CommonError.NOT_FOUND);
    const setting = await Model.findOne().lean();

    const data = await Model.findOneAndUpdate({ _id: setting._id }, value, { new: true }).lean();
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    data.pmis_pdkey = data.pmis_pdkey?.replace(new RegExp('.', 'g'), '*');
    data.pmis_service = data.pmis_service?.replace(new RegExp('.', 'g'), '*');
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}



