import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CAI_DAT_PMIS } from '../../constant/dbCollections';

const schema = new Schema({
  pmis_service: { type: String },
  pmis_pdkey: { type: String },

  allow_pmis_sync: { type: Boolean, default: false },
  allow_power_line_sync: { type: Boolean, default: false },

  allow_sync_construction_list: { type: Boolean, default: false },
  allow_sync_construction_info: { type: Boolean, default: false },

  allow_sync_construction_devices: { type: Boolean, default: false },
  tu_dong_cap_nhat_khoang_cot: { type: Boolean, default: false },

  allow_sync_location: { type: Boolean, default: false },


}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

export default mongoose.model(CAI_DAT_PMIS, schema, CAI_DAT_PMIS);
