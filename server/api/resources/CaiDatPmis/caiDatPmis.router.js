import express from 'express';
import passport from 'passport';
import * as caiDatPmis from './caiDatPmis.controller';
import { authorizationMiddleware } from '../RBAC/middleware';
import SettingPermission from '../RBAC/permissions/SettingPermission';
import { loggerMiddleware } from '../../logs/middleware';

export const caiDatPmisRouter = express.Router();


caiDatPmisRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
caiDatPmisRouter.post('*', authorizationMiddleware([SettingPermission.CREATE]));
caiDatPmisRouter.put('*', authorizationMiddleware([SettingPermission.UPDATE]));
caiDatPmisRouter.delete('*', authorizationMiddleware([SettingPermission.DELETE]));
caiDatPmisRouter
  .route('/')
  .get(caiDatPmis.findOne)
  .put(caiDatPmis.update)
