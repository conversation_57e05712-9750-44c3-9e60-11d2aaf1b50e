import * as ValidatorHelper from '../../../helpers/validatorHelper';
import VISITS from './visits.model';

const Joi = require('joi');

const objSchema = Joi.object({
  ten_may_do: Joi.string().messages(ValidatorHelper.messageDefine('đơn vị')),
  loai_may_do: Joi.string(),
  ma_may: Joi.string(),
  do_chinh_xac: Joi.number(),
  thoi_gian_hoi_dap: Joi.number(),
  ghi_chu: Joi.string(),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function getAll(query, projection = {}) {
  return VISITS.find(query, projection).lean();
}

export async function updateAll(dataUpdate) {
  for (const row of dataUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await VISITS.findByIdAndUpdate(value._id, value);
  }
}
