import * as Validator<PERSON>elper from '../../helpers/validatorHelper';
import ROLE from './role.model';
import Model from '../QuanLyTonTaiCongTrinhXayDung/TonTaiThoiDiemBanGiao/tonTaiThoiDiemBanGiao.model';

const Joi = require('joi');

const objSchema = Joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function getAll(query, projection = {}) {
  return ROLE.find(query, projection).lean();
}

export function remove(query) {
  return ROLE.deleteMany(query).lean();
}

export async function updateAll(arrData) {
  await ROLE.bulkWrite(
    arrData.map((row) => ({
        updateOne: {
          filter: { _id: row._id },
          update: { $set: row },
          upsert: false,
        },
      }),
    ),
  );
}
