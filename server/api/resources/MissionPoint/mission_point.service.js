import * as ValidatorHelper from '../../helpers/validatorHelper';
import CAU_HINH_BAY from './mission_point.model';

const Joi = require('joi');

export function getAll(query) {
  return CAU_HINH_BAY.find(query).lean();
}

export async function updateAll(arrData) {
  await CAU_HINH_BAY.bulkWrite(
    arrData.map((row) =>
      ({
        updateOne: {
          filter: { _id: row._id },
          update: { $set: row },
          upsert: false,
        },
      }),
    ),
  );
}

const objSchema = Joi.object({
  vi_tri_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Vị trí id')),
  phieu_giao_viec_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Phiếu giao việc id')),
});

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};
