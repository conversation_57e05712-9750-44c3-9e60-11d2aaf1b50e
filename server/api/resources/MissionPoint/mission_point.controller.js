import * as Service from './mission_point.service';
import Model from './mission_point.model';
import * as controllerHelper from '../../helpers/controllerHelper';
import * as responseHelper from '../../helpers/responseHelper';

export const findOne = controllerHelper.createFindOneFunction(Model, null);
export const getAll = controllerHelper.createGetAllFunction(Model, null);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service);

// export const create = controllerHelper.createCreateFunction(Model, Service);

export async function create(req, res) {
  try {
    const result = [];
    for (let item of req.body) {
      item.sync_time = Date();
      item.sync_status = 'synced';
      result.push(await Model.findOneAndUpdate({ _id: item._id }, item, { new: true, upsert: true }));
    }
    return responseHelper.success(res, result);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}
