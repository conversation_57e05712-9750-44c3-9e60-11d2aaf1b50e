import express from 'express';
import passport from 'passport';
import * as donviController from './mission_point.controller';
import { authorizationMiddleware } from '../RBAC/middleware';
import TongKePermission from '../RBAC/permissions/TongKePermission';
import { loggerMiddleware } from '../../logs/middleware';

export const missionPointRouter = express.Router();
missionPointRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
missionPointRouter.post('*', authorizationMiddleware([TongKePermission.READ]));
missionPointRouter.get('*', authorizationMiddleware([TongKePermission.READ]));
missionPointRouter.put('*', authorizationMiddleware([TongKePermission.READ]));
missionPointRouter.delete('*', authorizationMiddleware([TongKePermission.READ]));
missionPointRouter
  .route('/')
  .get(donviController.getAll)
  .post(donviController.create);

missionPointRouter
  .route('/:id')
  .get(donviController.findOne)
  .delete(donviController.remove)
  .put(donviController.update);
