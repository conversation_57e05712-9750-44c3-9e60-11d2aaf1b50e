import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CAU_HINH_BAY, COT_DIEN, MISSION_POINT, PHIEU_GIAO_VIEC, VI_TRI } from '../../constant/dbCollections';

const schema = new Schema({
  _id: String,
  cau_hinh_bay_id: { type: String, ref: CAU_HINH_BAY },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },

  latitude: Number,
  longitude: Number,
  altitude: Number,
  absolute_altitude: { type: Number, default: null },
  heading: Number,
  gimbal_pitch: Number,
  use_rtk: { type: Boolean, default: false },

  is_use_custom_direction: Boolean,
  action_repeat_times: Number,
  action_timeout_in_seconds: Number,
  corner_radius_in_meters: Number,
  speed: Number,
  shoot_photo_time_interval: Number,
  shoot_photo_distance_interval: Number,
  action_type: Number,

  is_deleted: { type: Boolean, default: false, select: false },
  sync_time: String,
  sync_status: String,
  created_time: String,
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(MISSION_POINT, schema, MISSION_POINT);
