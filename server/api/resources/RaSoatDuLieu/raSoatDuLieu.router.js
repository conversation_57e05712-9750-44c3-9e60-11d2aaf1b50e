import express from 'express';
import passport from 'passport';
import * as thongTinQuanLyController from './raSoatDuLieu.controller';

import { loggerMiddleware } from '../../logs/middleware';
import { khoangCotThieuDuLieu } from './raSoatDuLieu.controller';

export const raSoatDuLieuRouter = express.Router();
raSoatDuLieuRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);


raSoatDuLieuRouter.route('/duongdaychuacovitri')
  .get(thongTinQuanLyController.duongDayChuaCoViTri);

raSoatDuLieuRouter.route('/duongdaychuacovitri/donvi/:id/download')
  .get(thongTinQuanLyController.exportDuongDayChuaCoViTri);


raSoatDuLieuRouter.route('/vitri/thieudulieu')
  .get(thongTinQuanLyController.viTriThieuDuLieu);

raSoatDuLieuRouter.route('/vitri/thieudulieu/download')
  .get(thongTinQuanLyController.exportViTriThieuDuLieu);


raSoatDuLieuRouter.route('/vitri/thieudulieu')
  .get(thongTinQuanLyController.viTriThieuDuLieu);

raSoatDuLieuRouter.route('/vitri/thieudulieu/download')
  .get(thongTinQuanLyController.exportViTriThieuDuLieu);


raSoatDuLieuRouter.route('/khoangcot/thieudulieu')
  .get(thongTinQuanLyController.khoangCotThieuDuLieu);

raSoatDuLieuRouter.route('/khoangcot/thieudulieu/download')
  .get(thongTinQuanLyController.exportKhoangCotThieuDuLieu);


raSoatDuLieuRouter.route('/vanhanh/thieudulieu')
  .get(thongTinQuanLyController.vanHanhThieuDuLieu);

raSoatDuLieuRouter.route('/vanhanh/thieudulieu/download')
  .get(thongTinQuanLyController.exportVanHanhThieuDuLieu);
