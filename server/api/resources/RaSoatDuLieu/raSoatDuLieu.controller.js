import queryHelper from '../../helpers/queryHelper';
import CommonError from '../../error/CommonError';
import { buildQuery } from '../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';

import * as DonViService from '../DonVi/donVi.service';
import * as DuongDayService from '../TongKe/DuongDay/duongDay.service';
import * as VanHanhService from '../TongKe/VanHanh/vanHanh.service';
import * as responseHelper from '../../helpers/responseHelper';

import { extractIds } from '../../utils/dataconverter';
import { createDataTree } from '../../common/DataStructureHelper';

import { isValidObjectId } from '../../common/functionCommons';

import { getFilePath } from '../../utils/fileUtils';
import { TEMPLATES_DIRS } from '../../constant/constant';
import { generateDocument } from '../Report/GenerateFile/generate.controller';


import VI_TRI from '../TongKe/ViTri/viTri.model';
import KHOANG_COT from '../TongKe/KhoangCot/khoangCot.model';
import CACH_DIEN from '../TongKe/CachDien/cachDien.model';
import DAY_DAN from '../TongKe/DayDan/dayDan.model';
import VAN_HANH from '../TongKe/VanHanh/vanHanh.model';


async function getDuongDayKhongViTri(donViQuery) {
  const allDuongDay = await DuongDayService.getAll({
    don_vi_id: donViQuery,
    is_deleted: false,
  }, { ten_duong_day: 1, don_vi_id: 1, duong_day_id: 1 });
  const duongDayId = extractIds(allDuongDay);
  const allVanHanh = await VanHanhService.getAll({ duong_day_id: duongDayId, is_deleted: false });

  let allDonVi = await DonViService.getAll({ _id: allDuongDay.map(duongDay => duongDay.don_vi_id) })
    .populate({
      path: 'don_vi_cha_id', select: 'ten_don_vi don_vi_cha_id',
      populate: { path: 'don_vi_cha_id', select: 'ten_don_vi don_vi_cha_id' },
    });

  const mapDonViId = {};
  allDonVi.forEach(donVi => {
    !mapDonViId[donVi._id] && (mapDonViId[donVi._id] = []);
    let nodeData = donVi;
    do {
      mapDonViId[donVi._id].push(nodeData._id.toString());
      nodeData = nodeData.don_vi_cha_id;
    } while (!!nodeData);
  });

  const mapVanHanh = {};
  allVanHanh.forEach(vh => {
    !mapVanHanh[vh.duong_day_id] && (mapVanHanh[vh.duong_day_id] = []);
    mapVanHanh[vh.duong_day_id].push(vh._id);
  });

  let duongDayKhongViTri = [], donViCoDuongDayKhongViTri = [];
  for (let i = 0; i < allDuongDay.length; i++) {
    if (!mapVanHanh[allDuongDay[i]._id]) {
      duongDayKhongViTri.push(allDuongDay[i]);
      donViCoDuongDayKhongViTri = [donViCoDuongDayKhongViTri, mapDonViId[allDuongDay[i].don_vi_id]].flat();
    }
  }

  const donViData = await DonViService.getAll({ _id: [...new Set(donViCoDuongDayKhongViTri)], is_deleted: false });
  donViData.forEach(donVi => {
    donVi.duong_day_id = duongDayKhongViTri.filter(duongDay => duongDay.don_vi_id.toString() === donVi._id.toString());
    donVi.duong_day_id.sort((a, b) => a.ten_duong_day.localeCompare(b.ten_duong_day));
  });

  return donViData;
}

export async function duongDayChuaCoViTri(req, res) {
  try {
    const { criteria } = await buildQuery(req);
    const donViQuery = await DonViService.getDonViQuery(req, criteria.don_vi_id);

    const donViData = await getDuongDayKhongViTri(donViQuery);
    const allDonViTree = createDataTree(donViData, '_id', 'don_vi_cha_id');
    return responseHelper.success(res, allDonViTree);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function exportDuongDayChuaCoViTri(req, res) {
  try {
    const { id } = req.params;
    if (!isValidObjectId(id)) return responseHelper.error(res, CommonError.NOT_FOUND);
    const checkDonVi = await DonViService.getById(id);
    if (!checkDonVi) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    const donViData = await getDuongDayKhongViTri(id);
    const donVi = donViData.find(donVi => donVi?._id?.toString() === id);

    if (!donVi || !Array.isArray(donVi.duong_day_id)) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    donVi.duong_day_id.forEach((duongDay, index) => {
      duongDay.stt = index + 1;
      return duongDay;
    });


    const templateFilePath = getFilePath('duong_day_khong_vi_tri.xlsx', TEMPLATES_DIRS.RA_SOAT_DU_LIEU);
    await generateDocument(res, donVi, templateFilePath);

  } catch (err) {
    return responseHelper.error(res, err);
  }
}

async function getViTriThieuDuLieu(req) {
  if (req.query.ma_vi_tri) {
    req.query.ma_vi_tri = `"${req.query.ma_vi_tri}"`;
  }
  if (req.query.ten_vi_tri) {
    req.query.ten_vi_tri = `"${req.query.ten_vi_tri}"`;
  }
  const query = queryHelper.extractQueryParam(req, ['ma_vi_tri', 'ten_vi_tri']);
  const { criteria, options } = query;

  options.populate = [
    {
      path: 'don_vi_id', select: 'ten_don_vi ma_in',
      populate: { path: 'don_vi_cha_id', select: 'ten_don_vi ma_in' },
    },
    { path: 'cong_trinh_id', select: 'ten_cong_trinh' },
  ];

  options.sort = { cong_trinh_id: 1, thu_tu: 1 };
  criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);

  const errorValue = [null, ''];
  const criteriaCoordinate = {
    $or: [
      { kinh_do: { $in: errorValue } },
      { kinh_do: { $regex: /[a-zA-Z °'",]/ } },
      { vi_do: { $in: errorValue } },
      { vi_do: { $regex: /[a-zA-Z °'",]/ } },
    ],
  };

  return await VI_TRI.paginate({ ...criteria, ...criteriaCoordinate }, options);
}

export async function viTriThieuDuLieu(req, res) {
  try {
    const data = await getViTriThieuDuLieu(req);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function exportViTriThieuDuLieu(req, res) {
  try {
    req.query.limit = 0;
    const data = await getViTriThieuDuLieu(req);

    data.docs?.forEach((doc, index) => {
      doc.stt = index + 1;
    });

    const templateFilePath = getFilePath('vi_tri_thieu_du_lieu.xlsx', TEMPLATES_DIRS.RA_SOAT_DU_LIEU);
    await generateDocument(res, data.docs, templateFilePath);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

const selectViTri = {
  select: 'ten_vi_tri don_vi_id cong_trinh_id kinh_do vi_do thu_tu',
  populate: [
    { path: 'don_vi_id', select: 'ten_don_vi' },
    { path: 'cong_trinh_id', select: 'ten_cong_trinh' },
  ],
};

async function getKhoangCotThieuDuLieu(req) {
  const query = queryHelper.extractQueryParam(req, ['ten_khoang_cot']);
  const { criteria, options } = query;
  options.populate = [
    { path: 'vi_tri_bat_dau_id', ...selectViTri },
    { path: 'vi_tri_ket_thuc_id', ...selectViTri },
    {
      path: 'vi_tri_id', ...selectViTri,
      options: { sort: { 'vi_tri_id.cong_trinh_id': 1, 'vi_tri_id.thu_tu': 1 } },
    },
    {
      path: 'duong_day_id',
      select: 'ten_duong_day',
    },
  ];
  const dontInScope = await DonViService.getArrayDonViQuery(req, criteria.don_vi_id);
  const queryViTri = {};
  delete criteria.don_vi_id;

  if (criteria.cong_trinh_id) {
    queryViTri.cong_trinh_id = criteria.cong_trinh_id;
    delete criteria.cong_trinh_id;
  }
  criteria.chieu_dai = null;
  const viTriPromise = dontInScope.map(donVi => {
    return VI_TRI.find({ ...queryViTri, don_vi_id: donVi }, { _id: 1 }).lean();
  });
  const allViTri = (await Promise.all(viTriPromise)).flat();
  criteria.vi_tri_id = extractIds(allViTri);
  return await KHOANG_COT.paginate(criteria, options);
}


export async function khoangCotThieuDuLieu(req, res) {
  try {
    const data = await getKhoangCotThieuDuLieu(req);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function exportKhoangCotThieuDuLieu(req, res) {
  try {
    req.query.limit = 0;
    const data = await getKhoangCotThieuDuLieu(req);

    data.docs?.forEach((doc, index) => {
      doc.stt = index + 1;
    });

    const templateFilePath = getFilePath('khoang_cot_thieu_du_lieu.xlsx', TEMPLATES_DIRS.RA_SOAT_DU_LIEU);
    await generateDocument(res, data.docs, templateFilePath);

    // return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

async function getVanHanhThieuDuLieu(req) {
  function createError(t, row) {
    let errors = [];
    if (!hasCachDien[row._id]) errors = [...errors, t('THIEU_CACH_DIEN')];
    if (!hasDayDan[row._id]) errors = [...errors, t('THIEU_DAY_DAN')];

    row['error'] = errors.length ? errors : null;
    return row;
  }


  const { t } = req;
  const query = queryHelper.extractQueryParam(req, ['ten_van_hanh']);
  const { criteria, options } = query;
  options.populate = [
    { path: 'vi_tri_id', populate: 'cong_trinh_id' },
    { path: 'duong_day_id', populate: 'don_vi_id loai_duong_day_id' },
    { path: 'don_vi_id' },
  ];
  criteria.don_vi_id = await DonViService.getDonViInScope(criteria.don_vi_id || req.user?.don_vi_id);
  const [vanHanhInCachDien, vanHanhInDayDan] = await Promise.all([
    CACH_DIEN.distinct('van_hanh_id', { is_deleted: false }),
    DAY_DAN.distinct('van_hanh_id', { is_deleted: false }),
  ]);
  const [vanHanhNotInCachDien, vanHanhNotInDayDan] = await Promise.all([
    VAN_HANH.distinct('_id', { is_deleted: false, _id: { $nin: vanHanhInCachDien } }),
    VAN_HANH.distinct('_id', { is_deleted: false, _id: { $nin: vanHanhInDayDan } }),
  ]);

  const hasCachDien = {}, hasDayDan = {};
  vanHanhInCachDien.forEach(vanHanh => hasCachDien[vanHanh] = true);
  vanHanhInDayDan.forEach(vanHanh => hasDayDan[vanHanh] = true);

  const criteriaNew = {
    ...criteria,
    _id: { $in: [...vanHanhNotInCachDien, ...vanHanhNotInDayDan] },
  };

  const data = await VAN_HANH.paginate(criteriaNew, options);
  data.docs.map(row => createError(t, row));

  return data;
}

export async function vanHanhThieuDuLieu(req, res) {
  try {
    const data = await getVanHanhThieuDuLieu(req);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function exportVanHanhThieuDuLieu(req, res) {
  try {
    req.query.limit = 0;
    const data = await getVanHanhThieuDuLieu(req);

    data.docs?.forEach((doc, index) => {
      doc.stt = index + 1;
      doc.error_str = doc.error?.join('\n')
    });

    const templateFilePath = getFilePath('van_hanh_thieu_du_lieu.xlsx', TEMPLATES_DIRS.RA_SOAT_DU_LIEU);
    await generateDocument(res, data.docs, templateFilePath);

  } catch (err) {
    return responseHelper.error(res, err);
  }
}
