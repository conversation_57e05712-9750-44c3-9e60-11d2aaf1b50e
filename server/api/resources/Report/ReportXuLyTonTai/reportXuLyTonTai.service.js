import KET_QUA_KIEM_TRA from '../../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.model';

export async function reportXuLyTonTaiByDuongDay(queryKiemTra) {
  const ketQuaKiemTra = await KET_QUA_KIEM_TRA.aggregate([
    {
      '$match': queryKiemTra,
    },
    {
      '$unwind': {
        path: '$duong_day_id',
      },
    },
    {
      '$group': {
        _id: {
          duong_day_id: '$duong_day_id', // ❌ Không còn don_vi_id từ KET_QUA_KIEM_TRA
        },
        tongSoTonTai: { $sum: 1 },
        tongSoTonTaiDoi: {
          $sum: {
            $cond: [{ $ne: ['$de_xuat_cap_doi', 'BAO_CAO_TTD'] }, 1, 0],
          },
        },
        soTonTaiDoiCoYKien: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $ne: ['$de_xuat_cap_doi', 'BAO_CAO_TTD'] },
                  { $eq: [{ $type: '$de_xuat_cap_doi' }, 'string'] },
                ],
              },
              1,
              0,
            ],
          },
        },
        soTonTaiDoiKoYKien: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $ne: ['$de_xuat_cap_doi', 'BAO_CAO_TTD'] },
                  { $ne: [{ $type: '$de_xuat_cap_doi' }, 'string'] },
                ],
              },
              1,
              0,
            ],
          },
        },
        tongSoTonTaiTTD: {
          $sum: {
            $cond: [{ $eq: ['$de_xuat_cap_doi', 'BAO_CAO_TTD'] }, 1, 0],
          },
        },
        soTonTaiTTDCoYKien: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ['$de_xuat_cap_doi', 'BAO_CAO_TTD'] },
                  { $eq: [{ $type: '$ke_hoach_phe_duyet' }, 'string'] },
                ],
              },
              1,
              0,
            ],
          },
        },
        soTonTaiTTDKoYKien: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ['$de_xuat_cap_doi', 'BAO_CAO_TTD'] },
                  { $ne: [{ $type: '$ke_hoach_phe_duyet' }, 'string'] },
                ],
              },
              1,
              0,
            ],
          },
        },
      },
    },
    {
      $set: {
        duong_day_id: '$_id.duong_day_id',
      },
    },
    {
      $lookup: {
        from: 'DuongDay',
        localField: 'duong_day_id',
        foreignField: '_id',
        as: 'duongday',
      },
    },
    {
      $unwind: {
        path: '$duongday',
      },
    },
    {
      $match: {
        'duongday.don_vi_id': { $exists: true, $ne: null },
      },
    },
    {
      $set: {
        don_vi_id: '$duongday.don_vi_id', // ✅ Đơn vị thực lấy từ DuongDay
      },
    },
    {
      $group: {
        _id: '$don_vi_id',
        tongSoTonTai: { $sum: '$tongSoTonTai' },
        tongSoTonTaiDoi: { $sum: '$tongSoTonTaiDoi' },
        soTonTaiDoiCoYKien: { $sum: '$soTonTaiDoiCoYKien' },
        soTonTaiDoiKoYKien: { $sum: '$soTonTaiDoiKoYKien' },
        tongSoTonTaiTTD: { $sum: '$tongSoTonTaiTTD' },
        soTonTaiTTDCoYKien: { $sum: '$soTonTaiTTDCoYKien' },
        soTonTaiTTDKoYKien: { $sum: '$soTonTaiTTDKoYKien' },
        duong_days: {
          $push: {
            _id: '$duongday._id',
            ten_duong_day: '$duongday.ten_duong_day',
            tongSoTonTai: '$tongSoTonTai',
            tongSoTonTaiDoi: '$tongSoTonTaiDoi',
            soTonTaiDoiCoYKien: '$soTonTaiDoiCoYKien',
            soTonTaiDoiKoYKien: '$soTonTaiDoiKoYKien',
            tongSoTonTaiTTD: '$tongSoTonTaiTTD',
            soTonTaiTTDCoYKien: '$soTonTaiTTDCoYKien',
            soTonTaiTTDKoYKien: '$soTonTaiTTDKoYKien',
          },
        },
      },
    },
  ]);

  ketQuaKiemTra.forEach(result => {
    result.duong_days.sort((a, b) =>
      a.ten_duong_day.localeCompare(b.ten_duong_day, 'vi', { sensitivity: 'base' }),
    );
  });

  return ketQuaKiemTra;
}

export async function reportXuLyTonTaiByDonVi(queryKiemTra) {
  const ketQuaKiemTra = await KET_QUA_KIEM_TRA.aggregate([
    {
      '$match': queryKiemTra,
    },
    {
      '$group': {
        _id: '$don_vi_id',
        tongSoTonTai: { $sum: 1 },
        tongSoTonTaiDoi: {
          $sum: {
            $cond: [{ $ne: ['$de_xuat_cap_doi', 'BAO_CAO_TTD'] }, 1, 0],
          },
        },
        soTonTaiDoiCoYKien: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $ne: ['$de_xuat_cap_doi', 'BAO_CAO_TTD'] },
                  { $eq: [{ $type: '$de_xuat_cap_doi' }, 'string'] },
                ],
              },
              1,
              0,
            ],
          },
        },
        soTonTaiDoiKoYKien: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $ne: ['$de_xuat_cap_doi', 'BAO_CAO_TTD'] },
                  { $ne: [{ $type: '$de_xuat_cap_doi' }, 'string'] },
                ],
              },
              1,
              0,
            ],
          },
        },
        tongSoTonTaiTTD: {
          $sum: {
            $cond: [{ $eq: ['$de_xuat_cap_doi', 'BAO_CAO_TTD'] }, 1, 0],
          },
        },
        soTonTaiTTDCoYKien: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ['$de_xuat_cap_doi', 'BAO_CAO_TTD'] },
                  { $eq: [{ $type: '$ke_hoach_phe_duyet' }, 'string'] },
                ],
              },
              1,
              0,
            ],
          },
        },
        soTonTaiTTDKoYKien: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ['$de_xuat_cap_doi', 'BAO_CAO_TTD'] },
                  { $ne: [{ $type: '$ke_hoach_phe_duyet' }, 'string'] },
                ],
              },
              1,
              0,
            ],
          },
        },
      },
    },
  ]);

  return ketQuaKiemTra;
}


