import { Types } from 'mongoose';

import * as responseHelper from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';

import * as ReportXuLyTonTaiService from './reportXuLyTonTai.service';
import * as TonTaiService from '../../QuanLyVanHanh/TonTai/tonTai.service';
import * as DonViService from '../../DonVi/donVi.service';

import { CAP_DON_VI, TEMPLATES_DIRS } from '../../../constant/constant';
import { getFilePath } from '../../../utils/fileUtils';
import { generateDocument } from '../GenerateFile/generate.controller';

import { handleHangMucQuery } from '../../QuanLyVanHanh/TonTai/tonTai.controller';

export async function getDataXuLyTonTai(req) {
  const query = queryHelper.extractQueryParam(req);
  const tonTaiQuery = await TonTaiService.convertCriteria(req, query.criteria, true);
  await handleHangMucQuery(tonTaiQuery);

  if (tonTaiQuery.duong_day_id) {
    if (tonTaiQuery.duong_day_id.$in && Array.isArray(tonTaiQuery.duong_day_id.$in)) {
      tonTaiQuery.duong_day_id.$in = tonTaiQuery.duong_day_id.$in.map(id => Types.ObjectId(id));
    } else if (typeof tonTaiQuery.duong_day_id === 'string') {
      tonTaiQuery.duong_day_id = Types.ObjectId(tonTaiQuery.duong_day_id);
    }
  }

  tonTaiQuery.khoang_cot_id && (tonTaiQuery.khoang_cot_id = Types.ObjectId(tonTaiQuery.khoang_cot_id));
  tonTaiQuery.vi_tri_id && (tonTaiQuery.vi_tri_id = Types.ObjectId(tonTaiQuery.vi_tri_id));
  if (tonTaiQuery.thoi_gian_tao?.$gte) {
    tonTaiQuery.thoi_gian_tao.$gte = new Date(tonTaiQuery.thoi_gian_tao.$gte);
  }
  if (tonTaiQuery.thoi_gian_tao?.$lt) {
    tonTaiQuery.thoi_gian_tao.$lt = new Date(tonTaiQuery.thoi_gian_tao.$lt);
  }
  const reportXuLyTonTaiRecords = await ReportXuLyTonTaiService.reportXuLyTonTaiByDonVi(tonTaiQuery);
  const allDonVi = await DonViService.getAll({ _id: tonTaiQuery.don_vi_id });

  function initDataDonVi(allDonVi = []) {
    let initData = {
      'tongSoTonTai': 0,
      'tongSoTonTaiDoi': 0,
      'soTonTaiDoiCoYKien': 0,
      'soTonTaiDoiKoYKien': 0,
      'tongSoTonTaiTTD': 0.0,
      'soTonTaiTTDCoYKien': 0.0,
      'soTonTaiTTDKoYKien': 0.0,
    };
    return allDonVi.map(donVi => ({
      ...donVi,
      ...initData,
    }));
  }

  let allDonViData = initDataDonVi(allDonVi);
  allDonViData = allDonViData.map(donVi => {
    const recordForDonVi = reportXuLyTonTaiRecords.find(record => record._id.toString() === donVi._id.toString());
    if (recordForDonVi) {
      return { ...donVi, ...recordForDonVi };
    } else {
      return donVi;
    }
  });

  function updateDonViData(donviCha, donviCon) {
    donviCha['tongSoTonTai'] += donviCon['tongSoTonTai'];
    donviCha['tongSoTonTaiDoi'] += donviCon['tongSoTonTaiDoi'];
    donviCha['soTonTaiDoiCoYKien'] += donviCon['soTonTaiDoiCoYKien'];
    donviCha['soTonTaiDoiKoYKien'] += donviCon['soTonTaiDoiKoYKien'];
    donviCha['tongSoTonTaiTTD'] += donviCon['tongSoTonTaiTTD'];
    donviCha['soTonTaiTTDCoYKien'] += donviCon['soTonTaiTTDCoYKien'];
    donviCha['soTonTaiTTDKoYKien'] += donviCon['soTonTaiTTDKoYKien'];
  }

  function updateDataForParent(donVi, allDonVi) {
    const allChild = allDonVi.filter(dv => dv.don_vi_cha_id?.toString() === donVi._id?.toString());
    allChild.forEach(child => updateDonViData(donVi, child));
  }

  function updateDataForOrgUnit(capDonVi, allDonVi) {
    allDonVi.forEach(donVi => {
      donVi.cap_don_vi === capDonVi && updateDataForParent(donVi, allDonVi);
    });
  }

  [CAP_DON_VI.TRUYEN_TAI_DIEN, CAP_DON_VI.CONG_TY, CAP_DON_VI.TONG_CONG_TY]
    .forEach(item => updateDataForOrgUnit(item, allDonViData));
  return DonViService.buildTree(allDonViData);
}

export async function reportXuLyTonTai(req, res) {
  try {
    const congTyTreeForAdmin = await getDataXuLyTonTai(req);
    return responseHelper.success(res, congTyTreeForAdmin);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function downloadBaoCaoXuLyTonTai(req, res) {
  const dataTonTai = await getDataXuLyTonTai(req);
  const donViQuery = await DonViService.getById(req.query.don_vi_id || req.user.don_vi_id);
  let templateFilePath;
  switch (donViQuery.cap_don_vi) {
    case CAP_DON_VI.DOI_TRUYEN_TAI_DIEN:
      templateFilePath = getFilePath('bao_cao_ton_tai_cap_doi.xlsx', TEMPLATES_DIRS.REPORT);
      break;
    case CAP_DON_VI.TRUYEN_TAI_DIEN:
      templateFilePath = getFilePath('bao_cao_ton_tai_cap_truyen_tai.xlsx', TEMPLATES_DIRS.REPORT);
      break;
    case CAP_DON_VI.CONG_TY:
      templateFilePath = getFilePath('bao_cao_ton_tai_cap_cong_ty.xlsx', TEMPLATES_DIRS.REPORT);
      break;
    case CAP_DON_VI.TONG_CONG_TY:
      templateFilePath = getFilePath('bao_cao_ton_tai_cap_tong_cong_ty.xlsx', TEMPLATES_DIRS.REPORT);
      break;
  }

  const outputFileName = 'Thông kê theo dõi công tác xử lý tồn tại.xlsx';
  await generateDocument(res, dataTonTai, templateFilePath, outputFileName);
}
