import express from 'express';
import * as Controller from './reportXuLyTonTai.controller';
import passport from 'passport';

export const reportXuLyTonTaiRouter = express.Router();

reportXuLyTonTaiRouter
  .route('/xulytontai')
  .get(passport.authenticate('jwt', { session: false }), Controller.reportXuLyTonTai);

reportXuLyTonTaiRouter
  .route('/download')
  .get(passport.authenticate('jwt', { session: false }), Controller.downloadBaoCaoXuLyTonTai);


