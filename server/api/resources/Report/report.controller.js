import * as responseHelper from '../../helpers/responseHelper';
import queryHelper from '../../helpers/queryHelper';

import { generateDocument } from './GenerateFile/generate.controller';
import momentTimezone from 'moment-timezone';
import i18next from 'i18next';

import * as DuongDayService from '../TongKe/DuongDay/duongDay.service';
import * as Service from './report.service';
import * as PhieuGiaoViecService from '../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';
import * as KetQuaKiemTraService from '../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.service';
import * as ketQuaKiemTraService from '../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.service';
import * as DonViService from '../DonVi/donVi.service';
import * as ViTriService from '../TongKe/ViTri/viTri.service';
import * as CotDienService from '../TongKe/CotDien/cotDien.service';
import * as VanHanhService from '../TongKe/VanHanh/vanHanh.service';
import * as UserService from '../User/user.service';

import quanLyVanHanhCommons from '../QuanLyVanHanh/quanLyVanHanhCommons';
import { CAP_DON_VI, MACH, TEMPLATES_DIRS } from '../../constant/constant';
import { getFilePath } from '../../utils/fileUtils';
import { cloneObj, downlineData } from '../../common/functionCommons';
import { formatDate, formatToDateDetail } from '../../common/formatUTCDateToLocalDate';
import { convertObject, extractIds, groupBy } from '../../utils/dataconverter';
import { LOAI_CONG_VIEC } from '../DanhMuc/LoaiCongViec';
import { LOAI_VI_TRI } from '../TongKe/ViTri/viTri.model';

export async function formatQuery(req) {
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  if (!criteria.don_vi?.hasOwnProperty('$exists')) {
    criteria.don_vi_id = criteria.don_vi;
    criteria.don_vi_giao_phieu_id = await DonViService.getDonViQuery(req, criteria.don_vi_id, true);
  } else {
    criteria.don_vi_id = req.user.don_vi_id;
    criteria.don_vi_giao_phieu_id = await DonViService.getDonViQuery(req, criteria.don_vi_id, true);
  }
  return criteria;
}

export async function downloadPhieuGiaoViec(req, res) {
  const phieuGiaoViec = await Service.getPhieuGiaoViec(await formatQuery(req));

  const templateFilePath = getFilePath('phieu_giao_viec_don_vi.docx', TEMPLATES_DIRS.TEMPLATES);
  const outputFileName = `${i18next.t('bao_cao_phieu_giao_viec')}.docx`;
  await generateDocument(res, phieuGiaoViec, templateFilePath, outputFileName);
}

export async function downloadChamTienDo(req, res) {
  const { t } = req;
  const phieuGiaoViec = await Service.getAllChamTienDo(await formatQuery(req));

  const templateFilePath = getFilePath('phieu_cham_tien_do.docx', TEMPLATES_DIRS.TEMPLATES);
  const outputFileName = `${i18next.t('bao_cao_cham_tien_do')}.docx`;
  await generateDocument(res, phieuGiaoViec, templateFilePath, outputFileName);
}

export async function lyLichCot(req, res) {
  try {
    const { t } = req;
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const dataDuongDay = await Service.getViTriByID(criteria);

    const templateFilePath = getFilePath('ly_lich_cot.docx', TEMPLATES_DIRS.TEMPLATES);
    const outputFileName = `${t('ly_lich_cot')}.docx`;
    return generateDocument(res, dataDuongDay, templateFilePath, outputFileName);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function bieuMauDiaPhuong(req, res) {
  const { t } = req;
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  const dataViTri = await Service.getDataViTri(criteria.id);

  const templateFilePath = getFilePath('bieu_mau_dia_phuong.xlsx', TEMPLATES_DIRS.BIEU_MAU);
  const outputFileName = `${i18next.t('bieu_mau_dia_phuong')}.xlsx`;
  await generateDocument(res, dataViTri, templateFilePath, outputFileName);
}

export async function bieuMauGiaoCheo(req, res) {
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  const duongDay = await DuongDayService.getAll({ _id: criteria.id })
    .populate({ path: 'don_vi_id', populate: 'don_vi_cha_id' });
  let data = {};
  data.don_vi_id = duongDay[0].don_vi_id;
  data.ten_duong_day = duongDay[0].ten_duong_day;
  data.khoang_cot = await DuongDayService.bangTongHopGiaoCheo(req, criteria.id);

  const templateFilePath = getFilePath('theo_doi_giao_cheo.xlsx', TEMPLATES_DIRS.BIEU_MAU);
  const outputFileName = `${i18next.t('theo_doi_giao_cheo')}.xlsx`;
  await generateDocument(res, data, templateFilePath, outputFileName);
}

export async function tongKeDuongDay(req, res) {
  const { t } = req;
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;

  let data = {};
  const dataTongKe = await DuongDayService.bangTongKeDuongDay(req, criteria.id);
  data.tong_ke = dataTongKe;

  const templateFilePath = dataTongKe[0].day_chong_set.length > 0
    ? getFilePath('tong_ke_duong_day_1dcs.xlsx', TEMPLATES_DIRS.BIEU_MAU)
    : getFilePath('tong_ke_duong_day_2dcq.xlsx', TEMPLATES_DIRS.BIEU_MAU);
  const outputFileName = `${i18next.t('tong_ke_duong_day')}.xlsx`;
  await generateDocument(res, data, templateFilePath, outputFileName);
}

export async function nhatKyVanHanhDoiTruyenTaiDien(req, res) {
  const { t } = req;
  console.log('t', t);
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  const dataNhatKyVanHanhDoiTTD = await Service.nhatKyVanHanhDoiTruyentaiDien(criteria);

  const templateFilePath = getFilePath('so_nhat_ky_van_hanh.xlsx', TEMPLATES_DIRS.BIEU_MAU);
  const outputFileName = `${i18next.t('so_nhat_ky_van_hanh')}.xlsx`;
  await generateDocument(res, dataNhatKyVanHanhDoiTTD, templateFilePath, outputFileName);
}

export async function allNhatKyVanHanhDoiTruyenTaiDien(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const data = await Service.nhatKyVanHanhDoiTruyentaiDien(criteria);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

async function convertCriteria(req, criteria) {

  criteria.don_vi_id = await DonViService.getDonViInScope(criteria.don_vi_giao_phieu_id);
  if (criteria.date_filter && !criteria.date_filter?.hasOwnProperty('$exists')) {
    const oneDay = 24 * 60 * 60 * 1000;
    criteria.thoi_gian_cong_tac_bat_dau = {
      $gte: momentTimezone(criteria.date_filter),
      $lt: momentTimezone(new Date(criteria.date_filter.getTime() + oneDay)),
    };
    delete criteria.tu_ngay;
    delete criteria.den_ngay;
    delete criteria.date_filter;
    return criteria;
  }
  if (criteria.tu_ngay && !criteria.tu_ngay?.hasOwnProperty('$exists')) {
    if (criteria.den_ngay && !criteria.den_ngay.hasOwnProperty('$exists')) {
      criteria.thoi_gian_cong_tac_bat_dau = {
        $gte: momentTimezone(criteria.tu_ngay),
        $lte: momentTimezone(criteria.den_ngay),
      };
    } else {
      criteria.thoi_gian_cong_tac_bat_dau = { $gte: momentTimezone(criteria.tu_ngay) };
    }
  } else {
    if (criteria.den_ngay && !criteria.den_ngay.hasOwnProperty('$exists')) {
      criteria.thoi_gian_cong_tac_bat_dau = { $lte: momentTimezone(criteria.den_ngay) };
    }
  }
  delete criteria.date_filter;
  delete criteria.tu_ngay;
  delete criteria.den_ngay;
  return criteria;
}

export async function thongKeNoiDungKiemTraBangThietBiBay(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;
    criteria = await convertCriteria(req, criteria);
    const data = await PhieuGiaoViecService.tongHopPhieuSuDungThietBiBay(criteria);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function downloadNoiDungKiemTraBangThietBiBay(req, res) {
  const query = queryHelper.extractQueryParam(req);
  let { criteria } = query;
  criteria = await convertCriteria(req, criteria);
  const data = await PhieuGiaoViecService.tongHopPhieuSuDungThietBiBay(criteria);
  const dataAfterConvert = await Service.convertDataNoiDungKiemTraBangThietBiBay(data.data_table);
  dataAfterConvert[0].time_start = criteria.thoi_gian_cong_tac_bat_dau?.$gte ? formatDate(criteria.thoi_gian_cong_tac_bat_dau.$gte) : '';
  dataAfterConvert[0].time_end = criteria.thoi_gian_cong_tac_bat_dau?.$lte ? formatDate(criteria.thoi_gian_cong_tac_bat_dau.$lte) : '';

  const templateFilePath = getFilePath('cong_viec_kiem_tra_bang_thiet_bi_bay.xlsx', TEMPLATES_DIRS.BIEU_MAU);
  const outputFileName = 'Công việc kiểm tra bằng thiết bị bay.xlsx';
  await generateDocument(res, dataAfterConvert, templateFilePath, outputFileName);
}

export async function allCongViecByViTriId(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const data = await Service.getAllCongViecByViTriId(criteria);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function downloadCongViecByViTriId(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const allCongViec = await Service.getAllCongViecByViTriId(criteria);
    let data = {};
    data.tu_ngay = criteria.created_at.$gte ? formatDate(criteria.created_at.$gte) : '';
    data.den_ngay = criteria.created_at.$lte ? formatDate(criteria.created_at.$lte) : '';
    data.phieu_giao_viec = allCongViec;

    const templateFilePath = getFilePath('danh_sach_cong_viec_theo_vi_tri.docx', TEMPLATES_DIRS.BIEU_MAU);
    const outputFileName = 'Danh sách công việc theo vị trí.docx';
    await generateDocument(res, data, templateFilePath, outputFileName);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function downloadDanhSachNguoiDung(req, res) {
  try {
    let query = queryHelper.extractQueryParam(req, ['username', 'full_name', 'email', 'phone']);
    const criteria = query.criteria;
    criteria.is_system_admin = false;

    if ((!req.query.include_children || req.query.include_children === 'false') && req.query.don_vi_id) {
      criteria.don_vi_id = req.query.don_vi_id;
    } else {
      criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);
    }

    const allUsers = await UserService.getAll(criteria)
      .populate('don_vi_id role_id')
      .sort({ name: 1, full_name: 1, username: 1 });
    const usersToRender = allUsers.map(Service.convertDataUser);

    const donVi = await DonViService.getAll({ _id: criteria.don_vi_id });
    let dataToDown = {};
    dataToDown.don_vi_label = req.query.include_children === 'true' ? downlineData(donVi[0].ten_don_vi.toUpperCase(), '(Bao gồm đơn vị trực thuộc)') : donVi[0].ten_don_vi.toUpperCase();
    dataToDown.ngay = formatToDateDetail(new Date()).ngay;
    dataToDown.thang = formatToDateDetail(new Date()).thang;
    dataToDown.nam = formatToDateDetail(new Date()).nam;
    dataToDown.nhan_vien = usersToRender;

    const templateFilePath = getFilePath('danh_sach_nguoi_dung.xlsx', TEMPLATES_DIRS.TEMPLATES);
    const outputFileName = 'Thống kê danh sách người dùng.xlsx';
    await generateDocument(res, dataToDown, templateFilePath, outputFileName);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function reportInspectionWorkByDonVi(req, res) {
  try {
    req.query.don_vi_id = req.query.don_vi_giao_phieu_id;
    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;
    criteria = await convertCriteria(req, criteria);
    const allDonVi = await PhieuGiaoViecService.tongHopCongTacKiemTraTheoDonVi(req, criteria);
    const dataReturn = DonViService.buildTree(allDonVi);
    return responseHelper.success(res, dataReturn);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getTimePhieuGiaoViec(req, res) {
  try {
    req.query.don_vi_id = req.query.don_vi_giao_phieu_id;
    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;
    criteria = await convertCriteria(req, criteria);
    const lastTime = await PhieuGiaoViecService.getTimePhieuGiaoViec(req, criteria);
    return responseHelper.success(res, lastTime);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function reportInspectionWorkByDonViV1(req, res) {
  try {
    req.query.don_vi_id = req.query.don_vi_giao_phieu_id;
    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;
    criteria = await convertCriteria(req, criteria);
    const allDonVi = await PhieuGiaoViecService.tongHopCongTacKiemTraTheoDonViV1(req, criteria);
    const dataReturn = DonViService.buildTree(allDonVi);
    return responseHelper.success(res, dataReturn);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function downloadTongHopSuDungDrone(req, res) {
  try {
    // Mapping file template theo cấp đơn vị
    const TEMPLATE_FILENAME_MAP = {
      [CAP_DON_VI.DOI_TRUYEN_TAI_DIEN]: 'tong_hop_su_dung_drone_cap_doi.xlsx',
      [CAP_DON_VI.TRUYEN_TAI_DIEN]: 'tong_hop_su_dung_drone_cap_truyen_tai.xlsx',
      [CAP_DON_VI.CONG_TY]: 'tong_hop_su_dung_drone_cap_cong_ty.xlsx',
      [CAP_DON_VI.TONG_CONG_TY]: 'tong_hop_su_dung_drone_cap_tong_cong_ty.xlsx',
    };

    // Trích xuất và chuẩn bị tham số truy vấn
    req.query.don_vi_id = req.query.don_vi_giao_phieu_id;
    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;

    // Lấy type và xóa khỏi criteria
    const { type } = criteria;
    delete criteria.type;

    const [convertedCriteria, donViQuery] = await Promise.all([
      convertCriteria(req, criteria),
      DonViService.getById(req.query.don_vi_id || req.user.don_vi_id),
    ]);

    // Lấy dữ liệu tổ chức
    const allDonVi = await PhieuGiaoViecService.tongHopCongTacKiemTraTheoDonVi(req, convertedCriteria);
    const congTyTreeForAdmin = DonViService.buildTree(allDonVi);

    // Lấy tên file template dựa trên cấp đơn vị
    const templateFileName = TEMPLATE_FILENAME_MAP[donViQuery.cap_don_vi];
    if (!templateFileName) {
      throw new Error(`Cấp đơn vị không được hỗ trợ: ${donViQuery.cap_don_vi}`);
    }

    // Xây dựng đường dẫn file template
    const templatePath = type ? `/${type}/${templateFileName}` : templateFileName;
    const templateFilePath = getFilePath(templatePath, TEMPLATES_DIRS.REPORT);

    // Tạo và tải xuống tài liệu
    await generateDocument(res, congTyTreeForAdmin, templateFilePath, null);
  } catch (error) {
    console.error('Lỗi khi tải xuống báo cáo tổng hợp sử dụng drone:', error);
    return responseHelper.error(res, error);
  }
}

export async function downloadTongHopSuDungDroneV1(req, res) {
  req.query.don_vi_id = req.query.don_vi_giao_phieu_id;
  const query = queryHelper.extractQueryParam(req);
  let { criteria } = query;
  let type = criteria.type === 'phuongphapthuchien' ? 'phuongphapthuchien' : 'sudungdrone';
  delete criteria.type;
  criteria = await convertCriteria(req, criteria);
  const allDonVi = await PhieuGiaoViecService.tongHopCongTacKiemTraTheoDonViV1(req, criteria);
  const congTyTreeForAdmin = DonViService.buildTree(allDonVi);
  const donViQuery = await DonViService.getById(req.query.don_vi_id || req.user.don_vi_id);
  let templateFilePath;
  switch (donViQuery.cap_don_vi) {
    case CAP_DON_VI.DOI_TRUYEN_TAI_DIEN:
      templateFilePath = getFilePath(`/${type}/tong_hop_su_dung_drone_cap_doi_v1.xlsx`, TEMPLATES_DIRS.REPORT);
      break;
    case CAP_DON_VI.TRUYEN_TAI_DIEN:
      templateFilePath = getFilePath(`/${type}/tong_hop_su_dung_drone_cap_truyen_tai_v1.xlsx`, TEMPLATES_DIRS.REPORT);
      break;
    case CAP_DON_VI.CONG_TY:
      templateFilePath = getFilePath(`/${type}/tong_hop_su_dung_drone_cap_cong_ty_v1.xlsx`, TEMPLATES_DIRS.REPORT);
      break;
    case CAP_DON_VI.TONG_CONG_TY:
      templateFilePath = getFilePath(`/${type}/tong_hop_su_dung_drone_cap_tong_cong_ty_v1.xlsx`, TEMPLATES_DIRS.REPORT);
      break;
  }
  await generateDocument(res, congTyTreeForAdmin, templateFilePath, null);
}


export async function thongKeCotDoCotNeo(req, res) {
  try {
    const donViInScope = await DonViService.getDonViInScope(req.query.don_vi_id || req.user?.don_vi_id);
    const viTriList = await ViTriService.getAll(
      { loai_vi_tri: LOAI_VI_TRI.COT_DIEN, don_vi_id: donViInScope, is_deleted: false },
      { ten_vi_tri: 1, don_vi_id: 1 },
    ).populate({ path: 'don_vi_id', select: 'ten_don_vi' });

    const cotDienList = await CotDienService.getAll(
      { vi_tri_id: extractIds(viTriList), is_deleted: false },
      { cong_dung_cot: 1, vi_tri_id: 1 },
    );

    const cotDienObj = convertObject(cotDienList, 'vi_tri_id');

    viTriList.forEach(viTri => {
      viTri.cong_dung_cot = cotDienObj?.[viTri._id]?.cong_dung_cot;
    });

    const viTriGroupByDonVi = viTriList.reduce(function(grouped, element) {
      const donViId = element.don_vi_id._id;
      grouped[donViId] ||= [];
      grouped[donViId].push(element);
      return grouped;
    }, {});

    const donViList = await DonViService.getAll(
      { _id: donViInScope },
      { ten_don_vi: 1, don_vi_cha_id: 1 },
    ).sort({ ten_don_vi: 1 });

    donViList.forEach(donVi => {
      const viTriByDonVi = viTriGroupByDonVi?.[donVi._id];

      donVi.so_luong_cot_dien = viTriByDonVi?.length;
      donVi.so_luong_cot_do = 0;
      donVi.so_luong_cot_neo = 0;
      donVi.vi_tri_thieu_cong_dung_id = [];

      viTriByDonVi?.forEach(viTri => {
        if (viTri.cong_dung_cot?.toLowerCase().includes('đỡ')) donVi.so_luong_cot_do++;
        else if (viTri.cong_dung_cot?.toLowerCase().includes('néo')) donVi.so_luong_cot_neo++;
        else donVi.vi_tri_thieu_cong_dung_id.push(viTri);
      });
    });

    const donViTree = await DonViService.buildTree(donViList);
    const thongKeData = congDonSoLuong(donViTree);

    return responseHelper.success(res, thongKeData);
  } catch (err) {
    console.log('err', err);
    return responseHelper.error(res, err);
  }
}

function congDonSoLuong(donViData) {
  function calculateTotal(donVi) {
    donVi.so_luong_cot_dien ||= 0;
    donVi.so_luong_cot_do ||= 0;
    donVi.so_luong_cot_neo ||= 0;
    donVi.vi_tri_thieu_cong_dung_id ||= [];

    donVi.children?.forEach(child => {

      if (child.children?.length) {
        child = calculateTotal(child);
      }

      donVi.so_luong_cot_dien += child.so_luong_cot_dien || 0;
      donVi.so_luong_cot_do += child.so_luong_cot_do || 0;
      donVi.so_luong_cot_neo += child.so_luong_cot_neo || 0;
      if (Array.isArray(child.vi_tri_thieu_cong_dung_id)) {
        donVi.vi_tri_thieu_cong_dung_id = [...donVi.vi_tri_thieu_cong_dung_id, ...child.vi_tri_thieu_cong_dung_id];
      }
    });

    return donVi;
  }

  return cloneObj(donViData).map(calculateTotal);
}

export async function thongKeMachDonMachKep(req, res) {
  try {
    const donViInScope = await DonViService.getDonViInScope(req.query.don_vi_id || req.user?.don_vi_id);
    const viTriList = await ViTriService.getAll(
      { don_vi_id: donViInScope, is_deleted: false },
      { ten_vi_tri: 1, dac_diem_dia_hinh: 1, don_vi_id: 1 },
    );

    const vanHanhList = await VanHanhService.getAll(
      { vi_tri_id: extractIds(viTriList), is_deleted: false },
      { ten_van_hanh: 1, duong_day_id: 1, vi_tri_id: 1 },
    )
      .populate({ path: 'duong_day_id', select: 'ten_duong_day is_deleted' });

    const vanHanhGroupByViTri = groupBy(vanHanhList, 'vi_tri_id');

    viTriList.forEach(viTri => {
      viTri.duong_day_id = vanHanhGroupByViTri[viTri._id.toString()]
        ?.filter(vanHanh => !vanHanh.duong_day_id?.is_deleted)
        ?.map(vanHanh => vanHanh.duong_day_id);
    });

    const thongKeMachObj = {};

    viTriList
      .filter(viTri => !!viTri.duong_day_id?.length)
      .forEach(viTri => {
        const duongDayIdMerge = extractIds(viTri.duong_day_id)
          .sort((a, b) => a.localeCompare(b))
          .join('-');

        thongKeMachObj[duongDayIdMerge] ||= {
          dac_diem_dia_hinh: viTri.dac_diem_dia_hinh || '',
          duong_day_id: viTri.duong_day_id,
          don_vi_id: viTri.don_vi_id,
          loai_mach: viTri.duong_day_id?.length === 1 ? MACH.MACH_DON : MACH.MACH_KEP,
        };

        if (viTri.duong_day_id?.length !== 1) {
          thongKeMachObj[duongDayIdMerge].loai_mach = MACH.MACH_KEP;
        }


      });

    const thongKeByDonVi = groupBy(Object.values(thongKeMachObj), 'don_vi_id');
    const donViList = await DonViService.getAll({ _id: donViInScope },
      { ten_don_vi: 1, don_vi_cha_id: 1 },
    );

    donViList.forEach(donVi => {
      donVi.thong_ke = thongKeByDonVi[donVi._id];
    });

    const donViTree = await DonViService.buildTree(donViList);


    return responseHelper.success(res, donViTree);
  } catch (err) {
    console.log('err', err);
    return responseHelper.error(res, err);
  }
}

/**
 * API thống kê tồn tại theo chuyên đề kiểm tra
 */
export async function thongKeTonTaiTheoChuyenDeKiemTra(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    let { criteria, options } = query;

    criteria.loai_cong_viec = LOAI_CONG_VIEC.KIEM_TRA_CHUYEN_DE.code;
    criteria.don_vi_giao_phieu_id = await DonViService.getDonViQuery(req, criteria.don_vi_id, true);
    criteria = quanLyVanHanhCommons.buildThoiGianCongTacQuery(criteria);

    delete criteria.don_vi_id;

    options.populate = [
      {
        path: 'duong_day_id',
        select: 'ten_duong_day duong_day_cu_id',
        populate: { path: 'duong_day_cu_id', select: 'ten_duong_day' },
      },
      {
        path: 'don_vi_id',
        select: 'ten_don_vi don_vi_cha_id',
        populate: { path: 'don_vi_cha_id', select: 'ten_don_vi' },
      },
      { path: 'phieu_giao_viec_id', populate: { path: 'chuyen_de_id', select: 'ten_chuyen_de' } },
      { path: 'vi_tri_id khoang_cot_id' },
      { path: 'ton_tai_cap_tren_id', populate: 'don_vi_id duong_day_id nguoi_tao_id' },
      { path: 'tieu_chi_id', populate: { path: 'noi_dung_kiem_tra_id tieu_chi_cha_id' } },
      { path: 'cach_dien_id day_dan_id cot_dien_id day_chong_set_id day_cap_quang_id' },
      { path: 'nguoi_tao', select: 'full_name' },
    ];
    options.sort = { thoi_gian_tao: -1 };

    const allPhieuGiaoViec = await PhieuGiaoViecService.getAll(criteria, { _id: 1 }); //Get all phiếu giao việc
    const thongKeData = await KetQuaKiemTraService
      .paginate({ phieu_giao_viec_id: extractIds(allPhieuGiaoViec), is_deleted: false }, options);

    return responseHelper.success(res, thongKeData);
  } catch (error) {
    return responseHelper.error(res, error);
  }
}

/**
 * API tải xuống bảng thống kê tồn tại theo chuyên đề kiểm tra
 *
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 *
 * @return {Promise<void>}
 */
export async function downloadTonTaiTheoChuyenDeKiemTra(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;

    criteria = await convertCriteria(req, criteria);

    criteria.is_deleted = false;
    delete criteria.don_vi_id;
    const phieuGiaoViecList = await PhieuGiaoViecService.getAllPopulate(criteria);
    const tonTaiData = await ketQuaKiemTraService.bangThongKeTheoDoiTonTai({
      phieu_giao_viec_id: { $in: extractIds(phieuGiaoViecList) },
      is_deleted: false,
    });

    const reportData = { ton_tai: tonTaiData };
    const templateFilePath = getFilePath('bieu_mau_thong_ke_ton_tai_kiem_tra_chuyen_de.xlsx', TEMPLATES_DIRS.BIEU_MAU);
    const outputFileName = 'Bảng thống kê tồn tại.xlsx';

    return generateDocument(res, reportData, templateFilePath, outputFileName);
  } catch (error) {
    return responseHelper.error(res, error);
  }
}
