import carbone from 'carbone';
import { getDirPath, getFilePath } from '../../../utils/fileUtils';
import * as PhieuGiaoViecService from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';
import * as NguoiCongTacService from '../../QuanLyVanHanh/NguoiCongTac/nguoiCongTac.service';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import { addIndexToListData, groupBy } from '../../../common/functionCommons';
import UserModel from '../../User/user.model';
import { NHOM_CONG_VIEC } from '../../DanhMuc/LoaiCongViec';
import { extractIds } from '../../../utils/dataconverter';
import queryHelper from '../../../helpers/queryHelper';
import * as DonViService from '../../DonVi/donVi.service';

export function generateDocument(t, res, data, templateFileName) {
  let opt = {
    renderPrefix: 'bao_cao',
    reportName: t('report'),
    timezone: 'Asia/Saigon',
  };
  const templateFilePath = getFilePath(templateFileName, getDirPath('templates', './server'));
  carbone.render(templateFilePath, data, opt, function (err, resultFilePath) {
    try {
      res.download(resultFilePath);
    } catch (err) {
      console.log('Không tải được tập tin!');
      responseAction.error(res, { message: t('file_cant_download') }, 400);
    }
  });
}

export async function getAllNangSuatLamViec(req) {
  // //Get all user đơn vị
  const query = queryHelper.extractQueryParam(req, ['full_name']);
  const criteria = query.criteria;

  if ((!req.query.include_children || req.query.include_children === 'false') && req.query.don_vi_id) {
    criteria.don_vi_id = req.query.don_vi_id;
  } else {
    criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);
  }
  const {created_at, ...criteriaUser} =criteria; 

  const allUser = await UserModel.find(criteriaUser)
    .populate({ path: 'don_vi_id', select: 'ten_don_vi' })
    .lean();
    
  criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code;
  criteria.don_vi_giao_phieu_id = criteria.don_vi_id;
  delete criteria.don_vi_id;
  delete criteria.full_name;

  const allPhieuGiaoViec = await PhieuGiaoViecService.bangTongHopPhieuGiaoViec(criteria);
  const mapPhieuGiaoViec = {};
  allPhieuGiaoViec.forEach(phieu => {
    mapPhieuGiaoViec[phieu._id] = phieu;
  });
  const groupByChiHuyId = groupBy(allPhieuGiaoViec, 'chi_huy_truc_tiep_id');
  const allPhieuGiaoViecIds = extractIds(allPhieuGiaoViec);
  const allNguoiCongTac = await NguoiCongTacService.getAll({
    phieu_giao_viec_id: { $in: allPhieuGiaoViecIds },
    is_deleted: false,
  });

  const nguoiCongTacByPhieu = allNguoiCongTac.filter(item => allPhieuGiaoViecIds.includes(item.phieu_giao_viec_id?.toString()));

  nguoiCongTacByPhieu.forEach(item => {
    if (groupByChiHuyId[item.user_id]) {
      groupByChiHuyId[item.user_id].push(mapPhieuGiaoViec[item.phieu_giao_viec_id]);
    } else {
      groupByChiHuyId[item.user_id] = [];
      groupByChiHuyId[item.user_id].push(mapPhieuGiaoViec[item.phieu_giao_viec_id]);
    }
  });
  let result = [];
  allUser.forEach(user => {
    let soLuongCongViec = 0, hoanThanhDungHan = 0, hoanThanhCham = 0, chuaHoanThanh = 0;
    let mapSoLuongTheoNhom = {
      KIEM_TRA: { so_luong: 0, chua_hoan_thanh: 0 },
      DO_THONG_SO: { so_luong: 0, chua_hoan_thanh: 0 },
      SUA_CHUA_BAO_DUONG: { so_luong: 0, chua_hoan_thanh: 0 },
      CONG_TAC_PHU_TRO: { so_luong: 0, chua_hoan_thanh: 0 },
    };
    if (groupByChiHuyId[user._id]) {
      soLuongCongViec = groupByChiHuyId[user._id].length;
      groupByChiHuyId[user._id].forEach(phieu => {
        if (!phieu.chua_hoan_thanh_cong_viec) {
          phieu.hoan_thanh_dung_han && hoanThanhDungHan++;
          !phieu.hoan_thanh_dung_han && hoanThanhCham++;
        } else {
          chuaHoanThanh++;
          mapSoLuongTheoNhom[NHOM_CONG_VIEC[phieu.loai_cong_viec]?.code].chua_hoan_thanh++;
        }
        mapSoLuongTheoNhom[NHOM_CONG_VIEC[phieu.loai_cong_viec]?.code].so_luong++;
      });
    }
    const nhanVienInfo = {
      don_vi_id: user.don_vi_id,
      bac_an_toan: user.bac_an_toan,
      ten_nhan_vien: user,
      kiem_tra: mapSoLuongTheoNhom.KIEM_TRA.so_luong,
      kiem_tra_chua_hoan_thanh: mapSoLuongTheoNhom.KIEM_TRA.chua_hoan_thanh,
      kiem_tra_da_hoan_thanh: mapSoLuongTheoNhom.KIEM_TRA.so_luong - mapSoLuongTheoNhom.KIEM_TRA.chua_hoan_thanh,
      sua_chua_bao_duong: mapSoLuongTheoNhom.SUA_CHUA_BAO_DUONG.so_luong,
      sua_chua_bao_duong_chua_hoan_thanh: mapSoLuongTheoNhom.SUA_CHUA_BAO_DUONG.chua_hoan_thanh,
      sua_chua_bao_duong_da_hoan_thanh: mapSoLuongTheoNhom.SUA_CHUA_BAO_DUONG.so_luong - mapSoLuongTheoNhom.SUA_CHUA_BAO_DUONG.chua_hoan_thanh,
      do_thong_so: mapSoLuongTheoNhom.DO_THONG_SO.so_luong,
      do_thong_so_chua_hoan_thanh: mapSoLuongTheoNhom.DO_THONG_SO.chua_hoan_thanh,
      do_thong_so_da_hoan_thanh: mapSoLuongTheoNhom.DO_THONG_SO.so_luong - mapSoLuongTheoNhom.DO_THONG_SO.chua_hoan_thanh,
      cong_tac_phu_tro: mapSoLuongTheoNhom.CONG_TAC_PHU_TRO.so_luong,
      cong_tac_phu_tro_chua_hoan_thanh: mapSoLuongTheoNhom.CONG_TAC_PHU_TRO.chua_hoan_thanh,
      cong_tac_phu_tro_da_hoan_thanh: mapSoLuongTheoNhom.CONG_TAC_PHU_TRO.so_luong - mapSoLuongTheoNhom.CONG_TAC_PHU_TRO.chua_hoan_thanh,
      so_luong_cong_viec: soLuongCongViec,
      hoan_thanh_dung_han: hoanThanhDungHan,
      hoan_thanh_cham: hoanThanhCham,
      chua_hoan_thanh: chuaHoanThanh,
      ti_le_hoan_thanh: soLuongCongViec === 0 ? -1 : Math.round((soLuongCongViec - chuaHoanThanh) / soLuongCongViec * 100),
    };
    result = [...result, nhanVienInfo];
  });
  result.sort((nv1, nv2) => nv2.ti_le_hoan_thanh - nv1.ti_le_hoan_thanh);
  return addIndexToListData(result);
}
