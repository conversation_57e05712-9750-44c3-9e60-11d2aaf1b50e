import * as responseAction from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from './reportNSLV.service';
import { generateDocument } from './reportNSLV.service';
import * as DonViService from '../../DonVi/donVi.service';
import { formatDateTime, formatToDateDetail } from '../../../common/formatUTCDateToLocalDate';
import User from '../../User/user.model';
import { extractObjectIds, extractIds } from '../../../utils/dataconverter';
import { reportTotalVisitByUser, reportVisitByUsers } from '../../UserTrackings/tracking.service';
import { groupBy, addIndexToListData, downlineData } from '../../../common/functionCommons';
import { getFilePath } from '../../../utils/fileUtils';
import { TEMPLATES_DIRS } from '../../../constant/constant';
import { generateDocument as newGenerateDocumet } from './../GenerateFile/generate.controller';


export async function getNangSuatLamViecByDonVi(req, res) {
  try {
    const data = await Service.getAllNangSuatLamViec(req);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function taiXuongNangSuatLamViec(req, res) {
  const { t } = req;
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  if (!criteria.don_vi_id) criteria.don_vi_id = req.user?.don_vi_id;
  const donVi = await DonViService.getAll({ _id: criteria.don_vi_id });
  let dataToDown = {};
  dataToDown.don_vi_label = req.query.include_children === 'true' ? downlineData(donVi[0].ten_don_vi.toUpperCase(), '(Bao gồm đơn vị trực thuộc)') : donVi[0].ten_don_vi.toUpperCase();
  dataToDown.tu_ngay = criteria?.created_at?.$gte ? formatDateTime(criteria.created_at.$gte) : '';
  dataToDown.den_ngay = criteria?.created_at?.$lte ? formatDateTime(criteria.created_at.$lte) : '';
  dataToDown.ngay = formatToDateDetail(new Date()).ngay;
  dataToDown.thang = formatToDateDetail(new Date()).thang;
  dataToDown.nam = formatToDateDetail(new Date()).nam;
  dataToDown.nhan_vien = await Service.getAllNangSuatLamViec(req);
  const fileName = 'nang_suat_lam_viec.xlsx';
  generateDocument(t, res, dataToDown, fileName);
}

export async function getTanSuatTruyCapByDonVi(req, res) {
  try {
    const arrayData = await reportVisitByUsers(req);
    responseAction.success(res, arrayData);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function downloadTanSuatTruyCap(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    if (!criteria.don_vi_id) criteria.don_vi_id = req.user?.don_vi_id;
    const donVi = await DonViService.getAll({ _id: criteria.don_vi_id });
    let dataToDown = {};
    dataToDown.don_vi_label = req.query.include_children === 'true' ? downlineData(donVi[0].ten_don_vi.toUpperCase(), '(Bao gồm đơn vị trực thuộc)') : donVi[0].ten_don_vi.toUpperCase();
    dataToDown.tu_ngay = criteria?.created_at?.$gte ? formatDateTime(criteria.created_at.$gte) : '';
    dataToDown.den_ngay = criteria?.created_at?.$lte ? formatDateTime(criteria.created_at.$lte) : '';
    dataToDown.ngay = formatToDateDetail(new Date()).ngay;
    dataToDown.thang = formatToDateDetail(new Date()).thang;
    dataToDown.nam = formatToDateDetail(new Date()).nam;
    const arrayData = await reportVisitByUsers(req);
    dataToDown.nhan_vien = addIndexToListData(arrayData);
    const templateFilePath = getFilePath('tan_suat_truy_cap.xlsx', TEMPLATES_DIRS.REPORT);
    const outputFileName = 'Thống kê tần suất truy cập phần mềm.xlsx';
    await newGenerateDocumet(res, dataToDown, templateFilePath, outputFileName);
  } catch (err) {
    responseAction.error(res, err);
  }
}
