import express from 'express';
import passport from 'passport';
import * as Controller from './reportNSLV.controller';
import { loggerMiddleware } from '../../../logs/middleware';

export const reportNSLVRouter = express.Router();
reportNSLVRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
reportNSLVRouter
  .route('/')
  .get(Controller.getNangSuatLamViecByDonVi);

reportNSLVRouter
  .route('/tansuattruycap')
  .get(Controller.getTanSuatTruyCapByDonVi);

reportNSLVRouter
  .route('/taixuong')
  .get(Controller.taiXuongNangSuatLamViec);
  
  reportNSLVRouter
  .route('/tansuattruycap/taixuong')
  .get(Controller.downloadTanSuatTruyCap);

