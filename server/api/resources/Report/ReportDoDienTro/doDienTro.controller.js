import * as Service from './doDienTro.service';
import * as DuongDayService from '../../TongKe/DuongDay/duongDay.service';
import Model from './doDienTro.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import * as responseAction from '../../../helpers/responseHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import exelUtils from '../../../utils/exelUtils';
import queryHelper from '../../../helpers/queryHelper';
import DuongDayModel from '../../TongKe/DuongDay/duongDay.model';
import { generateDocument } from '../GenerateFile/generate.controller';
import { TIA_TIEP_DIA } from '../../QuanLyVanHanh/DoDienTro/doDienTro.constants';
import CommonError from '../../../error/CommonError';
import { extractIds } from '../../../utils/dataconverter';
import { getFilePath } from '../../../utils/fileUtils';
import { TEMPLATES_DIRS } from '../../../constant/constant';

const searchLike = [];
const populateOpts = ['vi_tri_id', 'dia_hinh_id', 'nguoi_tao_id', 'nguoi_chinh_sua_id'];
const uniqueOpts = [];
const sortOpts = { vi_tri_id: 1, ngay_do: 1 };

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);

const SHEET_NAMES = {
  REPORT_DO_DIEN_TRO: 'Điện trở tiếp địa',
};

export async function buildQuery(req) {
  const query = queryHelper.extractQueryParam(req);
  let { criteria, options } = query;

  if (criteria.tu_ngay) {
    if (criteria.den_ngay) {
      criteria.ngay_do = {
        $gte: criteria.tu_ngay,
        $lte: criteria.den_ngay,
      };
      criteria.tu_ngay = null;
      criteria.den_ngay = null;
    } else {
      criteria.ngay_do = { $gte: criteria.tu_ngay };
      criteria.tu_ngay = null;
    }
  } else {
    if (criteria.den_ngay) {
      criteria.ngay_do = { $lte: criteria.den_ngay };
      criteria.den_ngay = null;
    }
  }

  delete criteria.den_ngay;
  delete criteria.tu_ngay;

  return { criteria, options };
}

export async function getAll(req, res) {
  try {
    const { criteria, options } = await buildQuery(req);
    if (criteria.duong_day_id) {
      await Service.removeDuplicateData(req, criteria.duong_day_id);
      const allViTriByDuongDay = await DuongDayService.getViTriOnlyOnceDuongDay(req, criteria.duong_day_id);
      criteria.vi_tri_id = criteria.vi_tri_id || extractIds(allViTriByDuongDay);
      delete criteria.duong_day_id;
      delete criteria.don_vi_id;
    }
    if (criteria.is_latest !== 'KET_QUA_MOI_NHAT') delete criteria.is_latest;
    else {
      criteria.is_latest = true;
    }
    options.populate = populateOpts;
    if (!options.sort) options.sort = sortOpts;
    const data = await Model.paginate(criteria, options);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOne({ _id: id });
    const dataRes = await Model.deleteOne({ _id: id });
    if (data.is_latest) {
      const curentLatest = await Model.find({ vi_tri_id: data.vi_tri_id }).sort({ ngay_do: -1 }).limit(1);
      if (curentLatest[0]?._id) {
        await Model.updateOne({ _id: curentLatest[0]._id }, { is_latest: true });
      }
    }

    if (!dataRes) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, dataRes);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function checkImport(req, res) {

  try {
    let filePath = req.files.file.path;
    const sheetData = await exelUtils.transformFile(filePath);

    const resultArray = await checkImportByData(req, sheetData);
    responseAction.success(res, resultArray);
  } catch (e) {
    console.log(e);
    responseAction.error(res, e);
  }
}

async function checkImportByData(req, sheetData) {

  function getSheetByName(sheetData, name) {
    return sheetData.find((sheet) => sheet.name.toLowerCase().includes(name.toLowerCase()));
  }

  let resultArray = [];
  resultArray = [...resultArray, await Service.checkImport(req, getSheetByName(sheetData, SHEET_NAMES.REPORT_DO_DIEN_TRO))];
  return resultArray;
}

export async function importOne(req, res) {

  try {
    const sheetData = req.body;
    const result = await Service.importData(req, sheetData);
    responseAction.success(res, result);
  } catch (e) {
    console.log(e);
    responseAction.error(res, e);
  }
}

export async function downloadBaoCaoDienTroTiepDia(req, res) {
  const { criteria } = await buildQuery(req);
  const duongDay = await DuongDayModel.findOne({ _id: criteria.duong_day_id });
  if (!criteria.vi_tri_id) {
    const allViTriByDuongDay = await DuongDayService.getViTriOnlyOnceDuongDay(req, criteria.duong_day_id);
    criteria.vi_tri_id = extractIds(allViTriByDuongDay);
  }
  delete criteria.duong_day_id;
  delete criteria.don_vi_id;
  const ketQuaDoDienTro = await Service.bangTongHopBaoCaoDienTroTiepDia(criteria);

  let soLuongTia = 1;
  ketQuaDoDienTro.forEach(ketqua => {
    TIA_TIEP_DIA.forEach((tiaTiepDia, index) => {
      if (ketqua[tiaTiepDia.code]) {
        soLuongTia = soLuongTia > index + 1 ? soLuongTia : index + 1;
      }
    });
  });
  let data = {};
  data.ten_duong_day = duongDay.ten_duong_day;
  data.do_dien_tro = ketQuaDoDienTro;

  const templateFilePath = getFilePath(`bieu_mau_do_dien_tro_${soLuongTia}.xlsx`, TEMPLATES_DIRS.BIEU_MAU);
  const outputFileName = 'Kết qua đo điện trở.xlsx';
  generateDocument(res, data, templateFilePath, outputFileName);
}
