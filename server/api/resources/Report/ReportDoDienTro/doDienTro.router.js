import express from 'express';
import passport from 'passport';
import * as Controller from './doDienTro.controller';
import { checkTempFolder, multipartMiddleware } from '../../../utils/fileUtils';
import { loggerMiddleware } from '../../../logs/middleware';

export const reportDoDienTroRouter = express.Router();

reportDoDienTroRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

reportDoDienTroRouter
  .route('/download')
  .get(Controller.downloadBaoCaoDienTroTiepDia);

reportDoDienTroRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), Controller.getAll)
  .post(passport.authenticate('jwt', { session: false }), Controller.create);

reportDoDienTroRouter
  .route('/checkimport')
  .post(checkTempFolder, multipartMiddleware, Controller.checkImport);

reportDoDienTroRouter
  .route('/importone')
  .post(checkTempFolder, multipartMiddleware, Controller.importOne);

reportDoDienTroRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), Controller.findOne)
  .delete(passport.authenticate('jwt', { session: false }), Controller.remove)
  .put(passport.authenticate('jwt', { session: false }), Controller.update);
