import * as Validator<PERSON>elper from '../../../helpers/validatorHelper';
import REPORT_DO_DIEN_TRO from './doDienTro.model';
import * as DuongDayService from '../../TongKe/DuongDay/duongDay.service';
import * as CotDienService from '../../TongKe/CotDien/cotDien.service';
import * as TiepDatService from '../../TongKe/TiepDat/tiepDat.service';
import * as DiaHinhService from '../../DanhMuc/DiaHinh/diaHinh.service';
import { trim } from '../../../common/functionCommons';
import { checkNumber, convertDate } from '../../../helpers/checkDataHelper';
import { TIA_TIEP_DIA } from '../../QuanLyVanHanh/DoDienTro/doDienTro.constants';
import { formatTimeDate } from '../../../common/formatUTCDateToLocalDate';
import * as DonViService from '../../DonVi/donVi.service';
import * as UserService from '../../User/user.service';
import * as ketQuaDoDienTro from '../../QuanLyVanHanh/DoDienTro/doDienTro.service';
import { LOAI_CONG_VIEC } from '../../DanhMuc/LoaiCongViec';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import PHIEU_GIAO_VIEC from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.model';
import { extractIds, extractKeys } from '../../../utils/dataconverter';
import * as ViTriCongViecService from '../../QuanLyVanHanh/ViTriCongViec/viTriCongViec.service';
import DongBoModel from '../DongBoDuLieuDo/dongBo.model';
import { KET_LUAN } from '../../../constant/constant';

export function getAll(query, projection = {}) {
  return REPORT_DO_DIEN_TRO.find(query, projection).lean();
}

export async function updateAll(dataUpdate) {
  for (const row of dataUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await REPORT_DO_DIEN_TRO.findByIdAndUpdate(value._id, value);
  }
}

export function count(query) {
  return REPORT_DO_DIEN_TRO.count(query);
}

const Joi = require('joi');

const objSchema = Joi.object({
  vi_tri_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Vị trí')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}


const Headers = {
  SO_PHIEU: 'Số phiếu',
  TEN_DUONG_DAY: 'Đường dây',
  TEN_VI_TRI: 'Vị trí',
  CHIEU_CAO_COT: 'Chiều cao cột (m)',
  LOAI_TIEP_DIA: 'Loại tiếp địa',

  TIA_SO_MOT: 'Tia số 1',
  TIA_SO_HAI: 'Tia số 2',
  TIA_SO_BA: 'Tia số 3',
  TIA_SO_BON: 'Tia số 4',
  TIA_SO_NAM: 'Tia số 5',
  TIA_SO_SAU: 'Tia số 6',
  TIA_SO_BAY: 'Tia số 7',
  TIA_SO_TAM: 'Tia số 8',
  TIA_SO_CHIN: 'Tia số 9',
  TIA_SO_MUOI: 'Tia số 10',
  TIA_SO_MUOI_MOT: 'Tia số 11',
  TIA_SO_MUOI_HAI: 'Tia số 12',
  TIA_SO_MUOI_BA: 'Tia số 13',
  TIA_SO_MUOI_BON: 'Tia số 14',
  TIA_SO_MUOI_NAM: 'Tia số 15',

  DIEN_TRO_HE_THONG: 'Trị số điện trở của hệ thống Rnđ (Ώ)',
  TRO_SUAT_CUA_DAT: 'Trị số điện trở suất của đất (Ώ.m)',
  DIA_HINH: 'Đặc tính vùng đất lắp đặt tiếp địa',
  NGAY_DO: 'Ngày đo',
  HUONG_DO: 'Hướng đo',
  DIEN_TRO_THEO_QUY_PHAM: 'Giá trị điện trở đạt theo quy phạm (Ω)',
  KET_LUAN: 'Kết luận',
  NGUOI_DO: 'Người đo',
  GHI_CHU: 'Ghi chú',

};

export async function mapData(req, donViId, duongDayId) {
  const mapViTri = {}, mapDiaHinh = {}, mapKetLuan = {}, mapSoPhieu = {};
  const mapNguoiDo = {}, mapNgayDo = {}, mapCurrentLast = {};

  const donViQuery = await DonViService.getDonViInScope(donViId);

  const allUser = await UserService.getAll({ don_vi_id: donViQuery, is_deleted: false }).select('full_name');
  const allViTri = await DuongDayService.getViTriByDuongDay(req, duongDayId);
  // const allSoPhieu = await PHIEU_GIAO_VIEC.distinct('so_phieu', {
  //   don_vi_giao_phieu_id: donViId,
  //   loai_cong_viec: LOAI_CONG_VIEC.DO_DIEN_TRO_TIEP_DIA.code,
  //   trang_thai_cong_viec: TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code,
  // });

  const resultSortByTime = await REPORT_DO_DIEN_TRO.find({ vi_tri_id: extractIds(allViTri.vi_tris), is_latest: true })
    .select('ngay_do vi_tri_id')
    .sort({ ngay_do: 1 })
    .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });

  resultSortByTime.forEach(item => {
    mapNgayDo[item.vi_tri_id.ten_vi_tri] = item.ngay_do;
    mapCurrentLast[item.vi_tri_id.ten_vi_tri] = item;
  });

  allViTri.vi_tris.forEach(viTri => {
    mapViTri[viTri.ten_vi_tri] = viTri._id;
  });

  const allDiaHinh = await DiaHinhService.getAll({ is_deleted: false });
  allDiaHinh.forEach(diaHinh => {
    mapDiaHinh[diaHinh.ten_dia_hinh] = diaHinh._id;
  });

  Object.values(KET_LUAN).forEach(ketLuan => {
    mapKetLuan[ketLuan.label] = ketLuan.value;
  });

  allUser.forEach(user => {
    mapNguoiDo[user.full_name.toLowerCase()?.trim()] = user._id;
  });

  // allSoPhieu.forEach(sophieu => {
  //   mapSoPhieu[sophieu] = sophieu;
  // });

  return [mapViTri, mapDiaHinh, mapKetLuan, mapSoPhieu, mapNguoiDo, mapNgayDo, mapCurrentLast];
}

export async function importData(req, sheetData) {
  const { rows } = sheetData;
  const [mapViTri, mapDiaHinh, mapKetLuan, , mapNguoiDo, mapNgayDo, mapCurrentLast] = await mapData(req, sheetData.don_vi_id, sheetData.duong_day_id);

  let currentLastest = [];
  rows.forEach(row => {
    if (!mapNgayDo[row[Headers.TEN_VI_TRI]?.trim()]) {
      mapNgayDo[row[Headers.TEN_VI_TRI]?.trim()] = new Date(1, 1, 1);
    }
    if (mapNgayDo[row[Headers.TEN_VI_TRI]?.trim()] && mapNgayDo[row[Headers.TEN_VI_TRI]?.trim()] < convertDate(row[Headers.NGAY_DO])) {
      mapCurrentLast[row[Headers.TEN_VI_TRI]?.trim()] && currentLastest.push(mapCurrentLast[row[Headers.TEN_VI_TRI]?.trim()]);
      delete mapCurrentLast[row[Headers.TEN_VI_TRI]?.trim()];
      mapNgayDo[row[Headers.TEN_VI_TRI]?.trim()] = convertDate(row[Headers.NGAY_DO]);
    }
  });

  function convertToDB(row) {
    let isLatest = false;
    if (mapNgayDo[row[Headers.TEN_VI_TRI]?.trim()] && mapNgayDo[row[Headers.TEN_VI_TRI]?.trim()] <= convertDate(row[Headers.NGAY_DO])) {
      isLatest = true;
      delete mapNgayDo[row[Headers.TEN_VI_TRI]?.trim()];
    }
    return {
      vi_tri_id: mapViTri[row[Headers.TEN_VI_TRI]?.trim()],
      chieu_cao_cot: row[Headers.CHIEU_CAO_COT],
      loai_tiep_dia: trim(row[Headers.LOAI_TIEP_DIA]),

      tia_so_mot: row[Headers.TIA_SO_MOT],
      tia_so_hai: row[Headers.TIA_SO_HAI],
      tia_so_ba: row[Headers.TIA_SO_BA],
      tia_so_bon: row[Headers.TIA_SO_BON],
      tia_so_nam: row[Headers.TIA_SO_NAM],
      tia_so_sau: row[Headers.TIA_SO_SAU],
      tia_so_bay: row[Headers.TIA_SO_BAY],
      tia_so_tam: row[Headers.TIA_SO_TAM],
      tia_so_chin: row[Headers.TIA_SO_CHIN],
      tia_so_muoi: row[Headers.TIA_SO_MUOI],
      tia_so_muoi_mot: row[Headers.TIA_SO_MUOI_MOT],
      tia_so_muoi_hai: row[Headers.TIA_SO_MUOI_HAI],
      tia_so_muoi_ba: row[Headers.TIA_SO_MUOI_BA],
      tia_so_muoi_bon: row[Headers.TIA_SO_MUOI_BON],
      tia_so_muoi_nam: row[Headers.TIA_SO_MUOI_NAM],

      dien_tro_he_thong: row[Headers.DIEN_TRO_HE_THONG],
      tro_suat_cua_dat: row[Headers.TRO_SUAT_CUA_DAT],
      dia_hinh_id: mapDiaHinh[row[Headers.DIA_HINH]?.trim()],
      ngay_do: convertDate(row[Headers.NGAY_DO]),
      huong_do: trim(row[Headers.HUONG_DO]),
      dien_tro_theo_quy_pham: row[Headers.DIEN_TRO_THEO_QUY_PHAM],
      ket_luan: mapKetLuan[row[Headers.KET_LUAN]?.trim()],
      nguoi_tao_id: mapNguoiDo[row[Headers.NGUOI_DO]?.toLowerCase().trim()],
      ghi_chu: trim(row[Headers.GHI_CHU]),
      so_phieu: trim(row[Headers.SO_PHIEU]),
      thoi_gian_cap_nhat: new Date(),
      nguoi_chinh_sua_id: req.user?._id,
      is_latest: isLatest,
      is_import_by_file: true,
      is_deleted: false,
    };
  }

  const dataToDB = rows.map(convertToDB).filter(element => element.vi_tri_id);

  REPORT_DO_DIEN_TRO.bulkWrite(
    currentLastest.map((current) =>
      ({
        updateOne: {
          filter: { _id: current._id },
          update: { is_latest: false },
          upsert: true,
        },
      }),
    ),
  );
  return REPORT_DO_DIEN_TRO.create(dataToDB);
}

export async function checkImport(req, sheetData) {
  if (!sheetData) return null;
  const { t } = req;
  const { rows } = sheetData;

  const duongDayId = req.body.duong_day_id;
  const donViId = req.body.don_vi_id;
  const [mapViTri, mapDiaHinh, mapKetLuan, mapSoPhieu, mapNguoiDo] = await mapData(req, donViId, duongDayId);

  function createError(col, error) {
    return { col, error };
  }

  let countSoLuongTia = 0;

  function getSoLuongTia(rowObject, countSoLuongTia) {
    TIA_TIEP_DIA.forEach((tiaTiepDia, index) => {
      if (rowObject[tiaTiepDia.label]) {
        countSoLuongTia = countSoLuongTia > index + 1 ? countSoLuongTia : index + 1;
      }
    });
    return countSoLuongTia;
  }

  function validateRow(t, row) {
    let errors = [];
    if (!row[Headers.TEN_VI_TRI]?.trim()) {
      errors = [...errors, createError(Headers.TEN_VI_TRI, t('missing_location_info'))];
    } else {
      if (!mapViTri[row[Headers.TEN_VI_TRI]?.trim()]) {
        errors = [...errors, createError(Headers.TEN_VI_TRI, t('location_not_belong_line'))];
      }
    }

    // if (row[Headers.SO_PHIEU]?.toString()?.trim() && !mapSoPhieu[row[Headers.SO_PHIEU]?.trim()]) {
    //   errors = [...errors, createError(Headers.SO_PHIEU, t('task_number_incorrect_or_dont_create'))];
    // }

    if (row[Headers.DIA_HINH]?.trim() && !mapDiaHinh[row[Headers.DIA_HINH]?.trim()]) {
      errors = [...errors, createError(Headers.DIA_HINH, t('area_incorrect_or_dont_create'))];
    }

    if (row[Headers.KET_LUAN]?.trim() && !mapKetLuan[row[Headers.KET_LUAN]?.trim()]) {
      errors = [...errors, createError(Headers.KET_LUAN, t('conclude_incorrect_or_dont_create'))];
    }

    if (row[Headers.NGUOI_DO]?.trim() && !mapNguoiDo[row[Headers.NGUOI_DO]?.toLowerCase().trim()]) {
      errors = [...errors, createError(Headers.NGUOI_DO, t('measurer_not_belong_unit_line_management'))];
    }

    if (!checkNumber(row[Headers.DIEN_TRO_THEO_QUY_PHAM])) {
      errors = [...errors, createError(Headers.DIEN_TRO_THEO_QUY_PHAM, t('incorrect_or_not_available'))];
    }
    if (!checkNumber(row[Headers.DIEN_TRO_HE_THONG])) {
      errors = [...errors, createError(Headers.DIEN_TRO_HE_THONG, t('incorrect_or_not_available'))];
    }

    if (errors.length) {
      row['Lỗi'] = errors;
    } else {
      row['Lỗi'] = null;
    }
    return row;
  }

  const arraySoLuongTia = rows.map(row => getSoLuongTia(row, countSoLuongTia));
  sheetData.rows = rows.map(row => validateRow(t, row));
  sheetData.so_luong_tia = Math.max(...arraySoLuongTia);
  return sheetData;
}

export async function bangTongHopBaoCaoDienTroTiepDia(criteria) {

  const allDoDienTro = await REPORT_DO_DIEN_TRO.find(criteria)
    .populate([
      { path: 'vi_tri_id ', select: 'ten_vi_tri' },
      { path: 'dia_hinh_id ', select: 'ten_dia_hinh' },
      { path: 'nguoi_tao_id', select: 'full_name' },
    ])
    .sort({ is_latest: -1, ngay_do: -1 })
    .lean();

  const viTriIds = await REPORT_DO_DIEN_TRO.distinct('vi_tri_id', criteria);
  const cotDien = await CotDienService.getAll(
    { vi_tri_id: viTriIds, is_deleted: false },
    { vi_tri_id: 1, chieu_cao: 1 });
  const tiepDat = await TiepDatService.getAll(
    { vi_tri_id: viTriIds, is_deleted: false },
    { vi_tri_id: 1, ten_tiep_dat: 1 });

  const mapCotDien = {}, mapTiepDat = {};
  cotDien.forEach(cd => {
    mapCotDien[cd.vi_tri_id] = cd;
  });

  tiepDat.forEach(td => {
    mapTiepDat[td.vi_tri_id] = td;
  });

  function convertDataToRows(row, index) {
    return {
      stt: index + 1,
      ten_vi_tri: row.vi_tri_id?.ten_vi_tri,
      chieu_cao_cot: row.chieu_cao_cot || mapCotDien[row.vi_tri_id._id]?.chieu_cao,
      loai_tiep_dia: row.loai_tiep_dia || mapTiepDat[row.vi_tri_id._id]?.ten_tiep_dat,

      tia_so_mot: row.tia_so_mot,
      tia_so_hai: row.tia_so_hai,
      tia_so_ba: row.tia_so_ba,
      tia_so_bon: row.tia_so_bon,
      tia_so_nam: row.tia_so_nam,
      tia_so_sau: row.tia_so_sau,
      tia_so_bay: row.tia_so_bay,
      tia_so_tam: row.tia_so_tam,
      tia_so_chin: row.tia_so_chin,
      tia_so_muoi: row.tia_so_muoi,
      tia_so_muoi_mot: row.tia_so_muoi_mot,
      tia_so_muoi_hai: row.tia_so_muoi_hai,
      tia_so_muoi_ba: row.tia_so_muoi_ba,
      tia_so_muoi_bon: row.tia_so_muoi_bon,
      tia_so_muoi_nam: row.tia_so_muoi_nam,

      dac_diem_dia_hinh: row.vi_tri_id?.dac_diem_dia_hinh,
      nguoi_do: row.nguoi_tao_id?.full_name,
      dien_tro_he_thong: row.dien_tro_he_thong,
      dien_tro_dat: row.dien_tro_dat,
      tro_suat_cua_dat: row.tro_suat_cua_dat,
      ngay_do: formatTimeDate(row.ngay_do),
      ten_dia_hinh: row.dia_hinh_id?.ten_dia_hinh,
      ket_luan: KET_LUAN?.[row.ket_luan]?.label,
      ghi_chu: row.ghi_chu,
      huong_do: row.huong_do,
      dien_tro_theo_quy_pham: row.dien_tro_theo_quy_pham,

    };
  }

  return allDoDienTro.map(convertDataToRows);
}

export async function resultToReport(req, phieuGiaoViecData) {
  const allDoDienTro = await ketQuaDoDienTro.getAll({
    phieu_giao_viec_id: phieuGiaoViecData._id,
    is_deleted: false,
  });

  const viTriCongViec = await ViTriCongViecService.getAll({ phieu_giao_viec_id: phieuGiaoViecData._id });

  const resultSortByTime = await REPORT_DO_DIEN_TRO.find({
    vi_tri_id: extractKeys(viTriCongViec, 'vi_tri_id').filter(item => !!item),
    is_latest: true,
  })
    .select('ngay_do vi_tri_id')
    .sort({ ngay_do: 1 })
    .populate({ path: 'vi_tri_id', select: 'ten_khoang_cot' });

  const mapNgayDo = {}, mapCurrentLast = {};
  resultSortByTime.forEach(item => {
    mapNgayDo[item.vi_tri_id?._id] = item.ngay_do;
    mapCurrentLast[item.vi_tri_id?._id] = item;
  });
  let currentLastest = [];

  function convertToDB(row) {
    let isLatest = false;
    if (mapNgayDo[row.vi_tri_id] && mapNgayDo[row.vi_tri_id] <= row.ngay_do) {
      mapCurrentLast[row.vi_tri_id] && currentLastest.push(mapCurrentLast[row.vi_tri_id]);
      delete mapCurrentLast[row.vi_tri_id];
      isLatest = true;
    }
    if (!mapNgayDo[row.vi_tri_id]) {
      isLatest = true;
    }

    return {
      vi_tri_id: row.vi_tri_id,
      chieu_cao_cot: row.chieu_cao_cot,
      loai_tiep_dia: row.loai_tiep_dia,

      tia_so_mot: row.tri_so_tia_tiep_dia?.tia_so_mot,
      tia_so_hai: row.tri_so_tia_tiep_dia?.tia_so_hai,
      tia_so_ba: row.tri_so_tia_tiep_dia?.tia_so_ba,
      tia_so_bon: row.tri_so_tia_tiep_dia?.tia_so_bon,
      tia_so_nam: row.tri_so_tia_tiep_dia?.tia_so_nam,
      tia_so_sau: row.tri_so_tia_tiep_dia?.tia_so_sau,
      tia_so_bay: row.tri_so_tia_tiep_dia?.tia_so_bay,
      tia_so_tam: row.tri_so_tia_tiep_dia?.tia_so_tam,
      tia_so_chin: row.tri_so_tia_tiep_dia?.tia_so_chin,
      tia_so_muoi: row.tri_so_tia_tiep_dia?.tia_so_muoi,
      tia_so_muoi_mot: row.tri_so_tia_tiep_dia?.tia_so_muoi_mot,
      tia_so_muoi_hai: row.tri_so_tia_tiep_dia?.tia_so_muoi_hai,
      tia_so_muoi_ba: row.tri_so_tia_tiep_dia?.tia_so_muoi_ba,
      tia_so_muoi_bon: row.tri_so_tia_tiep_dia?.tia_so_muoi_bon,
      tia_so_muoi_nam: row.tri_so_tia_tiep_dia?.tia_so_muoi_nam,

      dien_tro_he_thong: row.dien_tro_he_thong,
      tro_suat_cua_dat: row.tro_suat_cua_dat,
      dia_hinh_id: row.dia_hinh_id,
      ngay_do: row.ngay_do,
      huong_do: row.huong_do,
      dien_tro_theo_quy_pham: row.dien_tro_theo_quy_pham,
      ket_luan: row.ket_luan,
      nguoi_tao_id: row.nguoi_tao_id,
      ghi_chu: row.ghi_chu,

      so_phieu: phieuGiaoViecData.so_phieu,
      phieu_giao_viec_id: phieuGiaoViecData._id,
      is_latest: row.ngay_do ? isLatest : false,
      is_deleted: false,
    };
  }

  const dataToDB = allDoDienTro.map(convertToDB).filter(element => element.vi_tri_id);

  REPORT_DO_DIEN_TRO.bulkWrite(
    currentLastest.map((ketQuaDo) =>
      ({
        updateOne: {
          filter: { _id: ketQuaDo._id },
          update: { is_latest: false },
          upsert: true,
        },
      }),
    ),
  );

  await REPORT_DO_DIEN_TRO.create(dataToDB);
}

export async function removeDuplicateData(req, duongDayId) {
  const sycnReport = await DongBoModel.findOne({
    duong_day_id: duongDayId,
    loai_phieu_do: LOAI_CONG_VIEC.DO_DIEN_TRO_TIEP_DIA.code,
  });
  if (!sycnReport?.is_remove_duplicate) {
    await updateDuplicateData(req, duongDayId);
    await DongBoModel.updateOne({
      duong_day_id: duongDayId,
      loai_phieu_do: LOAI_CONG_VIEC.DO_DIEN_TRO_TIEP_DIA.code,
    }, { is_remove_duplicate: true });
  }
}

export async function updateDuplicateData(req, duongDayId) {
  const allViTriByDuongDay = await DuongDayService.getViTriOnlyOnceDuongDay(req, duongDayId);
  const allKetQuaDo = await getAll({ vi_tri_id: extractIds(allViTriByDuongDay) });

  const mapDoDienTro = {};
  let dataDuplicate = [];
  for (let ketQuaDo of allKetQuaDo) {
    if (mapDoDienTro[ketQuaDo.so_phieu + ketQuaDo.vi_tri_id]) {
      dataDuplicate.push(ketQuaDo);
    } else {
      mapDoDienTro[ketQuaDo.so_phieu + ketQuaDo.vi_tri_id] = ketQuaDo;
    }
  }

  REPORT_DO_DIEN_TRO.bulkWrite(
    dataDuplicate.map((ketQuaDo) =>
      ({
        updateOne: {
          filter: { _id: ketQuaDo._id },
          update: { is_deleted: true },
          upsert: true,
        },
      }),
    ),
  );
}
