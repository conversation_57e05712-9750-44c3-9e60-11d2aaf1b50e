import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DIA_HINH, PHIEU_GIAO_VIEC, REPORT_DO_DIEN_TRO, USER, VI_TRI } from '../../../constant/dbCollections';
import { KET_LUAN } from '../../../constant/constant';

const schema = new Schema({
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI, required: true },
  chieu_cao_cot: { type: String },
  loai_tiep_dia: { type: String },
  tri_so_tia_tiep_dia: { type: Object },

  tia_so_mot: { type: String },
  tia_so_hai: { type: String },
  tia_so_ba: { type: String },
  tia_so_bon: { type: String },
  tia_so_nam: { type: String },
  tia_so_sau: { type: String },
  tia_so_bay: { type: String },
  tia_so_tam: { type: String },
  tia_so_chin: { type: String },
  tia_so_muoi: { type: String },
  tia_so_muoi_mot: { type: String },
  tia_so_muoi_hai: { type: String },
  tia_so_muoi_ba: { type: String },
  tia_so_muoi_bon: { type: String },
  tia_so_muoi_nam: { type: String },

  dien_tro_he_thong: { type: Number },
  dien_tro_dat: { type: Number },
  tro_suat_cua_dat: { type: Number },
  dia_hinh_id: { type: Schema.Types.ObjectId, ref: DIA_HINH },
  dien_tro_theo_quy_pham: { type: Number },
  ngay_do: { type: Date },
  huong_do: { type: String },
  dien_tro_dat_quy_pham: { type: String },
  ket_luan: { type: String, enum: Object.keys(KET_LUAN) },
  nguoi_tao_id: { type: Schema.Types.ObjectId, ref: USER },

  thoi_gian_cap_nhat: { type: Date, default: Date.now },
  nguoi_chinh_sua_id: { type: Schema.Types.ObjectId, ref: USER },
  ghi_chu: { type: String },

  so_phieu: String,
  phieu_giao_viec_id:  { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  is_latest: { type: Boolean, default: false },
  is_import_by_file: { type: Boolean, default: false },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(REPORT_DO_DIEN_TRO, schema, REPORT_DO_DIEN_TRO);
