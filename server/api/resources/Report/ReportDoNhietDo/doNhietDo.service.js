import * as ValidatorHelper from '../../../helpers/validatorHelper';
import REPORT_DO_NHIET_DO from './doNhietDo.model';
import { convertDate } from '../../../common/functionCommons';
import { HUONG_DO, KET_LUAN } from '../../../constant/constant';
import * as DuongDayService from '../../TongKe/DuongDay/duongDay.service';
import * as DonViService from '../../DonVi/donVi.service';
import * as UserService from '../../User/user.service';
import * as ketQuaDoNhietDo from '../../KetQuaDoNhietDo/ketQuaDoNhietDo.service';
import * as chiTietDoNhietDo from '../../KetQuaDoNhietDo/ChiTietDoNhietDo/chiTietDoNhietDo.service';
import * as ViTriCongViecService from '../../QuanLyVanHanh/ViTriCongViec/viTriCongViec.service';
import { extractIds, extractKeys } from '../../../utils/dataconverter';
import { formatDateTime } from '../../../common/formatUTCDateToLocalDate';
import { LOAI_CONG_VIEC } from '../../DanhMuc/LoaiCongViec';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import PHIEU_GIAO_VIEC from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.model';
import { checkNumber } from '../../../helpers/checkDataHelper';
import NGUOI_CONG_TAC from '../../QuanLyVanHanh/NguoiCongTac/nguoiCongTac.model';


const Headers = {
  SO_PHIEU: 'Số phiếu',
  TEN_KHOANG_COT: 'Khoảng cột',
  TEN_VI_TRI: 'Vị trí',
  MACH: 'Mạch',
  HUONG_DO: 'Hướng đo',
  DAY_DAN_PHAN_PHA: 'Dây dẫn phân pha',
  PHA_A: 'Pha A',
  PHA_B: 'Pha B',
  PHA_C: 'Pha C',
  DONG_TAI: 'Dòng tải tại thời điểm đo (A)',
  DANH_GIA: 'Đánh giá kết quả',
  NHIET_DO_MOI_TRUONG: 'Nhiệt độ môi trường khi đo (ºC)',
  NGUOI_DO: 'Người đo',
  THOI_GIAN_DO: 'Ngày đo',
  GIO_DO: 'Giờ đo',
  GHI_CHU: 'Ghi chú',

};

export async function mapData(req, donViId, duongDayId) {

  const mapKhoangCot = {}, mapViTri = {}, mapNguoiDo = {}, mapDanhGia = {}, mapHuongDo = {}, mapNgayDo = {},
    mapCurrentLast = {}, mapSoPhieu = {};

  const donViQuery = await DonViService.getDonViInScope(donViId);

  const allUser = await UserService.getAll({ don_vi_id: donViQuery, is_deleted: false }).select('full_name');
  const allViTri = await DuongDayService.getViTriByDuongDay(req, duongDayId);
  const allKhoangCot = allViTri.vi_tris?.flatMap(vt => vt.khoang_cot_id ? [...vt.khoang_cot_id] : []);

  const allSoPhieu = await PHIEU_GIAO_VIEC.distinct('so_phieu', {
    don_vi_giao_phieu_id: donViId,
    loai_cong_viec: LOAI_CONG_VIEC.DO_NHIET_DO_TIEP_XUC.code,
    trang_thai_cong_viec: TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code,
  });

  const resultSortByTime = await REPORT_DO_NHIET_DO.find({
    $or: [{ vi_tri_id: extractIds(allViTri.vi_tris) }, { khoang_cot_id: extractIds(allKhoangCot) }],
    is_latest: true,
  }).select('thoi_gian_do vi_tri_id khoang_cot_id mach huong_do day_dan_phan_pha')
    .sort({ thoi_gian_do: 1 })
    .populate([{ path: 'vi_tri_id', select: 'ten_vi_tri' }, { path: 'khoang_cot_id', select: 'ten_khoang_cot' }]);

  console.log(1);
  resultSortByTime.forEach(item => {
    if (!!item.vi_tri_id) {
      mapNgayDo[item.vi_tri_id.ten_vi_tri + item.mach?.toString() + item.huong_do + item.day_dan_phan_pha] = item.thoi_gian_do;
      mapCurrentLast[item.vi_tri_id.ten_vi_tri + item.mach?.toString() + item.huong_do + item.day_dan_phan_pha] = item;
    } else if (!!item.khoang_cot_id) {
      mapNgayDo[item.khoang_cot_id.ten_khoang_cot + item.mach?.toString() + item.day_dan_phan_pha] = item.thoi_gian_do;
      mapCurrentLast[item.khoang_cot_id.ten_khoang_cot + item.mach?.toString() + item.day_dan_phan_pha] = item;
    }
  });

  allViTri.vi_tris.forEach(viTri => {
    mapViTri[viTri.ten_vi_tri] = viTri._id;
  });

  allKhoangCot.forEach(khoangCot => {
    mapKhoangCot[khoangCot.ten_khoang_cot] = khoangCot._id;
  });

  for (const [key, value] of Object.entries(HUONG_DO)) {
    mapHuongDo[value] = key;
  }

  Object.values(KET_LUAN).forEach(danhGia => {
    mapDanhGia[danhGia.label] = danhGia.value;
  });

  allUser.forEach(user => {
    mapNguoiDo[user.full_name] = user._id;
  });

  // allSoPhieu.forEach(sophieu => {
  //   mapSoPhieu[sophieu] = sophieu;
  // });
  return [mapViTri, mapKhoangCot, mapNguoiDo, mapDanhGia, mapHuongDo, mapSoPhieu, mapNgayDo, mapCurrentLast];
}

export async function importData(req, sheetData) {
  const { rows } = sheetData;

  const [mapViTri, mapKhoangCot, mapNguoiDo, mapDanhGia, mapHuongDo, , mapNgayDo, mapCurrentLast] = await mapData(req, sheetData.don_vi_id, sheetData.duong_day_id);

  let currentLastest = [];
  rows.forEach(row => {
    const stringViTri = row[Headers.TEN_VI_TRI]?.trim() + row[Headers.MACH]?.toString() + mapHuongDo[row[Headers.HUONG_DO]] + row[Headers.DAY_DAN_PHAN_PHA];
    const stringKhoangCot = row[Headers.TEN_KHOANG_COT]?.trim() + row[Headers.MACH]?.toString() + row[Headers.DAY_DAN_PHAN_PHA];
    if (!mapNgayDo[stringViTri] && row[Headers.TEN_VI_TRI]?.trim()) {
      mapNgayDo[stringViTri] = new Date(1, 1, 1);
    }
    if (!mapNgayDo[stringKhoangCot] && row[Headers.TEN_KHOANG_COT]?.trim()) {
      mapNgayDo[stringKhoangCot] = new Date(1, 1, 1);
    }

    if (mapNgayDo[stringViTri] && mapNgayDo[stringViTri] < convertDate(row[Headers.THOI_GIAN_DO])) {
      mapCurrentLast[stringViTri] && currentLastest.push(mapCurrentLast[stringViTri]);
      delete mapCurrentLast[stringViTri];
      mapNgayDo[stringViTri] = convertDate(row[Headers.THOI_GIAN_DO]);
    }
    if (mapNgayDo[stringKhoangCot] && mapNgayDo[stringKhoangCot] < convertDate(row[Headers.THOI_GIAN_DO])) {
      mapCurrentLast[stringKhoangCot] && currentLastest.push(mapCurrentLast[stringKhoangCot]);
      delete mapCurrentLast[stringKhoangCot];
      mapNgayDo[stringKhoangCot] = convertDate(row[Headers.THOI_GIAN_DO]);
    }
  });

  function convertToDB(row) {
    const userArray = row[Headers.NGUOI_DO]?.trim().split('\r\n');
    const nguoiDoIds = [];
    userArray.forEach(user => {
      nguoiDoIds.push(mapNguoiDo[user.trim()]);
    });
    let isLatest = false;
    const stringViTri = row[Headers.TEN_VI_TRI]?.trim() + row[Headers.MACH]?.toString() + mapHuongDo[row[Headers.HUONG_DO]] + row[Headers.DAY_DAN_PHAN_PHA];
    const stringKhoangCot = row[Headers.TEN_KHOANG_COT]?.trim() + row[Headers.MACH]?.toString() + row[Headers.DAY_DAN_PHAN_PHA];
    if (mapNgayDo[stringViTri] && mapNgayDo[stringViTri] <= convertDate(row[Headers.THOI_GIAN_DO])) {
      isLatest = true;
      delete mapNgayDo[stringViTri];
    }

    if (mapNgayDo[stringKhoangCot] && mapNgayDo[stringKhoangCot] <= convertDate(row[Headers.THOI_GIAN_DO])) {
      isLatest = true;
      delete mapNgayDo[stringKhoangCot];
    }
    return {
      vi_tri_id: mapViTri[row[Headers.TEN_VI_TRI]?.trim()],
      khoang_cot_id: mapKhoangCot[row[Headers.TEN_KHOANG_COT]?.trim()],
      mach: row[Headers.MACH],
      huong_do: mapHuongDo[row[Headers.HUONG_DO]],
      day_dan_phan_pha: row[Headers.DAY_DAN_PHAN_PHA],
      pha_a: row[Headers.PHA_A],
      pha_b: row[Headers.PHA_B],
      pha_c: row[Headers.PHA_C],
      dong_tai: row[Headers.DONG_TAI],
      danh_gia: mapDanhGia[row[Headers.DANH_GIA]?.trim()],
      nhiet_do_moi_truong: row[Headers.NHIET_DO_MOI_TRUONG],
      nguoi_do: nguoiDoIds,
      thoi_gian_do: convertDate(row[Headers.THOI_GIAN_DO]),
      gio_do: row[Headers.GIO_DO],
      ghi_chu: row[Headers.GHI_CHU],

      thoi_gian_cap_nhat: new Date(),
      nguoi_chinh_sua_id: req.user?._id,
      is_latest: isLatest,
      is_import_by_file: true,
      is_deleted: false,
      version: 2,
    };
  }

  const dataToDB = rows.map(convertToDB);
  REPORT_DO_NHIET_DO.bulkWrite(
    currentLastest.map((current) =>
      ({
        updateOne: {
          filter: { _id: current._id },
          update: { is_latest: false },
          upsert: true,
        },
      }),
    ),
  );
  return REPORT_DO_NHIET_DO.create(dataToDB);
}

export async function checkImport(req, sheetData) {
  if (!sheetData) return null;
  const { t } = req;
  const { rows } = sheetData;

  const [mapViTri, mapKhoangCot, mapNguoiDo, mapDanhGia, mapHuongDo, mapSoPhieu] = await mapData(req, req.body.don_vi_id, req.body.duong_day_id);

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row) {
    let errors = [];

    if (!row[Headers.TEN_VI_TRI]?.trim() && !row[Headers.TEN_KHOANG_COT]?.trim()) {
      errors = [...errors, createError(Headers.TEN_VI_TRI, t('both_location_tower_not_empty'))];
    } else {
      if (row[Headers.TEN_VI_TRI]?.trim() && !mapViTri[row[Headers.TEN_VI_TRI]?.trim()]) {
        errors = [...errors, createError(Headers.TEN_VI_TRI, t('location_not_belong_line'))];
      }

      if (row[Headers.TEN_KHOANG_COT]?.trim() && !mapKhoangCot[row[Headers.TEN_KHOANG_COT]?.trim()]) {
        errors = [...errors, createError(Headers.TEN_KHOANG_COT, t('tower_not_belong_line'))];
      }
    }
    if (row[Headers.TEN_VI_TRI]?.trim() && !row[Headers.MACH]) {
      errors = [...errors, createError(Headers.MACH, t('missing_circuit_info_of_location'))];
    }

    if (row[Headers.NGUOI_DO]?.trim()) {
      const userArray = row[Headers.NGUOI_DO]?.trim().split('\r\n');
      userArray.forEach(user => {
        if (!mapNguoiDo[user.trim()]) {
          errors = [...errors, createError(Headers.NGUOI_DO, t('measurer_not_belong_unit_line_management'))];
        }
      });
    }

    // if (row[Headers.SO_PHIEU]?.trim() && !mapSoPhieu[row[Headers.SO_PHIEU]?.trim()]) {
    //   errors = [...errors, createError(Headers.SO_PHIEU, t('task_number_incorrect_or_dont_create'))];
    // }

    if (row[Headers.DANH_GIA]?.trim() && !mapDanhGia[row[Headers.DANH_GIA]?.trim()]) {
      errors = [...errors, createError(Headers.DANH_GIA, t('evaluation_result_incorrect_or_dont_create'))];
    }

    if (row[Headers.HUONG_DO]?.trim() && !mapHuongDo[row[Headers.HUONG_DO]?.trim()]) {
      errors = [...errors, createError(Headers.HUONG_DO, t('measure_direction_incorrect'))];
    }

    if (!row[Headers.DAY_DAN_PHAN_PHA]?.toString().trim()) {
      errors = [...errors, createError(Headers.DAY_DAN_PHAN_PHA, t('missing_wire_info'))];
    } else {
      if (!checkNumber(row[Headers.DAY_DAN_PHAN_PHA])) {
        errors = [...errors, createError(Headers.DAY_DAN_PHAN_PHA, t('incorrect_or_not_available'))];
      }
    }

    if (!checkNumber(row[Headers.MACH])) {
      errors = [...errors, createError(Headers.MACH, t('incorrect_or_not_available'))];
    }

    if (!checkNumber(row[Headers.NHIET_DO_MOI_TRUONG])) {
      errors = [...errors, createError(Headers.NHIET_DO_MOI_TRUONG, t('incorrect_or_not_available'))];
    }

    if (!checkNumber(row[Headers.DONG_TAI])) {
      errors = [...errors, createError(Headers.DONG_TAI, t('incorrect_or_not_available'))];
    }
    if (errors.length) {
      row['Lỗi'] = errors;
    } else {
      row['Lỗi'] = null;
    }
    return row;
  }

  sheetData.rows = rows.map(row => validateRow(t, row));
  return sheetData;
}

export function getAll(query, projection = {}) {
  return REPORT_DO_NHIET_DO.find(query, projection).lean();
}

export async function updateAll(dataUpdate) {
  for (const row of dataUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await REPORT_DO_NHIET_DO.findByIdAndUpdate(value._id, value);
  }
}

export function count(query) {
  return REPORT_DO_NHIET_DO.count(query);
}

const Joi = require('joi');

const objSchema = Joi.object({
  vi_tri_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Vị trí')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function buildCriteria(req, criteria) {
  const allViTri = await DuongDayService.getViTriIdByOneDuongDay(req, criteria);
  const allKhoangCot = allViTri?.flatMap(vt => vt.khoang_cot_id ? [...vt.khoang_cot_id] : []);
  return {
    $or: [
      { vi_tri_id: allViTri?.map(item => item._id) },
      { khoang_cot_id: allKhoangCot?.map(item => item._id) },
    ],
  };
}

export async function resultToReport(req, phieuGiaoViecData) {

  const allDoNhietDo = await ketQuaDoNhietDo.getAll({
    phieu_giao_viec_id: phieuGiaoViecData._id,
    is_deleted: false,
  });
  const nguoiCongTac = await NGUOI_CONG_TAC.find({ phieu_giao_viec_id: phieuGiaoViecData._id, is_deleted: false });
  const toCongTac = [...extractKeys(nguoiCongTac, 'user_id'), phieuGiaoViecData.chi_huy_truc_tiep_id._id];
  const allChiTietDoNhietDo = await chiTietDoNhietDo.getAll({
    ket_qua_do_nhiet_do_id: extractIds(allDoNhietDo),
    is_deleted: false,
  }).populate('ket_qua_do_nhiet_do_id');

  const viTriCongViec = await ViTriCongViecService.getAll({ phieu_giao_viec_id: phieuGiaoViecData._id });
  const resultSortByTime = await REPORT_DO_NHIET_DO.find({
    $or: [
      { vi_tri_id: extractKeys(viTriCongViec, 'vi_tri_id').filter(item => !!item) },
      { khoang_cot_id: extractKeys(viTriCongViec, 'khoang_cot_id').filter(item => !!item) },
    ],
    is_latest: true,
  }).select('thoi_gian_do vi_tri_id khoang_cot_id day_pha xuat_tuyen huong_do day_dan_phan_pha')
    .sort({ thoi_gian_do: 1 })
    .populate([{ path: 'vi_tri_id', select: 'ten_vi_tri' }, { path: 'khoang_cot_id', select: 'ten_khoang_cot' }]);

  const mapNgayDo = {}, mapCurrentLast = {};
  resultSortByTime.forEach(item => {
    if (item.vi_tri_id) {
      mapNgayDo[item.vi_tri_id._id + item.xuat_tuyen + item.huong_do + item.day_pha + item.day_dan_phan_pha] = item.thoi_gian_do;
      mapCurrentLast[item.vi_tri_id._id + item.xuat_tuyen + item.huong_do + item.day_pha + item.day_dan_phan_pha] = item;
    } else if (item.khoang_cot_id) {
      mapNgayDo[item.khoang_cot_id._id + item.day_pha + item.day_dan_phan_pha] = item.thoi_gian_do;
      mapCurrentLast[item.khoang_cot_id._id + item.xuat_tuyen + item.day_pha + item.day_dan_phan_pha] = item;
    }
  });

  let currentLastest = [];

  function checkIsLatest(key, row) {
    let isLatest = false;
    if (mapNgayDo[key] && mapNgayDo[key] <= row.ket_qua_do_nhiet_do_id.thoi_gian_do) {
      mapCurrentLast[key] && currentLastest.push(mapCurrentLast[key]);
      delete mapCurrentLast[key];
      isLatest = true;
    }
    if (!mapNgayDo[key]) {
      isLatest = true;
    }
    return isLatest;
  }

  function convertToDB(row) {
    let isLatest, stringViTri, stringKhoangCot;
    if (row.ket_qua_do_nhiet_do_id?.version === 2) {
      stringViTri = row.ket_qua_do_nhiet_do_id?.vi_tri_id + row.xuat_tuyen + row.huong_do + row.day_pha + row.day_dan_phan_pha;
      stringKhoangCot = row.ket_qua_do_nhiet_do_id?.khoang_cot_id + row.xuat_tuyen + row.day_pha + row.day_dan_phan_pha;
    } else {
      stringViTri = row.ket_qua_do_nhiet_do_id?.vi_tri_id + row.mach?.toString() + row.huong_do + row.day_dan_phan_pha;
      stringKhoangCot = row.ket_qua_do_nhiet_do_id?.khoang_cot_id + row.mach?.toString() + row.day_dan_phan_pha;
    }

    row.ket_qua_do_nhiet_do_id?.vi_tri_id ? isLatest = checkIsLatest(stringViTri, row) : isLatest = checkIsLatest(stringKhoangCot, row);
    return {
      khoang_cot_id: row.ket_qua_do_nhiet_do_id?.khoang_cot_id,
      vi_tri_id: row.ket_qua_do_nhiet_do_id?.vi_tri_id,
      mach: row.mach,
      huong_do: row.huong_do,
      day_dan_phan_pha: row.day_dan_phan_pha,
      pha_a: row.pha_a,
      pha_b: row.pha_b,
      pha_c: row.pha_c,
      dong_tai: row.dong_tai,
      danh_gia: row.danh_gia,
      nhiet_do_moi_truong: row.ket_qua_do_nhiet_do_id.nhiet_do_moi_truong || row.nhiet_do_moi_truong,
      nguoi_do: toCongTac,
      thoi_gian_do: row.ket_qua_do_nhiet_do_id.thoi_gian_do,
      ghi_chu: row.ket_qua_do_nhiet_do_id.ghi_chu,

      day_pha: row.day_pha,
      xuat_tuyen: row.xuat_tuyen,
      nhiet_do_day_dan: row.nhiet_do_day_dan,
      order: row.order,
      version: row.ket_qua_do_nhiet_do_id?.version,

      so_phieu: phieuGiaoViecData.so_phieu,
      phieu_giao_viec_id: phieuGiaoViecData._id,
      is_latest: row.ket_qua_do_nhiet_do_id.thoi_gian_do ? isLatest : false,
      is_deleted: false,
    };
  }

  const dataToDB = allChiTietDoNhietDo.map(convertToDB);

  REPORT_DO_NHIET_DO.bulkWrite(
    currentLastest.map((ketQuaDo) =>
      ({
        updateOne: {
          filter: { _id: ketQuaDo._id },
          update: { is_latest: false },
          upsert: true,
        },
      }),
    ),
  );
  await REPORT_DO_NHIET_DO.create(dataToDB);
}

export async function bangTongHopBaoCaoNhietDoTiepXuc(criteria) {

  const allNhietDoTiepXuc = await REPORT_DO_NHIET_DO.find(criteria)
    .populate('vi_tri_id khoang_cot_id nguoi_do nguoi_chinh_sua_id')
    .sort({
      so_phieu: -1,
      is_latest: -1,
      khoang_cot_id: 1,
      xuat_tuyen: 1,
      huong_do: -1,
      day_pha: 1,
      day_dan_phan_pha: 1,
    })
    .lean();

  const viTriNhietDo = allNhietDoTiepXuc.filter(ketQua => !!ketQua.vi_tri_id);
  const khoangCotNhietDo = allNhietDoTiepXuc.filter(ketQua => !!ketQua.khoang_cot_id);

  function convertUserIdsToName(listUser = []) {
    let stringName;
    listUser?.forEach(user => {
      !stringName ? stringName = `- ${user.full_name}` : stringName = [stringName, user.full_name].join('\n- ');
    });
    return stringName;
  }

  function convertDataToDB(ketQua, index) {
    let resObj = {
      stt: index + 1,
      ten_vi_tri: ketQua.vi_tri_id?.ten_vi_tri,
      ten_khoang_cot: ketQua.khoang_cot_id?.ten_khoang_cot,
      mach: ketQua.mach,
      huong_do: ketQua.huong_do,
      day_dan_phan_pha: ketQua.day_dan_phan_pha,
      pha_a: ketQua.pha_a,
      pha_b: ketQua.pha_b,
      pha_c: ketQua.pha_c,
      dong_tai: ketQua.dong_tai,

      day_pha: ketQua.day_pha,
      xuat_tuyen: ketQua.xuat_tuyen,
      nhiet_do_day_dan: ketQua.nhiet_do_day_dan,
      order: ketQua.order,

      danh_gia: KET_LUAN[ketQua.danh_gia]?.label,
      nhiet_do_moi_truong: ketQua.nhiet_do_moi_truong,
      nguoi_do: convertUserIdsToName(ketQua.nguoi_do),
      thoi_gian_do: formatDateTime(ketQua.thoi_gian_do),
      ghi_chu: ketQua.ghi_chu,
      thoi_gian_cap_nhat: formatDateTime(ketQua.thoi_gian_cap_nhat),
      nguoi_chinh_sua_id: convertUserIdsToName(ketQua.nguoi_chinh_sua_id),
    };
    if (ketQua.version === 2 && ketQua.vi_tri_id) {
      const arrayKetQua = spilitKetQuaKhoaNeoDauCotLeo(ketQua.nhiet_do_day_dan);
      const resViTriObj = {
        nhiet_do_khoa_neo: arrayKetQua ? arrayKetQua[0] : '',
        nhiet_do_lan_can_khoa_neo: arrayKetQua ? arrayKetQua[1] : '',
        nhiet_do_dau_cot_leo: arrayKetQua ? arrayKetQua[2] : '',
        nhiet_do_lan_can_dau_cot_leo: arrayKetQua ? arrayKetQua[3] : '',
      };
      resObj = { ...resObj, ...resViTriObj };
    }
    if (ketQua.version === 2 && ketQua.khoang_cot_id) {
      const arrayKetQua = spilitKetQuaOngNoi(ketQua.nhiet_do_day_dan);
      const resKhoangCotObj = {
        nhiet_do_ong_noi: arrayKetQua ? arrayKetQua[0] : '',
        nhiet_do_lan_can_ong_noi: arrayKetQua ? arrayKetQua[1] : '',
      };
      resObj = { ...resObj, ...resKhoangCotObj };
    }
    return resObj;
  }

  return [
    {
      stt: 'I', title: 'Kết quả đo nhiệt độ khóa néo, đầu cốt lèo',
      vi_tri: viTriNhietDo.map(convertDataToDB),
    },
    {
      stt: 'II', title: 'Kết quả đo nhiệt độ ống nối',
      khoang_cot: khoangCotNhietDo.map(convertDataToDB),
    },
  ];

}

function spilitKetQuaOngNoi(ketQua = '') {
  return ketQua?.toString().split(/[/]/);
}

function spilitKetQuaKhoaNeoDauCotLeo(ketQua = '') {
  const ketQuaKhoaNeo = ketQua?.toString().split(/[-]/)[0];
  const ketQuaDauCotLeo = ketQua?.toString().split(/[-]/)[1];
  const [nhietDoKhoaNeo, nhietDoLanCanKhoaNeo] = spilitKetQuaOngNoi(ketQuaKhoaNeo);
  const [nhietDoDauCotleo, nhietDoLanCanDauCotleo] = spilitKetQuaOngNoi(ketQuaDauCotLeo);
  return [nhietDoKhoaNeo, nhietDoLanCanKhoaNeo, nhietDoDauCotleo, nhietDoLanCanDauCotleo];
}
