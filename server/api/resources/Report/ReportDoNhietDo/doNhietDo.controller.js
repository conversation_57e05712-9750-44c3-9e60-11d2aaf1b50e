import * as Service from './doNhietDo.service';
import Model from './doNhietDo.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import * as responseAction from '../../../helpers/responseHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import exelUtils from '../../../utils/exelUtils';
import queryHelper from '../../../helpers/queryHelper';
import DuongDayModel from '../../TongKe/DuongDay/duongDay.model';
import { generateDocument } from '../GenerateFile/generate.controller';
import CommonError from '../../../error/CommonError';
import { getFilePath } from '../../../utils/fileUtils';
import { TEMPLATES_DIRS } from '../../../constant/constant';

const searchLike = [];
const populateOpts = ['khoang_cot_id', 'vi_tri_id', 'nguoi_do', 'nguoi_chinh_sua_id'];
const uniqueOpts = [];
const sortOpts = { is_latest: -1, thoi_gian_do: -1, day_dan_phan_pha: 1 };

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, searchLike, populateOpts, sortOpts);

const SHEET_NAMES = {
  REPORT_DO_NHIET_DO: 'Nhiệt độ tiếp xúc',
};

export async function buildQueryTime(req) {
  const query = queryHelper.extractQueryParam(req);
  let { criteria, options } = query;

  if (criteria.tu_ngay) {
    if (criteria.den_ngay) {
      criteria.thoi_gian_do = {
        $gte: criteria.tu_ngay,
        $lte: criteria.den_ngay,
      };
      criteria.tu_ngay = criteria.den_ngay = null;
    } else {
      criteria.thoi_gian_do = { $gte: criteria.tu_ngay };
      criteria.tu_ngay = null;
    }
  } else {
    if (criteria.den_ngay) {
      criteria.thoi_gian_do = { $lte: criteria.den_ngay };
      criteria.den_ngay = null;
    }
  }

  delete criteria.den_ngay;
  delete criteria.tu_ngay;

  return { criteria, options };
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOne({ _id: id });
    const dataRes = await Model.deleteOne({ _id: id });
    if (data.is_latest) {
      let curentLatest;
      if (data.vi_tri_id) {
        curentLatest = await Model.find({
          vi_tri_id: data.vi_tri_id,
          mach: data.mach,
          huong_do: data.huong_do,
        }).sort({ thoi_gian_do: -1 }).limit(1);
      } else {
        if (data.khoang_cot_id) {
          curentLatest = await Model.find({
            khoang_cot_id: data.khoang_cot_id,
            mach: data.mach,
          }).sort({ thoi_gian_do: -1 }).limit(1);
        }
      }
      if (curentLatest[0]?._id) {
        await Model.updateOne({ _id: curentLatest[0]._id }, { is_latest: true });
      }
    }
    if (!dataRes) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, dataRes);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function getAllDoNhietDo(req, res) {
  try {
    let { criteria, options } = await buildQueryTime(req);
    if (criteria.is_latest !== 'KET_QUA_MOI_NHAT') delete criteria.is_latest;
    else {
      criteria.is_latest = true;
    }
    options.populate = [
      { path: 'vi_tri_id' },
      { path: 'khoang_cot_id' },
      { path: 'nguoi_do' },
      { path: 'nguoi_chinh_sua_id' },
    ];
    options.sort = {
      so_phieu: -1,
      is_latest: -1,
      khoang_cot_id: 1,
      xuat_tuyen: 1,
      huong_do: -1,
      day_pha: 1,
      day_dan_phan_pha: 1,
    };

    if (criteria.duong_day_id) {
      const newCriteria = await Service.buildCriteria(req, criteria.duong_day_id);
      delete criteria.duong_day_id;
      delete criteria.don_vi_id;
      criteria = { ...criteria, ...newCriteria };
    }

    const dataVer1 = await Model.find({ ...criteria, version: { $ne: 2 } })
      .populate(options.populate)
      .sort(options.sort)
      .lean();

    const dataVer2 = await Model.find({ ...criteria, version: { $eq: 2 } })
      .populate(options.populate)
      .sort(options.sort)
      .lean();

    const data = {
      version1: dataVer1,
      version2: dataVer2,
    };


    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function checkImport(req, res) {

  try {
    let filePath = req.files.file.path;
    const sheetData = await exelUtils.transformFile(filePath);

    const resultArray = await checkImportByData(req, sheetData);
    responseAction.success(res, resultArray);
  } catch (e) {
    console.log(e);
    responseAction.error(res, e);
  }
}

async function checkImportByData(req, sheetData) {

  function getSheetByName(sheetData, name) {
    return sheetData.find((sheet) => sheet.name.toLowerCase().includes(name.toLowerCase()));
  }

  let resultArray = [];
  resultArray = [...resultArray, await Service.checkImport(req, getSheetByName(sheetData, SHEET_NAMES.REPORT_DO_NHIET_DO))];
  return resultArray;
}

export async function importOne(req, res) {

  try {
    const sheetData = req.body;
    const result = await Service.importData(req, sheetData);
    responseAction.success(res, result);
  } catch (e) {
    console.log(e);
    responseAction.error(res, e);
  }
}

export async function downloadBaoCaoNhietDoTiepXuc(req, res) {
  let { criteria } = await buildQueryTime(req);
  let duongDay;
  if (criteria.duong_day_id) {
    duongDay = await DuongDayModel.findOne({ _id: criteria.duong_day_id });
    const newCriteria = await Service.buildCriteria(req, criteria.duong_day_id);
    delete criteria.duong_day_id;
    delete criteria.don_vi_id;
    criteria = { ...criteria, ...newCriteria };
  }
  let version2 = false;
  if (criteria.version_2) {
    criteria.version = { $eq: 2 };
    version2 = true;
    delete criteria.version_2;
  } else {
    criteria.version = { $ne: 2 };
  }

  const ketQuaDoNhietDo = await Service.bangTongHopBaoCaoNhietDoTiepXuc(criteria);
  let data = {};
  data.ten_duong_day = duongDay?.ten_duong_day;
  data.ket_qua = ketQuaDoNhietDo;
  const fileName = version2 ? `bang_tong_hop_nhiet_do_tiep_xuc_version_2.xlsx` : `bang_tong_hop_nhiet_do_tiep_xuc.xlsx`;
  const templateFilePath = getFilePath(fileName, TEMPLATES_DIRS.BIEU_MAU);
  const outputFileName = 'Bảng tổng hợp kết quả đo nhiệt độ tiếp xúc.xlsx';
  generateDocument(res, data, templateFilePath, outputFileName);
}
