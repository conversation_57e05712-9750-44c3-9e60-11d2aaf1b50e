import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { KHOANG_COT, BAO_CAO_DO_NHIET_DO, USER, VI_TRI, PHIEU_GIAO_VIEC } from '../../../constant/dbCollections';

const schema = new Schema({
  khoang_cot_id: { type: Schema.Types.ObjectId, ref: KHOANG_COT },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  mach: { type: Number },
  huong_do: { type: String },
  day_dan_phan_pha: { type: String },
  pha_a: { type: String },
  pha_b: { type: String },
  pha_c: { type: String },
  dong_tai: { type: Number },
  danh_gia: { type: String },
  nhiet_do_moi_truong: { type: Number },
  nguoi_do: [{ type: Schema.Types.ObjectId, ref: USER }],
  thoi_gian_do: { type: Date },
  gio_do: { type: String },

  thoi_gian_cap_nhat: { type: Date, default: Date.now },
  nguoi_chinh_sua_id: [{ type: Schema.Types.ObjectId, ref: USER }],
  ghi_chu: { type: String },

  day_pha: { type: String },
  xuat_tuyen: { type: String },
  nhiet_do_day_dan: { type: String },
  order: { type: Number },
  version: { type: Number },

  so_phieu: String,
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  is_latest: { type: Boolean, default: false },
  is_import_by_file: { type: Boolean, default: false },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(BAO_CAO_DO_NHIET_DO, schema, BAO_CAO_DO_NHIET_DO);
