import express from 'express';
import passport from 'passport';
import * as Controller from './doNhietDo.controller';
import { loggerMiddleware } from '../../../logs/middleware';
import { checkTempFolder, multipartMiddleware } from '../../../utils/fileUtils';

export const reportDoNhietDoRouter = express.Router();
reportDoNhietDoRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

reportDoNhietDoRouter
  .route('/download')
  .get(Controller.downloadBaoCaoNhietDoTiepXuc);

reportDoNhietDoRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), Controller.getAllDoNhietDo)
  .post(passport.authenticate('jwt', { session: false }), Controller.create);

reportDoNhietDoRouter
  .route('/checkimport')
  .post(checkTempFolder, multipartMiddleware, Controller.checkImport);

reportDoNhietDoRouter
  .route('/importone')
  .post(checkTempFolder, multipartMiddleware, Controller.importOne);

reportDoNhietDoRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), Controller.findOne)
  .delete(passport.authenticate('jwt', { session: false }), Controller.remove)
  .put(passport.authenticate('jwt', { session: false }), Controller.update);
