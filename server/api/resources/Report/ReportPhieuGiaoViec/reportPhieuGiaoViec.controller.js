import queryHelper from '../../../helpers/queryHelper';
import * as responseHelper from '../../../helpers/responseHelper';

import * as Service from './reportPhieuGiaoViec.service';

import { generatePhieu } from '../GeneratePhieu/generatePhieu.controller';

import { replaceAll } from '../../../common/functionCommons';
import { EXTENSION_FILE, STORE_DIRS, TEMPLATES_DIRS } from '../../../constant/constant';
import { getAndCheckExistFilePath, getDirPath, getFilePath } from '../../../utils/fileUtils';
import {
  generateDocumentByColumns,
  generateDocumentWithImage,
  generateExcel,
} from '../GenerateFile/generate.controller';
import { DO_THONG_SO } from '../../DanhMuc/LoaiCongViec';
import PHIEU_GIAO_VIEC from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.model';

export async function phieuGiaoViecPDF(req, res) {
  // Hàm xử lý việc tải xuống phiếu giao việc ở định dạng PDF
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const dataPhieuGiaoViec = await PHIEU_GIAO_VIEC.findById(criteria.id)
      .populate([
        { path: 'don_vi_giao_phieu_id', select: 'ma_don_vi' },
      ])
      .lean();
    const folderName = [dataPhieuGiaoViec.don_vi_giao_phieu_id.ma_don_vi, 'v2'].join('_');
    const soPhieu = replaceAll(dataPhieuGiaoViec.so_phieu, '/', '-');
    const fileName = `${[soPhieu, criteria.tinh_trang_cong_viec].join('-')}.pdf`;
    const dirName = getDirPath(folderName, STORE_DIRS.PHIEU_GIAO_VIEC);
    const filePDFPath = getAndCheckExistFilePath(fileName, dirName);
    console.log('filePDFPath', filePDFPath);
    if (!!filePDFPath) {
      return res.download(filePDFPath);
    }
    await generatePhieu(res, dataPhieuGiaoViec, criteria.tinh_trang_cong_viec, EXTENSION_FILE.PDF);
    const fileAfterGenerate = getAndCheckExistFilePath(fileName, dirName);
    return res.download(fileAfterGenerate);
  } catch (e) {
    return responseHelper.error(res, e);
  }
}

export async function phieuGiaoViecDOCX(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const dataPhieuGiaoViec = await PHIEU_GIAO_VIEC.findById(criteria.id).lean();
    const fileAfterGenerate = await generatePhieu(res, dataPhieuGiaoViec, criteria.tinh_trang_cong_viec, EXTENSION_FILE.DOCX);
    return res.download(fileAfterGenerate);
  } catch (e) {
    return responseHelper.error(res, e);
  }
}

export async function phieuDoDienTroXlsx(req, res) {
  try {
    await downloadPhieuDoDienTro(req, res);
  } catch (e) {
    return responseHelper.error(res, e);
  }
}

export async function phieuDoDienTroPdf(req, res) {
  try {
    await downloadPhieuDoDienTro(req, res, EXTENSION_FILE.PDF);
  } catch (e) {
    return responseHelper.error(res, e);
  }
}

export async function downloadPhieuDoDienTro(req, res, type = EXTENSION_FILE.XLSX) {
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  const dataDoDienTro = await Service.getDoDienTroByID(criteria);
  const soPhieu = replaceAll(dataDoDienTro.so_phieu, '/', '-');
  const outputFileName = `Phiếu kết quả đo điện trở tiếp địa ${soPhieu}.${type}`;
  let templateFileName = `phieu_do_dien_tro_tiep_dia_${dataDoDienTro.so_tia}.xlsx`;
  let totalRow = dataDoDienTro.do_dien_tro?.length + 8;
  let rangeImage = {
    tl: { col: 10, row: totalRow },
    br: { col: 11.5, row: totalRow + 3 },
  };

  switch (type) {
    case EXTENSION_FILE.XLSX:
      await generateExcel(res, dataDoDienTro, templateFileName, rangeImage, outputFileName);
      break;
    case EXTENSION_FILE.PDF:
      if (dataDoDienTro.so_tia < 5) {
        templateFileName = `phieu_do_dien_tro_tiep_dia_${dataDoDienTro.so_tia}_to_pdf.xlsx`;
      }
      const templatePDFFile = `phieu_do_dien_tro_tiep_dia.pdf`;
      await generateExcel(res, dataDoDienTro, templateFileName, rangeImage, outputFileName, templatePDFFile, true);
      break;
  }
}

export async function phieuDoPhaDatDocx(req, res) {
  await downloadPhieuDoPhaDat(req, res);
}

export async function phieuDoPhaDatPdf(req, res) {
  await downloadPhieuDoPhaDat(req, res, EXTENSION_FILE.PDF);
}

export async function downloadPhieuDoPhaDat(req, res, type = EXTENSION_FILE.DOCX) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const dataDoPhaDat = await Service.getDoPhaDatByID(criteria);
    const soPhieu = replaceAll(dataDoPhaDat.so_phieu, '/', '-');
    const outputFileName = `Phiếu kết quả đo pha đất - thấp ${soPhieu}.${type}`;
    const templateFileName = `phieu_do_khoang_cach_pha_dat.docx`;

    await generateDocumentWithImage(res, dataDoPhaDat, templateFileName, DO_THONG_SO, outputFileName, type === EXTENSION_FILE.PDF);
  } catch (e) {
    return responseHelper.error(res, e);
  }
}

export async function phieuDoNhietDoXlsx(req, res) {
  await downloadPhieuDoNhietDo(req, res);
}

export async function phieuDoNhietDoPdf(req, res) {
  await downloadPhieuDoNhietDo(req, res, EXTENSION_FILE.PDF);
}

export async function downloadPhieuDoNhietDo(req, res, type = EXTENSION_FILE.XLSX) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const dataDoNhietDo = await Service.getDoNhietDoByID(criteria);
    const soPhieu = replaceAll(dataDoNhietDo.so_phieu, '/', '-');
    const outputFileName = `Phiếu kết quả đo nhiệt độ tiếp xúc${soPhieu}.${type}`;
    let templateFileName = dataDoNhietDo.version === 2 ? `phieu_do_nhiet_do_tiep_xuc_version_2.xlsx` : `phieu_do_nhiet_do_tiep_xuc.xlsx`;
    let totalRow = 18;
    dataDoNhietDo.ket_qua[0].vi_tri?.forEach(vitri => {
      totalRow += vitri.ket_qua_do?.length;
    });
    dataDoNhietDo.ket_qua[1].khoang_cot?.forEach(khoangcot => {
      totalRow += khoangcot.ket_qua_do?.length;
    });
    let rangeImage = {
      tl: { col: 10, row: totalRow },
      br: { col: 11, row: totalRow + 3 },
    };

    switch (type) {
      case EXTENSION_FILE.XLSX:

        await generateExcel(res, dataDoNhietDo, templateFileName, rangeImage, outputFileName);
        break;
      case EXTENSION_FILE.PDF:
        templateFileName = dataDoNhietDo.version === 2 ? `phieu_do_nhiet_do_tiep_xuc_to_pdf_version_2.xlsx` : `phieu_do_nhiet_do_tiep_xuc_to_pdf.xlsx`;
        const templatePDFFile = `phieu_do_nhiet_do_tiep_xuc.pdf`;
        await generateExcel(res, dataDoNhietDo, templateFileName, rangeImage, outputFileName, templatePDFFile, true);
        break;
    }
  } catch (e) {
    return responseHelper.error(res, e);
  }
}

export async function danhSachPhieuKiemTra(req, res) {
  try {
    const xuatPhieuInfo = req.query?.thong_tin_xuat_phieu;
    let selectedColumns = xuatPhieuInfo.split(',').map(item => item.trim());
    let mandatoryColumns;
    if (req.query?.loai_phieu !== 'CONG_TAC_PHU_TRO') {
      mandatoryColumns = ['STT', 'Số phiếu', 'Đơn vị giao phiếu', 'Đường dây', 'Vị trí công việc'];
    } else {
      mandatoryColumns = ['STT', 'Số phiếu', 'Đơn vị giao phiếu'];
    }
    selectedColumns = [...selectedColumns, ...mandatoryColumns];
    delete req.query.thong_tin_xuat_phieu;
    const phieuGiaoViecData = await Service.danhSachPhieuKiemTra(req);
    const templateFilePath = getFilePath('danh_sach_phieu_kiem_tra.xlsx', TEMPLATES_DIRS.REPORT);
    await generateDocumentByColumns(res, phieuGiaoViecData, templateFilePath, null, selectedColumns);
  } catch (e) {
    return responseHelper.error(res, e);
  }
}

///////////////////////////////////////////////////////

export async function phieuDoCorocamDocx(req, res) {
  await downloadPhieuDoCorocam(req, res);
}

export async function phieuDoCorocamPdf(req, res) {
  await downloadPhieuDoCorocam(req, res, EXTENSION_FILE.PDF);
}

async function downloadPhieuDoCorocam(req, res, type = EXTENSION_FILE.DOCX) {
  try {
    const { id } = req.params;
    const dataDoCorocam = await Service.getKetQuaDoCorocamByPhieu(id);
    const soPhieu = replaceAll(dataDoCorocam.so_phieu, '/', '-');
    const outputFileName = `Phiếu kết quả đo Corocam ${soPhieu}.${type}`;
    const templateFileName = `phieu_do_corocam.docx`;
    await generateDocumentWithImage(res, dataDoCorocam, templateFileName, DO_THONG_SO, outputFileName, type === EXTENSION_FILE.PDF);
  } catch (e) {
    return responseHelper.error(res, e);
  }
}


///////////////////////////////////////////////////////

export async function phieuDoNhietDoCompositeDocx(req, res) {
  await downloadPhieuDoNhietDoComposite(req, res);
}

export async function phieuDoNhietDoCompositePdf(req, res) {
  await downloadPhieuDoNhietDoComposite(req, res, EXTENSION_FILE.PDF);
}

async function downloadPhieuDoNhietDoComposite(req, res, type = EXTENSION_FILE.DOCX) {
  try {
    const { id } = req.params;
    const dataDoCorocam = await Service.getKetQuaDoNhietDoCompositeByPhieu(id);
    const soPhieu = replaceAll(dataDoCorocam.so_phieu, '/', '-');
    const outputFileName = `Phiếu kết quả đo nhiệt độ Composite ${soPhieu}.${type}`;
    const templateFileName = `phieu_do_nhiet_do_composite.docx`;
    await generateDocumentWithImage(res, dataDoCorocam, templateFileName, DO_THONG_SO, outputFileName, type === EXTENSION_FILE.PDF);
  } catch (e) {
    return responseHelper.error(res, e);
  }
}

///////////////////////////////////////////////////////

export async function phieuDoCuongDoDienTruongDocx(req, res) {
  await downloadPhieuDoCuongDoDienTruong(req, res);
}

export async function phieuDoCuongDoDienTruongPdf(req, res) {
  await downloadPhieuDoCuongDoDienTruong(req, res, EXTENSION_FILE.PDF);
}

async function downloadPhieuDoCuongDoDienTruong(req, res, type = EXTENSION_FILE.DOCX) {
  try {
    const { id } = req.params;
    const dataDoCorocam = await Service.getKetQuaDoCuongDoDienTruongByPhieu(id);
    const soPhieu = replaceAll(dataDoCorocam.so_phieu, '/', '-');
    const outputFileName = `Phiếu kết quả đo cường độ điện trường ${soPhieu}.${type}`;
    const templateFileName = `phieu_do_cuong_do_dien_truong.docx`;
    await generateDocumentWithImage(res, dataDoCorocam, templateFileName, DO_THONG_SO, outputFileName, type === EXTENSION_FILE.PDF);
  } catch (e) {
    return responseHelper.error(res, e);
  }
}

///////////////////////////////////////////////////////

export async function phieuDoKhoangCachGiaoCheoDocx(req, res) {
  await downloadPhieuDoKhoangCachGiaoCheo(req, res);
}

export async function phieuDoKhoangCachGiaoCheoPdf(req, res) {
  await downloadPhieuDoKhoangCachGiaoCheo(req, res, EXTENSION_FILE.PDF);
}

async function downloadPhieuDoKhoangCachGiaoCheo(req, res, type = EXTENSION_FILE.DOCX) {
  try {
    const { id } = req.params;
    const dataDoGiaoCheo = await Service.getKetQuaDoKhoangCachGiaoCheoByPhieu(id);
    const soPhieu = replaceAll(dataDoGiaoCheo.so_phieu, '/', '-');
    const outputFileName = `Phiếu kết quả đo khoảng cách giao chéo ${soPhieu}.${type}`;
    const templateFileName = `phieu_do_khoang_cach_giao_cheo.docx`;
    await generateDocumentWithImage(res, dataDoGiaoCheo, templateFileName, DO_THONG_SO, outputFileName, type === EXTENSION_FILE.PDF);
  } catch (e) {
    return responseHelper.error(res, e);
  }
}
