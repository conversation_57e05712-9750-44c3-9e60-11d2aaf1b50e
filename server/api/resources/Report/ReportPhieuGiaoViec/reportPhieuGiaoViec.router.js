import express from 'express';
import * as Controller from './reportPhieuGiaoViec.controller';
import passport from 'passport';

export const reportPhieuGiaoViecRouter = express.Router();

reportPhieuGiaoViecRouter
  .route('/phieugiaoviecpdf')
  .get(Controller.phieuGiaoViecPDF);

reportPhieuGiaoViecRouter
  .route('/phieugiaoviecdocx')
  .get(Controller.phieuGiaoViecDOCX);

reportPhieuGiaoViecRouter
  .route('/phieudodientroxlsx')
  .get(Controller.phieuDoDienTroXlsx);

reportPhieuGiaoViecRouter
  .route('/phieudodientropdf')
  .get(Controller.phieuDoDienTroPdf);

reportPhieuGiaoViecRouter
  .route('/phieudonhietdoxlsx')
  .get(Controller.phieuDoNhietDoXlsx);

reportPhieuGiaoViecRouter
  .route('/phieudonhietdopdf')
  .get(Controller.phieuDoNhietDoPdf);

reportPhieuGiaoViecRouter
  .route('/phieudophadatdocx')
  .get(Controller.phieuDoPhaDatDocx);

reportPhieuGiaoViecRouter
  .route('/phieudophadatpdf')
  .get(Controller.phieuDoPhaDatPdf);

reportPhieuGiaoViecRouter
  .route('/danhsachkiemtra')
  .get(passport.authenticate('jwt', { session: false }), Controller.danhSachPhieuKiemTra);


reportPhieuGiaoViecRouter
  .route('/:id/phieudocorocamdocx')
  .get(Controller.phieuDoCorocamDocx);

reportPhieuGiaoViecRouter
  .route('/:id/phieudocorocampdf')
  .get(Controller.phieuDoCorocamPdf);


reportPhieuGiaoViecRouter
  .route('/:id/phieudonhietdocompositedocx')
  .get(Controller.phieuDoNhietDoCompositeDocx);

reportPhieuGiaoViecRouter
  .route('/:id/phieudonhietdocompositepdf')
  .get(Controller.phieuDoNhietDoCompositePdf);


reportPhieuGiaoViecRouter
  .route('/:id/phieudocuongdodientruongdocx')
  .get(Controller.phieuDoCuongDoDienTruongDocx);

reportPhieuGiaoViecRouter
  .route('/:id/phieudocuongdodientruongpdf')
  .get(Controller.phieuDoCuongDoDienTruongPdf);


reportPhieuGiaoViecRouter
  .route('/:id/phieudokhoangcachgiaocheodocx')
  .get(Controller.phieuDoKhoangCachGiaoCheoDocx);

reportPhieuGiaoViecRouter
  .route('/:id/phieudokhoangcachgiaocheopdf')
  .get(Controller.phieuDoKhoangCachGiaoCheoPdf);
