import { Types } from 'mongoose';
import i18next from 'i18next';

import PhieuGiaoViecModel from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.model';
import PHIEU_GIAO_VIEC from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.model';
import NGUOI_CONG_TAC from '../../QuanLyVanHanh/NguoiCongTac/nguoiCongTac.model';

import * as BienPhapAnToanCongViecService
  from '../../QuanLyVanHanh/BienPhapAnToanCongViec/bienPhapAnToanCongViec.service';
import * as DieuKienAnToanCongViecService
  from '../../QuanLyVanHanh/DieuKienAnToanCongViec/dieuKienAnToanCongViec.service';
import * as NguoiCongTacService from '../../QuanLyVanHanh/NguoiCongTac/nguoiCongTac.service';
import * as NoiDungCongViecService from '../../QuanLyVanHanh/NoiDungCongViec/noiDungCongViec.service';
import * as CongViecPhuTroService from '../../QuanLyVanHanh/CongViecPhuTro/congViecPhuTro.service';
import * as CongViecPhatSinhService from '../../QuanLyVanHanh/CongViecPhatSinh/congViecPhatSinh.service';
import * as ViTriCongViecService from '../../QuanLyVanHanh/ViTriCongViec/viTriCongViec.service';
import * as VanHanhService from '../../TongKe/VanHanh/vanHanh.service';
import * as DuongDayService from '../../TongKe/DuongDay/duongDay.service';
import * as CongTrinhCongViecService from '../../QuanLyVanHanh/CongTrinhCongViec/congTrinhCongViec.service';
import * as KetQuaSuaChuaCoKeHoachService
  from '../../QuanLyVanHanh/KetQuaSuaChuaCoKeHoach/ketQuaSuaChuaCoKeHoach.service';
import * as HieuChinhTonTaiService from '../../QuanLyVanHanh/PhieuGiaoViec/HieuChinhTonTai/hieuChinhTonTai.service';
import * as TinhHinhTonTaiService from '../../QuanLyVanHanh/PhieuGiaoViec/TinhHinhTonTai/tinhHinhTonTai.service';
import * as KetQuaKiemTraService from '../../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.service';
import * as NoiDungKiemTraService from '../../DanhMuc/NoiDungKiemTra/noiDung.service';
import * as KetQuaSuaChuaKhongKeHoachService
  from '../../QuanLyVanHanh/KetQuaSuaChuaKhongKeHoach/ketQuaSuaChuaKhongKeHoach.service';
import * as CotDienService from '../../TongKe/CotDien/cotDien.service';
import * as TiepDatService from '../../TongKe/TiepDat/tiepDat.service';
import * as DoPhaDatService from '../../QuanLyVanHanh/KetQuaDoKhoangCachPhaDat/ketQuaDoKhoangCachPhaDat.service';
import * as DoNhietDoService from '../../KetQuaDoNhietDo/ketQuaDoNhietDo.service';
import * as DoNhietDoChiTietService from '../../KetQuaDoNhietDo/ChiTietDoNhietDo/chiTietDoNhietDo.service';
import * as DoDienTroService from '../../QuanLyVanHanh/DoDienTro/doDienTro.service';
import * as DonViService from '../../DonVi/donVi.service';
import * as KetQuaDoCorocamService from '../../QuanLyVanHanh/KetQuaDoCorocam/ketQuaDoCorocam.service';
import * as KetQuaDoNhietDoCompositeService
  from '../../QuanLyVanHanh/KetQuaDoNhietDoComposite/ketQuaDoNhietDoComposite.service';
import * as KetQuaDoCuongDoDienTruongService
  from '../../QuanLyVanHanh/KetQuaDoCuongDoDienTruong/ketQuaDoCuongDoDienTruong.service';
import * as KetQuaDoKhoangCachGiaoCheoService
  from '../../QuanLyVanHanh/KetQuaDoKhoangCachGiaoCheo/ketQuaDoKhoangCachGiaoCheo.service';

import {
  CONG_TAC_PHU_TRO,
  DO_THONG_SO,
  KIEM_TRA,
  LOAI_CONG_VIEC,
  LOAI_CONG_VIEC_SUA_CHUA,
  SUA_CHUA_BAO_DUONG,
} from '../../DanhMuc/LoaiCongViec';
import { DE_XUAT_XU_LY, GIAI_PHAP_XU_LY_PHA_DAT_THAP } from '../../DanhMuc/DeXuatXuLy';
import { TRANG_THAI_XU_LY } from '../../DanhMuc/TrangThaiXuLy';
import { TIA_TIEP_DIA } from '../../QuanLyVanHanh/DoDienTro/doDienTro.constants';
import { HUONG_DO, KET_LUAN } from '../../../constant/constant';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';

import {
  addIndexToListData,
  downlineData,
  groupBy,
  joinArrayString,
  removeDuplicateObject,
} from '../../../common/functionCommons';
import { extractIds, extractKeys } from '../../../utils/dataconverter';
import {
  formatDate,
  formatDateTime,
  formatTimeDate,
  formatToDateDetail,
} from '../../../common/formatUTCDateToLocalDate';

import { buildQuery, buildQueryNhanVien } from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';

function sortViTri(a, b) {
  return a.stt_van_hanh - b.stt_van_hanh;
}

function sortKhoangCot(a, b) {
  return a.vi_tri_ket_thuc_id?.thu_tu - b.vi_tri_ket_thuc_id?.thu_tu;
}

export async function getInfoPhieuGiaoViec(query) {
  let data = await PhieuGiaoViecModel.findById(query.id).populate([
    { path: 'chi_huy_truc_tiep_id', select: 'full_name alias chu_ky_id bac_an_toan chuc_vu' },
    { path: 'duong_day_ids', select: 'ten_duong_day' },
    { path: 'chuyen_de_id', select: 'ten_chuyen_de noi_dung_kiem_tra_id' },
    { path: 'nguoi_cap_phieu_id', populate: 'role_id', select: 'full_name chu_ky_id alias' },
    {
      path: 'nguoi_giao_phieu_id nguoi_tiep_nhan_id nguoi_khoa_phieu_id nguoi_xac_nhan_khoa_id',
      select: 'full_name chu_ky_id alias',
    },
    { path: 'may_do_id', populate: 'may_do_id' },
    {
      path: 'don_vi_giao_phieu_id',
      populate: {
        path: 'don_vi_cha_id',
        populate: { path: 'don_vi_cha_id', select: 'ten_don_vi' },
        select: 'ten_don_vi don_vi_cha_id ma_in ma_don_vi',
      },
      select: 'ten_don_vi don_vi_cha_id ma_in ma_don_vi',
    },
  ]).lean();
  addIndexToListData(data.duong_day_ids);

  const duongDayIds = extractIds(data.duong_day_ids);
  if (!data) {
    return null;
  }

  async function getBienPhapAnToan() {
    data.bien_phap_an_toan_cong_viec = await BienPhapAnToanCongViecService.getAll({
      phieu_giao_viec_id: data._id,
      is_deleted: false,
    }).populate('bien_phap_an_toan_id');
    data.bien_phap = data.bien_phap_an_toan_cong_viec.length > 0;
  }

  async function getDieuKienAnToan() {
    data.dieu_kien_an_toan_cong_viec = await DieuKienAnToanCongViecService.getAll({
      phieu_giao_viec_id: data._id,
      is_deleted: false,
    }).populate('dieu_kien_an_toan_id');
    data.dieu_kien = data.dieu_kien_an_toan_cong_viec.length > 0;
  }

  async function getToCongTac() {
    data.nguoi_cong_tac = await NguoiCongTacService.getAll({ phieu_giao_viec_id: data._id, is_deleted: false })
      .populate('user_id');
  }

  async function getNoiDungCongViec() {
    const allNoiDungCongViec = await NoiDungCongViecService.getAll({ phieu_giao_viec_id: data._id, is_deleted: false })
      .populate('cong_viec_id');
    data.noi_dung_cong_viec = allNoiDungCongViec.map((congViec, index) => {
      return {
        stt: index + 1,
        cong_viec: congViec.cong_viec_id?.ten_cong_viec,
        loai_cong_viec: LOAI_CONG_VIEC_SUA_CHUA[congViec.cong_viec_id?.loai_cong_viec]?.label,
        mo_ta: congViec.cong_viec_id?.mo_ta,
      };
    });
    data.cong_viec = data.noi_dung_cong_viec.length > 0;
  }

  async function updateCongViecPhuTro() {
    data.cong_viec_phu_tro = await CongViecPhuTroService
      .getAll({ phieu_giao_viec_id: data._id, is_deleted: false })
      .populate('cong_viec_id');
  }

  async function getCongViecPhatSinh() {
    data.cong_viec_phat_sinh = await CongViecPhatSinhService.getAll({ phieu_giao_viec_id: data._id, is_deleted: false })
      .populate('cong_viec_id khoang_cot_id vi_tri_id duong_day_id');
    data.show_phat_sinh = data.cong_viec_phat_sinh.length > 0;
  }

  async function getThongTinKhoangCot() {
    data.khoang_cot_cong_viec = await ViTriCongViecService.getAll({
      phieu_giao_viec_id: data._id,
      vi_tri_id: { $exists: false },
      khoang_cot_id: { $exists: true },
      is_deleted: false,
    }).populate({
      path: 'khoang_cot_id',
      populate: { path: 'vi_tri_bat_dau_id vi_tri_ket_thuc_id', select: 'ten_vi_tri thu_tu' },
      select: 'ten_khoang_cot vi_tri_bat_dau_id vi_tri_ket_thuc_id vi_tri_id',
    }).select({ trang_thai_hoan_thanh: 1, khoang_cot_id: 1, so_mach: 1, phieu_giao_viec_id: 1, is_deleted: 1 });

    const viTriIds = extractIds(data.khoang_cot_cong_viec.map(khoangCotCongViec => khoangCotCongViec?.khoang_cot_id?.vi_tri_id));
    const vanHanhData = await VanHanhService.findAllInOne({
      duong_day_id: { $in: duongDayIds },
      vi_tri_id: { $in: viTriIds },
      is_deleted: false,
    });

    const mapDuongDay = {};
    vanHanhData.forEach(vanHanh => {
      if (!mapDuongDay[vanHanh.duong_day_id._id]) {
        mapDuongDay[vanHanh.duong_day_id._id] = [];
      }
      mapDuongDay[vanHanh.duong_day_id._id] = [...mapDuongDay[vanHanh.duong_day_id._id], vanHanh.vi_tri_id.toString()];
    });

    const mapDuongDayKhoangCot = {};
    for (let i = 0; i < data.khoang_cot_cong_viec.length; i++) {
      const viTriId = data.khoang_cot_cong_viec[i]?.khoang_cot_id?.vi_tri_id;
      for (const [duongDayId, listViTriId] of Object.entries(mapDuongDay)) {
        if (listViTriId.includes(viTriId.toString())) {
          if (!mapDuongDayKhoangCot[duongDayId]) mapDuongDayKhoangCot[duongDayId] = [];
          mapDuongDayKhoangCot[duongDayId].push(data.khoang_cot_cong_viec[i].khoang_cot_id);
        }
      }
    }
    let allTenKhoangCot, allKhoangCot = [];

    Object.values(mapDuongDayKhoangCot).map(duongDay => {
      allKhoangCot = [...allKhoangCot, ...duongDay.sort(sortKhoangCot)];
      return duongDay.sort(sortKhoangCot);
    });

    let khoangCotIds = [], resultArr = [];
    for (const [duongDayId, listViTriId] of Object.entries(mapDuongDayKhoangCot)) {
      const checkExist = khoangCotIds.find(item => JSON.stringify(item) === JSON.stringify(listViTriId));
      if (checkExist) {
        resultArr.forEach(
          function(item, i) {
            if (JSON.stringify(item.khoang_cot) === JSON.stringify(listViTriId)) {
              resultArr[i].duong_day_id = [...resultArr[i].duong_day_id, duongDayId];
              resultArr[i].key = [resultArr[i].key, duongDayId].join('');
            }
          });
      } else {
        khoangCotIds.push(listViTriId);
        resultArr.push({
          duong_day_id: [duongDayId],
          key: duongDayId,
          khoang_cot: listViTriId,
        });
      }
    }

    removeDuplicateObject(allKhoangCot, '_id').forEach(khoangCot => {
      allTenKhoangCot ? allTenKhoangCot = [allTenKhoangCot, khoangCot.ten_khoang_cot].join(', ') : allTenKhoangCot = khoangCot.ten_khoang_cot;
    });
    data.khoang_cot_duong_day = resultArr;
    data.all_khoang_cot = allTenKhoangCot;
  }

  async function getThongTinViTri() {
    const viTriCongViec = await ViTriCongViecService
      .getAll({
        phieu_giao_viec_id: data._id,
        vi_tri_id: { $exists: true },
        khoang_cot_id: { $exists: false },
        is_deleted: false,
      })
      .populate({ path: 'vi_tri_id' });

    data.vi_tri_cong_viec = viTriCongViec?.sort((vt1, vt2) => vt1.vi_tri_id?.thu_tu - vt2.vi_tri_id?.thu_tu);
    const viTriIds = extractIds(data.vi_tri_cong_viec.map(viTriCongViec => viTriCongViec.vi_tri_id));
    const populateViTri = [{ path: 'vi_tri_id' }];
    const vanHanhData = await VanHanhService.findAllInOne({
      duong_day_id: { $in: duongDayIds },
      vi_tri_id: { $in: viTriIds },
      is_deleted: false,
    }, populateViTri);

    const mapDuongDay = {};
    vanHanhData.forEach(vanHanh => {
      if (!vanHanh.vi_tri_id.stt_van_hanh) {
        vanHanh.vi_tri_id.stt_van_hanh = vanHanh.thu_tu;
      }
      if (!mapDuongDay[vanHanh.duong_day_id._id]) {
        mapDuongDay[vanHanh.duong_day_id._id] = [];
      }
      mapDuongDay[vanHanh.duong_day_id._id].push(vanHanh.vi_tri_id);
    });

    let tempArray = [], resultArr = [];
    for (const [duongDayId, listViTris] of Object.entries(mapDuongDay)) {
      const checkExist = tempArray.find(item => JSON.stringify(item) === JSON.stringify(listViTris));
      if (checkExist) {
        resultArr.forEach((item, i) => {
          if (JSON.stringify(item.vi_tri) === JSON.stringify(listViTris)) {
            resultArr[i].duong_day_id = [...resultArr[i].duong_day_id, duongDayId];
            resultArr[i].key = [resultArr[i].key, duongDayId].join('');
          }
        });
      } else {
        tempArray.push(listViTris);
        resultArr.push({
          duong_day_id: [duongDayId],
          key: duongDayId,
          vi_tri: listViTris,
        });
      }
    }

    let allTenViTri, allViTri = [];
    Object.values(mapDuongDay).forEach(duongDay => {
      allViTri = [...allViTri, ...duongDay.sort(sortViTri)];
    });
    removeDuplicateObject(allViTri, '_id').forEach(viTri => {
      allTenViTri ? allTenViTri = [allTenViTri, viTri.ten_vi_tri].join(', ') : allTenViTri = viTri.ten_vi_tri;
    });
    data.vi_tri_duong_day = resultArr;
    data.all_vi_tri = allTenViTri;
  }

  async function getVitriChuaHoanThanh() {
    const viTriCongViec = await ViTriCongViecService.getAll({ phieu_giao_viec_id: data._id, is_deleted: false })
      .populate({ path: 'vi_tri_id', populate: 'cong_trinh_id' })
      .populate({ path: 'khoang_cot_id', populate: 'vi_tri_ket_thuc_id vi_tri_bat_dau_id' })
      .populate('cong_trinh_id');

    let viTriChuaHoanThanh = 0;
    for (let item of viTriCongViec) {
      if (item.trang_thai_hoan_thanh === 'Chưa hoàn thành') {
        viTriChuaHoanThanh += 1;
      }
    }
    data.vi_tri_chua_hoan_thanh = viTriChuaHoanThanh;
  }

  async function getCongTrinhCongViec() {
    data.cong_trinh_cong_viec = await CongTrinhCongViecService.getAll({
      phieu_giao_viec_id: data._id,
      is_deleted: false,
    })
      .populate('cong_trinh_id');
  }

  async function updateDanhSachTonTai() {
    const danhSachCongViec = await KetQuaSuaChuaCoKeHoachService.getAll({
      phieu_giao_viec_id: data._id,
      is_deleted: false,
    })
      .populate({
        path: 'ton_tai_id',
        populate: {
          path: 'tieu_chi_id vi_tri_id khoang_cot_id',
          select: 'ten_tieu_chi noi_dung_kiem_tra_id ten_vi_tri thu_tu ten_khoang_cot don_vi',
          populate: { path: 'noi_dung_kiem_tra_id', select: 'ten_noi_dung' },
        },
      }).lean();

    for (let i = 0; i < danhSachCongViec.length; i++) {
      const tonTaiId = await HieuChinhTonTaiService.getDetailHieuChinh([danhSachCongViec[i].ton_tai_id]);
      danhSachCongViec[i].ton_tai_id = tonTaiId[0];
    }

    data.danh_sach_ton_tai = danhSachCongViec;
  }

  async function getTinhHinhTonTai() {
    data.tinh_hinh_ton_tai = await TinhHinhTonTaiService.getTinhHinhTonTaiByPhieuGiaoViec(data._id);
  }

  const allPromises = [
    getBienPhapAnToan(),
    getDieuKienAnToan(),
    getToCongTac(),
    getNoiDungCongViec(),
    updateCongViecPhuTro(),
    getCongViecPhatSinh(),
    getThongTinKhoangCot(),
    getThongTinViTri(),
    getVitriChuaHoanThanh(),
    getCongTrinhCongViec(),
    updateDanhSachTonTai(),
    getTinhHinhTonTai(),
  ];
  await Promise.all(allPromises);

  data.phat_sinh = data.cong_viec_phat_sinh.map(convertCongViecPhatSinhToRow);
  data.ten_cong_viec = LOAI_CONG_VIEC[data.loai_cong_viec].name;
  data.ngay_bat_dau = formatToDateDetail(data.thoi_gian_ky_tiep_nhan ? data.thoi_gian_ky_tiep_nhan : data.thoi_gian_cong_tac_bat_dau);
  data.ngay_ket_thuc = formatToDateDetail(data.thoi_gian_ky_khoa_phieu);
  data.thoi_gian_cong_tac_bat_dau = formatTimeDate(data.thoi_gian_cong_tac_bat_dau);
  data.thoi_gian_cong_tac_ket_thuc = formatTimeDate(data.thoi_gian_cong_tac_ket_thuc);
  addIndexToListData(data.nguoi_cong_tac);

  function convertCongViecPhatSinhToRow(congViec, index) {
    let viTriKhoangCot;
    congViec.vi_tri_id?.map(viTri => {
      viTriKhoangCot ? viTriKhoangCot = [viTriKhoangCot, viTri.ten_vi_tri].join(', ') : viTriKhoangCot = viTri.ten_vi_tri;
    });
    congViec.khoang_cot_id?.map(viTri => {
      viTriKhoangCot ? viTriKhoangCot = [viTriKhoangCot, viTri.ten_khoang_cot].join(', ') : viTriKhoangCot = viTri.ten_khoang_cot;
    });

    return {
      stt: index + 1,
      vi_tri_or_khoang_cot: viTriKhoangCot,
      ten_cong_viec: congViec.cong_viec_id?.ten_cong_viec,
      ten_duong_day: congViec.duong_day_id?.ten_duong_day,
      khoi_luong: congViec.khoi_luong,
      ghi_chu: congViec.ghi_chu,
    };
  }

  const mapTenDuongDay = {}, mapViTri = {};
  data.duong_day_ids.forEach(item => {
    mapTenDuongDay[item._id.toString()] = item.ten_duong_day;
  });
  data.vi_tri_duong_day.forEach(item => {
    mapViTri[item.key] = item.vi_tri;
  });

  if (!!data.khoang_cot_duong_day.length) {
    data.khoang_cot_duong_day.forEach(item => {
      let tenDuongDay, tenViTri, tenKhoangCot;
      item.duong_day_id.forEach(duongDay => {
        if (!tenDuongDay) {
          tenDuongDay = mapTenDuongDay[duongDay];
        } else {
          tenDuongDay = [tenDuongDay, mapTenDuongDay[duongDay]].join(', ');
        }
      });
      !!mapViTri[item.key] && Object.values(mapViTri[item.key])?.forEach(viTri => {
        if (!tenViTri) {
          tenViTri = viTri.ten_vi_tri;
        } else {
          tenViTri = [tenViTri, viTri.ten_vi_tri].join(', ');
        }
      });
      item.khoang_cot.forEach(khoangCot => {
        if (!tenKhoangCot) {
          tenKhoangCot = khoangCot.ten_khoang_cot;
        } else {
          tenKhoangCot = [tenKhoangCot, khoangCot.ten_khoang_cot].join(', ');
        }
      });
      item.ten_duong_day = tenDuongDay;
      item.ten_vi_tri = tenViTri;
      item.ten_khoang_cot = tenKhoangCot;
    });
  } else {
    data.vi_tri_duong_day.forEach(item => {
      let tenDuongDay, tenViTri, tenKhoangCot;
      item.duong_day_id.forEach(duongDay => {
        if (!tenDuongDay) {
          tenDuongDay = mapTenDuongDay[duongDay];
        } else {
          tenDuongDay = [tenDuongDay, mapTenDuongDay[duongDay]].join(', ');
        }
      });
      item.vi_tri.forEach(viTri => {
        if (!tenViTri) {
          tenViTri = viTri.ten_vi_tri;
        } else {
          tenViTri = [tenViTri, viTri.ten_vi_tri].join(', ');
        }
      });
      item.ten_duong_day = tenDuongDay;
      item.ten_vi_tri = tenViTri;
      item.ten_khoang_cot = tenKhoangCot;
    });
  }
  !data.khoang_cot_duong_day.length && (data.khoang_cot_duong_day = data.vi_tri_duong_day);
  addIndexToListData(data.khoang_cot_duong_day);
  return data;
}

export async function getPhieuKiemTraDuongDayByID(query) {
  let data = await getInfoPhieuGiaoViec(query);
  const ketQuaTonTai = await KetQuaKiemTraService.getAll({ phieu_giao_viec_id: data._id, is_deleted: false })
    .populate('vi_tri_id khoang_cot_id duong_day_id')
    .populate({ path: 'tieu_chi_id', populate: 'noi_dung_kiem_tra_id tieu_chi_cha_id' });

  const allNoiDungKiemTra = await NoiDungKiemTraService.getAll({ is_deleted: false });
  const groupByNoiDungKiemTra = groupBy(ketQuaTonTai.map(convertDataTonTaiToRow), 'noi_dung_kiem_tra_id');

  allNoiDungKiemTra.forEach((noiDungKiemTra) => {
    noiDungKiemTra.ton_tai = groupByNoiDungKiemTra[noiDungKiemTra._id];
    noiDungKiemTra.trang_thai = !!noiDungKiemTra.ton_tai;
  });
  let noiDungKiemTra = [];
  if (data.chuyen_de_id) {
    allNoiDungKiemTra.forEach(item => {
      data.chuyen_de_id?.noi_dung_kiem_tra_id.forEach(noiDungId => {
        if (noiDungId.toString() === item._id.toString()) {
          noiDungKiemTra.push(item);
        }
      });
    });
  }

  data.ket_qua = data.chuyen_de_id ? addIndexToListData(noiDungKiemTra) : addIndexToListData(allNoiDungKiemTra);

  if (data.loai_cong_viec === LOAI_CONG_VIEC.CO_KE_HOACH.code
    || data.loai_cong_viec === LOAI_CONG_VIEC.DUY_TU_KET_HOP_KHAC_PHUC.code) {
    let stringViTri, stringKhoangCot;
    data.danh_sach_ton_tai?.map(item => {
      const viTri = item.ton_tai_id?.vi_tri_id;
      const khoangCot = item.ton_tai_id?.khoang_cot_id;
      if (!!viTri) {
        stringViTri ? stringViTri = [stringViTri, viTri?.ten_vi_tri].join(', ') : stringViTri = viTri?.ten_vi_tri;
      }
      if (!!khoangCot) {
        stringKhoangCot ? stringKhoangCot = [stringKhoangCot, khoangCot?.ten_khoang_cot].join(', ') : stringKhoangCot = khoangCot?.ten_khoang_cot;
      }
    });
    data.all_khoang_cot = stringKhoangCot;
    data.all_vi_tri = stringViTri;
  }
  data.show_tinh_hinh_ton_tai = data.tinh_hinh_ton_tai?.length > 0;
  data.tinh_hinh_ton_tai = data.tinh_hinh_ton_tai?.map(convertTinhHinh);

  function convertDataTonTaiToRow(tonTai) {
    let tieuChi;
    let tieuChiId = tonTai.tieu_chi_id;
    tieuChiId?.tieu_chi_cha_id ? tieuChi = [tieuChiId.tieu_chi_cha_id.ten_tieu_chi, tieuChiId.ten_tieu_chi].join('-> ') : tieuChi = tieuChiId.ten_tieu_chi;

    return {
      vi_tri_or_khoang_cot: tonTai.vi_tri_id?.ten_vi_tri || tonTai.khoang_cot_id?.ten_khoang_cot,
      ten_vi_tri: tonTai.vi_tri_id?.ten_vi_tri,
      ten_khoang_cot: tonTai.khoang_cot_id?.ten_khoang_cot,
      duong_day_id: tonTai.duong_day_id,
      khoi_luong: tonTai.khoi_luong,
      tieu_chi: tieuChi,
      noi_dung_kiem_tra_id: tonTai.tieu_chi_id?.noi_dung_kiem_tra_id._id,
      noi_dung_chi_tiet: tonTai.noi_dung_chi_tiet,
      de_xuat_to_cong_tac: tonTai.de_xuat_to_cong_tac,
      ghi_chu: tonTai.noi_dung_chi_tiet,
      thoi_han_xu_ly_to_cong_tac: tonTai.thoi_han_xu_ly_to_cong_tac ? formatDate(tonTai.thoi_han_xu_ly_to_cong_tac) : '',
    };
  }

  function convertHieuChinh(hieuChinh) {
    return {
      khoi_luong: hieuChinh.khoi_luong,
      phieu_giao_viec_id: hieuChinh.phieu_giao_viec_id,
      noi_dung_chi_tiet: hieuChinh.noi_dung_chi_tiet,
      thoi_gian_tao: hieuChinh.thoi_gian_tao ? formatTimeDate(hieuChinh.thoi_gian_tao) : '',
    };
  }

  function convertTinhHinh(tinhHinh, index) {
    let duongDay, tieuChi;
    tinhHinh.duong_day_id?.forEach(item => {
      duongDay ? duongDay = [duongDay, item.ten_duong_day].join('\n') : duongDay = item.ten_duong_day;
    });
    let tieuChiId = tinhHinh.tieu_chi_id;
    tieuChiId?.tieu_chi_cha_id ? tieuChi = [tieuChiId.tieu_chi_cha_id.ten_tieu_chi, tieuChiId.ten_tieu_chi].join('-> ') : tieuChi = tieuChiId.ten_tieu_chi;
    tinhHinh.hieu_chinh_ton_tai_id?.map(item => {
      item.thoi_gian_tao = item.thoi_gian_tao ? formatTimeDate(item.thoi_gian_tao) : '';
      return item;
    });
    let hieuChinh = tinhHinh.hieu_chinh_ton_tai_id?.map(convertHieuChinh);
    return {
      stt: index + 1,
      duong_day: duongDay,
      vi_tri_khoang_cot: tinhHinh.vi_tri_id?.ten_vi_tri || tinhHinh.khoang_cot_id?.ten_khoang_cot,
      tieu_chi: tieuChi,
      khoi_luong: tinhHinh.khoi_luong,
      mo_ta_chi_tiet: tinhHinh.mo_ta_chi_tiet,
      thoi_gian_phat_hien: tinhHinh.thoi_gian_tao ? formatTimeDate(tinhHinh.thoi_gian_tao) : '',
      hieu_chinh: hieuChinh,
    };
  }

  return data;
}

export async function getSuaChuaBaoDuongByID(query) {

  function convertKetQuaKhongKeHoachToRow(khongKeHoach, index) {
    return {
      stt: index + 1,
      so_phieu: khongKeHoach.phieu_giao_viec_id.so_phieu,
      vi_tri_or_khoang_cot: khongKeHoach.vi_tri_id?.ten_vi_tri || khongKeHoach.khoang_cot_id?.ten_khoang_cot,
      ten_cong_viec: khongKeHoach.cong_viec_id?.ten_cong_viec,
      khoi_luong: khongKeHoach.khoi_luong,
      ghi_chu: khongKeHoach.noi_dung_chi_tiet,
      cong_viec_id: khongKeHoach.cong_viec_id._id,
    };
  }

  function convertKetQuaCoKeHoachToRow(coKeHoach, index) {
    const viTri = coKeHoach.ton_tai_id?.vi_tri_id;
    const khoangCot = coKeHoach.ton_tai_id?.khoang_cot_id;
    if (!!viTri) {
      stringViTri ? stringViTri = [stringViTri, viTri?.ten_vi_tri].join(', ') : stringViTri = viTri?.ten_vi_tri;
    }
    if (!!khoangCot) {
      stringKhoangCot ? stringKhoangCot = [stringKhoangCot, khoangCot?.ten_khoang_cot].join(', ') : stringKhoangCot = khoangCot?.ten_khoang_cot;
    }
    return {
      stt: index + 1,
      hang_muc: coKeHoach.ton_tai_id?.tieu_chi_id?.noi_dung_kiem_tra_id?.ten_noi_dung,
      ten_tieu_chi: coKeHoach.ton_tai_id?.tieu_chi_id?.ten_tieu_chi,
      duong_day_id: coKeHoach.ton_tai_id?.duong_day_id,
      vi_tri_or_khoang_cot: viTri?.ten_vi_tri || khoangCot?.ten_khoang_cot,
      ten_vi_tri: viTri?.ten_vi_tri,
      ten_khoang_cot: khoangCot?.ten_khoang_cot,
      noi_dung_chi_tiet: coKeHoach.ton_tai_id?.noi_dung_chi_tiet,
      de_xuat_cap_doi: DE_XUAT_XU_LY[coKeHoach.ton_tai_id?.de_xuat_cap_doi]?.label,
      tinh_trang_xu_ly: TRANG_THAI_XU_LY[coKeHoach.tinh_trang_xu_ly]?.label,
      khoi_luong: coKeHoach.khoi_luong,
      ghi_chu: coKeHoach.ghi_chu,
    };
  }

  let data = await getInfoPhieuGiaoViec(query);
  const ketQuaSuaChuaKhongKeHoach = await KetQuaSuaChuaKhongKeHoachService.getAll({
    phieu_giao_viec_id: data._id,
    is_deleted: false,
  }).populate('phieu_giao_viec_id cong_viec_id vi_tri_id khoang_cot_id vi_tri_id');

  const ketQuaSuaChuaCoKeHoach = await KetQuaSuaChuaCoKeHoachService.getAll({
    phieu_giao_viec_id: data._id,
    is_deleted: false,
  }).populate({
    path: 'ton_tai_id', populate: [
      { path: 'vi_tri_id khoang_cot_id duong_day_id' },
      { path: 'tieu_chi_id', populate: { path: 'noi_dung_kiem_tra_id' } },
    ],
  });

  let stringViTri, stringKhoangCot;
  const ketQuaCoKeHoach = ketQuaSuaChuaCoKeHoach.map(convertKetQuaCoKeHoachToRow);
  const ketQuaKhongKeHoach = ketQuaSuaChuaKhongKeHoach.map(convertKetQuaKhongKeHoachToRow);
  let allTenViTri = [], allTenKhoangCot = [];
  data.vi_tri_cong_viec.forEach(vtcv => {
    allTenViTri.push(vtcv.vi_tri_id?.ten_vi_tri);
  });

  data.khoang_cot_cong_viec.forEach(vtcv => {
    allTenKhoangCot.push(vtcv.khoang_cot_id?.ten_khoang_cot);
  });
  data.danh_sach_ton_tai.forEach(tontai => {
    const tonTaiId = tontai.ton_tai_id;
    tonTaiId.vi_tri_id && allTenViTri.push(tonTaiId?.vi_tri_id?.ten_vi_tri);
    tonTaiId.khoang_cot_id && allTenKhoangCot.push(tonTaiId?.khoang_cot_id?.ten_khoang_cot);
  });

  switch (data.loai_cong_viec) {
    case LOAI_CONG_VIEC.CO_KE_HOACH.code:
      data.ket_qua_sua_chua = ketQuaCoKeHoach;
      data.all_khoang_cot = stringKhoangCot;
      data.all_vi_tri = stringViTri;
      break;
    case LOAI_CONG_VIEC.KHONG_CO_KE_HOACH.code:
      data.ket_qua_sua_chua = ketQuaKhongKeHoach;
      break;
    case LOAI_CONG_VIEC.DUY_TU_KET_HOP_KHAC_PHUC.code:
      data.ket_qua_co_ke_hoach = ketQuaCoKeHoach;
      data.ket_qua_khong_ke_hoach = ketQuaKhongKeHoach;
      data.all_khoang_cot = joinArrayString([...new Set(allTenKhoangCot)], ', ');
      data.all_vi_tri = joinArrayString([...new Set(allTenViTri)], ', ');
      break;
  }

  return data;
}

export async function getDoDienTroByID(query) {
  let data = await getInfoPhieuGiaoViec(query);
  const doDienTro = await DoDienTroService.getAll({ phieu_giao_viec_id: data._id })
    .populate({ path: 'phieu_giao_viec_id', select: 'so_phieu' })
    .populate({ path: 'dia_hinh_id', select: 'ten_dia_hinh' })
    .populate('nguoi_tao_id')
    .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });
  let maxLength = 1;
  doDienTro.map(phieu => {
    TIA_TIEP_DIA.forEach((tiaTiepDia, index) => {
      if (phieu.tri_so_tia_tiep_dia?.[tiaTiepDia.code]) {
        maxLength = maxLength < index + 1 ? index + 1 : maxLength;
      }
    });
  });
  const viTriIds = doDienTro.map(item => item.vi_tri_id._id);
  const cotDienData = await CotDienService.getAll({ vi_tri_id: viTriIds, is_deleted: false });
  const tiepDatData = await TiepDatService.getAll({ vi_tri_id: viTriIds, is_deleted: false });

  doDienTro.forEach(dienTro => {
    dienTro.chieu_cao_cot = cotDienData.find(cotDien => cotDien.vi_tri_id?.toString() === dienTro.vi_tri_id._id.toString())?.chieu_cao;
    dienTro.loai_tiep_dia = tiepDatData.find(tiepDat => tiepDat.vi_tri_id?.toString() === dienTro.vi_tri_id._id.toString())?.ten_tiep_dat;
  });

  data.so_tia = maxLength;
  data.do_dien_tro = doDienTro.map(convertDBtoRow);

  function convertDBtoRow(phieuDo, index) {
    return {
      stt: index + 1,
      ten_vi_tri: phieuDo.vi_tri_id?.ten_vi_tri,
      chieu_cao_cot: phieuDo.chieu_cao_cot,
      loai_tiep_dia: phieuDo.loai_tiep_dia,
      tri_so_tia_tiep_dia: phieuDo.tri_so_tia_tiep_dia,
      dac_diem_dia_hinh: phieuDo.vi_tri_id?.dac_diem_dia_hinh,
      nguoi_do: phieuDo.nguoi_tao_id.full_name,
      dien_tro_he_thong: phieuDo.dien_tro_he_thong,
      dien_tro_dat: phieuDo.dien_tro_dat,
      tro_suat_cua_dat: phieuDo.tro_suat_cua_dat,
      dien_tro_theo_quy_pham: phieuDo.dien_tro_theo_quy_pham,
      ngay_do: phieuDo.ngay_do ? formatDate(phieuDo.ngay_do) : '',
      ten_dia_hinh: phieuDo.dia_hinh_id?.ten_dia_hinh,
      ket_luan: KET_LUAN?.[phieuDo.ket_luan]?.label,
      ghi_chu: phieuDo.ghi_chu,
      huong_do: phieuDo.huong_do,
      dien_tro_dat_quy_pham: phieuDo.dien_tro_dat_quy_pham,
    };
  }

  return data;
}

export async function getDoPhaDatByID(query) {
  let data = await getInfoPhieuGiaoViec(query);
  const doPhaDat = await DoPhaDatService.getAll({ phieu_giao_viec_id: data._id })
    .populate({ path: 'khoang_cot_id' });

  data.do_pha_dat = doPhaDat.map(convertDataToRows);

  function convertDataToRows(phaDat, index) {
    return {
      stt: index + 1,
      khoang_cot_id: phaDat.khoang_cot_id,
      ngay_do: phaDat.ngay_do ? formatDate(phaDat.ngay_do) : '',
      khoang_cach_pha_dat: phaDat.khoang_cach_pha_dat,
      khoang_cach_theo_quy_pham: phaDat.khoang_cach_theo_quy_pham,
      dong_tai_khi_do: phaDat.dong_tai_khi_do,
      nhiet_do_moi_truong: phaDat.nhiet_do_moi_truong,
      dac_diem_khu_vuc: phaDat.dac_diem_khu_vuc,
      ket_luan: KET_LUAN?.[phaDat.ket_luan]?.label,
      de_xuat_xu_ly: GIAI_PHAP_XU_LY_PHA_DAT_THAP[phaDat.de_xuat_xu_ly]?.label,
    };
  }

  return data;
}

export async function getDoNhietDoByID(query) {
  let dataPhieuGiaoViec = await getInfoPhieuGiaoViec(query);
  let viTriCongViecId = [], khoangCotCongViecId = [];
  dataPhieuGiaoViec.vi_tri_cong_viec.forEach(viTri => {
    viTri.vi_tri_id && viTriCongViecId.push(viTri);
  });
  dataPhieuGiaoViec.khoang_cot_cong_viec.forEach(khoangCot => {
    khoangCot.khoang_cot_id && khoangCotCongViecId.push(khoangCot);
  });
  const nguoiCongTac = await NGUOI_CONG_TAC.find({ phieu_giao_viec_id: dataPhieuGiaoViec?._id, is_deleted: false })
    .populate('user_id');
  const userId = nguoiCongTac.map(item => item.user_id);
  const toCongTac = [dataPhieuGiaoViec.chi_huy_truc_tiep_id, ...userId];
  const doNhietDo = await DoNhietDoService.getAll({ phieu_giao_viec_id: dataPhieuGiaoViec?._id });

  let tenDuongDay;
  dataPhieuGiaoViec.duong_day_ids.forEach(duongDay => {
    tenDuongDay ? tenDuongDay = [tenDuongDay, duongDay.ten_duong_day].join([' - ']) : tenDuongDay = duongDay.ten_duong_day;
  });
  dataPhieuGiaoViec = convertDataPhieuGiaoViec(dataPhieuGiaoViec);

  const ketQuaDoNhietDo = await DoNhietDoChiTietService.getAll({ ket_qua_do_nhiet_do_id: extractIds(doNhietDo) })
    .populate({
      path: 'ket_qua_do_nhiet_do_id',
      populate: [
        { path: 'nguoi_do', select: 'full_name' },
        { path: ' vi_tri_id  khoang_cot_id' },
      ],
    });

  ketQuaDoNhietDo.forEach(ketQua => {
    ketQua.vi_tri_id = ketQua.ket_qua_do_nhiet_do_id?.vi_tri_id;
    ketQua.khoang_cot_id = ketQua.ket_qua_do_nhiet_do_id?.khoang_cot_id;
  });

  let ketQuaByViTri = [], ketQuaByKhoangCot = [];
  ketQuaDoNhietDo.forEach(ketQua => {
    if (ketQua.vi_tri_id) {
      ketQuaByViTri.push(ketQua);
      ketQua.vi_tri = ketQua.vi_tri_id._id;
    } else {
      ketQuaByKhoangCot.push(ketQua);
      ketQua.khoang_cot = ketQua.khoang_cot_id._id;
    }
  });

  const ketQuaGroupByViTriId = groupBy(ketQuaByViTri, 'vi_tri');
  const ketQuaGroupByKhoangCotId = groupBy(ketQuaByKhoangCot, 'khoang_cot');

  viTriCongViecId.forEach((item) => {
    item.ket_qua_do = ketQuaGroupByViTriId[item.vi_tri_id._id]?.map(convertKetQuaDoToRows);
  });
  const viTriCongViec = viTriCongViecId.filter(item => item.ket_qua_do).sort(sortViTri);
  viTriCongViec.map((item, index) => {
    item.stt = index + 1;
    item.vi_tri_id = item.vi_tri_id?.ten_vi_tri;
  });

  khoangCotCongViecId.forEach(item => {
    item.ket_qua_do = ketQuaGroupByKhoangCotId[item.khoang_cot_id._id]?.map(convertKetQuaDoToRows);
  });
  const khoangCotCongViec = khoangCotCongViecId.filter(item => item.ket_qua_do).sort(sortKhoangCot);
  khoangCotCongViec.forEach((item, index) => {
    item.stt = index + 1;
    item.khoang_cot_id = item.khoang_cot_id.ten_khoang_cot;
  });
  dataPhieuGiaoViec.ket_qua = DoNhietDoService.templateDataDoNhietDo(viTriCongViec, khoangCotCongViec);
  dataPhieuGiaoViec.ten_duong_day = tenDuongDay;

  function convertDataPhieuGiaoViec(phieuGiaoViec) {
    return {
      duong_day: phieuGiaoViec.duong_day_ids,
      version: doNhietDo[0]?.version,
      don_vi_giao_phieu_id: phieuGiaoViec.don_vi_giao_phieu_id,
      may_do: phieuGiaoViec.may_do_id?.may_do_id?.ten_may,
      so_phieu: phieuGiaoViec.so_phieu,
      chi_huy_truc_tiep_id: phieuGiaoViec.chi_huy_truc_tiep_id?.full_name,
      nguoi_giao_phieu_id: phieuGiaoViec.nguoi_giao_phieu_id,
    };
  }

  function convertKetQuaDoToRows(ketQuaDo) {
    let nguoiDo;
    if (!!ketQuaDo.ket_qua_do_nhiet_do_id?.nguoi_do.length) {
      ketQuaDo.ket_qua_do_nhiet_do_id?.nguoi_do.forEach(user => {
        nguoiDo ? nguoiDo = [nguoiDo, user.full_name].join('\n- ') : nguoiDo = `- ${user.full_name}`;
      });
    } else {
      toCongTac
        .forEach(user => {
          nguoiDo ? nguoiDo = [nguoiDo, user.full_name].join('\n- ') : nguoiDo = `- ${user.full_name}`;
        });
    }

    return {
      mach: ketQuaDo.mach,
      day_dan_phan_pha: `Dây ${ketQuaDo.day_dan_phan_pha}`,
      huong_do: HUONG_DO[ketQuaDo.huong_do],
      pha_a: ketQuaDo.pha_a,
      pha_b: ketQuaDo.pha_b,
      pha_c: ketQuaDo.pha_c,

      day_pha: ketQuaDo.day_pha,
      xuat_tuyen: ketQuaDo.xuat_tuyen,
      nhiet_do_day_dan: ketQuaDo.nhiet_do_day_dan,
      order: ketQuaDo.order,
      version: ketQuaDo.ket_qua_do_nhiet_do_id.version,

      dong_tai: ketQuaDo.dong_tai,
      danh_gia: ketQuaDo.danh_gia ? KET_LUAN[ketQuaDo.danh_gia].label : '',
      nguoi_do: nguoiDo,
      ghi_chu: ketQuaDo.ket_qua_do_nhiet_do_id?.ghi_chu,
      nhiet_do_moi_truong: ketQuaDo.ket_qua_do_nhiet_do_id?.nhiet_do_moi_truong || ketQuaDo.nhiet_do_moi_truong,
      thoi_gian_do: ketQuaDo.ket_qua_do_nhiet_do_id?.thoi_gian_do ? formatDateTime(ketQuaDo.ket_qua_do_nhiet_do_id?.thoi_gian_do) : '',
    };
  }

  return dataPhieuGiaoViec;
}

export async function danhSachPhieuKiemTra(req) {
  const { criteria } = await buildQuery(req, true);
  let fileTitle;
  switch (criteria.loai_phieu) {
    case KIEM_TRA:
      fileTitle = `${i18next.t('DANH_SACH')} ${i18next.t(KIEM_TRA)}`.toUpperCase();
      break;
    case SUA_CHUA_BAO_DUONG:
      fileTitle = `${i18next.t('DANH_SACH')} ${i18next.t(SUA_CHUA_BAO_DUONG)}`.toUpperCase();
      break;
    case DO_THONG_SO:
      fileTitle = `${i18next.t('DANH_SACH')} ${i18next.t(DO_THONG_SO)}`.toUpperCase();
      break;
    case CONG_TAC_PHU_TRO:
      fileTitle = `${i18next.t('DANH_SACH')} ${i18next.t(CONG_TAC_PHU_TRO)}`.toUpperCase();
      break;
    default:
  }
  if (!criteria.loai_cong_viec) {
    let loaiCongViec = Object.values(LOAI_CONG_VIEC).filter(congViec => congViec.type === criteria.loai_phieu);
    const loaiCongViecCodes = loaiCongViec.map(loai => loai.code);
    criteria.loai_cong_viec = { $in: loaiCongViecCodes };
  }
  if (criteria.duong_day_ids) {
    criteria.duong_day_ids = Types.ObjectId(criteria.duong_day_ids);
  }
  delete criteria.loai_phieu;
  const criteriaNhanVien = await buildQueryNhanVien(req, criteria);
  const dataPhieuGiaoViec = await reportDanhSachPhieuGiaoViec(criteriaNhanVien);

  function convertToDownload(row) {
    let allViTri = '', allKhoangCot = '', allDuongDay = '';
    row.vitris?.forEach(vt => {
      !allViTri ? (allViTri = vt.ten_vi_tri) : allViTri = [allViTri, vt.ten_vi_tri].join(', ');
    });
    row.khoangcots?.forEach(kc => {
      !allKhoangCot ? (allKhoangCot = kc.ten_khoang_cot) : allKhoangCot = [allKhoangCot, kc.ten_khoang_cot].join(', ');
    });

    row.duongdays?.forEach(dd => {
      !allDuongDay ? (allDuongDay = `- ${dd.ten_duong_day}`) : allDuongDay = downlineData(allDuongDay, `- ${dd.ten_duong_day}`);
    });
    const viTriCongViec = downlineData(`- Vị trí: ${allViTri}`, `- Khoảng cột: ${allKhoangCot}`);
    return {
      don_vi_giao_phieu_id: row.donvi,
      nguoi_cap_phieu_id: row.nguoicapphieu,
      so_phieu: row.so_phieu,
      thoi_gian_tao_phieu: formatTimeDate(row.created_at),
      thoi_gian_ky_tiep_nhan: formatTimeDate(row.thoi_gian_ky_tiep_nhan),
      thoi_gian_ky_khoa_phieu: formatTimeDate(row.thoi_gian_ky_khoa_phieu),
      thoi_gian_xac_nhan_khoa_phieu: formatTimeDate(row.thoi_gian_xac_nhan_khoa_phieu),
      trang_thai_cong_viec: TRANG_THAI_PHIEU[row.trang_thai_cong_viec].name,
      loai_cong_viec: LOAI_CONG_VIEC[row.loai_cong_viec].name,
      vi_tri_cong_viec: viTriCongViec,
      duong_day: allDuongDay,
    };
  }

  let queryToRender = [];
  const donViQuery = await DonViService.getById(req.query.don_vi_giao_phieu_id ? req.query.don_vi_giao_phieu_id : req.user.don_vi_id);
  queryToRender.push({ label: 'Đơn vị', value: donViQuery.ten_don_vi });
  if (req.query.duong_day_ids) {
    const duongDay = await DuongDayService.getById(req.query.duong_day_ids);
    queryToRender.push({ label: 'Đường dây', value: duongDay.ten_duong_day });
  }
  req.query.trang_thai_cong_viec && queryToRender.push({
    label: 'Trạng thái công việc',
    value: TRANG_THAI_PHIEU[req.query.trang_thai_cong_viec].name,
  });
  req.query.loai_cong_viec && queryToRender.push({
    label: 'Loại công việc',
    value: LOAI_CONG_VIEC[req.query.loai_cong_viec].name,
  });

  req.query['created_at>'] && queryToRender.push({ label: 'Từ ngày', value: formatDate(req.query['created_at>']) });
  req.query['created_at<'] && queryToRender.push({ label: 'Đến ngày', value: formatDate(req.query['created_at<']) });
  req.query.tu_ngay && queryToRender.push({ label: 'Từ ngày', value: formatDate(req.query.tu_ngay) });
  req.query.den_ngay && queryToRender.push({ label: 'Đến ngày', value: formatDate(req.query.den_ngay) });
  req.query.so_phieu && queryToRender.push({ label: 'Số phiếu', value: req.query.so_phieu });
  req.query.ten_nhan_vien && queryToRender.push({ label: 'Nhân viên', value: req.query.ten_nhan_vien });


  return {
    title: fileTitle,
    query: queryToRender,
    danh_sach: addIndexToListData(dataPhieuGiaoViec.map(convertToDownload)),
  };
}

export async function reportDanhSachPhieuGiaoViec(queryPhieuGiaoViec) {
  return PHIEU_GIAO_VIEC.aggregate([
    {
      $match: queryPhieuGiaoViec,
    },
    {
      $sort: {
        created_at: -1,
      },
    },
    // Join đến bảng Đơn vị để lấy tên đơn vị
    {
      $lookup: {
        from: 'DonVi',
        localField: 'don_vi_giao_phieu_id',
        foreignField: '_id',
        as: 'donvi',
      },
    },
    {
      $unwind: {
        path: '$donvi',
      },
    },
    {
      $lookup: {
        from: 'DuongDay',
        localField: 'duong_day_ids',
        foreignField: '_id',
        as: 'duongdays',
      },
    },

    // Join đến bảng User
    {
      $lookup: {
        from: 'User',
        localField: 'nguoi_cap_phieu_id',
        foreignField: '_id',
        as: 'nguoicapphieu',
      },
    },
    // Join đến bảng Vị trí công việc
    {
      $lookup: {
        from: 'ViTriCongViec',
        localField: '_id',
        foreignField: 'phieu_giao_viec_id',
        as: 'vitricongviecs',
      },
    },
    {
      $set: {
        vitriids: '$vitricongviecs.vi_tri_id',
        khoangcotids: '$vitricongviecs.khoang_cot_id',
      },
    },
    //Join bảng Khoảng cột
    {
      $lookup: {
        from: 'KhoangCot',
        localField: 'khoangcotids',
        foreignField: '_id',
        as: 'khoangcots',
      },
    },
    {
      $lookup: {
        from: 'ViTri',
        localField: 'vitriids',
        foreignField: '_id',
        as: 'vitris',
      },
    },
    {
      $project: {
        'donvi.ten_don_vi': 1,
        'nguoicapphieu.full_name': 1,
        'vitris.ten_vi_tri': 1,
        'duongdays.ten_duong_day': 1,
        'khoangcots.ten_khoang_cot': 1,
        'so_phieu': 1,
        'thoi_gian_tao_phieu': 1,
        'trang_thai_cong_viec': 1,
        'loai_cong_viec': 1,
        'created_at': 1,
        'thoi_gian_ky_tiep_nhan': 1,
        'thoi_gian_ky_khoa_phieu': 1,
        'thoi_gian_xac_nhan_khoa_phieu': 1,
      },
    },

  ]);
}


export async function getKetQuaDoCorocamByPhieu(phieuId) {
  let data = await getInfoPhieuGiaoViec({ id: phieuId });

  const doCorocam = await KetQuaDoCorocamService.getAll({ phieu_giao_viec_id: phieuId, is_deleted: false })
    .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });

  data.thiet_bi_do = data.may_do_id?.may_do_id?.ten_may || 'sdkjfsnjdv';
  data.do_corocam = doCorocam.map((row, index) => {
    return {
      stt: index + 1,
      vi_tri_id: row.vi_tri_id,
      huong_do: HUONG_DO[row.huong_do],
      pha: row.pha,
      nhiet_do_moi_truong: row.nhiet_do_moi_truong,
      dong_tai: row.dong_tai,
      nguoi_do: row.nguoi_do,
      thoi_gian_do: formatTimeDate(row.thoi_gian_do),
      gia_tri_do: row.gia_tri_do,
      danh_gia: KET_LUAN?.[row.danh_gia]?.label,
      ghi_chu: row.ghi_chu,
    };
  });

  return data;
}

export async function getKetQuaDoNhietDoCompositeByPhieu(phieuId) {
  let data = await getInfoPhieuGiaoViec({ id: phieuId });

  const doCorocam = await KetQuaDoNhietDoCompositeService.getAll({ phieu_giao_viec_id: phieuId, is_deleted: false })
    .populate([
      { path: 'vi_tri_id', select: 'ten_vi_tri' },
      { path: 'duong_day_id', select: 'ten_duong_day' },
    ]);

  data.thiet_bi_do = data.may_do_id?.may_do_id?.ten_may || '';
  data.do_corocam = doCorocam.map((row, index) => {
    return {
      stt: index + 1,
      vi_tri_id: row.vi_tri_id,
      duong_day_id: row.duong_day_id,
      huong_do: HUONG_DO[row.huong_do],
      pha: row.pha,
      nhiet_do_moi_truong: row.nhiet_do_moi_truong,
      dong_tai: row.dong_tai,
      nguoi_do: row.nguoi_do,
      thoi_gian_do: formatTimeDate(row.thoi_gian_do),
      nhiet_do_diem_phat_nhiet: row.nhiet_do_diem_phat_nhiet,
      danh_gia: KET_LUAN?.[row.danh_gia]?.label,
      ghi_chu: row.ghi_chu,
    };
  });

  return data;
}

export async function getKetQuaDoCuongDoDienTruongByPhieu(phieuId) {
  let data = await getInfoPhieuGiaoViec({ id: phieuId });

  const ketQua = await KetQuaDoCuongDoDienTruongService.getAll({ phieu_giao_viec_id: phieuId, is_deleted: false })
    .populate([
      { path: 'vi_tri_id', select: 'ten_vi_tri' },
      { path: 'khoang_cot_id', select: 'ten_khoang_cot' },
    ]);

  data.ten_duong_day = data.duong_day_ids?.ten_duong_day || '';
  data.thiet_bi_do = data.may_do_id?.may_do_id?.ten_may || '';
  data.ket_qua = ketQua.map((row, index) => {
    return {
      ...row,
      stt: index + 1,
      ngay_do: formatTimeDate(row.thoi_gian_do),
    };
  });

  return data;
}


export async function getKetQuaDoKhoangCachGiaoCheoByPhieu(phieuId) {
  let data = await getInfoPhieuGiaoViec({ id: phieuId });

  const ketQua = await KetQuaDoKhoangCachGiaoCheoService.getAll({ phieu_giao_viec_id: phieuId, is_deleted: false })
    .populate([
      { path: 'khoang_cot_id', select: 'ten_khoang_cot' },
    ]);

  data.ten_duong_day = data.duong_day_ids?.ten_duong_day || '';
  data.thiet_bi_do = data.may_do_id?.may_do_id?.ten_may || '';
  data.ket_qua = ketQua.map((row, index) => {
    return {
      ...row,
      stt: index + 1,
      ngay_do: formatTimeDate(row.thoi_gian_do),
      doi_tuong_giao_cheo: i18next.t(row.doi_tuong_giao_cheo),
    };
  });

  return data;
}
