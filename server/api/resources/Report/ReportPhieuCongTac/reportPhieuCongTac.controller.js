import { EXTENSION_FILE } from '../../../constant/constant';
import queryHelper from '../../../helpers/queryHelper';
import * as PhieuCongTacService from '../../QuanLyVanHanh/PhieuCongTac/phieuCongTac.service';
import { generatePhieuCongTac } from '../GenerateFile/generate.controller';


export async function downloadPhieuCongTac(req, res) {
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  const phieuCongTacData = await PhieuCongTacService.getOneById(criteria.id);
  const templateFileName = `phieu_cong_tac.docx`;
  let outputFileName = `Phiếu công tác.${criteria.type_file}`;
  await generatePhieuCongTac(res, phieuCongTacData, templateFileName, outputFileName, criteria.type_file === EXTENSION_FILE.PDF);
}
