import express from 'express';
import passport from 'passport';
import * as Controller from './report.controller';
import { loggerMiddleware } from '../../logs/middleware';
import { thongKeMachDonMachKep } from './report.controller';

export const reportRouter = express.Router();

reportRouter
  .route('/baocaophieugiaoviec')
  .get(passport.authenticate('jwt', { session: false }), Controller.downloadPhieuGiaoViec);
reportRouter
  .route('/baocaochamtiendo')
  .get(passport.authenticate('jwt', { session: false }), Controller.downloadChamTienDo);

reportRouter
  .route('/lylichcot')
  .get(Controller.lyLichCot);

reportRouter
  .route('/congviectheovitri')
  .get(Controller.allCongViecByViTriId);

reportRouter
  .route('/congviectheovitri/download')
  .get(Controller.downloadCongViecByViTriId);

reportRouter
  .route('/diaphuong')
  .get(Controller.bieuMauDiaPhuong);

reportRouter
  .route('/giaocheo')
  .get(Controller.bieuMauGiaoCheo);

reportRouter
  .route('/tongke')
  .get(Controller.tongKeDuongDay);

reportRouter
  .route('/nhatkyvanhanh')
  .get(Controller.nhatKyVanHanhDoiTruyenTaiDien);

reportRouter
  .route('/allnhatkyvanhanh')
  .get(Controller.allNhatKyVanHanhDoiTruyenTaiDien);

reportRouter
  .route('/checkedbydrone')
  .get(Controller.thongKeNoiDungKiemTraBangThietBiBay);

reportRouter
  .route('/downloadcheckedbydrone')
  .get(Controller.downloadNoiDungKiemTraBangThietBiBay);

reportRouter
  .route('/danhsachnguoidung')
  .get(passport.authenticate('jwt', { session: false }), Controller.downloadDanhSachNguoiDung);

reportRouter
  .route('/inspectionwork')
  .get(passport.authenticate('jwt', { session: false }), Controller.reportInspectionWorkByDonVi);

reportRouter
  .route('/inspectionwork/time')
  .get(passport.authenticate('jwt', { session: false }), Controller.getTimePhieuGiaoViec);

reportRouter
  .route('/v1/inspectionwork')
  .get(passport.authenticate('jwt', { session: false }), Controller.reportInspectionWorkByDonViV1);

reportRouter
  .route('/inspectionwork/download')
  .get(passport.authenticate('jwt', { session: false }), Controller.downloadTongHopSuDungDrone);

reportRouter
  .route('/v1/inspectionwork/download')
  .get(passport.authenticate('jwt', { session: false }), Controller.downloadTongHopSuDungDroneV1);


reportRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

reportRouter
  .route('/cotdocotneo')
  .get(Controller.thongKeCotDoCotNeo);

reportRouter
  .route('/machdonmachkep')
  .get(Controller.thongKeMachDonMachKep);

reportRouter
  .route('/tontaitheochuyende')
  .get(Controller.thongKeTonTaiTheoChuyenDeKiemTra);
reportRouter
  .route('/tontaitheochuyende/download')
  .get(Controller.downloadTonTaiTheoChuyenDeKiemTra);
