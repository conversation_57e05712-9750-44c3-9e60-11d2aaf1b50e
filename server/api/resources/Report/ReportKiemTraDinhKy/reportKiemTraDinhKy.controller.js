import queryHelper from '../../../helpers/queryHelper';
import * as responseAction from '../../../helpers/responseHelper';
import * as DonViService from '../../DonVi/donVi.service';
import { buildTree } from '../../DonVi/donVi.service';
import * as Service from './reportKiemTraDinhKy.service';
import * as ReportService from '../report.service';
import { getFilePath } from '../../../utils/fileUtils';
import { CAP_DON_VI, TEMPLATES_DIRS } from '../../../constant/constant';
import { generateDocument } from '../GenerateFile/generate.controller';

export async function reportInspectionWorkByDonVi(req, res) {
  try {
    req.query.don_vi_id = req.query.don_vi_giao_phieu_id;
    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;
    criteria.don_vi_id = await DonViService.getDonViInScope(criteria.don_vi_giao_phieu_id);
    const allDonVi = await Service.tongHopCongTacKiemTraTheoDonVi(req, criteria);
    const dataReturn = buildTree(allDonVi);
    responseAction.success(res, dataReturn);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function reportInspectionWorkByDonViVersion1(req, res) {
  try {
    req.query.don_vi_id = req.query.don_vi_giao_phieu_id;
    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;
    criteria.don_vi_id = await DonViService.getDonViInScope(criteria.don_vi_giao_phieu_id);
    const allDonVi = await Service.tongHopCongTacKiemTraTheoDonViV1(req, criteria);
    const dataReturn = buildTree(allDonVi);
    responseAction.success(res, dataReturn);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function thongKeNoiDungKiemTraBangThietBiBay(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;
    if (!criteria.nam_kiem_tra) {
      criteria.nam_kiem_tra = new Date().getFullYear();
    }
    criteria.don_vi_giao_phieu_id = await DonViService.getDonViInScope(criteria.don_vi_giao_phieu_id);
    const data = await Service.tongHopPhieuSuDungThietBiBay(criteria);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function downloadNoiDungKiemTraBangThietBiBay(req, res) {
  const query = queryHelper.extractQueryParam(req);
  let { criteria } = query;
  criteria.don_vi_giao_phieu_id = await DonViService.getDonViInScope(criteria.don_vi_giao_phieu_id);
  const data = await Service.tongHopPhieuSuDungThietBiBay(criteria);
  const dataAfterConvert = await ReportService.convertDataNoiDungKiemTraBangThietBiBay(data.data_table);
  const thangQuy = criteria.thang_kiem_tra ? `Tháng ${criteria.thang_kiem_tra.split('_')[1]}` : `Quý ${criteria.quy_kiem_tra.split('_')[1]}`;
  dataAfterConvert[0].ky_kiem_tra = `${thangQuy} - Năm ${criteria.nam_kiem_tra}`;
  const templateFilePath = getFilePath('cong_viec_kiem_tra_dinh_ky_bang_thiet_bi_bay.xlsx', TEMPLATES_DIRS.BIEU_MAU);
  const outputFileName = 'Công việc kiểm tra bằng thiết bị bay.xlsx';
  await generateDocument(res, dataAfterConvert, templateFilePath, outputFileName);
}


export async function downloadTongHopSuDungDrone(req, res) {
  req.query.don_vi_id = req.query.don_vi_giao_phieu_id;
  const query = queryHelper.extractQueryParam(req);
  let { criteria } = query;
  criteria.don_vi_id = await DonViService.getDonViInScope(criteria.don_vi_giao_phieu_id);
  const allDonVi = await Service.tongHopCongTacKiemTraTheoDonVi(req, criteria);
  const congTyTreeForAdmin = buildTree(allDonVi);
  const donViQuery = await DonViService.getById(req.query.don_vi_id || req.user.don_vi_id);
  let templateFilePath;
  switch (donViQuery.cap_don_vi) {
    case CAP_DON_VI.DOI_TRUYEN_TAI_DIEN:
      templateFilePath = getFilePath('tong_hop_su_dung_drone_cap_doi.xlsx', TEMPLATES_DIRS.REPORT);
      break;
    case CAP_DON_VI.TRUYEN_TAI_DIEN:
      templateFilePath = getFilePath('tong_hop_su_dung_drone_cap_truyen_tai.xlsx', TEMPLATES_DIRS.REPORT);
      break;
    case CAP_DON_VI.CONG_TY:
      templateFilePath = getFilePath('tong_hop_su_dung_drone_cap_cong_ty.xlsx', TEMPLATES_DIRS.REPORT);
      break;
    case CAP_DON_VI.TONG_CONG_TY:
      templateFilePath = getFilePath('tong_hop_su_dung_drone_cap_tong_cong_ty.xlsx', TEMPLATES_DIRS.REPORT);
      break;
  }
  await generateDocument(res, congTyTreeForAdmin, templateFilePath, null);
}


export async function downloadTongHopSuDungDroneV1(req, res) {
  req.query.don_vi_id = req.query.don_vi_giao_phieu_id;
  const query = queryHelper.extractQueryParam(req);
  let { criteria } = query;
  criteria.don_vi_id = await DonViService.getDonViInScope(criteria.don_vi_giao_phieu_id);
  const allDonVi = await Service.tongHopCongTacKiemTraTheoDonViV1(req, criteria);
  const congTyTreeForAdmin = buildTree(allDonVi);
  const donViQuery = await DonViService.getById(req.query.don_vi_id || req.user.don_vi_id);
  let templateFilePath;
  switch (donViQuery.cap_don_vi) {
    case CAP_DON_VI.DOI_TRUYEN_TAI_DIEN:
      templateFilePath = getFilePath('tong_hop_su_dung_drone_cap_doi_v1.xlsx', TEMPLATES_DIRS.REPORT);
      break;
    case CAP_DON_VI.TRUYEN_TAI_DIEN:
      templateFilePath = getFilePath('tong_hop_su_dung_drone_cap_truyen_tai_v1.xlsx', TEMPLATES_DIRS.REPORT);
      break;
    case CAP_DON_VI.CONG_TY:
      templateFilePath = getFilePath('tong_hop_su_dung_drone_cap_cong_ty_v1.xlsx', TEMPLATES_DIRS.REPORT);
      break;
    case CAP_DON_VI.TONG_CONG_TY:
      templateFilePath = getFilePath('tong_hop_su_dung_drone_cap_tong_cong_ty_v1.xlsx', TEMPLATES_DIRS.REPORT);
      break;
  }
  await generateDocument(res, congTyTreeForAdmin, templateFilePath, null);
}
