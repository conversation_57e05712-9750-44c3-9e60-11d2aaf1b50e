import { Types } from 'mongoose';

import { extractIds } from '../../../utils/dataconverter';
import { groupBy } from '../../../common/functionCommons';
import { tongHopPhamViBayTheoDonVi } from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';

import { CAP_DON_VI, PHUONG_PHAP_THUC_HIEN } from '../../../constant/constant';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import { TRANG_THAI_HOAN_THANH } from '../../DanhMuc/TrangThaiHoanThanh';
import { KIEM_TRA, LOAI_CONG_VIEC } from '../../DanhMuc/LoaiCongViec';

import PHIEU_GIAO_VIEC from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.model';
import VI_TRI from '../../TongKe/ViTri/viTri.model';
import VAN_HANH from '../../TongKe/VanHanh/vanHanh.model';
import KHOANG_COT from '../../TongKe/KhoangCot/khoangCot.model';

import * as DonViService from '../../DonVi/donVi.service';
import * as NguoiCongTacService from '../../QuanLyVanHanh/NguoiCongTac/nguoiCongTac.service';
import * as ViTriCongViecService from '../../QuanLyVanHanh/ViTriCongViec/viTriCongViec.service';
import * as ThongTinQuanLyService from '../../ThongTinQuanLy/thongTinQuanLy.service';
import * as KetQuaKiemTraService from '../../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.service';
import * as KhoangCotService from '../../TongKe/KhoangCot/khoangCot.service';


export async function tongHopCongTacKiemTraTheoDonVi(req, criteria) {
  criteria.don_vi_giao_phieu_id = criteria.don_vi_id;
  if (!criteria.loai_cong_viec) {
    criteria.loai_cong_viec = { $in: ['KIEM_TRA_DINH_KY_NGAY', 'KIEM_TRA_DINH_KY_DEM'] };
  }
  delete criteria.don_vi_id;
  criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code;
  if (!criteria.nam_kiem_tra) {
    criteria.nam_kiem_tra = new Date().getFullYear();
  }

  const [phieuBayTuDong, phieuBayThuCong, phieuKiemTraTruyenThong] = await Promise.all([
    PHIEU_GIAO_VIEC.distinct('_id', { ...criteria, phuong_phap_thuc_hien: PHUONG_PHAP_THUC_HIEN.BAY_TU_DONG }),
    PHIEU_GIAO_VIEC.distinct('_id', { ...criteria, phuong_phap_thuc_hien: PHUONG_PHAP_THUC_HIEN.BAY_THU_CONG }),
    PHIEU_GIAO_VIEC.distinct('_id', {
      ...criteria,
      phuong_phap_thuc_hien: PHUONG_PHAP_THUC_HIEN.KIEM_TRA_TRUYEN_THONG,
    }),
  ]);

  const mapDonViBayThuCong = {};
  const mapDonViBayTuDong = {};
  const mapPhamViBayTuDong = {};
  const mapPhamViBayThuCong = {};
  const mapDonViKiemTraTruyenThong = {};
  const mapPhamViTruyenThong = {};
  const mapKhoiLuongQuanLyDonVi = {};

  async function getViTriBayTuDong() {
    const vtcvBayTuDong = await ViTriCongViecService.reportViTriCongViecByDonVi({
      phieu_giao_viec_id: { $in: phieuBayTuDong },
      is_deleted: false,
    });
    vtcvBayTuDong.forEach(dv => mapDonViBayTuDong[dv.don_vi_id] = dv);
  }

  async function getViTriThuCong() {
    const vtcvBayThuCong = await ViTriCongViecService.reportViTriCongViecByDonVi({
      phieu_giao_viec_id: { $in: phieuBayThuCong },
      is_deleted: false,
    });
    vtcvBayThuCong.forEach(dv => mapDonViBayThuCong[dv.don_vi_id] = dv);
  }

  async function getViTriTruyenThong() {
    const vtcvKiemTraTruyenThong = await ViTriCongViecService.reportViTriCongViecByDonVi({
      phieu_giao_viec_id: { $in: phieuKiemTraTruyenThong },
      is_deleted: false,
    });
    vtcvKiemTraTruyenThong.forEach(dv => mapDonViKiemTraTruyenThong[dv.don_vi_id] = dv);
  }

  async function getPhamViBayThuCong() {
    const phamViBayThuCong = await tongHopPhamViBayTheoDonVi({ _id: { $in: phieuBayThuCong } });
    phamViBayThuCong.forEach(phieu => mapPhamViBayThuCong[phieu.don_vi_id] = phieu);
  }

  async function getPhamViBayTuDong() {
    const phamViBayTuDong = await tongHopPhamViBayTheoDonVi({ _id: { $in: phieuBayTuDong } });
    phamViBayTuDong.forEach(phieu => mapPhamViBayTuDong[phieu.don_vi_id] = phieu);
  }

  async function getPhamViBayTruyenThong() {
    const phamViKiemTraTruyenThong = await ViTriCongViecService.tongHopPhamViBayKiemTraTruyenThong({
      phieu_giao_viec_id: { $in: phieuKiemTraTruyenThong },
    });
    phamViKiemTraTruyenThong.forEach(dv => mapPhamViTruyenThong[dv.don_vi_id] = dv);
  }

  async function getKhoiLuongQuanLy() {
    const khoiLuongQuanLy = await Promise.all(criteria.don_vi_giao_phieu_id.map(async dv => {
      const viTriIds = await VI_TRI.distinct('_id', { don_vi_id: dv, is_deleted: false });
      if (!viTriIds.length) return { don_vi_id: dv, khoi_luong_quan_ly: 0 };

      const khoangCot = await ThongTinQuanLyService.khoiLuongQuanLyByMotDonVi({
        vi_tri_id: { $in: viTriIds }, is_deleted: false,
      });

      return {
        don_vi_id: dv,
        khoi_luong_quan_ly: khoangCot.length > 0 ? khoangCot[0].khoi_luong_quan_ly : 0,
      };
    }));

    khoiLuongQuanLy.forEach(item => {
      mapKhoiLuongQuanLyDonVi[item.don_vi_id] = item.khoi_luong_quan_ly;
    });
  }

  const allPromiseAggregate = [
    getViTriBayTuDong(),
    getViTriThuCong(),
    getViTriTruyenThong(),
    getPhamViBayThuCong(),
    getPhamViBayTuDong(),
    getPhamViBayTruyenThong(),
    getKhoiLuongQuanLy(),
  ];

  await Promise.all(allPromiseAggregate);

  let allDonVi = await DonViService.getAll({ _id: { $in: criteria.don_vi_giao_phieu_id }, is_deleted: false });

  allDonVi.forEach(element => {
    element.pham_vi_bay_tu_dong = mapPhamViBayTuDong[element._id]?.pham_vi_bay_da_thuc_hien || 0;
    element.thoi_gian_bay_tu_dong = mapPhamViBayTuDong[element._id]?.thoi_gian_bay || 0;

    element.pham_vi_bay_thu_cong = mapPhamViBayThuCong[element._id]?.pham_vi_bay_da_thuc_hien || 0;
    element.thoi_gian_bay_thu_cong = mapPhamViBayThuCong[element._id]?.thoi_gian_bay || 0;

    element.so_vi_tri_bay_tu_dong = mapDonViBayTuDong[element._id]?.so_luong_vi_tri || 0;
    element.so_khoang_cot_bay_tu_dong = mapDonViBayTuDong[element._id]?.so_luong_khoang_cot || 0;

    element.so_vi_tri_bay_thu_cong = mapDonViBayThuCong[element._id]?.so_luong_vi_tri || 0;
    element.so_khoang_cot_bay_thu_cong = mapDonViBayThuCong[element._id]?.so_luong_khoang_cot || 0;

    element.so_vi_tri_truyen_thong = mapDonViKiemTraTruyenThong[element._id]?.so_luong_vi_tri || 0;
    element.so_khoang_cot_truyen_thong = mapDonViKiemTraTruyenThong[element._id]?.so_luong_khoang_cot || 0;


    element.pham_vi_truyen_thong = mapPhamViTruyenThong[element._id]?.pham_vi_thuc_hien || 0;
    //Khối lượng quản lý tổng
    element.khoi_luong_quan_ly = mapKhoiLuongQuanLyDonVi[element._id] / 1000 || 0;
  });

  const updateDataForParent = (donVi, allDonVi) => {
    allDonVi
      .filter(dv => dv.don_vi_cha_id?.toString() === donVi._id?.toString())
      .forEach(child => {
        donVi.so_vi_tri_bay_tu_dong += child.so_vi_tri_bay_tu_dong || 0;
        donVi.so_khoang_cot_bay_tu_dong += child.so_khoang_cot_bay_tu_dong || 0;
        donVi.so_vi_tri_bay_thu_cong += child.so_vi_tri_bay_thu_cong || 0;
        donVi.so_khoang_cot_bay_thu_cong += child.so_khoang_cot_bay_thu_cong || 0;
        donVi.so_vi_tri_truyen_thong += child.so_vi_tri_truyen_thong || 0;
        donVi.so_khoang_cot_truyen_thong += child.so_khoang_cot_truyen_thong || 0;


        donVi.pham_vi_bay_tu_dong += child.pham_vi_bay_tu_dong || 0;
        donVi.thoi_gian_bay_tu_dong += child.thoi_gian_bay_tu_dong || 0;
        donVi.pham_vi_bay_thu_cong += child.pham_vi_bay_thu_cong || 0;
        donVi.thoi_gian_bay_thu_cong += child.thoi_gian_bay_thu_cong || 0;

        donVi.pham_vi_truyen_thong += child.pham_vi_truyen_thong || 0;
        donVi.khoi_luong_quan_ly += child.khoi_luong_quan_ly || 0;
      });
  };

  const updateDataForOrgUnit = (capDonVi, allDonVi) => {
    for (const donVi of allDonVi) {
      if (donVi.cap_don_vi === capDonVi) {
        updateDataForParent(donVi, allDonVi);
      }
    }
  };

//Cộng kết quả của đơn vị con => đơn vị cha
  [CAP_DON_VI.TRUYEN_TAI_DIEN, CAP_DON_VI.CONG_TY, CAP_DON_VI.TONG_CONG_TY]
    .forEach(item => updateDataForOrgUnit(item, allDonVi));
// Làm tròn
  allDonVi.forEach((donVi) => {
    donVi.pham_vi_bay_thu_cong = Math.round(donVi.pham_vi_bay_thu_cong / 10) / 100;
    donVi.pham_vi_bay_tu_dong = Math.round(donVi.pham_vi_bay_tu_dong / 10) / 100;
    donVi.thoi_gian_bay_tu_dong = Math.round(donVi.thoi_gian_bay_tu_dong * 100 / 60) / 100;
    donVi.thoi_gian_bay_thu_cong = Math.round(donVi.thoi_gian_bay_thu_cong * 100 / 60) / 100;
    donVi.pham_vi_truyen_thong = Math.round(donVi.pham_vi_truyen_thong / 10) / 100;
    donVi.khoi_luong_quan_ly = Math.round(donVi.khoi_luong_quan_ly * 100) / 100;
  });
  return allDonVi;
}

export async function tongHopCongTacKiemTraTheoDonViV1(req, criteria) {
  criteria.don_vi_giao_phieu_id = criteria.don_vi_id;
  if (!criteria.loai_cong_viec) {
    criteria.loai_cong_viec = { $in: ['KIEM_TRA_DINH_KY_NGAY', 'KIEM_TRA_DINH_KY_DEM'] };
  }
  delete criteria.don_vi_id;
  criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code;
  criteria.phuong_phap_thuc_hien = {
    $nin: [PHUONG_PHAP_THUC_HIEN.BAY_TU_DONG, PHUONG_PHAP_THUC_HIEN.BAY_THU_CONG, PHUONG_PHAP_THUC_HIEN.KIEM_TRA_TRUYEN_THONG],
  };
  if (!criteria.nam_kiem_tra) {
    criteria.nam_kiem_tra = new Date().getFullYear();
  }

  let allPhieuSuDungDrone = await PHIEU_GIAO_VIEC.distinct('_id', { ...criteria, su_dung_drone: true });
  let allPhieuTruyenThong = await PHIEU_GIAO_VIEC.distinct('_id', { ...criteria, su_dung_drone: false });

  const mapDonViDrone = {};
  const mapPhieuGiaoViec = {};
  const mapDonViTruyenThong = {};
  const mapPhamViTruyenThong = {};
  const mapKhoiLuongQuanLyDonVi = {};

  async function getViTriDrone() {
    const viTriCongViecDrone = await ViTriCongViecService.reportViTriCongViecByDonVi({
      phieu_giao_viec_id: { $in: allPhieuSuDungDrone },
      is_deleted: false,
    });
    viTriCongViecDrone.forEach(dv => mapDonViDrone[dv.don_vi_id] = dv);
  }

  async function getViTriTruyenThong() {
    const viTriCongViecTruyenThong = await ViTriCongViecService.reportViTriCongViecByDonVi({
      phieu_giao_viec_id: { $in: allPhieuTruyenThong },
      is_deleted: false,
    });
    viTriCongViecTruyenThong.forEach(dv => mapDonViTruyenThong[dv.don_vi_id] = dv);
  }

  async function getPhamViBayDrone() {
    const phieuSuDungDrone = await tongHopPhamViBayTheoDonVi({ _id: { $in: allPhieuSuDungDrone } });
    phieuSuDungDrone.forEach(phieu => mapPhieuGiaoViec[phieu.don_vi_id] = phieu);
  }

  async function getPhamViBayTruyenThong() {
    const phamViTruyenThong = await ViTriCongViecService.tongHopPhamViBayKiemTraTruyenThong({
      phieu_giao_viec_id: { $in: allPhieuTruyenThong },
    });
    phamViTruyenThong.forEach(dv => mapPhamViTruyenThong[dv.don_vi_id] = dv);
  }

  async function getKhoiLuongQuanLy() {
    //all đơn vị và khối lượng quản lý theo đường dây
    const khoiLuongQuanLy = await Promise.all(criteria.don_vi_giao_phieu_id.map(async dv => {
      const viTriIds = await VI_TRI.distinct('_id', { don_vi_id: dv, is_deleted: false });
      if (!viTriIds.length) return { don_vi_id: dv, khoi_luong_quan_ly: 0 };

      const khoangCot = await ThongTinQuanLyService.khoiLuongQuanLyByMotDonVi({
        vi_tri_id: { $in: viTriIds }, is_deleted: false,
      });

      return {
        don_vi_id: dv,
        khoi_luong_quan_ly: khoangCot.length > 0 ? khoangCot[0].khoi_luong_quan_ly : 0,
      };
    }));

    khoiLuongQuanLy.forEach(item => mapKhoiLuongQuanLyDonVi[item.don_vi_id] = item.khoi_luong_quan_ly);
  }

  const allPromiseAggregate = [
    getViTriDrone(),
    getViTriTruyenThong(),
    getPhamViBayDrone(),
    getPhamViBayTruyenThong(),
    getKhoiLuongQuanLy(),
  ];

  await Promise.all(allPromiseAggregate);

  let allDonVi = await DonViService.getAll({ _id: { $in: criteria.don_vi_giao_phieu_id }, is_deleted: false });

  allDonVi.forEach(element => {
    element.so_vi_tri_drone = mapDonViDrone[element._id]?.so_luong_vi_tri || 0;
    element.so_khoang_cot_drone = mapDonViDrone[element._id]?.so_luong_khoang_cot || 0;
    element.so_vi_tri_truyen_thong = mapDonViTruyenThong[element._id]?.so_luong_vi_tri || 0;
    element.so_khoang_cot_truyen_thong = mapDonViTruyenThong[element._id]?.so_luong_khoang_cot || 0;
    element.pham_vi_bay = mapPhieuGiaoViec[element._id]?.pham_vi_bay_da_thuc_hien || 0;
    element.thoi_gian_bay = mapPhieuGiaoViec[element._id]?.thoi_gian_bay || 0;
    element.pham_vi_truyen_thong = mapPhamViTruyenThong[element._id]?.pham_vi_thuc_hien || 0;
    //Khối lượng quản lý tổng
    element.khoi_luong_quan_ly = mapKhoiLuongQuanLyDonVi[element._id] / 1000 || 0;
  });

  const updateDataForParent = (donVi, allDonVi) => {
    const allChild = allDonVi.filter(dv => dv.don_vi_cha_id?.toString() === donVi._id?.toString());
    allChild.forEach(child => {
      donVi.so_vi_tri_drone += child.so_vi_tri_drone || 0;
      donVi.so_khoang_cot_drone += child.so_khoang_cot_drone || 0;
      donVi.pham_vi_bay += child.pham_vi_bay || 0;
      donVi.thoi_gian_bay += child.thoi_gian_bay || 0;
      donVi.so_vi_tri_truyen_thong += child.so_vi_tri_truyen_thong || 0;
      donVi.so_khoang_cot_truyen_thong += child.so_khoang_cot_truyen_thong || 0;
      donVi.pham_vi_truyen_thong += child.pham_vi_truyen_thong || 0;
      donVi.khoi_luong_quan_ly += child.khoi_luong_quan_ly || 0;
    });
  };

  const updateDataForOrgUnit = (capDonVi, allDonVi) => {
    allDonVi.forEach(donVi => {
      if (donVi.cap_don_vi === capDonVi) updateDataForParent(donVi, allDonVi);
    });
  };

  //Cộng kết quả của đơn vị con => đơn vị cha
  [CAP_DON_VI.TRUYEN_TAI_DIEN, CAP_DON_VI.CONG_TY, CAP_DON_VI.TONG_CONG_TY]
    .forEach(item => updateDataForOrgUnit(item, allDonVi));
  // Làm tròn
  allDonVi.forEach((donVi) => {
    donVi.pham_vi_bay = Math.round(donVi.pham_vi_bay / 10) / 100;
    donVi.thoi_gian_bay = Math.round(donVi.thoi_gian_bay * 100 / 60) / 100;
    donVi.pham_vi_truyen_thong = Math.round(donVi.pham_vi_truyen_thong / 10) / 100;
    donVi.khoi_luong_quan_ly = Math.round(donVi.khoi_luong_quan_ly * 100) / 100;
  });
  return allDonVi;
}

export async function tongHopPhieuSuDungThietBiBay(criteria) {
  const baseCriteria = {
    ...criteria,
    $or: [],
  };

  if (criteria.phuong_phap_thuc_hien === PHUONG_PHAP_THUC_HIEN.KIEM_TRA_TRUYEN_THONG) {
    baseCriteria.$or.push(
      { su_dung_drone: false },
      { phuong_phap_thuc_hien: PHUONG_PHAP_THUC_HIEN.KIEM_TRA_TRUYEN_THONG },
    );
  } else {
    baseCriteria.$or.push(
      {
        $and: [
          { su_dung_drone: true },
          { phuong_phap_thuc_hien: { $exists: false } },
        ],
      },
      { phuong_phap_thuc_hien: { $in: [PHUONG_PHAP_THUC_HIEN.BAY_TU_DONG, PHUONG_PHAP_THUC_HIEN.BAY_THU_CONG] } },
    );
  }

  delete baseCriteria.su_dung_drone;
  delete baseCriteria.phuong_phap_thuc_hien;

  criteria = { ...baseCriteria };
  criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code;
  if (criteria.loai_kiem_tra) {
    criteria.loai_cong_viec = criteria.loai_kiem_tra;
    delete criteria.loai_kiem_tra;
  } else {
    criteria.loai_cong_viec = { $in: ['KIEM_TRA_DINH_KY_NGAY', 'KIEM_TRA_DINH_KY_DEM'] };
  }

  if (criteria.duong_day_id) {
    criteria.duong_day_ids = criteria.duong_day_id;
    delete criteria.duong_day_id;
  }

  const allPhieuGiaoViec = await PHIEU_GIAO_VIEC.find(criteria)
    .populate({ path: 'duong_day_ids', select: 'ten_duong_day' })
    .populate({ path: 'don_vi_giao_phieu_id', populate: { path: 'don_vi_cha_id', select: 'ten_don_vi' } })
    .sort('-thoi_gian_xac_nhan_khoa_phieu')
    .lean();
  let nguoiCongTac, viTriCongViec, khoangCotCongViec, ketQuaKiemTra, khoiLuongQuanLy = 0;

  async function getNguoiCongTac() {
    nguoiCongTac = await NguoiCongTacService.getAll({
      phieu_giao_viec_id: { $in: extractIds(allPhieuGiaoViec) },
      is_deleted: false,
    });
  }

  async function getViTriCongViec() {
    let viTriCriteria = {
      phieu_giao_viec_id: { $in: extractIds(allPhieuGiaoViec) },
      vi_tri_id: { $exists: true },
      khoang_cot_id: { $exists: false },
      trang_thai_hoan_thanh: TRANG_THAI_HOAN_THANH.DA_HOAN_THANH,
      is_deleted: false,
    };
    if (criteria.duong_day_ids) {
      const viTriIds = await VAN_HANH.distinct(
        'vi_tri_id', { duong_day_id: criteria.duong_day_ids, is_deleted: false },
      );
      viTriCriteria.vi_tri_id = { $in: viTriIds };
    }
    viTriCongViec = await ViTriCongViecService.getAll(viTriCriteria, { vi_tri_id: 1, phieu_giao_viec_id: 1 })
      .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });
  }

  async function getKhoangCotCongViec() {
    let khoangCotCriteria = {
      phieu_giao_viec_id: { $in: extractIds(allPhieuGiaoViec) },
      vi_tri_id: { $exists: false },
      khoang_cot_id: { $exists: true },
      is_deleted: false,
      trang_thai_hoan_thanh: TRANG_THAI_HOAN_THANH.DA_HOAN_THANH,
    };
    if (criteria.duong_day_ids) {
      let khoangCotIds = await KHOANG_COT.distinct(
        '_id',
        { duong_day_id: criteria.duong_day_ids, is_deleted: false },
      );
      khoangCotCriteria.khoang_cot_id = { $in: khoangCotIds };
    }
    khoangCotCongViec = await ViTriCongViecService.getAll(khoangCotCriteria,
      { khoang_cot_id: 1, phieu_giao_viec_id: 1 })
      .populate({ path: 'khoang_cot_id', select: 'ten_khoang_cot chieu_dai' });
  }

  async function getKetQuaKiemTra() {
    ketQuaKiemTra = await KetQuaKiemTraService.getAll({
      phieu_giao_viec_id: { $in: extractIds(allPhieuGiaoViec) },
      is_deleted: false,
    }).populate([
      { path: 'vi_tri_id', select: 'ten_vi_tri' },
      { path: 'khoang_cot_id', select: 'ten_khoang_cot' },
      { path: 'duong_day_id', select: 'ten_duong_day' },
      {
        path: 'tieu_chi_id',
        populate: 'tieu_chi_cha_id',
      }]).select('vi_tri_id khoang_cot_id duong_day_id phieu_giao_viec_id');

    ketQuaKiemTra.forEach(ketqua => {
      ketqua.noi_dung_kiem_tra = ketqua.tieu_chi_id?.noi_dung_kiem_tra_id;
    });
  }

  async function getKhoiLuongQuanLy() {
    khoiLuongQuanLy = await Promise.all(criteria.don_vi_giao_phieu_id.map(async dv => {
      const viTriIds = await VI_TRI.distinct('_id', { don_vi_id: dv, is_deleted: false });
      if (!viTriIds.length) return 0;

      const khoangCot = await ThongTinQuanLyService.khoiLuongQuanLyByMotDonVi({
        vi_tri_id: { $in: viTriIds }, is_deleted: false,
      });

      return khoangCot.length > 0 ? khoangCot[0].khoi_luong_quan_ly/1000 : 0;
    }));
    khoiLuongQuanLy = khoiLuongQuanLy.reduce((a, b) => a + b, 0);
  }

  const allPromise = [
    getNguoiCongTac(),
    getViTriCongViec(),
    getKhoangCotCongViec(),
    getKetQuaKiemTra(),
    getKhoiLuongQuanLy(),
  ];

  await Promise.all(allPromise);

  const groupNguoiCongTacByPhieuId = groupBy(nguoiCongTac, 'phieu_giao_viec_id');
  const groupViTriCongViecByPhieuId = groupBy(viTriCongViec, 'phieu_giao_viec_id');
  const groupKhoangCotCongViecByPhieuId = groupBy(khoangCotCongViec, 'phieu_giao_viec_id');
  const groupKetQuaByPhieuId = groupBy(ketQuaKiemTra, 'phieu_giao_viec_id');

  allPhieuGiaoViec.forEach(element => {
    element.so_luong_nguoi_tham_gia = groupNguoiCongTacByPhieuId[element._id]?.length + 1 || 1;
    element.vi_tri_cong_viec = groupViTriCongViecByPhieuId[element._id] || [];
    element.khoang_cot_cong_viec = groupKhoangCotCongViecByPhieuId[element._id] || [];
    element.ket_qua_kiem_tra = groupKetQuaByPhieuId[element._id] || [];
  });

  // Group theo loại công việc
  const groupByLoaiCongViec = groupBy(allPhieuGiaoViec, 'loai_cong_viec');

  let loaiCongViecKiemTra = [];
  Object.keys(LOAI_CONG_VIEC).filter(key => {
    if (LOAI_CONG_VIEC[key]?.type === KIEM_TRA) {
      loaiCongViecKiemTra.push({
        type: LOAI_CONG_VIEC[key].name,
        so_luong: groupByLoaiCongViec[key]?.length || 0,
      });
    }
  });
  // Thống kê dữ liệu bay
  let thoiGianBay = 0, soViTri = 0, soKhoangCot = 0, phamViBayDaThucHien = 0;
  allPhieuGiaoViec.forEach(item => {
    thoiGianBay += (item.thoi_gian_bay || 0);
    soViTri += (item.vi_tri_cong_viec.length || 0);
    soKhoangCot += (item.khoang_cot_cong_viec.length || 0);
    phamViBayDaThucHien += item.pham_vi_bay_da_thuc_hien || 0;
  });
  const thongKeDulieuBay = {
    thoi_gian_bay: thoiGianBay,
    so_vi_tri: soViTri,
    so_khoang_cot: soKhoangCot,
    pham_vi_bay_da_thuc_hien: Math.round(phamViBayDaThucHien) / 1000,
    khoi_luong_quan_ly: Math.round(khoiLuongQuanLy * 100) / 100,
  };

  allPhieuGiaoViec.sort(function(a, b) {
    return new Date(b.created_at) - new Date(a.created_at);
  });
  return {
    data_table: allPhieuGiaoViec,
    data_pie_chart: loaiCongViecKiemTra,
    data_statistical: thongKeDulieuBay,
  };
}
