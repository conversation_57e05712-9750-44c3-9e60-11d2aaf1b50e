import express from 'express';
import passport from 'passport';
import * as Controller from './reportKiemTraDinhKy.controller';
import { reportInspectionWorkByDonViVersion1 } from './reportKiemTraDinhKy.controller';

export const reportKiemTraDinhKyRouter = express.Router();

reportKiemTraDinhKyRouter.use(passport.authenticate('jwt', { session: false }));

reportKiemTraDinhKyRouter
  .route('/inspectionwork')
  .get(Controller.reportInspectionWorkByDonVi);

reportKiemTraDinhKyRouter
  .route('/v1/inspectionwork')
  .get(Controller.reportInspectionWorkByDonViVersion1);

reportKiemTraDinhKyRouter
  .route('/checkedbydrone')
  .get(Controller.thongKeNoiDungKiemTraBangThietBiBay);

reportKiemTraDinhKyRouter
  .route('/downloadcheckedbydrone')
  .get(Controller.downloadNoiDungKiemTraBangThietBiBay);

reportKiemTraDinhKyRouter
  .route('/downloadinspectionwork')
  .get(Controller.downloadTongHopSuDungDrone);

reportKiemTraDinhKyRouter
  .route('/v1/downloadinspectionwork')
  .get(Controller.downloadTongHopSuDungDroneV1);


