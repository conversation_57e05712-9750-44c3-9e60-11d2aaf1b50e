import carbone from 'carbone';
import ExcelJs from 'exceljs';
import { getFileExtension } from '../../../utils/fileUtils';
import * as soap from 'soap';
import { base64Decode } from '../../../common/functionCommons';
import { PDF_HEIGHT } from '../../../constant/constant';
import pdfParse from '../../../helpers/pdfParse';

const fs = require('fs');

export async function renderDataToFileTemp(templateFilePath, data) {
  let opt = {
    renderPrefix: 'bao_cao',
    reportName: 'Báo cáo',
    timezone: 'Asia/Saigon',
  };
  return new Promise((resolve, reject) => {
    carbone.render(templateFilePath, data, opt, function(err, resultFilePath) {
      if (err) return reject(err);
      resolve(resultFilePath);
    });
  });
}

export async function updateFileTempByColumns(templateFilePath, selectedColumns = null) {
  try {
    // Load file Excel bằng ExcelJS
    const workbook = new ExcelJs.Workbook();
    await workbook.xlsx.readFile(templateFilePath);
    const worksheet = workbook.getWorksheet(1); // Lấy sheet đầu tiên

    // Định nghĩa cột và vị trí
    const columnHeaders = worksheet.getRow(6).values; // Lấy tiêu đề cột
    const columnIndexes = {}; // Lưu vị trí của từng cột

    console.log('columnHeaders', columnHeaders);

    columnHeaders.forEach((header, index) => {
      if (header) columnIndexes[header] = index; // Map tên cột với vị trí
    });
    console.log('columnIndexes', columnIndexes);
    // Xóa các cột không nằm trong selectedColumns
    Object.keys(columnIndexes)
      .reverse() // Xóa từ phải sang trái để tránh lệch index
      .forEach((col) => {
        if (!selectedColumns.includes(col)) {
          worksheet.spliceColumns(columnIndexes[col], 1);
        }
      });

    // Lưu lại file sau khi xóa cột
    const tempUpdatedTemplate = 'temp_updated_template.xlsx';
    await workbook.xlsx.writeFile(tempUpdatedTemplate);
    return tempUpdatedTemplate;
  } catch (error) {
    reject(error);
  }
}

export async function convertFileToPDF(res, inputFilePath, fileExt, outPDFPath) {
  const fileBuffer = fs.readFileSync(inputFilePath);
  const options = {
    convertTo: 'pdf',
    extension: fileExt,
  };
  return new Promise((resolve, reject) => {
    carbone.convert(fileBuffer, options, function(err, result) {
      if (err) return reject(err);
      fs.writeFileSync(outPDFPath, result);
      resolve(outPDFPath);
    });
  });
}

export async function connectEVNSignService(apiKyDienTu) {
  return new Promise((resolve, reject) => {
    soap.createClient(apiKyDienTu, { wsdl_options: { timeout: 5000 } }, function(err, client) {
      if (err) {
        return resolve(false);
      }
      return resolve(client);
    });
  });
}

export function signPdfBase64RectangleText(res, client, args, outputToBase64) {
  return new Promise((resolve, reject) => {
    client.signPdfBase64RectangleText(args, function(error, result) {
      if (error) reject(error);
      if (result?.return.objectError) {
        resolve(false);
      }
      base64Decode(result?.return?.signedFileBase64, outputToBase64);
      resolve(outputToBase64);
    });
  });
}

export async function getCoordinatesSign(file, textToFind = '', listAlias = []) {
  function renderPage(pageData) {
    let renderOptions = {
      normalizeWhitespace: false,
      disableCombineTextItems: false,
    };

    return pageData.getTextContent(renderOptions)
      .then(function(textContent) {
        let textArr = [];
        for (let item of textContent.items) {
          textArr.push(item);
        }
        return textArr;
      });
  }

  let options = {
    pagerender: renderPage,
  };
  let dataBuffer = fs.readFileSync(file);
  let listCoordinates = [];

  return new Promise((resolve, reject) => {
    pdfParse(dataBuffer, options).then(function(data) {
      if (!data) reject(false);
      let numberLocate = 0;
      for (let content of data.item) {
        const [, , , , xTransform, yTransform] = content.transform;
        if (content.str.toLowerCase().includes(textToFind)) {
          listCoordinates.push({
            x: xTransform - 10,
            y: PDF_HEIGHT - yTransform + 3,
            numPageSign: content.pageNum, // Trang chứa tọa độ chữ ký
            alias: listAlias[numberLocate], // Map tọa độ chữ ký với alias user
          });
          numberLocate++;
        }
      }
      resolve(listCoordinates);
    });
  });

}

export async function addImageToExcel(chuKyDoiTruong, templateFilePath, imageRange, templateXlsxPath) {
  let workbook = new ExcelJs.Workbook();
  return new Promise((resolve, reject) => {
    workbook.xlsx.readFile(templateFilePath)
      .then(async function() {
        const worksheet = workbook.getWorksheet('Sheet1');
        const imageID = workbook.addImage({
          filename: chuKyDoiTruong,
          extension: getFileExtension(chuKyDoiTruong),
        });
        worksheet.addImage(imageID, imageRange);
        await workbook.xlsx.writeFile(templateXlsxPath);
        resolve(templateXlsxPath);
      })
      .then(() => console.log('File is written'))
      .catch(err => {
        console.error(err);
        reject(err);
      });
  });
}
