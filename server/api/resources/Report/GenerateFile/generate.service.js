import { LOAI_CONG_VIEC } from '../../DanhMuc/LoaiCongViec';
import { getAndCheckImagePath, getDirPath, getFileExtension, getFilePath } from '../../../utils/fileUtils';
import { base64Encode } from '../../../common/functionCommons';
import * as responseAction from '../../../helpers/responseHelper';
import CommonError from '../../../error/CommonError';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import { DocxImager } from '../../../common/docxImager';
import { connectEVNSignService, signPdfBase64RectangleText } from './generate.common';
import { LOAI_PHIEU } from './generate.controller';
import { STORE_DIRS } from '../../../constant/constant';

export function getAliasPhieuGiaoViec(data, type) {
  let listAlias;
  const aliasTiepNhan = data.nguoi_tiep_nhan_id?.alias;
  const aliasGiaoPhieu = data.nguoi_giao_phieu_id?.alias;
  const aliasXacNhanKhoa = data.nguoi_xac_nhan_khoa_id?.alias;
  const aliasKhoaPhieu = data.nguoi_khoa_phieu_id?.alias;
  switch (type) {
    case TRANG_THAI_PHIEU.GIAO_PHIEU.code:
    case TRANG_THAI_PHIEU.TIEP_NHAN.code:
      listAlias = [aliasTiepNhan, aliasGiaoPhieu];
      break;
    case TRANG_THAI_PHIEU.KHOA_PHIEU.code:
    case TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code:
      listAlias = [aliasTiepNhan, aliasGiaoPhieu, aliasXacNhanKhoa, aliasKhoaPhieu];
      break;
    default:
  }
  return listAlias;
}

export function getAliasPhieuCongTac(data) {
  return [
    data.nguoi_cap_phieu_id?.alias,
    data.nguoi_cho_phep_id?.alias,
    data.lanh_dao_cong_viec_id?.alias,
    data.phieu_giao_viec_id.chi_huy_truc_tiep_id?.alias,
    data.giam_sat_an_toan_id?.alias,
    data.phieu_giao_viec_id.chi_huy_truc_tiep_id?.alias,
    data.lanh_dao_cong_viec_id?.alias,
    data.nguoi_cho_phep_id?.alias,
    data.nguoi_cap_phieu_id?.alias,
  ];
}

export function getAliasPhieuKTPTCKH(data) {
  return [
    data.doi_truong_id?.alias,
    data.to_truong_id?.alias,
    data.chi_huy_truc_tiep_id?.alias,
    data.giam_doc_id?.alias,
  ];
}

export async function addImageToPhieuGiaoViec(data, type, resultFilePath) {

  const sigGiaoPhieuPath = getAndCheckImagePath(data?.nguoi_giao_phieu_id?.chu_ky_id, STORE_DIRS.CHU_KY);
  const sigTiepNhanPath = getAndCheckImagePath(data?.nguoi_tiep_nhan_id?.chu_ky_id, STORE_DIRS.CHU_KY);
  const sigTraPhieuPath = getAndCheckImagePath(data?.nguoi_khoa_phieu_id?.chu_ky_id, STORE_DIRS.CHU_KY);
  const sigXacNhanKhoaPath = getAndCheckImagePath(data?.nguoi_xac_nhan_khoa_id?.chu_ky_id, STORE_DIRS.CHU_KY);
  let docxImager = new DocxImager();
  await docxImager.load(resultFilePath);
  switch (type) {
    case LOAI_CONG_VIEC.DO_DIEN_TRO_TIEP_DIA.type:
      sigGiaoPhieuPath && docxImager.replaceWithLocalImage(sigGiaoPhieuPath, 1, getFileExtension(sigGiaoPhieuPath));
      break;
    case TRANG_THAI_PHIEU.GIAO_PHIEU.code:
      sigGiaoPhieuPath && docxImager.replaceWithLocalImage(sigGiaoPhieuPath, 2, getFileExtension(sigGiaoPhieuPath));
      break;
    case TRANG_THAI_PHIEU.TIEP_NHAN.code:
      sigTiepNhanPath && docxImager.replaceWithLocalImage(sigTiepNhanPath, 1, getFileExtension(sigTiepNhanPath));
      sigGiaoPhieuPath && docxImager.replaceWithLocalImage(sigGiaoPhieuPath, 2, getFileExtension(sigGiaoPhieuPath));
      break;
    case TRANG_THAI_PHIEU.KHOA_PHIEU.code:
      sigTiepNhanPath && docxImager.replaceWithLocalImage(sigTiepNhanPath, 1, getFileExtension(sigTiepNhanPath));
      sigGiaoPhieuPath && docxImager.replaceWithLocalImage(sigGiaoPhieuPath, 2, getFileExtension(sigGiaoPhieuPath));
      sigTraPhieuPath && docxImager.replaceWithLocalImage(sigTraPhieuPath, 4, getFileExtension(sigTraPhieuPath));
      break;
    case TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code:
      sigTiepNhanPath && docxImager.replaceWithLocalImage(sigTiepNhanPath, 1, getFileExtension(sigTiepNhanPath));
      sigGiaoPhieuPath && docxImager.replaceWithLocalImage(sigGiaoPhieuPath, 2, getFileExtension(sigGiaoPhieuPath));
      sigXacNhanKhoaPath && docxImager.replaceWithLocalImage(sigXacNhanKhoaPath, 3, getFileExtension(sigXacNhanKhoaPath));
      sigTraPhieuPath && docxImager.replaceWithLocalImage(sigTraPhieuPath, 4, getFileExtension(sigTraPhieuPath));
      break;
    default:
  }
  await docxImager.save(resultFilePath);
}

export async function addImageToPhieuCongtac(data, resultFilePath) {
  let docxImager = new DocxImager();
  await docxImager.load(resultFilePath);

  const sigNguoiCapPhieuPath = getAndCheckImagePath(data?.nguoi_cap_phieu_id?.chu_ky_id, STORE_DIRS.CHU_KY);
  const sigNguoiChoPhepPath = getAndCheckImagePath(data?.nguoi_cho_phep_id?.chu_ky_id, STORE_DIRS.CHU_KY);
  const sigLanhDaoPath = getAndCheckImagePath(data?.lanh_dao_cong_viec_id?.chu_ky_id, STORE_DIRS.CHU_KY);
  const sigGiamSatPath = getAndCheckImagePath(data?.giam_sat_an_toan_id?.chu_ky_id, STORE_DIRS.CHU_KY);
  const sigChiHuyPath = getAndCheckImagePath(data?.phieu_giao_viec_id.chi_huy_truc_tiep_id?.chu_ky_id, STORE_DIRS.CHU_KY);

  sigNguoiCapPhieuPath && docxImager.replaceWithLocalImage(sigNguoiCapPhieuPath, 1, getFileExtension(sigNguoiCapPhieuPath));
  sigNguoiCapPhieuPath && docxImager.replaceWithLocalImage(sigNguoiCapPhieuPath, 9, getFileExtension(sigNguoiCapPhieuPath));
  sigNguoiChoPhepPath && docxImager.replaceWithLocalImage(sigNguoiChoPhepPath, 2, getFileExtension(sigNguoiChoPhepPath));
  sigNguoiChoPhepPath && docxImager.replaceWithLocalImage(sigNguoiChoPhepPath, 8, getFileExtension(sigNguoiChoPhepPath));
  sigLanhDaoPath && docxImager.replaceWithLocalImage(sigLanhDaoPath, 3, getFileExtension(sigLanhDaoPath));
  sigLanhDaoPath && docxImager.replaceWithLocalImage(sigLanhDaoPath, 7, getFileExtension(sigLanhDaoPath));
  sigChiHuyPath && docxImager.replaceWithLocalImage(sigChiHuyPath, 4, getFileExtension(sigChiHuyPath));
  sigChiHuyPath && docxImager.replaceWithLocalImage(sigChiHuyPath, 6, getFileExtension(sigChiHuyPath));
  sigGiamSatPath && docxImager.replaceWithLocalImage(sigGiamSatPath, 5, getFileExtension(sigGiamSatPath));

  await docxImager.save(resultFilePath);
}

export async function addImageToPhieuKTPTCKH(data, resultFilePath) {

  let docxImager = new DocxImager();
  await docxImager.load(resultFilePath);

  const sigDoiTruongPath = getAndCheckImagePath(data?.doi_truong_id?.chu_ky_id, STORE_DIRS.CHU_KY);
  const sigToTruongPath = getAndCheckImagePath(data?.to_truong_id?.chu_ky_id, STORE_DIRS.CHU_KY);
  const sigChiHuyPath = getAndCheckImagePath(data?.chi_huy_truc_tiep_id?.chu_ky_id, STORE_DIRS.CHU_KY);
  const sigGiamDocPath = getAndCheckImagePath(data?.giam_doc_id?.chu_ky_id, STORE_DIRS.CHU_KY);

  sigDoiTruongPath && docxImager.replaceWithLocalImage(sigDoiTruongPath, 1, getFileExtension(sigDoiTruongPath));
  sigToTruongPath && docxImager.replaceWithLocalImage(sigToTruongPath, 2, getFileExtension(sigToTruongPath));
  sigChiHuyPath && docxImager.replaceWithLocalImage(sigChiHuyPath, 3, getFileExtension(sigChiHuyPath));
  sigGiamDocPath && docxImager.replaceWithLocalImage(sigGiamDocPath, 4, getFileExtension(sigGiamDocPath));

  await docxImager.save(resultFilePath);
}

export async function digitalSign(res, data, inPDFPath, outFileName, coordinatesResult = [], setupDefault, loaiPhieu) {
  let index = 0;
  let client;
  client = await connectEVNSignService(setupDefault.api_ky_dien_tu);
  if (!client) {
    return responseAction.error(res, CommonError[402]);
  }
  for (let coordinate of coordinatesResult) {
    const outputToBase64 = getFilePath('signed_file.pdf', getDirPath('digitalSignFile', './server/templates'));
    if (outputToBase64) {
      const encodedPdf = index === 0 ? base64Encode(inPDFPath) : base64Encode(outputToBase64);
      if (!!coordinate.alias) {
        index++;
        let { height, width, marginLeft, marginTop } = coordinateRectangle(coordinate, loaiPhieu);
        const args = {
          arg0: setupDefault.sign_appcode, //Appcode
          arg1: setupDefault.sign_password, // Password
          arg2: '', // Serial number
          arg3: coordinate.alias, // Alias
          arg4: encodedPdf, // File base64
          arg5: 'SHA-1', //Digest Algorithm
          // Display rectangle text config
          arg6: {
            numberPageSign: coordinate.numPageSign,
            locateSign: 1,
            heightRectangle: height,
            widthRectangle: width,
            marginLeftOfRectangle: marginLeft,
            marginTopOfRectangle: marginTop,
            formatRectangleText: ' ',
          },
        };
        let trySign = false;
        while (!trySign) {
          trySign = await signPdfBase64RectangleText(res, client, args, outputToBase64);
        }
      }
    }
  }
}

export function coordinateRectangle(coordinate, loaiPhieu) {
  let height, width, marginLeft, marginTop;
  switch (loaiPhieu) {
    case LOAI_PHIEU.GIAO_VIEC:
      height = 80;
      width = 120;
      marginLeft = coordinate.x;
      marginTop = coordinate.y;
      break;
    case LOAI_PHIEU.PHIEU_CONG_TAC:
      height = 30;
      width = 45;
      marginLeft = coordinate.x + 60;
      marginTop = coordinate.y - 20;
      break;
    case  LOAI_PHIEU.KIEM_TRA_PTCKH:
      height = 70;
      width = 130;
      marginLeft = coordinate.x;
      marginTop = coordinate.y - 15;
      break;
    default:
  }
  return { height, width, marginLeft, marginTop };
}
