import { DO_THONG_SO, LOAI_CONG_VIEC } from '../../DanhMuc/LoaiCongViec';
import { getAndCheckImagePath, getDirPath, getFileExtension, getFilePath } from '../../../utils/fileUtils';
import * as CaiDatHeThongService from '../../CaiDatHeThong/caiDatHeThong.service';
import { STORE_DIRS, TEMPLATES_DIRS, TEXT_TO_FIND } from '../../../constant/constant';
import {
  addImageToPhieuGiaoViec,
  digitalSign,
  getAliasPhieuCongTac,
  getAliasPhieuGiaoViec,
  getAliasPhieuKTPTCKH,
  addImageToPhieuCongtac,
  addImageToPhieuKTPTCKH,
} from './generate.service';
import {
  addImageToExcel,
  convertFileToPDF,
  getCoordinatesSign,
  renderDataToFileTemp,
  updateFileTempByColumns,
} from './generate.common';

export const LOAI_PHIEU = {
  GIAO_VIEC: 'PHIEU_GIAO_VIEC',
  PHIEU_CONG_TAC: 'PHIEU_CONG_TAC',
  KIEM_TRA_PTCKH: 'KIEM_TRA_PTCKH',
};

export async function generateDocument(res, data, templateFilePath, outputFileName = '') {
  const fileAfterRender = await renderDataToFileTemp(templateFilePath, data);
  res.download(fileAfterRender, outputFileName);
}

export async function generateDocumentByColumns(res, data, templateFilePath, outputFileName = '', selectedColumns = null) {
  const fileTemplateFinal = await updateFileTempByColumns(templateFilePath, selectedColumns);
  const fileAfterRender = await renderDataToFileTemp(fileTemplateFinal, data);
  res.download(fileAfterRender, outputFileName);
}

export async function generateDocumentModuleCTXD(res, data, templateFileName, outputFileName) {
  const templateFilePath = getFilePath(templateFileName, TEMPLATES_DIRS.CONG_TRINH_XAY_DUNG);
  const fileAfterRender = await renderDataToFileTemp(templateFilePath, data);
  res.download(fileAfterRender, outputFileName);
}

export async function generateExcel(res, data, templateFileName, imageRange, outputFileName, templatePDFFile = null, isPdfExtension = false) {
  let templateFilePath = getFilePath(templateFileName, TEMPLATES_DIRS.PHIEU_DO_THONG_SO);
  const chuKyDoiTruong = getAndCheckImagePath(data?.nguoi_giao_phieu_id?.chu_ky_id, STORE_DIRS.CHU_KY);
  const templateXlsxPath = getFilePath('newfile.xlsx', TEMPLATES_DIRS.OUTPUT_FILE);

  !!chuKyDoiTruong && await addImageToExcel(chuKyDoiTruong, templateFilePath, imageRange, templateXlsxPath);

  const excelAfterRender = await renderDataToFileTemp(!!chuKyDoiTruong ? templateXlsxPath : templateFilePath, data);
  if (!isPdfExtension) {
    return res.download(excelAfterRender, outputFileName);
  }
  const outPDFPath = getFilePath(templatePDFFile, TEMPLATES_DIRS.PDF);
  const excelAfterConvert = await convertFileToPDF(res, excelAfterRender, getFileExtension(excelAfterRender), outPDFPath);
  res.download(excelAfterConvert, outputFileName);
}

export async function generateDocumentWithImage(res, data, tmpFileName, type, outFileName, isPdfExt = false) {
  let templateFilePath = getFilePath(tmpFileName, TEMPLATES_DIRS.TEMPLATES);
  if (type === DO_THONG_SO) {
    templateFilePath = getFilePath(tmpFileName, TEMPLATES_DIRS.PHIEU_DO_THONG_SO);
  }
  await fiveStepsToGenerate(res, LOAI_PHIEU.GIAO_VIEC, data, templateFilePath, outFileName, isPdfExt, type);
}

export async function generatePhieuCongTac(res, data, tmpFileName, outFileName, isPdfExt = false) {
  const templateFilePath = getFilePath(tmpFileName, TEMPLATES_DIRS.TEMPLATES);
  await fiveStepsToGenerate(res, LOAI_PHIEU.PHIEU_CONG_TAC, data, templateFilePath, outFileName, isPdfExt);
}

// export async function generatePhieuKTPTCKH(res, data, tmpFileName, outFileName, isPdfExt = false) {
//   const templateFilePath = getFilePath(tmpFileName, getDirPath('templates', './server'));
//   await fiveStepsToGenerate(res, LOAI_PHIEU.KIEM_TRA_PTCKH, data, templateFilePath, outFileName, isPdfExt);
// }

export async function fiveStepsToGenerate(res, loaiPhieu, data, templateFilePath, outFileName, isPdfExt = false, type) {
  // Step 1: Render data to file using carbone.render()
  const fileAfterRender = await renderDataToFileTemp(templateFilePath, data);

  // Step 2: Add sign image to file
  let { listAlias, textToFind } = await addImgAndTakeAlias(data, type, fileAfterRender, loaiPhieu);
  if (!isPdfExt) {
    return res.download(fileAfterRender, outFileName);
  }

  // Step 3: Convert file to PDF using carbone.convert()
  const outPDFPath = getFilePath('digital_sign.pdf', TEMPLATES_DIRS.DIGITAL_SIGN);
  const fileAfterConvert = await convertFileToPDF(res, fileAfterRender, getFileExtension(fileAfterRender), outPDFPath);

  // Step 4: Get cordinates to digital sign in PDF
  const setupDefault = await CaiDatHeThongService.getOne({ is_deleted: false });
  let downloadWithSign = false;
  let listCoordinates = [];
  const newListAlias = listAlias.filter(alias => !!alias);
  if (newListAlias.length > 0) {
    listCoordinates = await getCoordinatesSign(outPDFPath, textToFind, listAlias);
    downloadWithSign = true;
  }

  // Step 5: Connect sign server service to sign file
  if (downloadWithSign && setupDefault.api_ky_dien_tu) {
    await digitalSign(res, data, fileAfterConvert, outFileName, listCoordinates, setupDefault, loaiPhieu);
    res.download(getFilePath('signed_file.pdf', TEMPLATES_DIRS.DIGITAL_SIGN), outFileName);
  } else {
    res.download(fileAfterConvert, outFileName);
  }
}

export async function addImgAndTakeAlias(data, type, fileAfterRender, loaiPhieu) {
  let listAlias = [], textToFind = '';
  switch (loaiPhieu) {
    case LOAI_PHIEU.GIAO_VIEC:
      await addImageToPhieuGiaoViec(data, type, fileAfterRender);
      listAlias = getAliasPhieuGiaoViec(data, type);
      textToFind = TEXT_TO_FIND.PHIEU_GIAO_VIEC;
      break;
    case LOAI_PHIEU.PHIEU_CONG_TAC:
      await addImageToPhieuCongtac(data, fileAfterRender);
      listAlias = getAliasPhieuCongTac(data, type);
      textToFind = TEXT_TO_FIND.PHIEU_CONG_TAC;
      break;
    case  LOAI_PHIEU.KIEM_TRA_PTCKH:
      await addImageToPhieuKTPTCKH(data, fileAfterRender);
      listAlias = getAliasPhieuKTPTCKH(data, type);
      textToFind = TEXT_TO_FIND.PHIEU_KT_KH;
      break;
    default:
  }
  return { listAlias, textToFind };
}
