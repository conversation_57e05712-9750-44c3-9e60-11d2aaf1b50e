import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import {
  DON_VI,
  DUONG_DAY,
  DUONG_DAY_CHUNG,
  LOAI_DUONG_DAY,
  REPORT_TRAO_LUU_CONG_SUAT,
} from '../../../constant/dbCollections';

const schema = new Schema({
  ten_duong_day: { type: String, required: true, validate: /\S+/ },
  ma_duong_day: { type: String, required: true, validate: /\S+/ },
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  loai_duong_day_id: { type: Schema.Types.ObjectId, ref: LOAI_DUONG_DAY },
  asset_id: String,
  dao_nguoc_thong_so: { type: Boolean, default: false },
  i: String,
  p: String,
  q: String,
  u: String,
  thoi_diem_thong_so: Date,
  thoi_gian_dong_bo: Date,
  i_dinh_muc: String,

  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(REPORT_TRAO_LUU_CONG_SUAT, schema, REPORT_TRAO_LUU_CONG_SUAT);
