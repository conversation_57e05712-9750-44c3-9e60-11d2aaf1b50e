import express from 'express';
import passport from 'passport';
import * as Controller from './reportCongSuat.controller';

export const reportCongSuatRouter = express.Router();

reportCongSuatRouter.use(passport.authenticate('jwt', { session: false }));
reportCongSuatRouter
  .route('/')
  .get(Controller.getAll)
  .post(Controller.create);

reportCongSuatRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);


