import * as Service from './reportCongSuat.service';
import Model from './reportCongSuat.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import queryHelper from '../../../helpers/queryHelper';
import * as responseHelper from '../../../helpers/responseHelper';

const searchLike = [];
const populateOpts = [];
const uniqueOpts = [];
const sortGetALl = { created_at: -1 };
const pupulateOptsGetAll = [];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);


export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_phieu_mau']);
    const { criteria, options } = query;
    const data = await Model.find({ thoi_diem_thong_so: { $lte: criteria.thoi_diem_thong_so } })
      .sort({ thoi_diem_thong_so: -1 }).limit(1).lean();
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}
