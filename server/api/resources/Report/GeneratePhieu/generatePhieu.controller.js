import { LOAI_CONG_VIEC } from '../../DanhMuc/LoaiCongViec';
import * as ReportPhieuGiaoViecService from '../ReportPhieuGiaoViec/reportPhieuGiaoViec.service';
import { getDirPath, getFileExtension, getFilePath } from '../../../utils/fileUtils';
import { convertFileToPDF, getCoordinatesSign, renderDataToFileTemp } from '../GenerateFile/generate.common';
import { replaceAll } from '../../../common/functionCommons';
import {
  addImageToPhieuCongtac,
  addImageToPhieuGiaoViec, addImageToPhieuKTPTCKH, getAliasPhieuCongTac,
  getAliasPhieuGiaoViec, getAliasPhieuKTPTCKH,
} from '../GenerateFile/generate.service';

import * as GeneratePhieuService from './generatePhieu.service';
import { EXTENSION_FILE, STORE_DIRS, TEMPLATES_DIRS } from '../../../constant/constant';
import { TEXT_TO_FIND } from '../../../constant/constant';
import { LOAI_PHIEU } from '../GenerateFile/generate.controller';
import * as CaiDatHeThongService from '../../CaiDatHeThong/caiDatHeThong.service';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import * as responseHelper from '../../../helpers/responseHelper';
import CommonError from '../../../error/CommonError';

export async function generatePhieu(res, dataPhieuGiaoViec, trangThaiPhieu, extensionFile = EXTENSION_FILE.DOCX) {

  function getTemplateFileName() {
    //Hàm lấy tên tập tin mẫu dùng để render dữ liệu
    switch (trangThaiPhieu) {
      case TRANG_THAI_PHIEU.GIAO_PHIEU.code:
      case TRANG_THAI_PHIEU.TIEP_NHAN.code:
        return getFileNameLenhCongTac(dataToRender.loai_cong_viec);
      case TRANG_THAI_PHIEU.KHOA_PHIEU.code:
      case TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code:
        return getFileNameKetQuaPhieuGiaoVice(dataToRender.loai_cong_viec);
    }
  }

  async function getDataPhieu() {
    switch (dataPhieuGiaoViec.loai_cong_viec) {
      case LOAI_CONG_VIEC.CONG_TAC_PHU_TRO.code:
        return await ReportPhieuGiaoViecService.getInfoPhieuGiaoViec({ id: dataPhieuGiaoViec._id });
      case LOAI_CONG_VIEC.CO_KE_HOACH.code:
      case LOAI_CONG_VIEC.KHONG_CO_KE_HOACH.code:
      case LOAI_CONG_VIEC.DUY_TU_KET_HOP_KHAC_PHUC.code:
        return await ReportPhieuGiaoViecService.getSuaChuaBaoDuongByID({ id: dataPhieuGiaoViec._id });
      default:
        return await ReportPhieuGiaoViecService.getPhieuKiemTraDuongDayByID({ id: dataPhieuGiaoViec._id });
    }
  }

  const dataToRender = await getDataPhieu();

  if (!dataToRender) return responseHelper.error(res, CommonError.NOT_FOUND);

  dataToRender.loai_cv = LOAI_CONG_VIEC[dataToRender.loai_cong_viec]?.name;

  const templateFileName = getTemplateFileName();

  // Render file docx
  const templateFilePath = getFilePath(templateFileName, TEMPLATES_DIRS.TEMPLATES);
  const fileAfterRender = await renderDataToFileTemp(templateFilePath, dataToRender);
  let [listAlias, textToFind] = await addImgAndTakeAlias(dataToRender, trangThaiPhieu, fileAfterRender, LOAI_PHIEU.GIAO_VIEC);
  if (extensionFile == EXTENSION_FILE.DOCX) {
    return fileAfterRender;
  }
  // const folderName = dataToRender.don_vi_giao_phieu_id.ma_don_vi;
  const folderName = [dataToRender.don_vi_giao_phieu_id.ma_don_vi, 'v2'].join('_');

  const soPhieu = replaceAll(dataToRender.so_phieu, '/', '-');
  const outPDFPath = getFilePath(`${[soPhieu, trangThaiPhieu].join('-')}.pdf`, getDirPath(folderName, STORE_DIRS.PHIEU_GIAO_VIEC));
  console.log('outPDFPath111', outPDFPath);
  console.log('getDirPath', getDirPath(folderName, STORE_DIRS.PHIEU_GIAO_VIEC));
  const fileAfterConvert = await convertFileToPDF(res, fileAfterRender, getFileExtension(fileAfterRender), outPDFPath);
  console.log('fileAfterConvert', fileAfterConvert);
  const setupDefault = await CaiDatHeThongService.getOne({ is_deleted: false });
  let downloadWithSign = false;
  let listCoordinates = [];
  const newListAlias = listAlias.filter(alias => !!alias);
  if (newListAlias.length > 0) {
    listCoordinates = await getCoordinatesSign(outPDFPath, textToFind, listAlias);
    downloadWithSign = true;
  }

  if (downloadWithSign && setupDefault.api_ky_dien_tu) {
    await GeneratePhieuService.digitalSign(res, dataToRender, fileAfterConvert, listCoordinates, setupDefault, LOAI_PHIEU.GIAO_VIEC);
  }
  console.log(1);
}

function getFileNameLenhCongTac(loaiCongViec) {
  switch (loaiCongViec) {
    case LOAI_CONG_VIEC.CONG_TAC_PHU_TRO.code:
      return 'lenh_cong_tac_cong_viec_phu_tro.docx';
    case LOAI_CONG_VIEC.CO_KE_HOACH.code:
      return 'lenh_cong_tac_sua_chua_co_ke_hoach.docx';
    case LOAI_CONG_VIEC.KHONG_CO_KE_HOACH.code:
    case LOAI_CONG_VIEC.DUY_TU_KET_HOP_KHAC_PHUC.code:
      return 'lenh_cong_tac_sua_chua_bao_tri_duong_day.docx';
    default:
      return 'phieu_tiep_nhan_cong_viec.docx';
  }
}

function getFileNameKetQuaPhieuGiaoVice(loaiCongViec) {
  switch (loaiCongViec) {
    case LOAI_CONG_VIEC.CONG_TAC_PHU_TRO.code:
      return 'phieu_cong_viec_phu_tro.docx';
    case LOAI_CONG_VIEC.CO_KE_HOACH.code:
      return 'phieu_sua_chua_bao_duong_co_ke_hoach_da_ky.docx';
    case LOAI_CONG_VIEC.KHONG_CO_KE_HOACH.code:
      return 'phieu_sua_chua_bao_duong_da_ky.docx';
    case LOAI_CONG_VIEC.DUY_TU_KET_HOP_KHAC_PHUC.code:
      return 'phieu_sua_chua_bao_duong_ket_hop_da_ky.docx';
    default:
      return 'phieu_ket_qua_kiem_tra_da_ky.docx';
  }
}

export async function addImgAndTakeAlias(data, type, fileAfterRender, loaiPhieu) {
  switch (loaiPhieu) {
    case LOAI_PHIEU.GIAO_VIEC:
      await addImageToPhieuGiaoViec(data, type, fileAfterRender);
      return [getAliasPhieuGiaoViec(data, type), TEXT_TO_FIND.PHIEU_GIAO_VIEC];
    case LOAI_PHIEU.PHIEU_CONG_TAC:
      await addImageToPhieuCongtac(data, fileAfterRender);
      return [getAliasPhieuCongTac(data, type), TEXT_TO_FIND.PHIEU_CONG_TAC];
    case  LOAI_PHIEU.KIEM_TRA_PTCKH:
      await addImageToPhieuKTPTCKH(data, fileAfterRender);
      return [getAliasPhieuKTPTCKH(data, type), TEXT_TO_FIND.PHIEU_KT_KH];
    default:
  }
}
