import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import { connectEVNSignService, signPdfBase64RectangleText } from '../GenerateFile/generate.common';
import { base64Encode } from '../../../common/functionCommons';
import { LOAI_PHIEU } from '../GenerateFile/generate.controller';

export function getAliasPhieuGiaoViec(data, type) {
  let listAlias;
  const aliasTiepNhan = data.nguoi_tiep_nhan_id?.alias;
  const aliasGiaoPhieu = data.nguoi_giao_phieu_id?.alias;
  const aliasXacNhanKhoa = data.nguoi_xac_nhan_khoa_id?.alias;
  const aliasKhoaPhieu = data.nguoi_khoa_phieu_id?.alias;
  switch (type) {
    case TRANG_THAI_PHIEU.GIAO_PHIEU.code:
    case TRANG_THAI_PHIEU.TIEP_NHAN.code:
      listAlias = [aliasTiepNhan, aliasGiaoPhieu];
      break;
    case TRANG_THAI_PHIEU.KHOA_PHIEU.code:
    case TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code:
      listAlias = [aliasTiepNhan, aliasGiaoPhieu, aliasXacNhanKhoa, aliasKhoaPhieu];
      break;
    default:
  }
  return listAlias;
}

export function getAliasPhieuCongTac(data) {
  return [
    data.nguoi_cap_phieu_id?.alias,
    data.nguoi_cho_phep_id?.alias,
    data.lanh_dao_cong_viec_id?.alias,
    data.phieu_giao_viec_id.chi_huy_truc_tiep_id?.alias,
    data.giam_sat_an_toan_id?.alias,
    data.phieu_giao_viec_id.chi_huy_truc_tiep_id?.alias,
    data.lanh_dao_cong_viec_id?.alias,
    data.nguoi_cho_phep_id?.alias,
    data.nguoi_cap_phieu_id?.alias,
  ];
}

export function getAliasPhieuKTPTCKH(data) {
  return [
    data.doi_truong_id?.alias,
    data.to_truong_id?.alias,
    data.chi_huy_truc_tiep_id?.alias,
    data.giam_doc_id?.alias,
  ];
}


export async function digitalSign(res, data, inPDFPath, coordinatesResult = [], setupDefault, loaiPhieu) {
  let index = 0;
  let client;
  client = await connectEVNSignService(setupDefault.api_ky_dien_tu);
  if (!client) return;
  for (let coordinate of coordinatesResult) {
    const encodedPdf = base64Encode(inPDFPath);
    if (!!coordinate.alias) {
      index++;
      let { height, width, marginLeft, marginTop } = coordinateRectangle(coordinate, loaiPhieu);
      const args = {
        arg0: setupDefault.sign_appcode, //Appcode
        arg1: setupDefault.sign_password, // Password
        arg2: '', // Serial number
        arg3: coordinate.alias, // Alias
        arg4: encodedPdf, // File base64
        arg5: 'SHA-1', //Digest Algorithm
        // Display rectangle text config
        arg6: {
          numberPageSign: coordinate.numPageSign,
          locateSign: 1,
          heightRectangle: height,
          widthRectangle: width,
          marginLeftOfRectangle: marginLeft,
          marginTopOfRectangle: marginTop,
          formatRectangleText: ' ',
        },
      };
      let trySign = false;
      while (!trySign) {
        trySign = await signPdfBase64RectangleText(res, client, args, inPDFPath);
      }
    }
  }
}

export function coordinateRectangle(coordinate, loaiPhieu) {
  let height, width, marginLeft, marginTop;
  switch (loaiPhieu) {
    case LOAI_PHIEU.GIAO_VIEC:
      height = 80;
      width = 120;
      marginLeft = coordinate.x;
      marginTop = coordinate.y;
      break;
    case LOAI_PHIEU.PHIEU_CONG_TAC:
      height = 30;
      width = 45;
      marginLeft = coordinate.x + 60;
      marginTop = coordinate.y - 20;
      break;
    case  LOAI_PHIEU.KIEM_TRA_PTCKH:
      height = 70;
      width = 130;
      marginLeft = coordinate.x;
      marginTop = coordinate.y - 15;
      break;
    default:
  }
  return { height, width, marginLeft, marginTop };
}
