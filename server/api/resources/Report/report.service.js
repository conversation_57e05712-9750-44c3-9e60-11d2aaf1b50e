import momentTimezone from 'moment-timezone';

import * as PhieuGiaoViecService from '../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';
import * as NoiDungKiemTraService from '../DanhMuc/NoiDungKiemTra/noiDung.service';
import * as NguoiCongTacService from '../QuanLyVanHanh/NguoiCongTac/nguoiCongTac.service';
import * as ViTriCongViecService from '../QuanLyVanHanh/ViTriCongViec/viTriCongViec.service';
import * as CotDienService from '../TongKe/CotDien/cotDien.service';
import * as TiepDatService from '../TongKe/TiepDat/tiepDat.service';
import * as DayChongSetService from '../TongKe/DayChongSet/dayChongSet.service';
import * as DayCapQuangService from '../TongKe/DayCapQuang/dayCapQuang.service';
import * as GiaoCheoService from '../TongKe/GiaoCheo/giaoCheo.service';
import * as VanHanhService from '../TongKe/VanHanh/vanHanh.service';
import * as KetQuaSuaChuaCoKeHoachService from '../QuanLyVanHanh/KetQuaSuaChuaCoKeHoach/ketQuaSuaChuaCoKeHoach.service';

import PHIEU_GIAO_VIEC from '../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.model';
import DonViModel from '../DonVi/donVi.model';
import ViTriModel from '../TongKe/ViTri/viTri.model';
import KET_QUA_KIEM_TRA from '../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.model';

import { GENDER_OPTIONS, TINH_TRANG_PHIEU } from '../../constant/constant';
import { TRANG_THAI_PHIEU } from '../DanhMuc/TrangThaiCongViec';
import { LOAI_CONG_VIEC, NHOM_CONG_VIEC } from '../DanhMuc/LoaiCongViec';
import { TRANG_THAI_HOAN_THANH } from '../DanhMuc/TrangThaiHoanThanh';
import { LOAI_TAI_KHOAN } from '../User/LoaiTaiKhoan';
import { TRANG_THAI_XU_LY } from '../DanhMuc/TrangThaiXuLy';

import { addIndexToListData, downlineData, groupBy } from '../../common/functionCommons';
import { formatDate, formatDateTime, formatToDateDetail } from '../../common/formatUTCDateToLocalDate';
import { extractIds } from '../../utils/dataconverter';
import * as DonViService from '../DonVi/donVi.service';
import quanLyVanHanhCommons from '../QuanLyVanHanh/quanLyVanHanhCommons';
import * as KetQuaKiemTraService from '../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.service';
import queryHelper from '../../helpers/queryHelper';

export async function buildQueryTime(query, dataResponse) {
  let result = {};
  if (query.thoi_gian === 'DATE') {
    const oneDay = 24 * 60 * 60 * 1000;
    query.thoi_gian_cong_tac_bat_dau = {
      $gte: momentTimezone(query.date_filter),
      $lt: momentTimezone(new Date(query.date_filter?.getTime() + oneDay)),
    };
    delete query.date_filter;
    dataResponse.tu_ngay = formatDate(momentTimezone(query.date_filter));
    dataResponse.den_ngay = formatDate(momentTimezone(query.date_filter));
  } else if (query.thoi_gian === 'MONTH') {
    query.thoi_gian_cong_tac_bat_dau = {
      $gte: momentTimezone().startOf('month'),
      $lt: momentTimezone().endOf('month'),
    };
    dataResponse.tu_ngay = formatDate(momentTimezone().startOf('month'));
    dataResponse.den_ngay = formatDate(new Date());
  } else if (query.thoi_gian === 'QUARTER') {
    query.thoi_gian_cong_tac_bat_dau = {
      $gte: momentTimezone().startOf('quarter'),
      $lt: momentTimezone().endOf('quarter'),
    };
    dataResponse.tu_ngay = formatDate(momentTimezone().startOf('quarter'));
    dataResponse.den_ngay = formatDate(new Date());
  } else if (query.thoi_gian === 'YEAR') {
    query.thoi_gian_cong_tac_bat_dau = {
      $gte: momentTimezone().startOf('year'),
      $lt: momentTimezone().endOf('year'),
    };
    dataResponse.tu_ngay = formatDate(momentTimezone().startOf('year'));
    dataResponse.den_ngay = formatDate(new Date());
  } else if (query.tu_ngay && query.den_ngay) {
    if (!query.tu_ngay?.hasOwnProperty('$exists')) {
      if (!query.den_ngay?.hasOwnProperty('$exists')) {
        query.thoi_gian_cong_tac_bat_dau = { $gte: new Date(query.tu_ngay), $lt: new Date(query.den_ngay) };
        dataResponse.tu_ngay = query.tu_ngay ? formatDate(new Date(query.tu_ngay)) : '';
        dataResponse.den_ngay = query.den_ngay ? formatDate(new Date(query.den_ngay)) : '';
      } else {
        query.thoi_gian_cong_tac_bat_dau = { $gte: new Date(query.tu_ngay) };
        dataResponse.tu_ngay = query.tu_ngay ? formatDate(new Date(query.tu_ngay)) : '';
      }
    } else if (query.tu_ngay?.hasOwnProperty('$exists')) {
      if (!query.den_ngay?.hasOwnProperty('$exists')) {
        query.thoi_gian_cong_tac_bat_dau = { $lt: new Date(query.den_ngay) };
        dataResponse.den_ngay = query.den_ngay ? formatDate(new Date(query.den_ngay)) : '';
      }
    }
  }

  const tenDonVi = await DonViModel.findById(query.don_vi);
  dataResponse.ten_don_vi = tenDonVi?.ten_don_vi;

  delete query.tu_ngay;
  delete query.den_ngay;
  delete query.don_vi;
  delete query.don_vi_id;
  delete query.key_tabs;
  delete query.thoi_gian;
  delete query.date_filter;

  result.query = query;
  result.dataResponse = dataResponse;
  return result;
}

export async function getPhieuGiaoViec(query) {
  let dataResponse = {};
  let congViec;
  query.cong_viec && (congViec = query.cong_viec) && delete query.cong_viec;

  query.tinh_trang_phieu && delete query.tinh_trang_phieu;
  const queryBuild = await buildQueryTime(query, dataResponse);
  query = queryBuild.query;
  dataResponse = queryBuild.dataResponse;

  let phieuGiaoViecList = (await PhieuGiaoViecService.getAllPopulate(query)).sort((a, b) => new Date(b?.created_at) - new Date(a?.created_at));

  phieuGiaoViecList.map(phieu => {
    phieu.cong_viec = LOAI_CONG_VIEC[phieu.loai_cong_viec].type;
    phieu.loai_cong_viec = LOAI_CONG_VIEC[phieu.loai_cong_viec].name;
    phieu.trang_thai_cong_viec = TRANG_THAI_PHIEU[phieu.trang_thai_cong_viec].name;
    phieu.created_at = formatDateTime(phieu.created_at);
    phieu.thoi_gian_cong_tac_bat_dau = phieu.thoi_gian_cong_tac_bat_dau ? formatDateTime(phieu.thoi_gian_cong_tac_bat_dau) : '';
    phieu.thoi_gian_cong_tac_ket_thuc = phieu.thoi_gian_cong_tac_ket_thuc ? formatDateTime(phieu.thoi_gian_cong_tac_ket_thuc) : '';
  });
  congViec && (phieuGiaoViecList = phieuGiaoViecList.filter(item => item.cong_viec === congViec));

  dataResponse.phieu_giao_viec = addIndexToListData(phieuGiaoViecList);
  return dataResponse;
}

export async function getViTriByID(criteria) {
  const data = await ViTriModel.findById(criteria.id)
    .populate([
      { path: 'cong_trinh_id', populate: { path: 'don_vi_id' } },
      {
        path: 'don_vi_id',
        populate: { path: 'don_vi_cha_id', populate: { path: 'don_vi_cha_id' } },
      },
    ]).lean();

  data.cot_dien = await CotDienService.getAll({ vi_tri_id: data._id, is_deleted: false });
  data.tiep_dat = await TiepDatService.getAll({ vi_tri_id: data._id, is_deleted: false });
  data.giao_cheo = await GiaoCheoService.getAll({ vi_tri_id: data._id, is_deleted: false });
  data.day_chong_set = await DayChongSetService.getAll({ vi_tri_id: data._id, is_deleted: false });
  data.day_cap_quang = await DayCapQuangService.getAll({ vi_tri_id: data._id, is_deleted: false });

  data.so_day_chong_set = data.day_chong_set?.length || 0;

  const allVanHanh = await VanHanhService.findAllInOne({ vi_tri_id: data._id, is_deleted: false });

  const duongDayIds = [], mapDuongDayID = {};
  allVanHanh.forEach(vanHanh => {
    vanHanh.so_luong_pha = vanHanh.day_dan?.length;
    if (vanHanh.duong_day_id?._id) {
      const duongDayId = vanHanh.duong_day_id._id.toString();
      duongDayIds.push(duongDayId);
      mapDuongDayID[duongDayId] = [];
    }
  });

  const allPhieuGiaoViec = await PHIEU_GIAO_VIEC.find({
    $or: [
      { duong_day_ids: { $in: duongDayIds } },
      { duong_day_id: duongDayIds },
    ],
    trang_thai_cong_viec: TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code,
  })
    .select('duong_day_id duong_day_ids loai_cong_viec trang_thai_cong_viec ' +
      'thoi_gian_ky_tao_phieu thoi_gian_cong_tac_bat_dau thoi_gian_cong_tac_ket_thuc don_vi_cong_tac_id')
    .populate({ path: 'don_vi_cong_tac_id', populate: 'don_vi_cha_id' })
    .lean();

  allPhieuGiaoViec.map(phieu => {
    if (phieu.duong_day_id) {
      mapDuongDayID[phieu.duong_day_id].push(phieu);
    } else {
      phieu.duong_day_ids.forEach(duongDayId => {
        if (duongDayIds.includes(duongDayId.toString())) {
          mapDuongDayID[duongDayId].push(phieu);
        }
      });
    }
    phieu.cong_viec = NHOM_CONG_VIEC[LOAI_CONG_VIEC[phieu.loai_cong_viec]?.type]?.name;
    phieu.trang_thai_cong_viec = TRANG_THAI_PHIEU[phieu.trang_thai_cong_viec]?.name;
    phieu.thoi_gian_ky_tao_phieu = phieu.thoi_gian_ky_tao_phieu ? formatDateTime(phieu.thoi_gian_ky_tao_phieu) : '';
    phieu.loai_cong_viec = LOAI_CONG_VIEC[phieu.loai_cong_viec]?.name;
    phieu.thoi_gian_cong_tac_bat_dau = phieu.thoi_gian_cong_tac_bat_dau ? formatDateTime(phieu.thoi_gian_cong_tac_bat_dau) : '';
    phieu.thoi_gian_cong_tac_ket_thuc = phieu.thoi_gian_cong_tac_ket_thuc ? formatDateTime(phieu.thoi_gian_cong_tac_ket_thuc) : '';

  });

  for (let vanHanh of allVanHanh) {
    vanHanh.phieu_giao_viec = mapDuongDayID[vanHanh.duong_day_id?._id];
  }

  data.van_hanh = allVanHanh;
  return data;
}

export async function getAllChamTienDo(query) {
  let dataResponse = {};
  let tinhTrangPhieu;
  query.cong_viec && delete query.cong_viec;
  query.loai_cong_viec && delete query.loai_cong_viec;
  query.trang_thai_cong_viec && delete query.trang_thai_cong_viec;
  query.tinh_trang_phieu && (tinhTrangPhieu = query.tinh_trang_phieu) && delete query.tinh_trang_phieu;
  const queryBuild = await buildQueryTime(query, dataResponse);
  query = queryBuild.query;
  dataResponse = queryBuild.dataResponse;

  query.thoi_gian_cong_tac_bat_dau['$gte'] = new Date(query.thoi_gian_cong_tac_bat_dau['$gte']);
  query.thoi_gian_cong_tac_bat_dau['$lt'] = new Date(query.thoi_gian_cong_tac_bat_dau['$lt']);

  const tongHopPhieuChamTienDo = await PhieuGiaoViecService.tongHopPhieuGiaoViecChamTienDo(query);
  let phieuHuyIds = [], phieuThucHienChamIds = [], phieuXacNhanKhoaChamIds = [];
  tongHopPhieuChamTienDo.forEach(item => {
    item.phieu_da_huy_do_tiep_nhan_cham === 1.0 && phieuHuyIds.push(item.phieu_giao_viec_id);
    item.phieu_dang_thuc_hien_cham === 1.0 && phieuThucHienChamIds.push(item.phieu_giao_viec_id);
    item.phieu_dang_xac_nhan_khoa_cham === 1.0 && phieuXacNhanKhoaChamIds.push(item.phieu_giao_viec_id);
  });

  const phieuGiaoViecIds = tinhTrangPhieu === 'HUY_PHIEU_DO_TIEP_NHAN_CHAM' ? phieuHuyIds
    : tinhTrangPhieu === 'DANG_THUC_HIEN_CHAM' ? phieuThucHienChamIds : phieuXacNhanKhoaChamIds;

  const allPhieuGiaoViec = await PhieuGiaoViecService.getAllPopulate({ _id: phieuGiaoViecIds });

  Object.values(TINH_TRANG_PHIEU).forEach(item => {
    item.code === tinhTrangPhieu && (dataResponse.tinh_trang_phieu = item.label);
  });

  allPhieuGiaoViec.map((phieu) => {
    phieu.cong_viec = NHOM_CONG_VIEC[LOAI_CONG_VIEC[phieu.loai_cong_viec]?.type]?.name;
    phieu.loai_cong_viec = LOAI_CONG_VIEC[phieu.loai_cong_viec]?.name;
    phieu.trang_thai_cong_viec = TRANG_THAI_PHIEU[phieu.trang_thai_cong_viec]?.name;
    phieu.thoi_gian_ky_tao_phieu = phieu.created_at ? formatDateTime(phieu.created_at) : '';
    phieu.thoi_gian_cong_tac_bat_dau = phieu.thoi_gian_cong_tac_bat_dau ? formatDateTime(phieu.thoi_gian_cong_tac_bat_dau) : '';
    phieu.thoi_gian_cong_tac_ket_thuc = phieu.thoi_gian_cong_tac_ket_thuc ? formatDateTime(phieu.thoi_gian_cong_tac_ket_thuc) : '';
  });
  dataResponse.ngay = formatToDateDetail(new Date()).ngay;
  dataResponse.thang = formatToDateDetail(new Date()).thang;
  dataResponse.nam = formatToDateDetail(new Date()).nam;
  dataResponse.cham_tien_do = addIndexToListData(allPhieuGiaoViec);
  return dataResponse;
}

export async function getDataViTri(duongDayId) {
  const allVanHanh = await VanHanhService.getAll({
    duong_day_id: duongDayId,
    is_deleted: false,
  }).populate({ path: 'vi_tri_id duong_day_id' });

  let dataResponse = {};
  dataResponse.vi_tri = allVanHanh.map(element => element.vi_tri_id).sort((a, b) => a.thu_tu - b.thu_tu);
  dataResponse.vi_tri.forEach(item => item.ten_duong_day = allVanHanh[0]?.duong_day_id.ten_duong_day);

  addIndexToListData(dataResponse.vi_tri);
  return dataResponse;
}

export async function nhatKyVanHanhDoiTruyentaiDien(criteria) {
  const query = {
    don_vi_giao_phieu_id: criteria.don_vi_id,
    duong_day_ids: criteria.duong_day_id,
    is_deleted: false,
    thoi_gian_cong_tac_bat_dau: criteria.created_at,
  };
  const allPhieuGiaoViec = await PHIEU_GIAO_VIEC.find(query)
    .populate({ path: 'nguoi_cap_phieu_id', select: 'full_name' })
    .populate({ path: 'chi_huy_truc_tiep_id' })
    .populate({ path: 'duong_day_ids', select: 'ten_duong_day' })
    .populate({
      path: 'don_vi_cong_tac_id',
      populate: { path: 'don_vi_cha_id', select: 'ten_don_vi' },
      select: 'don_vi_cha_id ten_don_vi',
    })
    .select('loai_cong_viec noi_dung_cong_tac ket_qua_cong_viec nguoi_cap_phieu_id created_at ' +
      'don_vi_cong_tac_id duong_day_ids thoi_gian_xac_nhan_khoa_phieu chi_huy_truc_tiep_id').lean();

  const nguoiCongTac = await NguoiCongTacService.getAll({
    phieu_giao_viec_id: { $in: extractIds(allPhieuGiaoViec) },
    is_deleted: false,
  }).populate('user_id');


  const allViTriCongViec = await ViTriCongViecService.getAll(
    { phieu_giao_viec_id: extractIds(allPhieuGiaoViec), is_deleted: false })
    .populate('khoang_cot_id vi_tri_id')
    .select('trang_thai_hoan_thanh phieu_giao_viec_id khoang_cot_id vi_tri_id');


  const ketQuaSuaChuaCoKeHoach = await KetQuaSuaChuaCoKeHoachService.getAll(
    { phieu_giao_viec_id: extractIds(allPhieuGiaoViec), is_deleted: false })
    .populate(
      {
        path: 'ton_tai_id', populate: { path: 'vi_tri_id khoang_cot_id', select: 'ten_vi_tri ten_khoang_cot' },
        select: 'vi_tri_id khoang_cot_id ',
      })
    .select('ton_tai_id phieu_giao_viec_id tinh_trang_xu_ly');
  let viTriTonTai = [], khoangCotTonTai = [];
  ketQuaSuaChuaCoKeHoach.forEach(ketqua => {
    !!ketqua.ton_tai_id.vi_tri_id && viTriTonTai.push({
      phieu_giao_viec_id: ketqua.phieu_giao_viec_id,
      tinh_trang_xu_ly: ketqua.tinh_trang_xu_ly,
      vi_tri_id: ketqua.ton_tai_id.vi_tri_id,
    });
    !!ketqua.ton_tai_id.khoang_cot_id && khoangCotTonTai.push({
      phieu_giao_viec_id: ketqua.phieu_giao_viec_id,
      tinh_trang_xu_ly: ketqua.tinh_trang_xu_ly,
      khoang_cot_id: ketqua.ton_tai_id.khoang_cot_id,
    });
  });

  const groupNguoiCongTac = groupBy(nguoiCongTac, 'phieu_giao_viec_id');
  const groupViTriCongViec = groupBy(allViTriCongViec, 'phieu_giao_viec_id');
  const groupKetQuaSuaChua = groupBy([...viTriTonTai, ...khoangCotTonTai], 'phieu_giao_viec_id');

  allPhieuGiaoViec
    .sort((a, b) => new Date(b?.created_at) - new Date(a?.created_at))
    .forEach(phieuGiaoViec => {
      phieuGiaoViec.nguoi_cong_tac = groupNguoiCongTac[phieuGiaoViec._id] || [];
      phieuGiaoViec.vi_tri_cong_viec = groupViTriCongViec[phieuGiaoViec._id] || [];
      phieuGiaoViec.ton_tai_id = groupKetQuaSuaChua[phieuGiaoViec._id] || [];
    });

  const mapKhoiLuong = {};

  allViTriCongViec.forEach(element => {
    if (!mapKhoiLuong[element.phieu_giao_viec_id]) {
      mapKhoiLuong[element.phieu_giao_viec_id] = {
        khoi_luong: 0,
        so_vi_tri: 0,
        so_khoang_cot: 0,
        hoan_thanh: 0,
      };
    }
    mapKhoiLuong[element.phieu_giao_viec_id].khoi_luong += 1;
    element.vi_tri_id && (mapKhoiLuong[element.phieu_giao_viec_id].so_vi_tri += 1);
    element.khoang_cot_id && (mapKhoiLuong[element.phieu_giao_viec_id].so_khoang_cot += 1);
    element.trang_thai_hoan_thanh === TRANG_THAI_HOAN_THANH.DA_HOAN_THANH && (mapKhoiLuong[element.phieu_giao_viec_id].hoan_thanh += 1);
  });

  [...viTriTonTai, ...khoangCotTonTai].forEach(ketqua => {
    if (!mapKhoiLuong[ketqua.phieu_giao_viec_id]) {
      mapKhoiLuong[ketqua.phieu_giao_viec_id] = {
        khoi_luong: 0, so_vi_tri: 0,
        so_khoang_cot: 0, hoan_thanh: 0,
      };
    }
    mapKhoiLuong[ketqua.phieu_giao_viec_id].khoi_luong += 1;
    ketqua.vi_tri_id && (mapKhoiLuong[ketqua.phieu_giao_viec_id].so_vi_tri += 1);
    ketqua.khoang_cot_id && (mapKhoiLuong[ketqua.phieu_giao_viec_id].so_khoang_cot += 1);
    ketqua.tinh_trang_xu_ly === TRANG_THAI_XU_LY.DA_XU_LY.code && (mapKhoiLuong[ketqua.phieu_giao_viec_id].hoan_thanh += 1);
  });


  function convertDataToRow(phieuGiaoViec, index) {
    let nguoiCongTac = [];
    if (phieuGiaoViec.nguoi_cong_tac) {
      phieuGiaoViec.nguoi_cong_tac.forEach(user => {
        nguoiCongTac.push(user.user_id);
      });
    }
    let stringDuongDay;
    phieuGiaoViec.duong_day_ids?.forEach(duongDay => {
      stringDuongDay = downlineData(stringDuongDay, `- ${duongDay.ten_duong_day}`);
    });

    let stringViTriCongViec;

    phieuGiaoViec.vi_tri_cong_viec.forEach(vtcv => {
      if (!stringViTriCongViec) {
        stringViTriCongViec = vtcv.vi_tri_id ? vtcv.vi_tri_id?.ten_vi_tri : vtcv.khoang_cot_id?.ten_khoang_cot;
      } else {
        stringViTriCongViec = [stringViTriCongViec, vtcv.vi_tri_id ? vtcv.vi_tri_id?.ten_vi_tri : vtcv.khoang_cot_id?.ten_khoang_cot].join(', ');
      }
    });

    phieuGiaoViec.ton_tai_id.forEach(vtcv => {
      if (!stringViTriCongViec) {
        stringViTriCongViec = vtcv.vi_tri_id ? vtcv.vi_tri_id?.ten_vi_tri : vtcv.khoang_cot_id?.ten_khoang_cot;
      } else {
        stringViTriCongViec = [stringViTriCongViec, vtcv.vi_tri_id ? vtcv.vi_tri_id?.ten_vi_tri : vtcv.khoang_cot_id?.ten_khoang_cot].join(', ');
      }
    });

    phieuGiaoViec.chi_huy_truc_tiep_id && (nguoiCongTac = [{
      ...phieuGiaoViec.chi_huy_truc_tiep_id,
      is_chi_huy: true,
    }, ...nguoiCongTac]);

    let stringNguoiThucHien;
    nguoiCongTac.forEach(user => {
      stringNguoiThucHien = downlineData(stringNguoiThucHien, `- ${user?.full_name}`);
    });

    return {
      _id: phieuGiaoViec._id,
      stt: index + 1,
      loai_cong_viec_name: LOAI_CONG_VIEC[phieuGiaoViec.loai_cong_viec]?.name,
      loai_cong_viec: phieuGiaoViec.loai_cong_viec,
      noi_dung_cong_viec: phieuGiaoViec.noi_dung_cong_tac,
      ket_qua_cong_viec: phieuGiaoViec.ket_qua_cong_viec,
      khoi_luong: mapKhoiLuong[phieuGiaoViec._id],
      nguoi_cap_phieu_id: phieuGiaoViec.nguoi_cap_phieu_id.full_name,
      thoi_gian_giao_phieu: phieuGiaoViec.created_at ? formatDate(phieuGiaoViec.created_at) : '',
      nguoi_cong_tac: nguoiCongTac,
      created_at: phieuGiaoViec.created_at,
      don_vi_cong_tac_id: phieuGiaoViec.don_vi_cong_tac_id,
      thoi_gian_xac_nhan_khoa_phieu: phieuGiaoViec.thoi_gian_xac_nhan_khoa_phieu ? formatDate(phieuGiaoViec.thoi_gian_xac_nhan_khoa_phieu) : '',
      vi_tri_cong_viec: phieuGiaoViec.vi_tri_cong_viec,
      duong_day_ids: phieuGiaoViec.duong_day_ids,
      ton_tai_id: phieuGiaoViec.ton_tai_id,
      string_duong_day: stringDuongDay,
      string_vi_tri_cong_viec: stringViTriCongViec,
      string_nguoi_thuc_hien: stringNguoiThucHien,
    };
  }

  return allPhieuGiaoViec.map(convertDataToRow);
}

export async function getAllCongViecByViTriId(criteria) {
  const allViTriCongViec = await ViTriCongViecService.getAll({
    vi_tri_id: criteria.vi_tri_id,
    is_deleted: false,
  }).populate('vi_tri_id');

  delete criteria.vi_tri_id;
  criteria._id = allViTriCongViec.map(viTriCongViec => viTriCongViec?.phieu_giao_viec_id);
  const allPhieuGiaoViec = await PhieuGiaoViecService.getAllPopulate(criteria);

  function convertDataToRows(phieu, index) {
    return {
      stt: index + 1,
      vi_tri_id: allViTriCongViec[0]?.vi_tri_id,
      _id: phieu._id,
      so_phieu: phieu.so_phieu,
      loai_cong_viec_name: LOAI_CONG_VIEC[phieu.loai_cong_viec]?.name,
      loai_cong_viec_type: NHOM_CONG_VIEC[LOAI_CONG_VIEC[phieu.loai_cong_viec]?.type]?.name,
      loai_cong_viec: phieu.loai_cong_viec,
      thoi_gian_tao: phieu.created_at ? formatDateTime(phieu.created_at) : '',
      trang_thai_cong_viec: phieu.trang_thai_cong_viec,
      trang_thai_cong_viec_name: TRANG_THAI_PHIEU[phieu.trang_thai_cong_viec]?.name,
    };
  }

  return allPhieuGiaoViec.map(convertDataToRows);
}

export async function convertDataNoiDungKiemTraBangThietBiBay(data) {
  let noiDungKiemTra = await NoiDungKiemTraService.getAll({ is_deleted: false }).select('ten_noi_dung');
  data.map(item => {
    item.noi_dung_kiem_tra = JSON.parse(JSON.stringify(noiDungKiemTra));
    let groupKetQuaByNoiDungId = groupBy(item.ket_qua_kiem_tra, 'noi_dung_kiem_tra');
    item.noi_dung_kiem_tra.forEach(noidung => {
      noidung.ket_qua = groupKetQuaByNoiDungId[noidung._id] || [];
      noidung.print = convertTieuChi(noidung.ket_qua);
    });
  });

  function convertTieuChi(listData) {
    let resultString;
    listData.forEach(item => {
      let viTriKhoangCot = `${item.vi_tri_id ? item.vi_tri_id?.ten_vi_tri : item.khoang_cot_id?.ten_khoang_cot}`;
      let batThuong = `${item.tieu_chi_id?.tieu_chi_cha_id?.ten_tieu_chi ? item.tieu_chi_id?.tieu_chi_cha_id?.ten_tieu_chi + ' > ' : ''}${item.tieu_chi_id?.ten_tieu_chi}`;
      let temp = 'VT-KC: ' + viTriKhoangCot + '- Có bất thường ' + batThuong;
      resultString ? resultString = [resultString, temp].join('\n') : resultString = temp;
    });
    return resultString;
  }

  function convertData(element, index) {
    let tenDuongDay, viTriKhoangCot;
    element.duong_day_ids?.forEach(line => {
      tenDuongDay ? (tenDuongDay = [tenDuongDay, line.ten_duong_day].join('\n')) : (tenDuongDay = line.ten_duong_day);
    });
    [element.vi_tri_cong_viec, element.khoang_cot_cong_viec].flat().forEach(item => {
      viTriKhoangCot ? (viTriKhoangCot = [viTriKhoangCot, item.vi_tri_id?.ten_vi_tri ? item.vi_tri_id?.ten_vi_tri : item.khoang_cot_id?.ten_khoang_cot].join(', '))
        : (viTriKhoangCot = item.vi_tri_id?.ten_vi_tri ? item.vi_tri_id?.ten_vi_tri : item.khoang_cot_id?.ten_khoang_cot);
    });

    return {
      stt: index + 1,
      don_vi_quan_ly: element.don_vi_giao_phieu_id?.don_vi_cha_id?.ten_don_vi,
      don_vi_thuc_hien: element.don_vi_giao_phieu_id?.ten_don_vi,
      duong_day_ids: tenDuongDay,
      vi_tri_khoang_cot: viTriKhoangCot,
      cung_doan_kiem_tra: element.cung_doan_kiem_tra,
      so_khoang_cot: element.khoang_cot_cong_viec?.length || 0,
      so_cot: element.vi_tri_cong_viec?.length || 0,
      pham_vi_bay_da_thuc_hien: element.pham_vi_bay_da_thuc_hien,
      hang_muc_kiem_tra: LOAI_CONG_VIEC[element.loai_cong_viec]?.name,
      noi_dung_cong_tac: element.noi_dung_cong_tac,
      thiet_bi_bay: element.ten_drone,
      serial_drone: element.serial_drone,
      tu_ngay: formatDate(element.thoi_gian_cong_tac_bat_dau),
      den_ngay: formatDate(element.thoi_gian_cong_tac_ket_thuc),
      thoi_gian_bay: element.thoi_gian_bay ? `${element.thoi_gian_bay / 60} phút` : '',
      so_luong_nguoi_tham_gia: element.so_luong_nguoi_tham_gia,
      ket_qua_kiem_tra: element.ket_qua_kiem_tra,
      noi_dung_kiem_tra: element.noi_dung_kiem_tra,
      ly_do_chua_hoan_thanh: element.ly_do_cong_viec_chua_hoan_thanh,
    };
  }

  return data.map(convertData);
}


export function convertDataUser(user, index) {
  let roleText;
  user.role_id?.forEach(role => {
    roleText = downlineData(roleText, `- ${role.name}`);
  });

  return {
    stt: index + 1,
    gender: GENDER_OPTIONS[user.gender]?.label,
    loai_tai_khoan: LOAI_TAI_KHOAN[user.loai_tai_khoan]?.label,
    don_vi_id: user.don_vi_id,
    username: user.username,
    full_name: user.full_name,
    permissions: user.permissions?.length || 0,
    role: roleText,
    phone: user.phone,
  };
}
