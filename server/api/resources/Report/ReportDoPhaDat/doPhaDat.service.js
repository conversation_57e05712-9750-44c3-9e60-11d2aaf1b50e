import * as ValidatorHelper from '../../../helpers/validatorHelper';
import REPORT_DO_PHA_DAT from './doPhaDat.model';
import { trim } from '../../../common/functionCommons';
import { checkNumber, convertDate } from '../../../helpers/checkDataHelper';
import * as DuongDayService from '../../TongKe/DuongDay/duongDay.service';
import { GIAI_PHAP_XU_LY_PHA_DAT_THAP } from '../../DanhMuc/DeXuatXuLy';
import { formatTimeDate } from '../../../common/formatUTCDateToLocalDate';
import * as ketQuaDoKhoangCachPhaDat
  from '../../QuanLyVanHanh/KetQuaDoKhoangCachPhaDat/ketQuaDoKhoangCachPhaDat.service';
import { extractIds, extractKeys } from '../../../utils/dataconverter';
import { LOAI_CONG_VIEC } from '../../DanhMuc/LoaiCongViec';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import PHIEU_GIAO_VIEC from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.model';
import * as ViTriCongViecService from '../../QuanLyVanHanh/ViTriCongViec/viTriCongViec.service';
import DongBoModel from '../DongBoDuLieuDo/dongBo.model';
import { KET_LUAN } from '../../../constant/constant';


export function getAll(query, projection = {}) {
  return REPORT_DO_PHA_DAT.find(query, projection).lean();
}

export async function updateAll(dataUpdate) {
  for (const row of dataUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await REPORT_DO_PHA_DAT.findByIdAndUpdate(value._id, value);
  }
}

export function count(query) {
  return REPORT_DO_PHA_DAT.count(query);
}

const Joi = require('joi');

const objSchema = Joi.object({
  ma_day_dan: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã tiếp địa')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}


const Headers = {
  TEN_DUONG_DAY: 'Đường dây',
  SO_PHIEU: 'Số phiếu',
  TEN_KHOANG_COT: 'Khoảng cột',
  KHOANG_CACH_PHA_DAT: 'Khoảng cách Pha - Đất (m)',
  DONG_TAI_KHI_DO: 'Dòng tải khi đo (A)',
  NGAY_DO: 'Ngày đo',
  STT: 'STT',
  NHIET_DO_MOI_TRUONG: 'Nhiệt độ môi trường (°C)',
  KHOANG_CACH_THEO_QUY_PHAM: 'Khoảng cách theo quy phạm (m)',
  DAC_DIEM_KHU_VUC: 'Đặc điểm khu vực khi đưa vào vận hành',
  DE_XUAT_GIAI_PHAP_XU_LY: 'Đề xuất giải pháp xử lý',
  GHI_CHU: 'Ghi chú',
};

export async function mapData(req, donViId, duongDayId) {

  const mapKhoangCot = {}, mapDeXuat = {}, mapSoPhieu = {}, mapNgayDo = {}, mapCurrentLast = {};

  const allViTri = await DuongDayService.getViTriByDuongDay(req, duongDayId);
  const allKhoangCot = allViTri.vi_tris?.flatMap(vt => vt.khoang_cot_id ? [...vt.khoang_cot_id] : []);
  const allSoPhieu = await PHIEU_GIAO_VIEC.distinct('so_phieu', {
    don_vi_giao_phieu_id: donViId,
    loai_cong_viec: LOAI_CONG_VIEC.DO_KHOANG_CACH_PHA_DAT.code,
    trang_thai_cong_viec: TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code,
  });

  const resultSortByTime = await REPORT_DO_PHA_DAT.find({ khoang_cot_id: extractIds(allKhoangCot), is_latest: true })
    .select('ngay_do khoang_cot_id')
    .sort({ ngay_do: 1 })
    .populate({ path: 'khoang_cot_id', select: 'ten_khoang_cot' });

  resultSortByTime.forEach(item => {
    mapNgayDo[item.khoang_cot_id.ten_khoang_cot] = item.ngay_do;
    mapCurrentLast[item.khoang_cot_id.ten_khoang_cot] = item;
  });

  allKhoangCot.forEach(khoangCot => {
    mapKhoangCot[khoangCot.ten_khoang_cot] = khoangCot._id;
  });

  Object.values(GIAI_PHAP_XU_LY_PHA_DAT_THAP).forEach(deXuat => {
    mapDeXuat[deXuat.label.toLowerCase()] = deXuat.code;
  });

  // allSoPhieu.forEach(sophieu => {
  //   mapSoPhieu[sophieu] = sophieu;
  // });
  return [mapKhoangCot, mapDeXuat, mapSoPhieu, mapNgayDo, mapCurrentLast];
}

export async function importData(req, sheetData) {
  const { rows } = sheetData;

  const [mapKhoangCot, mapDeXuat, , mapNgayDo, mapCurrentLast] = await mapData(req, sheetData.don_vi_id, sheetData.duong_day_id);

  let currentLastest = [];
  rows.forEach(row => {
    if (!mapNgayDo[row[Headers.TEN_KHOANG_COT]?.trim()]) {
      mapNgayDo[row[Headers.TEN_KHOANG_COT]?.trim()] = new Date(1, 1, 1);
    }
    if (mapNgayDo[row[Headers.TEN_KHOANG_COT]?.trim()] && mapNgayDo[row[Headers.TEN_KHOANG_COT]?.trim()] < convertDate(row[Headers.NGAY_DO])) {
      mapCurrentLast[row[Headers.TEN_KHOANG_COT]?.trim()] && currentLastest.push(mapCurrentLast[row[Headers.TEN_KHOANG_COT]?.trim()]);
      delete mapCurrentLast[row[Headers.TEN_KHOANG_COT]?.trim()];
      mapNgayDo[row[Headers.TEN_KHOANG_COT]?.trim()] = convertDate(row[Headers.NGAY_DO]);
    }
  });

  function convertToDB(row) {
    let isLatest = false;
    if (mapNgayDo[row[Headers.TEN_KHOANG_COT]?.trim()] && mapNgayDo[row[Headers.TEN_KHOANG_COT]?.trim()] <= convertDate(row[Headers.NGAY_DO])) {
      isLatest = true;
      delete mapNgayDo[row[Headers.TEN_KHOANG_COT]?.trim()];
    }
    return {
      khoang_cot_id: mapKhoangCot[row[Headers.TEN_KHOANG_COT]?.trim()],
      khoang_cach_pha_dat: trim(row[Headers.KHOANG_CACH_PHA_DAT]),
      dong_tai_khi_do: trim(row[Headers.DONG_TAI_KHI_DO]),
      ngay_do: convertDate(row[Headers.NGAY_DO]),
      nhiet_do_moi_truong: trim(row[Headers.NHIET_DO_MOI_TRUONG]),
      khoang_cach_theo_quy_pham: trim(row[Headers.KHOANG_CACH_THEO_QUY_PHAM]),
      dac_diem_khu_vuc: trim(row[Headers.DAC_DIEM_KHU_VUC]),
      de_xuat_xu_ly: mapDeXuat[row[Headers.DE_XUAT_GIAI_PHAP_XU_LY]?.trim()?.toLowerCase()],
      ghi_chu: trim(row[Headers.GHI_CHU]),
      so_phieu: trim(row[Headers.SO_PHIEU]),
      thoi_gian_cap_nhat: new Date(),
      nguoi_chinh_sua_id: req.user?._id,
      is_import_by_file: true,
      is_latest: isLatest,
      is_deleted: false,
    };
  }

  const dataToDB = rows.map(convertToDB).filter(item => item.khoang_cot_id);

  REPORT_DO_PHA_DAT.bulkWrite(
    currentLastest.map((current) =>
      ({
        updateOne: {
          filter: { _id: current._id },
          update: { is_latest: false },
          upsert: true,
        },
      }),
    ),
  );
  return REPORT_DO_PHA_DAT.create(dataToDB);
}

export async function checkImport(req, sheetData) {
  if (!sheetData) return null;
  const { t } = req;
  const { rows } = sheetData;
  const duongDayId = req.body.duong_day_id;
  const donViId = req.body.don_vi_id;

  const [mapKhoangCot, mapDeXuat, mapSoPhieu] = await mapData(req, donViId, duongDayId);

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row) {
    let errors = [];
    if (!row[Headers.TEN_KHOANG_COT]?.trim()) {
      errors = [...errors, createError(Headers.TEN_KHOANG_COT, t('missing_tower_info'))];
    } else {
      if (!mapKhoangCot[row[Headers.TEN_KHOANG_COT]?.trim()]) {
        errors = [...errors, createError(Headers.TEN_KHOANG_COT, t('tower_not_belong_line'))];
      }
    }

    // if (row[Headers.SO_PHIEU]?.trim() && !mapSoPhieu[row[Headers.SO_PHIEU]?.trim()]) {
    //   errors = [...errors, createError(Headers.SO_PHIEU, t('task_number_incorrect_or_dont_create'))];
    // }

    if (row[Headers.DE_XUAT_GIAI_PHAP_XU_LY] && !mapDeXuat[row[Headers.DE_XUAT_GIAI_PHAP_XU_LY]?.trim()?.toLowerCase()]) {
      errors = [...errors, createError(Headers.DE_XUAT_GIAI_PHAP_XU_LY, t('solution_incorrect_or_dont_create'))];
    }
    if (errors.length) {
      row['Lỗi'] = errors;
    } else {
      row['Lỗi'] = null;
    }
    return row;
  }

  sheetData.rows = rows.map(row => validateRow(t, row));
  return sheetData;
}

export async function bangTongHopBaoCaoKhoangCachPhaDat(criteria) {
  const data = await REPORT_DO_PHA_DAT.find(criteria)
    .populate('khoang_cot_id')
    .sort({ ngay_do: -1 })
    .lean();

  function convertDataToRows(phieuDoPhaDat, index) {
    let deXuatXuLy = GIAI_PHAP_XU_LY_PHA_DAT_THAP[phieuDoPhaDat?.de_xuat_xu_ly]?.label;
    return {
      stt: index + 1,
      khoang_cot_id: phieuDoPhaDat.khoang_cot_id,
      dong_tai_khi_do: phieuDoPhaDat.dong_tai_khi_do,
      nhiet_do_moi_truong: phieuDoPhaDat.nhiet_do_moi_truong,
      khoang_cach_pha_dat: phieuDoPhaDat.khoang_cach_pha_dat,
      khoang_cach_theo_quy_pham: phieuDoPhaDat.khoang_cach_theo_quy_pham,
      ngay_do: formatTimeDate(phieuDoPhaDat.ngay_do),
      de_xuat_xu_ly: phieuDoPhaDat.de_xuat_xu_ly,
      de_xuat: deXuatXuLy,
      dac_diem_khu_vuc: phieuDoPhaDat.dac_diem_khu_vuc,
      ghi_chu: phieuDoPhaDat.ghi_chu,
      nguoi_tao: phieuDoPhaDat.nguoi_tao,
      ket_luan: KET_LUAN?.[phieuDoPhaDat.ket_luan]?.label,
      nguoi_chinh_sua: phieuDoPhaDat.nguoi_chinh_sua,
    };
  }

  return data.map(convertDataToRows);
}

export async function resultToReport(req, phieuGiaoViecData) {
  const allKetQuaDoPhaDat = await ketQuaDoKhoangCachPhaDat.getAll({
    phieu_giao_viec_id: phieuGiaoViecData._id,
    is_deleted: false,
  });

  const viTriCongViec = await ViTriCongViecService.getAll({ phieu_giao_viec_id: phieuGiaoViecData._id });
  const resultSortByTime = await REPORT_DO_PHA_DAT.find({
    khoang_cot_id: extractKeys(viTriCongViec, 'khoang_cot_id').filter(item => !!item),
    is_latest: true,
  })
    .select('ngay_do khoang_cot_id')
    .sort({ ngay_do: 1 })
    .populate({ path: 'khoang_cot_id', select: 'ten_khoang_cot' });

  const mapNgayDo = {}, mapCurrentLast = {};
  resultSortByTime.forEach(item => {
    mapNgayDo[item.khoang_cot_id._id] = item.ngay_do;
    mapCurrentLast[item.khoang_cot_id._id] = item;
  });

  let currentLastest = [];

  function convertToDB(row) {
    let isLatest = false;
    if (mapNgayDo[row.khoang_cot_id] && mapNgayDo[row.khoang_cot_id] <= row.ngay_do) {
      mapCurrentLast[row.khoang_cot_id] && currentLastest.push(mapCurrentLast[row.khoang_cot_id]);
      delete mapCurrentLast[row.khoang_cot_id];
      isLatest = true;
    }
    if (!mapNgayDo[row.khoang_cot_id]) {
      isLatest = true;
    }
    return {
      khoang_cot_id: row.khoang_cot_id,
      khoang_cach_pha_dat: row.khoang_cach_pha_dat,
      dong_tai_khi_do: row.dong_tai_khi_do,
      ngay_do: row.ngay_do,
      nhiet_do_moi_truong: row.nhiet_do_moi_truong,
      khoang_cach_theo_quy_pham: row.khoang_cach_theo_quy_pham,
      dac_diem_khu_vuc: row.dac_diem_khu_vuc,
      de_xuat_xu_ly: row.de_xuat_xu_ly,
      ghi_chu: row.ghi_chu,
      ket_luan: row.ket_luan,

      so_phieu: phieuGiaoViecData.so_phieu,
      phieu_giao_viec_id: phieuGiaoViecData._id,
      is_latest: row.ngay_do ? isLatest : false,
      is_deleted: false,
    };
  }

  const dataToDB = allKetQuaDoPhaDat.map(convertToDB).filter(element => element.khoang_cot_id);
  REPORT_DO_PHA_DAT.bulkWrite(
    currentLastest.map((ketQuaDo) =>
      ({
        updateOne: {
          filter: { _id: ketQuaDo._id },
          update: { is_latest: false },
          upsert: true,
        },
      }),
    ),
  );

  await REPORT_DO_PHA_DAT.create(dataToDB);
}

export async function removeDuplicateData(req, duongDayId) {
  const sycnReport = await DongBoModel.findOne({
    duong_day_id: duongDayId,
    loai_phieu_do: LOAI_CONG_VIEC.DO_KHOANG_CACH_PHA_DAT.code,
  });
  if (!sycnReport?.is_remove_duplicate) {
    await updateDuplicateData(req, duongDayId);
    await DongBoModel.updateOne({
      duong_day_id: duongDayId,
      loai_phieu_do: LOAI_CONG_VIEC.DO_KHOANG_CACH_PHA_DAT.code,
    }, { is_remove_duplicate: true });
  }
}

export async function updateDuplicateData(req, duongDayId) {
  const allViTri = await DuongDayService.getViTriByDuongDay(req, duongDayId, true);
  const allKhoangCot = allViTri.vi_tris?.flatMap(vt => vt.khoang_cot_id ? [...vt.khoang_cot_id] : []);
  const allKetQuaDo = await getAll({ khoang_cot_id: extractIds(allKhoangCot) });
  const mapDoDienTro = {};
  let dataDuplicate = [];
  for (let ketQuaDo of allKetQuaDo) {
    if (mapDoDienTro[ketQuaDo.so_phieu + ketQuaDo.khoang_cot_id]) {
      dataDuplicate.push(ketQuaDo);
    } else {
      mapDoDienTro[ketQuaDo.so_phieu + ketQuaDo.khoang_cot_id] = ketQuaDo;
    }
  }
  REPORT_DO_PHA_DAT.bulkWrite(
    dataDuplicate.map((ketQuaDo) =>
      ({
        updateOne: {
          filter: { _id: ketQuaDo._id },
          update: { is_deleted: true },
          upsert: true,
        },
      }),
    ),
  );
}
