import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { REPORT_DO_PHA_DAT, KHOANG_COT, USER, PHIEU_GIAO_VIEC } from '../../../constant/dbCollections';
import { KET_LUAN } from '../../../constant/constant';

const schema = new Schema({
  khoang_cot_id: { type: Schema.Types.ObjectId, ref: KHOANG_COT },
  khoang_cach_pha_dat: { type: String },
  dong_tai_khi_do: { type: String },
  ngay_do: { type: Date },
  nhiet_do_moi_truong: { type: String },
  khoang_cach_theo_quy_pham: { type: String },
  dac_diem_khu_vuc: { type: String },
  de_xuat_xu_ly: { type: String },

  ghi_chu: { type: String },
  nguoi_tao: { type: Schema.Types.ObjectId, ref: USER },

  nguoi_chinh_sua_id: { type: Schema.Types.ObjectId, ref: USER },
  thoi_gian_cap_nhat: { type: Date, default: Date.now },
  ket_luan: { type: String, enum: Object.keys(KET_LUAN) },
  so_phieu: String,
  phieu_giao_viec_id:  { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  is_latest: { type: Boolean, default: false },
  is_import_by_file: { type: Boolean, default: false },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(REPORT_DO_PHA_DAT, schema, REPORT_DO_PHA_DAT);
