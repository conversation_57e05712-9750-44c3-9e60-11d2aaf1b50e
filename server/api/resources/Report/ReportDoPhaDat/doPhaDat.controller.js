import * as Service from './doPhaDat.service';
import DuongDayModel from '../../TongKe/DuongDay/duongDay.model';
import Model from './doPhaDat.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import * as responseAction from '../../../helpers/responseHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import exelUtils from '../../../utils/exelUtils';
import queryHelper from '../../../helpers/queryHelper';
import { generateDocument } from '../GenerateFile/generate.controller';
import CommonError from '../../../error/CommonError';
import * as DuongDayService from '../../TongKe/DuongDay/duongDay.service';
import { extractIds } from '../../../utils/dataconverter';
import { getFilePath } from '../../../utils/fileUtils';
import { TEMPLATES_DIRS } from '../../../constant/constant';

const populateOpts = [
  { path: 'khoang_cot_id', populate: 'vi_tri_id' },
  { path: 'nguoi_chinh_sua_id' },
];
const uniqueOpts = [];
const sortOpts = { ngay_do: -1 };

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);

const SHEET_NAMES = {
  REPORT_DO_PHA_DAT: 'Khoảng cách Pha - Đất',
};

export async function buildQuery(req) {
  const query = queryHelper.extractQueryParam(req);
  let { criteria, options } = query;

  if (criteria.tu_ngay) {
    if (criteria.den_ngay) {
      criteria.ngay_do = {
        $gte: criteria.tu_ngay,
        $lte: criteria.den_ngay,
      };
      criteria.den_ngay = null;
    } else {
      criteria.ngay_do = { $gte: criteria.tu_ngay };
    }
    criteria.tu_ngay = null;
  } else {
    if (criteria.den_ngay) {
      criteria.ngay_do = { $lte: criteria.den_ngay };
    }
    criteria.den_ngay = null;
  }

  delete criteria.tu_ngay;
  delete criteria.den_ngay;

  return { criteria, options };
}

export async function getAll(req, res) {
  try {
    const { criteria, options } = await buildQuery(req);
    if (criteria.duong_day_id) {
      await Service.removeDuplicateData(req, criteria.duong_day_id);
      const allViTri = await DuongDayService.getViTriByDuongDay(req, criteria.duong_day_id, true);
      const allKhoangCot = allViTri.vi_tris?.flatMap(vt => vt.khoang_cot_id ? [...vt.khoang_cot_id] : []);
      criteria.khoang_cot_id = criteria.khoang_cot_id || extractIds(allKhoangCot);
      delete criteria.duong_day_id;
      delete criteria.don_vi_id;
    }
    if (criteria.is_latest !== 'KET_QUA_MOI_NHAT') delete criteria.is_latest;
    else {
      criteria.is_latest = true;
    }

    options.populate = populateOpts;
    options.sort = sortOpts;

    const data = await Model.paginate(criteria, options);
    responseAction.success(res, data);
  } catch (err) {
    console.log(err);
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOne({ _id: id });
    const dataRes = await Model.deleteOne({ _id: id });
    if (data.is_latest) {
      const curentLatest = await Model.find({ vi_tri_id: data.vi_tri_id }).sort({ ngay_do: -1 }).limit(1);
      if (curentLatest[0]?._id) {
        await Model.updateOne({ _id: curentLatest[0]._id }, { is_latest: true });
      }
    }

    if (!dataRes) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, dataRes);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function checkImport(req, res) {
  try {
    let filePath = req.files.file.path;
    const sheetData = await exelUtils.transformFile(filePath);

    const resultArray = await checkImportByData(req, sheetData);
    responseAction.success(res, resultArray);
  } catch (err) {
    console.log(err);
    responseAction.error(res, err);
  }
}

async function checkImportByData(req, sheetData) {
  function getSheetByName(sheetData, name) {
    return sheetData.find((sheet) => sheet.name.toLowerCase().includes(name.toLowerCase()));
  }

  let resultArray = [];
  resultArray = [...resultArray, await Service.checkImport(req, getSheetByName(sheetData, SHEET_NAMES.REPORT_DO_PHA_DAT))];
  return resultArray;
}

export async function importOne(req, res) {
  try {
    const sheetData = req.body;
    const result = await Service.importData(req, sheetData);
    responseAction.success(res, result);
  } catch (e) {
    console.log(e);
    responseAction.error(res, e);
  }
}

export async function downloadBaoCaoKhoangCachPhaDat(req, res) {
  const { criteria } = await buildQuery(req);
  const duongDay = await DuongDayModel.findOne({ _id: criteria.duong_day_id });
  if (!criteria.khoang_cot_id) {
    const allViTri = await DuongDayService.getViTriByDuongDay(req, criteria.duong_day_id, true);
    const allKhoangCot = allViTri.vi_tris?.flatMap(vt => vt.khoang_cot_id ? [...vt.khoang_cot_id] : []);
    criteria.khoang_cot_id = extractIds(allKhoangCot);
  }
  delete criteria.don_vi_id;
  delete criteria.duong_day_id;
  const ketQuaDoPhaDat = await Service.bangTongHopBaoCaoKhoangCachPhaDat(criteria);
  let data = {};
  data.ten_duong_day = duongDay?.ten_duong_day;
  data.do_pha_dat = ketQuaDoPhaDat;

  const templateFilePath = getFilePath(`report_khoang_cach_pha_dat.xlsx`, TEMPLATES_DIRS.BIEU_MAU);
  const outputFileName = 'Bảng tổng hợp kết quả đo khoảng cách pha đất.xlsx';
  generateDocument(res, data, templateFilePath, outputFileName);
}

