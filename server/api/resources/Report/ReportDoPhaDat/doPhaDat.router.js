import express from 'express';
import passport from 'passport';
import * as Controller from './doPhaDat.controller';
import { checkTempFolder, multipartMiddleware } from '../../../utils/fileUtils';
import { loggerMiddleware } from '../../../logs/middleware';

export const reportDoPhaDatRouter = express.Router();
reportDoPhaDatRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

reportDoPhaDatRouter
  .route('/download')
  .get(Controller.downloadBaoCaoKhoangCachPhaDat);

reportDoPhaDatRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), Controller.getAll)
  .post(passport.authenticate('jwt', { session: false }), Controller.create);

reportDoPhaDatRouter
  .route('/checkimport')
  .post(checkTempFolder, multipartMiddleware, Controller.checkImport);

reportDoPhaDatRouter
  .route('/importone')
  .post(checkTempFolder, multipartMiddleware, Controller.importOne);

reportDoPhaDatRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), Controller.findOne)
  .delete(passport.authenticate('jwt', { session: false }), Controller.remove)
  .put(passport.authenticate('jwt', { session: false }), Controller.update);
