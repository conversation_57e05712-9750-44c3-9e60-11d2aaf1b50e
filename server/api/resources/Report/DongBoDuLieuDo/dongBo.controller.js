import * as Service from './dongBo.service';
import Model from './dongBo.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import { LOAI_CONG_VIEC } from '../../DanhMuc/LoaiCongViec';

const populateOpts = [];
const uniqueOpts = [];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, null, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);


export async function create(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    switch (value.loai_phieu_do) {
      case LOAI_CONG_VIEC.DO_KHOANG_CACH_PHA_DAT.code:
        await Service.syncDataDoPhaDat(req, value);
        break;
      case LOAI_CONG_VIEC.DO_DIEN_TRO_TIEP_DIA.code:
        await Service.syncDataDoDienTro(req, value);
        break;
      case LOAI_CONG_VIEC.DO_NHIET_DO_TIEP_XUC.code:
        await Service.syncDataDoNhietDo(req, value);
        break;
      default:
    }

    const data = await Model.create(value);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function reSyncData(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    let data;
    switch (value.loai_phieu_do) {
      case LOAI_CONG_VIEC.DO_KHOANG_CACH_PHA_DAT.code:
        data = await Service.syncDataDoPhaDat(req, value);
        break;
      case LOAI_CONG_VIEC.DO_DIEN_TRO_TIEP_DIA.code:
        data = await Service.syncDataDoDienTro(req, value);
        break;
      case LOAI_CONG_VIEC.DO_NHIET_DO_TIEP_XUC.code:
        data = await Service.syncDataDoNhietDo(req, value);
        break;
      default:
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}
