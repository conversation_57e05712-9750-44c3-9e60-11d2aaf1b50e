import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DUONG_DAY, DONG_BO_PHIEU_DO } from '../../../constant/dbCollections';

const schema = new Schema({
  loai_phieu_do: { type: String },
  duong_day_id: { type: Schema.Types.ObjectId, ref: DUONG_DAY },
  is_remove_duplicate: { type: Boolean, default: false },
  is_synced: { type: Boolean, default: false },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(DONG_BO_PHIEU_DO, schema, DONG_BO_PHIEU_DO);
