import * as ValidatorHelper from '../../../helpers/validatorHelper';
import * as DuongDayService from '../../TongKe/DuongDay/duongDay.service';
import { extractIds } from '../../../utils/dataconverter';
import REPORT_DO_PHA_DAT from '../ReportDoPhaDat/doPhaDat.model';
import REPORT_DO_DIEN_TRO from '../ReportDoDienTro/doDienTro.model';
import * as KetQuaDoPhaDatService from '../../QuanLyVanHanh/KetQuaDoKhoangCachPhaDat/ketQuaDoKhoangCachPhaDat.service';
import * as KetQuaDoDienTroService from '../../QuanLyVanHanh/DoDienTro/doDienTro.service';
import * as ketQuaDoNhietDo from '../../KetQuaDoNhietDo/ketQuaDoNhietDo.service';
import * as chiTietDoNhietDo from '../../KetQuaDoNhietDo/ChiTietDoNhietDo/chiTietDoNhietDo.service';
import REPORT_DO_NHIET_DO from '../ReportDoNhietDo/doNhietDo.model';
import DONG_BO_PHIEU_DO from './dongBo.model';


export function getAll(query, projection = {}) {
  return DONG_BO_PHIEU_DO.find(query, projection).lean();
}

export async function createMany(dataList = []) {
  if (!Array.isArray(dataList)) {
    throw new Error('Input must be an array');
  }

  const validRecords = [];

  for (const data of dataList) {
    const { error, value } = validate(data);
    if (!error) {
      validRecords.push(value);
    }
  }

  return DONG_BO_PHIEU_DO.insertMany(validRecords);
}

export async function updateAll(dataUpdate, options = {}) {
  options.new = true;
  const results = [];
  for (const row of dataUpdate) {
    try {
      const { error, value } = validate(row);
      if (!error) {
        const updated = await DONG_BO_PHIEU_DO.findByIdAndUpdate(value._id, value, options);
        if (updated) results.push(updated);
      }
    } catch {
    }
  }
  return results;
}

const Joi = require('joi');

const objSchema = Joi.object({
  vi_tri_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Vị trí')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function syncDataDoPhaDat(req, value) {

  const mapNgayDo = {}, mapKhoangCot = {};
  const allViTri = await DuongDayService.getViTriByDuongDay(req, value.duong_day_id, true);
  const allKhoangCot = allViTri.vi_tris?.flatMap(vt => vt.khoang_cot_id ? [...vt.khoang_cot_id] : []);

  const dataToSync = await KetQuaDoPhaDatService.getAll({ khoang_cot_id: extractIds(allKhoangCot) })
    .populate('phieu_giao_viec_id');


  dataToSync.forEach(item => {
    if (!mapNgayDo[item.khoang_cot_id]) {
      mapNgayDo[item.khoang_cot_id] = new Date(1, 1, 1);
    }

    if (mapNgayDo[item.khoang_cot_id] < item.ngay_do) {
      mapNgayDo[item.khoang_cot_id] = item.ngay_do;
      mapKhoangCot[item.khoang_cot_id] = item._id;
    }
  });

  function convertToDB(row) {
    let isLatest = false;
    if (mapKhoangCot[row.khoang_cot_id] === row._id) {
      isLatest = true;
    }

    return {
      khoang_cot_id: row.khoang_cot_id,
      khoang_cach_pha_dat: row.khoang_cach_pha_dat,
      dong_tai_khi_do: row.dong_tai_khi_do,
      ngay_do: row.ngay_do,
      nhiet_do_moi_truong: row.nhiet_do_moi_truong,
      khoang_cach_theo_quy_pham: row.khoang_cach_theo_quy_pham,
      dac_diem_khu_vuc: row.dac_diem_khu_vuc,
      de_xuat_xu_ly: row.de_xuat_xu_ly,
      ghi_chu: row.ghi_chu,

      so_phieu: row.phieu_giao_viec_id.so_phieu,
      phieu_giao_viec_id: row.phieu_giao_viec_id._id,
      is_latest: isLatest,
      is_deleted: false,
    };
  }

  const dataToDB = dataToSync.map(convertToDB).filter(element => element.khoang_cot_id);
  console.log('dataToDB', dataToDB);

  await REPORT_DO_PHA_DAT.bulkWrite(
    dataToDB.map((element) =>
      ({
        updateOne: {
          filter: { khoang_cot_id: element.khoang_cot_id, so_phieu: element.so_phieu },
          update: { $set: element },
          upsert: true,
        },
      }),
    ),
  );
}

export async function syncDataDoDienTro(req, value) {

  const mapNgayDo = {}, mapViTri = {};

  const allViTri = await DuongDayService.getViTriByDuongDay(req, value.duong_day_id, true);
  const dataToSync = await KetQuaDoDienTroService.getAll({ vi_tri_id: extractIds(allViTri.vi_tris), is_deleted: false })
    .populate('phieu_giao_viec_id');

  dataToSync.forEach(item => {
    if (!mapNgayDo[item.vi_tri_id]) {
      mapNgayDo[item.vi_tri_id] = new Date(1, 1, 1);
    }
    if (mapNgayDo[item.vi_tri_id] < item.ngay_do) {
      mapNgayDo[item.vi_tri_id] = item.ngay_do;
      mapViTri[item.vi_tri_id] = item._id;
    }
  });

  function convertToDB(row) {
    let isLatest = mapViTri[row.vi_tri_id] === row._id;
    return {
      vi_tri_id: row.vi_tri_id,
      chieu_cao_cot: row.chieu_cao_cot,
      loai_tiep_dia: row.loai_tiep_dia,

      tia_so_mot: row.tri_so_tia_tiep_dia?.tia_so_mot,
      tia_so_hai: row.tri_so_tia_tiep_dia?.tia_so_hai,
      tia_so_ba: row.tri_so_tia_tiep_dia?.tia_so_ba,
      tia_so_bon: row.tri_so_tia_tiep_dia?.tia_so_bon,
      tia_so_nam: row.tri_so_tia_tiep_dia?.tia_so_nam,
      tia_so_sau: row.tri_so_tia_tiep_dia?.tia_so_sau,
      tia_so_bay: row.tri_so_tia_tiep_dia?.tia_so_bay,
      tia_so_tam: row.tri_so_tia_tiep_dia?.tia_so_tam,
      tia_so_chin: row.tri_so_tia_tiep_dia?.tia_so_chin,
      tia_so_muoi: row.tri_so_tia_tiep_dia?.tia_so_muoi,
      tia_so_muoi_mot: row.tri_so_tia_tiep_dia?.tia_so_muoi_mot,
      tia_so_muoi_hai: row.tri_so_tia_tiep_dia?.tia_so_muoi_hai,
      tia_so_muoi_ba: row.tri_so_tia_tiep_dia?.tia_so_muoi_ba,
      tia_so_muoi_bon: row.tri_so_tia_tiep_dia?.tia_so_muoi_bon,
      tia_so_muoi_nam: row.tri_so_tia_tiep_dia?.tia_so_muoi_nam,

      dien_tro_he_thong: row.dien_tro_he_thong,
      tro_suat_cua_dat: row.tro_suat_cua_dat,
      dia_hinh_id: row.dia_hinh_id,
      ngay_do: row.ngay_do,
      huong_do: row.huong_do,
      dien_tro_theo_quy_pham: row.dien_tro_theo_quy_pham,
      ket_luan: row.ket_luan,
      nguoi_tao_id: row.nguoi_tao_id,
      ghi_chu: row.ghi_chu,

      so_phieu: row.phieu_giao_viec_id.so_phieu,
      phieu_giao_viec_id: row.phieu_giao_viec_id._id,
      is_latest: isLatest,
      is_deleted: false,
    };
  }

  const dataToDB = dataToSync.map(convertToDB).filter(element => element.vi_tri_id);
  await REPORT_DO_DIEN_TRO.bulkWrite(
    dataToDB.map((element) =>
      ({
        updateOne: {
          filter: { vi_tri_id: element.vi_tri_id, so_phieu: element.so_phieu },
          update: { $set: element },
          upsert: true,
        },
      }),
    ),
  );
}

export async function syncDataDoNhietDo(req, value) {

  const mapNgayDo = {}, mapData = {};
  const allViTri = await DuongDayService.getViTriByDuongDay(req, value.duong_day_id, true);
  const allKhoangCot = allViTri.vi_tris?.flatMap(vt => vt.khoang_cot_id ? [...vt.khoang_cot_id] : []);
  const allDoNhietDo = await ketQuaDoNhietDo.getAll({
    $or: [
      { vi_tri_id: extractIds(allViTri.vi_tris) },
      { khoang_cot_id: extractIds(allKhoangCot) },
    ],
    is_deleted: false,
  }).select('vi_tri_id khoang_cot_id');

  const allChiTietDoNhietDo = await chiTietDoNhietDo.getAll({
    ket_qua_do_nhiet_do_id: extractIds(allDoNhietDo),
    is_deleted: false,
  }).populate({ path: 'ket_qua_do_nhiet_do_id', populate: 'phieu_giao_viec_id' });

  function mapDataTemp(key, row) {
    if (!mapNgayDo[key]) {
      mapNgayDo[key] = new Date(1, 1, 1);
    }
    if (mapNgayDo[key] < row.ket_qua_do_nhiet_do_id?.thoi_gian_do) {
      mapNgayDo[key] = row.ket_qua_do_nhiet_do_id?.thoi_gian_do;
      mapData[key] = row._id;
    }
  }

  function getStringMap(row) {
    let stringViTri, stringKhoangCot;
    if (row.ket_qua_do_nhiet_do_id.version === 2) {
      stringViTri = row.ket_qua_do_nhiet_do_id?.vi_tri_id + row.xuat_tuyen + row.huong_do + row.day_pha + row.day_dan_phan_pha;
      stringKhoangCot = row.ket_qua_do_nhiet_do_id?.khoang_cot_id + row.xuat_tuyen + row.day_pha + row.day_dan_phan_pha;
    } else {
      stringViTri = row.ket_qua_do_nhiet_do_id?.vi_tri_id + row.mach?.toString() + row.huong_do + row.day_dan_phan_pha;
      stringKhoangCot = row.ket_qua_do_nhiet_do_id?.khoang_cot_id + row.mach?.toString() + row.day_dan_phan_pha;
    }
    return { stringViTri, stringKhoangCot };
  }

  allChiTietDoNhietDo.forEach(ketQua => {
    const { stringViTri, stringKhoangCot } = getStringMap(ketQua);
    !!ketQua.ket_qua_do_nhiet_do_id?.vi_tri_id ? mapDataTemp(stringViTri, ketQua) : mapDataTemp(stringKhoangCot, ketQua);
  });

  function convertToDB(row) {
    const ketQuaDo = row.ket_qua_do_nhiet_do_id;
    let isLatest;
    const { stringViTri, stringKhoangCot } = getStringMap(row);
    ketQuaDo?.vi_tri_id ? isLatest = mapData[stringViTri] === row._id : isLatest = mapData[stringKhoangCot] === row._id;

    return {
      khoang_cot_id: ketQuaDo?.khoang_cot_id,
      vi_tri_id: ketQuaDo?.vi_tri_id,
      mach: row.mach,
      huong_do: row.huong_do,
      day_dan_phan_pha: row.day_dan_phan_pha,
      pha_a: row.pha_a,
      pha_b: row.pha_b,
      pha_c: row.pha_c,
      dong_tai: row.dong_tai,
      danh_gia: row.danh_gia,
      nhiet_do_moi_truong: ketQuaDo?.nhiet_do_moi_truong || row.nhiet_do_moi_truong,

      day_pha: row.day_pha,
      xuat_tuyen: row.xuat_tuyen,
      nhiet_do_day_dan: row.nhiet_do_day_dan,
      order: row.order,
      version: ketQuaDo?.version,

      nguoi_do: ketQuaDo.nguoi_do,
      thoi_gian_do: ketQuaDo?.thoi_gian_do,
      ghi_chu: ketQuaDo?.ghi_chu,

      so_phieu: ketQuaDo?.phieu_giao_viec_id?.so_phieu,
      phieu_giao_viec_id: ketQuaDo?.phieu_giao_viec_id?._id,
      is_latest: isLatest,
      is_deleted: false,
    };

  }

  const hasVersion = {};

  function mapVersion(listData = [], key) {
    listData.forEach(item => {
      hasVersion[item[key]] = true;
    });
  }

  function setLastest(listData = [], key) {
    listData.forEach(item => {
      if (hasVersion[item[key]]) {
        item.is_latest = false;
      }
    });
  }

  const dataViTri = allChiTietDoNhietDo.map(convertToDB).filter(item => item.vi_tri_id);
  const dataKhoangCot = allChiTietDoNhietDo.map(convertToDB).filter(item => item.khoang_cot_id);
  const viTriVer1 = dataViTri.filter(item => item.version !== 2);
  const viTriVer2 = dataViTri.filter(item => item.version === 2);
  const khoangCotVer1 = dataKhoangCot.filter(item => item.version !== 2);
  const khoangCotVer2 = dataKhoangCot.filter(item => item.version === 2);
  mapVersion(viTriVer2, 'vi_tri_id');
  mapVersion(khoangCotVer2, 'khoang_cot_id');
  setLastest(viTriVer1, 'vi_tri_id');
  setLastest(khoangCotVer1, 'khoang_cot_id');


  function dongBoViTriVer1() {
    REPORT_DO_NHIET_DO.bulkWrite(
      viTriVer1.map((element) =>
        ({
          updateOne: {
            filter: {
              vi_tri_id: element.vi_tri_id,
              so_phieu: element.so_phieu,
              mach: element.mach,
              huong_do: element.huong_do,
              day_dan_phan_pha: element.day_dan_phan_pha,
            },
            update: { $set: element },
            upsert: true,
          },
        }),
      ),
    );
  }

  function dongBoViTriVer2() {
    REPORT_DO_NHIET_DO.bulkWrite(
      viTriVer2.map((element) =>
        ({
          updateOne: {
            filter: {
              vi_tri_id: element.vi_tri_id,
              so_phieu: element.so_phieu,
              huong_do: element.huong_do,
              xuat_tuyen: element.xuat_tuyen,
              day_pha: element.day_pha,
              day_dan_phan_pha: element.day_dan_phan_pha,
            },
            update: { $set: element },
            upsert: true,
          },
        }),
      ),
    );
  }

  function dongBoKhoangCotVer1() {
    REPORT_DO_NHIET_DO.bulkWrite(
      khoangCotVer1.map((element) =>
        ({
          updateOne: {
            filter: {
              khoang_cot_id: element.khoang_cot_id,
              so_phieu: element.so_phieu,
              mach: element.mach,
              day_dan_phan_pha: element.day_dan_phan_pha,
            },
            update: { $set: element },
            upsert: true,
          },
        }),
      ),
    );
  }

  function dongBoKhoangCotVer2() {
    REPORT_DO_NHIET_DO.bulkWrite(
      khoangCotVer2.map((element) =>
        ({
          updateOne: {
            filter: {
              khoang_cot_id: element.khoang_cot_id,
              so_phieu: element.so_phieu,
              xuat_tuyen: element.xuat_tuyen,
              day_pha: element.day_pha,
              day_dan_phan_pha: element.day_dan_phan_pha,
            },
            update: { $set: element },
            upsert: true,
          },
        }),
      ),
    );
  }

  const allPromise = [
    dongBoViTriVer1(),
    dongBoViTriVer2(),
    dongBoKhoangCotVer1(),
    dongBoKhoangCotVer2(),
  ];
  await Promise.all(allPromise);

}
