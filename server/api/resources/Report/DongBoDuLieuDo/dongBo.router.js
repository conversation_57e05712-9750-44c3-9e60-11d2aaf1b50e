import express from 'express';
import passport from 'passport';
import * as Controller from './dongBo.controller';
import { loggerMiddleware } from '../../../logs/middleware';

export const dongBoRouter = express.Router();

dongBoRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

dongBoRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), Controller.getAll)
  .post(passport.authenticate('jwt', { session: false }), Controller.create);

dongBoRouter
  .route('/capnhat')
  .post(passport.authenticate('jwt', { session: false }), Controller.reSyncData);

dongBoRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), Controller.findOne)
  .delete(passport.authenticate('jwt', { session: false }), Controller.remove)
  .put(passport.authenticate('jwt', { session: false }), Controller.update);
