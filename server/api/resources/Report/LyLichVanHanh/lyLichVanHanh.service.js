import VI_TRI_CONG_VIEC from '../../QuanLyVanHanh/ViTriCongViec/viTriCongViec.model';
import PHIEU_GIAO_VIEC from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.model';
import { extractKeyObjectIds, extractObjectIds } from '../../../utils/dataconverter';
import { LOAI_CONG_VIEC } from '../../DanhMuc/LoaiCongViec';
import KET_QUA_KIEM_TRA from '../../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.model';
import KET_QUA_SUA_CHUA_CO_KE_HOACH from '../../QuanLyVanHanh/KetQuaSuaChuaCoKeHoach/ketQuaSuaChuaCoKeHoach.model';
import CONG_VIEC_PHAT_SINH from '../../QuanLyVanHanh/CongViecPhatSinh/congViecPhatSinh.model';
import KET_QUA_DO_NHIET_DO from '../../KetQuaDoNhietDo/ketQuaDoNhietDo.model';
import DO_DIEN_TRO from '../../QuanLyVanHanh/DoDienTro/doDienTro.model';
import KET_QUA_DO_KHOANG_CACH_PHA_DAT
  from '../../QuanLyVanHanh/KetQuaDoKhoangCachPhaDat/ketQuaDoKhoangCachPhaDat.model';
import { formatDateTime, formatDate } from '../../../common/formatUTCDateToLocalDate';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import { TRANG_THAI_XU_LY } from '../../DanhMuc/TrangThaiXuLy';
import { downlineData } from '../../../common/functionCommons';
import { fisrtDayOfCurrentMonth } from '../../../utils/dateUtil';

export async function getDataCongTacKiemTra(criteria, is_download = false) {
  const allViTriCongViec = await VI_TRI_CONG_VIEC.find(
    criteria, { phieu_giao_viec_id: 1 },
  );

  let loaiCongViec = Object.values(LOAI_CONG_VIEC).filter(congViec => congViec.type === 'KIEM_TRA');
  const loaiCongViecCodes = loaiCongViec.map(loai => loai.code);

  const allPhieuGiaoViec = await PHIEU_GIAO_VIEC.find(
    {
      _id: { $in: extractKeyObjectIds(allViTriCongViec, 'phieu_giao_viec_id') },
      loai_cong_viec: { $in: loaiCongViecCodes },
    },
    {
      so_phieu: 1,
      nguoi_cap_phieu_id: 1,
      loai_cong_viec: 1,
      chi_huy_truc_tiep_id: 1,
      thoi_gian_cong_tac_bat_dau: 1,
      thoi_gian_xac_nhan_khoa_phieu: 1,
      trang_thai_cong_viec: 1,
    },
  ).populate([
    { path: 'nguoi_cap_phieu_id', select: 'full_name' },
    { path: 'chi_huy_truc_tiep_id', select: 'full_name' },
  ]).sort({ created_at: -1 });

  function convertToDownLoad(row) {
    return {
      so_phieu: row.so_phieu,
      nguoi_cap_phieu_id: row.nguoi_cap_phieu_id?.full_name,
      loai_cong_viec: LOAI_CONG_VIEC[row.loai_cong_viec]?.name,
      chi_huy_truc_tiep_id: row.chi_huy_truc_tiep_id?.full_name,
      trang_thai_cong_viec: TRANG_THAI_PHIEU[row.trang_thai_cong_viec]?.name,
      thoi_gian_cong_tac_bat_dau: formatDateTime(row.thoi_gian_cong_tac_bat_dau),
      thoi_gian_xac_nhan_khoa_phieu: formatDate(row.thoi_gian_xac_nhan_khoa_phieu),
    };
  }

  return !is_download ? allPhieuGiaoViec : allPhieuGiaoViec.map(convertToDownLoad);
}

export async function getDataTonTaiViTriKhoangCot(criteria) {
  const allTonTai = await KET_QUA_KIEM_TRA.find(criteria,
    {
      tieu_chi_id: 1,
      thoi_gian_tao: 1,
      tinh_trang_xu_ly: 1,
      noi_dung_chi_tiet: 1,
      phieu_giao_viec_id: 1,
    }).populate([
    { path: 'tieu_chi_id', populate: 'tieu_chi_cha_id noi_dung_kiem_tra_id' },
    { path: 'phieu_giao_viec_id', match: { trang_thai_cong_viec: TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code } },
  ]).sort({ created_at: -1 }).lean();

  const allKetQuaCoKeHoach = await KET_QUA_SUA_CHUA_CO_KE_HOACH.find(
    { ton_tai_id: { $in: extractObjectIds(allTonTai) }, is_deleted: false },
    { phieu_giao_viec_id: 1, nguoi_tao: 1, ton_tai_id: 1 },
  ).populate({ path: 'phieu_giao_viec_id' }).lean();

  const mapPhieuSuaChua = {};
  allKetQuaCoKeHoach.forEach(ketqua => {
    mapPhieuSuaChua[ketqua.ton_tai_id] = ketqua;
  });
  allTonTai.forEach(tontai => {
    const tieuChi = tontai.tieu_chi_id;
    tontai.thoi_gian = formatDate(tontai.thoi_gian_tao);
    tontai.ket_qua_sua_chua = mapPhieuSuaChua[tontai._id] || [];
    tontai.tinh_trang = TRANG_THAI_XU_LY[tontai.tinh_trang_xu_ly]?.label;
    tontai.noi_dung_ton_tai = tieuChi?.tieu_chi_cha_id ? tieuChi.ten_tieu_chi : '';
    tontai.tieu_chi_danh_gia = tieuChi?.tieu_chi_cha_id ? tieuChi?.tieu_chi_cha_id.ten_tieu_chi : tieuChi.ten_tieu_chi;
  });
  return allTonTai.filter(item => item.phieu_giao_viec_id !== null);

}

export async function getDataCongViecPhatSinh(criteria) {
  const dataCongViecPhatSinh = await CONG_VIEC_PHAT_SINH.find(criteria)
    .populate('cong_viec_id ')
    .populate({
      path: 'phieu_giao_viec_id',
      match: {
        trang_thai_cong_viec: TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code,
        is_deleted: false,
      },
    })
    .sort({ thoi_gian_tao: -1 })
    .lean();
  return dataCongViecPhatSinh.filter(item => item.phieu_giao_viec_id !== null);
}

export async function getDataDoThongSoViTri(criteria) {
  if (!criteria.hasOwnProperty('phieu_giao_viec_id')) {
    criteria.phieu_giao_viec_id = { $exists: true };
  }

  const allDoNhietDo = await KET_QUA_DO_NHIET_DO.find(criteria)
    .populate('nguoi_do phieu_giao_viec_id')
    .lean();

  const allDoDienTro = await DO_DIEN_TRO.find(criteria)
    .populate('nguoi_tao_id phieu_giao_viec_id')
    .lean();

  const allDoKhoangCach = await KET_QUA_DO_KHOANG_CACH_PHA_DAT.find(criteria)
    .populate({ path: 'phieu_giao_viec_id', populate: 'nguoi_khoa_phieu_id' })
    .lean();

  function convertNhietDoToRow(row) {
    let stringNguoiDo;
    row.nguoi_do.forEach(item => {
      if (!stringNguoiDo) {
        stringNguoiDo = item ? `- ${item?.full_name}` : '';
      } else {
        stringNguoiDo = downlineData(stringNguoiDo, `- ${item?.full_name}`);
      }
    });
    return {
      nguoi_do: row.nguoi_do,
      string_nguoi_do: stringNguoiDo,
      thoi_gian_do: row.thoi_gian_do,
      phieu_giao_viec_id: row.phieu_giao_viec_id,
      ngay_do: formatDateTime(row.thoi_gian_do),
      trang_thai_phieu: TRANG_THAI_PHIEU[row.phieu_giao_viec_id.trang_thai_cong_viec]?.name,
      loai_cong_viec: LOAI_CONG_VIEC[row.phieu_giao_viec_id.loai_cong_viec]?.name,
    };
  }

  function convertDienTroToRow(row) {
    let stringNguoiDo;
    [row.nguoi_tao_id].forEach(item => {
      if (!stringNguoiDo) {
        stringNguoiDo = item ? `- ${item?.full_name}` : '';
      } else {
        stringNguoiDo = downlineData(stringNguoiDo, `- ${item?.full_name}`);
      }
    });
    return {
      thoi_gian_do: row.ngay_do,
      nguoi_do: [row.nguoi_tao_id],
      string_nguoi_do: stringNguoiDo,
      phieu_giao_viec_id: row.phieu_giao_viec_id,
      ngay_do: formatDateTime(row.ngay_do),
      loai_cong_viec: LOAI_CONG_VIEC[row.phieu_giao_viec_id.loai_cong_viec]?.name,
      trang_thai_phieu: TRANG_THAI_PHIEU[row.phieu_giao_viec_id.trang_thai_cong_viec]?.name,
    };
  }

  function convertKhoangCachToRow(row) {
    let stringNguoiDo;
    [row.phieu_giao_viec_id.nguoi_khoa_phieu_id].forEach(item => {
      if (!stringNguoiDo) {
        stringNguoiDo = item ? `- ${item?.full_name}` : '';
      } else {
        stringNguoiDo = downlineData(stringNguoiDo, `- ${item?.full_name}`);
      }
    });
    return {
      thoi_gian_do: row.ngay_do,
      string_nguoi_do: stringNguoiDo,
      ngay_do: formatDateTime(row.ngay_do),
      phieu_giao_viec_id: row.phieu_giao_viec_id,
      nguoi_do: [row.phieu_giao_viec_id.nguoi_khoa_phieu_id],
      trang_thai_phieu: TRANG_THAI_PHIEU[row.phieu_giao_viec_id.trang_thai_cong_viec]?.name,
      loai_cong_viec: LOAI_CONG_VIEC[row.phieu_giao_viec_id.loai_cong_viec]?.name,
    };
  }

  const dataResponse = [
    allDoNhietDo.map(convertNhietDoToRow),
    criteria.vi_tri_id
      ? allDoDienTro.map(convertDienTroToRow)
      : allDoKhoangCach.map(convertKhoangCachToRow)].flat();

  return dataResponse.sort((a, b) => b.thoi_gian_do - a.thoi_gian_do);
}
