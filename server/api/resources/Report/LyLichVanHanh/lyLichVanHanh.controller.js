import * as Service from './lyLichVanHanh.service';
import * as responseHelper from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';

import { getFilePath } from '../../../utils/fileUtils';
import { TEMPLATES_DIRS } from '../../../constant/constant';
import { generateDocument } from '../GenerateFile/generate.controller';
import { addIndexToListData } from '../../../common/functionCommons';

import VI_TRI from '../../TongKe/ViTri/viTri.model';
import KHOANG_COT from '../../TongKe/KhoangCot/khoangCot.model';

const TON_TAI = 'TON_TAI';
const KIEM_TRA = 'KIEM_TRA';
const DO_THONG_SO = 'DO_THONG_SO';
const SUA_CHUA_BAO_DUONG = 'SUA_CHUA_BAO_DUONG';
const CONG_VIEC_PHAT_SINH = 'CONG_VIEC_PHAT_SINH';

export async function getAllCongTacKiemTra(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;

    const data = await Service.getDataCongTacKiemTra(criteria);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllTonTaiViTriKhoangCot(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;

    const data = await Service.getDataTonTaiViTriKhoangCot(criteria);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllCongViecPhatSinh(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;

    const data = await Service.getDataCongViecPhatSinh(criteria);

    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllDoThongSo(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    criteria.phieu_giao_viec_id = { $exists: true };

    const data = await Service.getDataDoThongSoViTri(criteria);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function downloadLyLichVanHanh(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    let viTriKhoangCot;
    if (criteria.vi_tri_id) {
      viTriKhoangCot = (await VI_TRI.findOne({ _id: criteria.vi_tri_id }).lean()).ten_vi_tri;
    } else {
      viTriKhoangCot = (await KHOANG_COT.findOne({ _id: criteria.khoang_cot_id }).lean()).ten_khoang_cot;
    }

    const loaiLyLich = criteria.type;
    delete criteria.type;
    let dataLyLich = [];
    switch (loaiLyLich) {
      case KIEM_TRA:
        dataLyLich = await Service.getDataCongTacKiemTra(criteria, true);
        break;
      case SUA_CHUA_BAO_DUONG:
        dataLyLich = await Service.getDataTonTaiViTriKhoangCot(criteria);
        break;
      case DO_THONG_SO:
        dataLyLich = await Service.getDataDoThongSoViTri(criteria);
        break;
      case CONG_VIEC_PHAT_SINH:
        dataLyLich = await Service.getDataCongViecPhatSinh(criteria);
        break;
      case TON_TAI:
        dataLyLich = await Service.getDataTonTaiViTriKhoangCot(criteria);
        break;
      default:
    }
    const dataToRender = {
      header_text: `${criteria.vi_tri_id ? 'VỊ TRÍ' : 'KHOẢNG CỘT'} ${viTriKhoangCot}`,
      ly_lich: addIndexToListData(dataLyLich),
    };
    const templateFilePath = getFilePath(`${loaiLyLich.toLowerCase()}.xlsx`, TEMPLATES_DIRS.LY_LICH_VAN_HANH);
    await generateDocument(res, dataToRender, templateFilePath);
  } catch (err) {
    console.log('err', err);
    return responseHelper.error(res, err);
  }
}
