import express from 'express';
import passport from 'passport';
import * as Controller from './lyLichVanHanh.controller';
import { loggerMiddleware } from '../../../logs/middleware';

export const lyLichVanHanhRouter = express.Router();

lyLichVanHanhRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

lyLichVanHanhRouter
  .route('/congtackiemtra')
  .get(Controller.getAllCongTacKiemTra);

lyLichVanHanhRouter
  .route('/tontai')
  .get(Controller.getAllTonTaiViTriKhoangCot);

lyLichVanHanhRouter
  .route('/congviecphatsinh')
  .get(Controller.getAllCongViecPhatSinh);

lyLichVanHanhRouter
  .route('/congtacdothongso')
  .get(Controller.getAllDoThongSo);

lyLichVanHanhRouter
  .route('/download')
  .get(Controller.downloadLyLichVanHanh);


