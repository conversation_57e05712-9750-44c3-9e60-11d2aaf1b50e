import path from 'path';

const ExcelJS = require('exceljs');


import * as responseHelper from '../../helpers/responseHelper';
import queryHelper from '../../helpers/queryHelper';
import * as Service from './taiLieu.service';
import * as DuongDayService from '../TongKe/DuongDay/duongDay.service';
import Model from './taiLieu.model';
import * as fileUtils from '../../utils/fileUtils';
import { deleteFile, getAndCheckExistFilePath, getFilePath } from '../../utils/fileUtils';
import { getFileSizeInBytes, removeDuplicateObject } from '../../common/functionCommons';
import { CONG_TRINH } from '../../constant/dbCollections';
import { extractKeys, groupBy } from '../../utils/dataconverter';
import * as CongTrinhService from '../TongKe/CongTrinh/congTrinh.service';
import { STORE_DIRS } from '../../constant/constant';

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id)
      .populate('nhom_ho_so_id ho_so_id').lean();
    if (!data) {
      return responseHelper.error(res, 404, '');
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseHelper.error(res, '', 400);
    }
    if (data.file_id) {
      deleteFile(getFilePath(data.file_id));
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function update(req, res) {
  try {
    let { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    let filePath = undefined;
    if (!value.file) {
      filePath = req.files?.file?.path;
      const file_id = fileUtils.createUniqueFileName(filePath);
      await fileUtils.createByName(filePath, file_id);
      value.file_id = file_id;
      value.file_name = req.files?.file?.name;
    } else {
      delete value.file;
    }
    // const beforeUpdated = await Model.findById(value._id);
    const data = await Model.findOneAndUpdate({ _id: value._id }, value, { new: true });
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    // if (beforeUpdated.file_id) {
    //   deleteFile(getFilePath(beforeUpdated.file_id));
    // }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function create(req, res) {

  try {
    let value = req.body;
    let filePath = undefined;
    if (!value.hoso_id) {
      delete value.hoso_id;
    }
    if (!value.file) {
      filePath = req.files?.file?.path;
      const file_id = fileUtils.createUniqueFileName(filePath);
      await fileUtils.createByName(filePath, file_id);
      value.file_id = file_id;
      value.file_name = req.files?.file?.name;
    } else {
      delete value.file;
    }
    const data = await Model.create(value);
    let dataRtn = await data
      .populate({ path: 'hoso_id' }).execPopulate();
    return responseHelper.success(res, dataRtn);
  } catch (err) {
    console.error(err);
    return responseHelper.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    options.populate = [
      { path: 'hoso_id', populate: { path: 'loai_ho_so_id' } },
      { path: 'nhom_ho_so_id' },
      { path: 'nguoi_tai_id' },
    ];
    options.sort = { createdAt: 1 };
    const data = await Model.paginate(criteria, options);
    data.docs?.forEach(item => {
      const path = getAndCheckExistFilePath(item?.file_id);
      if (path) {
        item.size = getFileSizeInBytes(path) / (1024 * 1000);
      }
    });
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

function unwrapHoSo(hoSo) {
  const result = [];
  let current = hoSo;

  while (current && current.ho_so_id) {
    result.unshift({
      _id: current.ho_so_id._id,
      ten_ho_so: current.ho_so_id.ten_ho_so,
    });
    current = current.ho_so_id;
  }

  return extractKeys(result, 'ten_ho_so');
}


export async function getAllFileMissing(req, res) {
  try {

    const data = await Model.find(
      { is_deleted: false },
      // { doi_tuong_id: 1, file_id: 1, file_name: 1 },
    )
      .populate({
        path: 'ho_so_id', select: 'ten_ho_so ho_so_id',
        populate: {
          path: 'ho_so_id', select: 'ten_ho_so ho_so_id',
          populate: { path: 'ho_so_id', select: 'ten_ho_so ho_so_id' },
        },
      })
      .lean();

    const taiLieuMissing = data
      .map(item => {
        item.path = getAndCheckExistFilePath(item?.file_id);
        if (!item.path) {
          const hsList = unwrapHoSo(item);
          hsList.forEach((hs, index) => {
            item[`hsCap${index + 1}`] = hs;
          });
        }

        delete item.ho_so_id;
        return item;
      })
      .filter(item => !item.path);

    const taiLieuGroupByCongTrinh = groupBy(taiLieuMissing, 'doi_tuong_id');

    const congTrinhId = Object.keys(taiLieuGroupByCongTrinh);
    const allCongTrinh = await CongTrinhService.getAll(
      { _id: congTrinhId, is_deleted: false },
      { ten_cong_trinh: 1 },
    );
    allCongTrinh.forEach(congTrinh => {
      congTrinh.tai_lieu_missing = taiLieuGroupByCongTrinh[congTrinh._id];
    });

    const filesDir = await exportTaiLieuMerged(allCongTrinh);
    return res.download(filesDir);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllCongTrinhByDuongDayId(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const data = await DuongDayService.getViTriByDuongDay(req, criteria.duong_day_id);
    const allViTriByDuongDay = data.vi_tris;
    const allCongTrinhByDuongDay = allViTriByDuongDay.map(item => item.cong_trinh_id);
    const allCongTrinh = removeDuplicateObject(allCongTrinhByDuongDay, '_id');
    return responseHelper.success(res, allCongTrinh);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

async function exportTaiLieuMerged(data) {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Thiếu hồ sơ');

  // Header
  const headers = ['SST', 'Tên công trình', 'Hồ sơ', '', '', 'Tên file'];
  worksheet.addRow(headers);
  worksheet.mergeCells('C1:E1');

  const borderStyle = {
    top: { style: 'thin' },
    bottom: { style: 'thin' },
    left: { style: 'thin' },
    right: { style: 'thin' },
  };

  let currentRow = 2;

  data.forEach((congTrinh, index) => {
    const groupedCap1 = {};

    for (const file of congTrinh.tai_lieu_missing) {
      const { hsCap1, hsCap2, hsCap3 } = file;
      groupedCap1[hsCap1] ??= {};
      groupedCap1[hsCap1][hsCap2] ??= {};
      groupedCap1[hsCap1][hsCap2][hsCap3] ??= [];
      groupedCap1[hsCap1][hsCap2][hsCap3].push(file);
    }

    for (const hsCap1 in groupedCap1) {
      const cap2Group = groupedCap1[hsCap1];

      for (const hsCap2 in cap2Group) {
        const cap3Group = cap2Group[hsCap2];

        for (const hsCap3 in cap3Group) {
          const files = cap3Group[hsCap3];

          for (const file of files) {
            worksheet.addRow([
              index + 1,
              congTrinh.ten_cong_trinh,
              hsCap1,
              hsCap2,
              hsCap3,
              file.file_name,
            ]);
          }

          // Merge hsCap3
          if (files.length > 1) {
            worksheet.mergeCells(`E${currentRow}:E${currentRow + files.length - 1}`);
          }

          currentRow += files.length;
        }

        const hsCap2Count = Object.values(cap3Group).reduce((sum, arr) => sum + arr.length, 0);
        if (hsCap2Count > 1) {
          worksheet.mergeCells(`D${currentRow - hsCap2Count}:D${currentRow - 1}`);
        }
      }

      const hsCap1Count = Object.values(cap2Group).reduce((sum, cap3) => {
        return sum + Object.values(cap3).reduce((s, arr) => s + arr.length, 0);
      }, 0);

      if (hsCap1Count > 1) {
        worksheet.mergeCells(`C${currentRow - hsCap1Count}:C${currentRow - 1}`);
      }
    }

    // Merge công trình nếu nhiều dòng
    const congTrinhCount = congTrinh.tai_lieu_missing.length;
    if (congTrinhCount > 1) {
      worksheet.mergeCells(`A${currentRow - congTrinhCount}:A${currentRow - 1}`);
      worksheet.mergeCells(`B${currentRow - congTrinhCount}:B${currentRow - 1}`);
    }
  });

  // Border cho tất cả ô có dữ liệu
  worksheet.eachRow((row, rowNumber) => {
    row.eachCell(cell => {
      cell.border = borderStyle;
      cell.alignment = { vertical: 'middle', wrapText: true };
    });
  });

  worksheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' };
  worksheet.getRow(1).font = { bold: true };

  for (let i = 2; i <= worksheet.rowCount; i++) {
    worksheet.getCell(`A${i}`).alignment = { vertical: 'middle', horizontal: 'center' };
  }

  worksheet.columns = headers.map(() => ({ width: 40 }));
  worksheet.columns[0].width = 10;


  const filesDir = path.join(STORE_DIRS.STORAGE, 'fileMissing.xlsx');
  await workbook.xlsx.writeFile(filesDir);

  return filesDir;
}

