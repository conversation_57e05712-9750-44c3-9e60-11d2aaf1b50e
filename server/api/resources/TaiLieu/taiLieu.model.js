import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { HO_SO, LOAI_TAI_LIEU, USER, VI_TRI } from '../../constant/dbCollections';

const schema = new Schema({
  file_id: { type: String },
  file_name: { type: String },
  ho_so_id: { type: Schema.Types.ObjectId, ref: HO_SO },
  doi_tuong_id: { type: Schema.Types.ObjectId },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  link_file: { type: String },
  ma_thiet_bi: { type: String },
  nguoi_tai_id: { type: Schema.Types.ObjectId, ref: USER },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(LOAI_TAI_LIEU, schema, LOAI_TAI_LIEU);
