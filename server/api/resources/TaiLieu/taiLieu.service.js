import * as ValidatorHelper from '../../helpers/validatorHelper';
import TaiLieuModel from './taiLieu.model';
import createBaseService from '../../base/baseService';

const Joi = require('joi');

const objSchema = Joi.object({
  name: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên tài liệu')),
  hoso_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Tê<PERSON> hồ sơ')),
  doi_tuong_id: Joi.string().messages(ValidatorHelper.messageDefine('<PERSON><PERSON><PERSON> tượng')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

const baseService = createBaseService(TaiLieuModel);

export const createMulti = baseService.createMulti;
export const getAll = baseService.getAll;
export const getOne = baseService.getOne;
export const remove = baseService.remove;
export const distinctId = baseService.distinctId;
export const updateByQuery = baseService.updateByQuery;
export const updateByRows = baseService.updateByRows;
export const deleteAll = baseService.deleteAll;
export const updateAll = baseService.updateAll;
