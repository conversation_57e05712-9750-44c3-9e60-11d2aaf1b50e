import express from 'express';
import passport from 'passport';
import * as tailieuController from './taiLieu.controller';
import { checkTempFolder, multipartMiddleware } from '../../utils/fileUtils';
import { authorizationMiddleware, sysadminAuthorizationMiddleware } from '../RBAC/middleware';
import HoSoPermission from '../RBAC/permissions/HoSoPermission';
import { loggerMiddleware } from '../../logs/middleware';

export const taiLieuRouter = express.Router();

taiLieuRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
taiLieuRouter.post('*', authorizationMiddleware([HoSoPermission.CREATE]));
taiLieuRouter.put('*', authorizationMiddleware([HoSoPermission.UPDATE]));
taiLieuRouter.delete('*', authorizationMiddleware([HoSoPermission.DELETE]));
taiLieuRouter.get('*', authorizationMiddleware([HoSoPermission.READ]));

taiLieuRouter
  .route('/')
  .get(tailieuController.getAll)
  .post(checkTempFolder, multipartMiddleware, tailieuController.create)
  .put(checkTempFolder, multipartMiddleware, tailieuController.update);

taiLieuRouter
  .route('/missing')
  .get(sysadminAuthorizationMiddleware(), tailieuController.getAllFileMissing);

taiLieuRouter
  .route('/duongday')
  .get(tailieuController.getAllCongTrinhByDuongDayId);

taiLieuRouter
  .route('/:id')
  .get(tailieuController.findOne)
  .delete(tailieuController.remove);


