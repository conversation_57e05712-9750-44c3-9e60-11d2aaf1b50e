import path, { resolve } from 'path';
import fs from 'fs';

import * as responseHelper from '../../helpers/responseHelper';

import { STORE_DIRS } from '../../constant/constant';
import FileTrashModel from './fileTrash.model';
import { getFilePath } from '../../utils/fileUtils';

// import { readdir, unlink } from 'node:fs/promises';
import { cloneObj } from '../../../api/common/functionCommons';
import anhViTriModel from '../AnhViTri/anhViTri.model';
import userModel from '../User/user.model';
import anhCongTacModel from '../QuanLyVanHanh/PhieuCongTac/AnhCongTac/anhCongTac.model';
import anhTonTaiModel from '../QuanLyTonTaiCongTrinhXayDung/AnhTonTai/anhTonTai.model';
import hoSoThietBiModel from '../HoSoThietBi/hoSoThietBi.model';
import fileBienBanModel from '../QuanLyTonTaiCongTrinhXayDung/FileBienBan/fileBienBan.model';
import phieuCongTacNgoaiModel from '../QuanLyVanHanh/PhieuCongTac/PhieuCongTacNgoai/phieuCongTacNgoai.model';
import tonTaiCapTrenModel from '../QuanLyVanHanh/PhieuGiaoViec/TonTaiCapTren/tonTaiCapTren.model';
import tapTinModel from '../QuanLyVanHanh/TapTin/tapTin.model';
import taiLieuModel from '../TaiLieu/taiLieu.model';
import { buildChunks } from '../../common/DataStructureHelper';


export async function insertImageTrash(req, res) {
  try {
    const filesDir = path.join(STORE_DIRS.STORAGE);
    const imageExtension = ['.jpeg', ',JPEG', '.jpg', '.JPG', '.png', '.PNG']
    const listFileAndFolder = await readdir(filesDir, { withFileTypes: true });
    const listImage = listFileAndFolder.filter((dirent) => imageExtension.includes(path.extname(dirent.name)));
    let insertNumber = 0;
    if (listImage.length) {
      const options = {
        ordered: false,
        lean: false,
        limit: 1000,
        populate: null
      }
      const chunks = buildChunks(listImage, 1000);
      for (let i = 0; i < chunks.length; i++) {
        const listDataToInsert = chunks[i].map((image) => {
          return {
            fileName: image.name
          }
        });
        try {
          await FileTrashModel.insertMany(listDataToInsert, options);
        } catch (err) {
        };
        insertNumber += listDataToInsert.length;
      }
    }

    return responseHelper.success(res, { count: insertNumber });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function checkImageTrash(req, res) {
  try {
    const { executeNumber } = req.body;

    //Get list image haven't check in mode, limit by executeList param from body of request.
    const listImage = await FileTrashModel.find({ checked: false }, { fileName: 1 }, { limit: executeNumber }).lean();
    let countError = 0;
    if (listImage.length) {
      const chunks = buildChunks(listImage, 1000);
      for (let i = 0; i < chunks.length; i++) {
        const executeList = chunks[i].map(async (image) => {
          let dataUpdate = cloneObj(image);
          delete dataUpdate._id;

          //call request check exits this image in db.
          return new Promise(async (resolve, reject) => {
            const imageThumbnailQuery = {
              $or: [
                { image_id: image.fileName },
                { thumbnail_id: image.fileName }
              ]
            };

            const userQuery = {
              $or: [
                { avatar: image.fileName },
                { chu_ky_id: image.fileName }
              ]
            };

            const fileIdQuery = {
              file_id: image.fileName
            }

            const hashNameQuery = {
              hash_name: image.fileName
            }

            const checkArray = await Promise.all([
              anhViTriModel.find(imageThumbnailQuery, { _id: 1 }, { limit: 1 }),
              userModel.find(userQuery, { _id: 1 }, { limit: 1 }),
              anhCongTacModel.find(imageThumbnailQuery, { _id: 1 }, { limit: 1 }),
              anhTonTaiModel.find(imageThumbnailQuery, { _id: 1 }, { limit: 1 }),
              hoSoThietBiModel.find(fileIdQuery, { _id: 1 }, { limit: 1 }),
              fileBienBanModel.find(fileIdQuery, { _id: 1 }, { limit: 1 }),
              phieuCongTacNgoaiModel.find(hashNameQuery, { _id: 1 }, { limit: 1 }),
              tonTaiCapTrenModel.find(fileIdQuery, { _id: 1 }, { limit: 1 }),
              tapTinModel.find(hashNameQuery, { _id: 1 }, { limit: 1 }),
              taiLieuModel.find(fileIdQuery, { _id: 1 }, { limit: 1 })
            ]);

            const checkExist = checkArray.some((exits) => exits.length);

            //update documnet with result of results of check
            if (!checkExist) {
              dataUpdate.need_to_delete = true;
            }
            dataUpdate.checked = true;
            await FileTrashModel.findOneAndUpdate({ _id: image._id }, dataUpdate);
            resolve();
          }).catch((e) => {
            countError++;
          });
        });

        await Promise.all(executeList);
      }
    }
    return responseHelper.success(res, { checkNumber: listImage.length, countError });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function deleteImageStorage(req, res) {
  try {

    //Get list image to delete
    const listImageToDelete = await FileTrashModel.find({
      need_to_delete: true,
      is_deleted: false
    }, { fileName: 1 });
    let countError = 0;
    let countDeleted = 0;

    const chunks = buildChunks(listImageToDelete, 1000);

    for (let i = 0; i < chunks.length; i++) {
      let executeList = [];
      let listFileTrashIdToDelete = [];
      chunks[i].forEach(async (image) => {
        //If exits image => delete
        const pathName = getFilePath(image.fileName);
        if (fs.existsSync(pathName)) {
          executeList.push(new Promise(async (resolve, reject) => {
            const error = await unlink(pathName);
            if (!error) {
              listFileTrashIdToDelete.push(image._id);
            }
            resolve();
          }).catch((err) => {
            countError++;
          }));
        }
      });

      await Promise.all(executeList);
      const count = await FileTrashModel.deleteMany({ _id: listFileTrashIdToDelete });
      countDeleted += count.deletedCount;
    }

    await FileTrashModel.deleteMany({ checked: true, need_to_delete: false });
    return responseHelper.success(res, {
      countNeedDelete: listImageToDelete.length,
      countError,
      countDeleted,
    });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

