import mongoose, { Schema } from 'mongoose';
import { FILE_TRASH } from '../../constant/dbCollections';

const schema = new Schema({
  fileName: { type: String, required: true, unique: true, index: true },
  checked: { type: Boolean, default: false },
  need_to_delete: { type: Boolean, default: false },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.index({ fileName: 1 });
export { schema as DocumentSchema };
export default mongoose.model(FILE_TRASH, schema, FILE_TRASH);
