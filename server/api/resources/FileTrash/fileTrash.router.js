import express from 'express';
import * as Controller from './fileTrash.controller';
import SystemAdminPermission from '../RBAC/permissions/SystemAdminPermission';
import { authorizationMiddleware } from '../RBAC/middleware';
import passport from 'passport';
import { loggerMiddleware } from '../../logs/middleware';

export const fileTrashRouter = express.Router();

fileTrashRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

fileTrashRouter.route('/inserttrash')
  .put(authorizationMiddleware([SystemAdminPermission.ALL_ALL]), Controller.insertImageTrash);

fileTrashRouter.route('/checkimage')
  .put(authorizationMiddleware([SystemAdminPermission.ALL_ALL]), Controller.checkImageTrash)

fileTrashRouter.route('/deleteimage')
  .put(authorizationMiddleware([SystemAdminPermission.ALL_ALL]), Controller.deleteImageStorage)




