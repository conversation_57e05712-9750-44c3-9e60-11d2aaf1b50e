import express from 'express';
import passport from 'passport';
import * as Controller from './nhomThietBiDungCu.controller';
import { authorizationMiddleware } from '../RBAC/middleware';
import DanhMucPermision from '../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../logs/middleware';

export const nhomThietBiDungCuRouter = express.Router();
nhomThietBiDungCuRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
nhomThietBiDungCuRouter.post('*', authorizationMiddleware([DanhMucPermision.CREATE]));
nhomThietBiDungCuRouter.put('*', authorizationMiddleware([DanhMucPermision.UPDATE]));
nhomThietBiDungCuRouter.delete('*', authorizationMiddleware([DanhMucPermision.DELETE]));
nhomThietBiDungCuRouter
  .route('/')
  .get(Controller.getAll)
  .post(Controller.create);

nhomThietBiDungCuRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
