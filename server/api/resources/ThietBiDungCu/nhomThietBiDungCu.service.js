import * as ValidatorHelper from '../../helpers/validatorHelper';
import THIETBI_DUNGCU from './nhomThietBiDungCu.model';

const Joi = require('joi');

const objSchema = Joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function getAll(query) {
  return THIETBI_DUNGCU.find(query).lean();
}
