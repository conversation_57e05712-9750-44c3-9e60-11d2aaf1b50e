import * as responseHelper from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from './soTheoDoi.service';
import Model from './soTheoDoi.model';
import * as DonViService from '../../DonVi/donVi.service';
import { generateDocument } from '../../Report/GenerateFile/generate.controller';
import { getFilePath } from '../../../utils/fileUtils';
import { TEMPLATES_DIRS } from '../../../constant/constant';

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id)
      .populate('thiet_bi_id don_vi_id');
    if (!data) {
      return responseHelper.error(res, 404, '');
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseHelper.error(res, 404, '');
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);


    if (value.ma_tai_san) {
      const isExist = await Model.findOne({ _id: { $ne: value._id }, ma_tai_san: value.ma_tai_san, is_deleted: false });
      if (isExist) {
        return responseHelper.error(res, { message: t('asset_code_code_existed') }, 400);
      }
    }

    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true })
      .populate('thiet_bi_id');
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function create(req, res) {

  try {
    const { t } = req;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    if (value.ma_tai_san) {
      const isExist = await Model.findOne({ ma_tai_san: value.ma_tai_san, is_deleted: false });
      if (isExist) {
        return responseHelper.error(res, { message: t('asset_code_code_existed') }, 400);
      }
    }

    const data = await Model.create(value);
    let dataRtn = await data.populate('thiet_bi_id').execPopulate();
    return responseHelper.success(res, dataRtn);
  } catch (err) {
    console.error(err);
    return responseHelper.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['serial']);
    const { criteria, options } = query;
    criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);
    options.sort = { don_vi_id: 1 };
    const data = await Model.paginate(criteria, options);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function downloadSoTheoDoi(req, res) {
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);
  const soTheoDoi = await Service.soTheoDoiThietBiDungCu(criteria);
  let data = {};
  data.so_theo_doi = soTheoDoi;
  const templateFilePath = getFilePath('so_theo_doi_thiet_bi_dung_cu.xlsx', TEMPLATES_DIRS.BIEU_MAU);
  const outputFileName = 'Sổ theo dõi thiết bị dụng cụ.xlsx';
  generateDocument(res, data, templateFilePath, outputFileName);
}
