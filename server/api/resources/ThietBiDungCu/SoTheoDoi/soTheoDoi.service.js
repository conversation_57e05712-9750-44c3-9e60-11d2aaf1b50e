import * as ValidatorHelper from '../../../helpers/validatorHelper';
import SO_THEO_DOI from './soTheoDoi.model';
import { groupBy } from '../../../common/functionCommons';
import { formatDate } from '../../../common/formatUTCDateToLocalDate';
import * as NhomThietBiDungCuService from '../nhomThietBiDungCu.service';

const Joi = require('joi');

const objSchema = Joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function getAll(query) {
  return SO_THEO_DOI.find(query).lean();
}


export async function soTheoDoiThietBiDungCu(criteria) {

  const allThietBi = await getAll(criteria)
    .populate('don_vi_id thiet_bi_id')
    .sort({ don_vi_id: 1 });

  function convertDataToRows(thietBi, index) {
    return {
      ten_don_vi: thietBi?.don_vi_id?.ten_don_vi,
      ten_thiet_bi: thietBi.thiet_bi_id?.ten_thiet_bi,
      ma_hieu_nha_san_xuat: thietBi?.ma_hieu_nha_san_xuat,
      so_the: thietBi?.so_the,
      ma_tai_san: thietBi.ma_tai_san,
      don_vi_tinh: thietBi.thiet_bi_id?.don_vi_tinh,
      nuoc_san_xuat: thietBi?.nuoc_san_xuat,
      ngay_dua_vao_su_dung: thietBi.ngay_dua_vao_su_dung ? formatDate(thietBi.ngay_dua_vao_su_dung) : '',
      thoi_han_kiem_tra_lan_dau: thietBi.thoi_han_kiem_tra_lan_dau ? formatDate(thietBi.thoi_han_kiem_tra_lan_dau) : '',
      thoi_han_kiem_tra_lan_ke_tiep: thietBi.thoi_han_kiem_tra_lan_ke_tiep ? formatDate(thietBi.thoi_han_kiem_tra_lan_ke_tiep) : '',
      ngay_sua_chua: thietBi.ngay_sua_chua ? formatDate(thietBi.ngay_sua_chua) : '',
      noi_dung_sua_chua: thietBi.noi_dung_sua_chua || '',
      ghi_chu: thietBi.ghi_chu || '',
      nhom_thiet_bi_id: thietBi.thiet_bi_id?.nhom_thiet_bi_id,
    };
  }

  const thietBiConverted = allThietBi.map(convertDataToRows);

  const groupSoTheoDoiByNhomId = groupBy(allThietBi.map(convertDataToRows), 'nhom_thiet_bi_id');
  const allNhomThietBi = await NhomThietBiDungCuService.getAll({ is_deleted: false });

  allNhomThietBi.forEach((item, index) => {
    item.stt = index + 1;
    item.thiet_bi = groupSoTheoDoiByNhomId[item._id] || [];
    return item;
  });
  return allNhomThietBi;
}
