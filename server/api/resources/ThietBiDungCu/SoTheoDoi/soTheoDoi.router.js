import express from 'express';
import passport from 'passport';
import * as Controller from './soTheoDoi.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/QuanLyThietBiPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const soTheoDoiThietBiRouter = express.Router();
soTheoDoiThietBiRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
soTheoDoiThietBiRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
soTheoDoiThietBiRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
soTheoDoiThietBiRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
soTheoDoiThietBiRouter
  .route('/')
  .get(Controller.getAll)
  .post(Controller.create);

soTheoDoiThietBiRouter
  .route('/download')
  .get(Controller.downloadSoTheoDoi);

soTheoDoiThietBiRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
