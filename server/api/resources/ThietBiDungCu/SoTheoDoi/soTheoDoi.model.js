import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CHITIET_THIETBI_DUNGCU, SO_THEO_DOI_THIET_BI, DON_VI } from '../../../constant/dbCollections';

const schema = new Schema({
  thiet_bi_id: { type: Schema.Types.ObjectId, ref: CHITIET_THIETBI_DUNGCU, required: true },
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI, required: true },
  so_the: { type: String },
  ma_hieu_nha_san_xuat: { type: String },
  ma_tai_san: { type: String },
  serial: { type: String },
  don_vi_tinh: { type: String },
  nuoc_san_xuat: { type: String },
  ngay_dua_vao_su_dung: { type: Date },
  tinh_trang: { type: String },
  thoi_han_kiem_tra_lan_dau: { type: Date },
  thoi_han_kiem_tra_lan_ke_tiep: { type: Date },
  ngay_sua_chua: { type: Date },
  noi_dung_sua_chua: { type: String },
  ghi_chu: { type: String },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(SO_THEO_DOI_THIET_BI, schema, SO_THEO_DOI_THIET_BI);

