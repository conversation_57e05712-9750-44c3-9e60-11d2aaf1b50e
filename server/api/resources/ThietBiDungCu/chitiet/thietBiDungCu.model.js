import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CHITIET_THIETBI_DUNGCU, THIETBI_DUNGCU } from '../../../constant/dbCollections';

const schema = new Schema({
  nhom_thiet_bi_id: { type: Schema.Types.ObjectId, ref: THIETBI_DUNGCU, required: true },
  ten_thiet_bi: { type: String, required: true, validate: /\S+/ },
  don_vi_tinh: { type: String },
  ghi_chu: { type: String },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(CHITIET_THIETBI_DUNGCU, schema, CHITIET_THIETBI_DUNGCU);

