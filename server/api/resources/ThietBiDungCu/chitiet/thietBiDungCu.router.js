import express from 'express';
import passport from 'passport';
import * as Controller from './thietBiDungCu.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const thietBiDungCuRouter = express.Router();
thietBiDungCuRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
thietBiDungCuRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
thietBiDungCuRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
thietBiDungCuRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
thietBiDungCuRouter
  .route('/')
  .get(Controller.getAll)
  .post(Controller.create);

thietBiDungCuRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
