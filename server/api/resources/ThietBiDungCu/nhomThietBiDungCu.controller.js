import * as responseHelper from '../../helpers/responseHelper';
import * as Service from './nhomThietBiDungCu.service';
import * as ChiTietService from './chitiet/thietBiDungCu.service';
import * as SoTheoDoiService from './SoTheoDoi/soTheoDoi.service';
import * as controllerHelper from '../../helpers/controllerHelper';
import Model from './nhomThietBiDungCu.model';
import ThietBiDungCuModel from './chitiet/thietBiDungCu.model';
import { groupBy } from '../../common/functionCommons';

const searchLike = ['nhom_thiet_bi'];
const populateOpts = [];

export const getAll = controllerHelper.createGetAllFunction(Model, searchLike, populateOpts);

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id).lean();
    if (!data) {
      return responseHelper.error(res, 404, '');
    }
    data.chitiet = await ChiTietService.getAll({ nhom_thiet_bi_id: id, is_deleted: false });
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true }).lean();
    if (!data) {
      return responseHelper.error(res, 404, '');
    } else {
      await ChiTietService.removeAll({ nhom_thiet_bi_id: data._id });
      data.chitiet = await ChiTietService.getAll({ nhom_thiet_bi_id: data._id });
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    const isExist = await Model.findOne({
      _id: { $ne: value._id },
      nhom_thiet_bi: value.nhom_thiet_bi,
      is_deleted: false,
    });
    if (isExist) {
      return responseHelper.error(res, { message: t('group_devices_existed') }, 400);
    }

    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true }).lean();
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    let chitietResponse = [];
    if (Array.isArray(value.chitiet)) {
      for (let i = 0; i < value.chitiet.length; i++) {
        const chitiet = value.chitiet[i];
        if (chitiet._id) {
          if (chitiet.delete) {
            const allSoTheoDoi = await SoTheoDoiService.getAll({ is_deleted: false });
            const groupByThietBiId = groupBy(allSoTheoDoi, 'thiet_bi_id');
            if (groupByThietBiId[chitiet._id]) {
              return responseHelper.error(res, { message: t('delete_device_tool_in_tracking_notebook') }, 400);
            }
            await ThietBiDungCuModel.findOneAndUpdate({ _id: chitiet._id }, { is_deleted: true });
          } else {
            const dataUpdated = await ThietBiDungCuModel.findOneAndUpdate({ _id: chitiet._id }, chitiet, { new: true }).lean();
            if (dataUpdated) {
              chitietResponse = [...chitietResponse, JSON.parse(JSON.stringify(dataUpdated))];
            }
          }
        } else {
          // tao moi khi khong co _id
          chitiet.nhom_thiet_bi_id = id;
          const dataCreated = await ThietBiDungCuModel.create(chitiet);
          if (dataCreated) {
            chitietResponse = [...chitietResponse, JSON.parse(JSON.stringify(dataCreated))];
          }
        }
      }
    }
    data.chitiet = chitietResponse;
    return responseHelper.success(res, data);
  } catch (err) {
    console.log('err', err);
    return responseHelper.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const { t } = req;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    const isExist = await Model.findOne({
      nhom_thiet_bi: value.nhom_thiet_bi,
      is_deleted: false,
    });
    if (isExist) {
      return responseHelper.error(res, { message: t('group_devices_existed') }, 400);
    }

    const data = await Model.create(value);

    if (Array.isArray(value.chitiet)) {
      let chitietResponse = [];
      for (let i = 0; i < value.chitiet.length; i++) {
        const chitiet = value.chitiet[i];
        chitiet.nhom_thiet_bi_id = data._id;
        const chitietCreated = await ThietBiDungCuModel.create(chitiet);
        if (chitietCreated) {
          chitietResponse = [...chitietResponse, JSON.parse(JSON.stringify(chitietCreated))];
        }
      }
      data.chitiet = chitietResponse;
    }

    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseHelper.error(res, err, 500);
  }
}
