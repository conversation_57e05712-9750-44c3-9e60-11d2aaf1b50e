import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { THIETBI_DUNGCU } from '../../constant/dbCollections';

const schema = new Schema({
  nhom_thiet_bi: { type: String, required: true, validate: /\S+/ },
  ghi_chu: { type: String },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(THIETBI_DUNGCU, schema, THIETBI_DUNGCU);
