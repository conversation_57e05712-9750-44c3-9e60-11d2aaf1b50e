import * as ValidatorHelper from '../../../helpers/validatorHelper';

import KET_QUA_DO_CUONG_DO_DIEN_TRUONG from './ketQuaDoKhoangCachGiaoCheo.model';

const Joi = require('joi');
const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return KET_QUA_DO_CUONG_DO_DIEN_TRUONG.create(value);
}

export function getAll(query, projection = {}) {
  return KET_QUA_DO_CUONG_DO_DIEN_TRUONG.find(query, projection).lean();
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await KET_QUA_DO_CUONG_DO_DIEN_TRUONG.findByIdAndUpdate(value._id, value);
  }
}

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};
