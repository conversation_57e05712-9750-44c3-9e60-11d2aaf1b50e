import express from 'express';
import passport from 'passport';

import * as Controller from './ketQuaDoKhoangCachGiaoCheo.controller';

import WorkPermission from '../../RBAC/permissions/WorkPermission';

import { authorizationMiddleware } from '../../RBAC/middleware';
import { loggerMiddleware } from '../../../logs/middleware';

export const ketQuaDoKhoangCachGiaoCheoRouter = express.Router();


ketQuaDoKhoangCachGiaoCheoRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

ketQuaDoKhoangCachGiaoCheoRouter.post('*', authorizationMiddleware([WorkPermission.CREATE]));
ketQuaDoKhoangCachGiaoCheoRouter.put('*', authorizationMiddleware([WorkPermission.UPDATE]));
ketQuaDoKhoangCachGiaoCheoRouter.delete('*', authorizationMiddleware([WorkPermission.DELETE]));

ketQuaDoKhoangCachGiaoCheoRouter
  .route('/phieugiaoviec/:id')
  .get(Controller.getByPhieuGiaoViec)
  .post(Controller.createPhieu);
