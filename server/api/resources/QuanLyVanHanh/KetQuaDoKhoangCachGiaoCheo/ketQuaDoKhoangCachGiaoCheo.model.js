import mongoose, { <PERSON>hem<PERSON> } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { KET_QUA_DO_KHOANG_CACH_GIAO_CHEO, KHOANG_COT, PHIEU_GIAO_VIEC, USER } from '../../../constant/dbCollections';

export const DOI_TUONG_GIAO_CHEO = {
  SONG: 'SONG',
  SUOI: 'SUOI',
  DUONG_DAN_SINH: 'DUONG_DAN_SINH',
  DUONG_BE_TONG: 'DUONG_BE_TONG',
  DUONG_LIEN_THON: 'DUONG_LIEN_THON',
  DUONG_DO_THI: 'DUONG_DO_THI',
  DUONG_QUOC_LO: 'DUONG_QUOC_LO',
  DUONG_CAO_TOC: 'DUONG_CAO_TOC',
  DUONG_DAY_THONG_TIN: 'DUONG_DAY_THONG_TIN',
  DUONG_DAY_220KV: 'DUONG_DAY_220KV',
  DUONG_DAY_110KV: 'DUONG_DAY_110KV',
  DUONG_DAY_35K: 'DUONG_DAY_35K',
};

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC, required: true },
  khoang_cot_id: { type: Schema.Types.ObjectId, ref: KHOANG_COT },
  doi_tuong_giao_cheo: {
    type: String,
    enum: Object.values(DOI_TUONG_GIAO_CHEO),
  },
  khoang_cach_giao_cheo_thap_nhat: { type: String },
  khoang_cach_den_vi_tri_gan_nhat: { type: String },
  dong_tai: { type: String },
  nhiet_do_moi_truong: { type: String },
  nguoi_do: { type: String },
  thoi_gian_do: { type: Date },
  ghi_chu: { type: String },

  nguoi_chinh_sua: { type: Schema.Types.ObjectId, ref: USER },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
    updatedAt: 'thoi_gian_cap_nhat',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(KET_QUA_DO_KHOANG_CACH_GIAO_CHEO, schema, KET_QUA_DO_KHOANG_CACH_GIAO_CHEO);
