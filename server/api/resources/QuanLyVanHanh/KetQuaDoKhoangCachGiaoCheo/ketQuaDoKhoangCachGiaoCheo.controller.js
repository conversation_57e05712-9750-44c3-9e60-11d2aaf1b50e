import * as responseHelper from '../../../helpers/responseHelper';

import Model from './ketQuaDoKhoangCachGiaoCheo.model';

export async function createPhieu(req, res) {
  try {
    const { id } = req.params;

    const arrData = req.body.map(ketQua => {
      ketQua.phieu_giao_viec_id = id;
      ketQua.nguoi_chinh_sua = req.user._id;
      ketQua.is_deleted = false;
      return ketQua;
    });

    const result = await Model.bulkWrite(
      arrData.map((row) => ({
        updateOne: {
          filter: {
            khoang_cot_id: row.khoang_cot_id,
            phieu_giao_viec_id: row.phieu_giao_viec_id,
          },
          update: { $set: row },
          upsert: true,
        },
      })),
    );

    if (!result.result.ok) {
      return responseHelper.error(res);
    }

    const ketQuaDo = await Model.find({ phieu_giao_viec_id: id, is_deleted: false }).lean();

    return responseHelper.success(res, ketQuaDo);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getByPhieuGiaoViec(req, res) {
  try {
    const { id } = req.params;
    const ketQuaDo = await Model.find({ phieu_giao_viec_id: id, is_deleted: false }).lean();


    return responseHelper.success(res, ketQuaDo);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}
