import momentTimezone from 'moment-timezone';

import * as ValidatorHelper from '../../../../helpers/validatorHelper';
import CongSuatPmisModel from './congSuatPmiss.model';

import * as DonViService from '../../../DonVi/donVi.service';
import * as DuongDayService from '../../../TongKe/DuongDay/duongDay.service';
import { getDataPmis, logSyncPmis } from '../../../TongKe/SyncPmis/syncPmis.service';

import { CAP_DON_VI } from '../../../../constant/constant';
import { extractIds, groupBy } from '../../../../utils/dataconverter';

import { cloneObj } from '../../../../common/functionCommons';
import PMIS_SOAP_NAME from '../../../TongKe/SyncPmis/soapName';
import { convertMoment, momentValid } from '../../../../helpers/checkDataHelper';
import createBaseService from '../../../../base/baseService';

momentTimezone.tz.setDefault('Asia/Ho_Chi_Minh');

const Joi = require('joi');

const objSchema = Joi.object({});

const baseService = createBaseService(CongSuatPmisModel);

export const createMulti = baseService.createMulti;
export const getAll = baseService.getAll;
export const getOne = baseService.getOne;
export const remove = baseService.remove;
export const distinctId = baseService.distinctId;
export const updateByQuery = baseService.updateByQuery;
export const updateByRows = baseService.updateByRows;
export const deleteAll = baseService.deleteAll;


export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function syncThongSoDuongDayPmis() {
  try {
    const timeNow = momentTimezone().tz('Etc/GMT-7').set({ minute: 0, second: 0 });
    const startTime = momentTimezone(timeNow).tz('Etc/GMT-7')
      .subtract(1, 'minute')
      .subtract(24, 'hours')
      .format('MM/DD/YYYY HH:mm:ss');
    const endTime = momentTimezone(timeNow).tz('Etc/GMT-7').add(1, 'minute').format('MM/DD/YYYY HH:mm:ss');
    
    // Tạo điều kiện thời gian để chỉ kiểm tra bản ghi được tạo trong vòng 7 ngày gần đây
    const sevenDaysAgo = momentTimezone().tz('Etc/GMT-7').subtract(7, 'days').toDate();
    
    const allCongTy = await DonViService.getAll({ cap_don_vi: CAP_DON_VI.CONG_TY, is_deleted: false });
    const allDuongDay = await DuongDayService.getAll({ don_vi_id: extractIds(allCongTy), is_deleted: false });

    const dataResponse = await dataPmiss(allDuongDay, startTime, endTime);
    return CongSuatPmisModel.bulkWrite(
      cloneObj(dataResponse).map((row) =>
        ({
          updateOne: {
            filter: { 
              asset_id: row.asset_id, 
              thoi_diem_thong_so: row.thoi_diem_thong_so,
              created_at: { $gte: sevenDaysAgo }
            },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
  } catch (e) {
    console.log('e', e);
    return await logSyncPmis('DUONG_DAY', null, e);
  }
}

export async function dataPmiss(allDuongDay = [], startTime, endTime) {
  const mapIDinhMuc = {}, mapTenDuongDay = {}, mapLoaiDuongDay = {};

  const duongDayList = allDuongDay.filter(duongDay => !!duongDay.asset_id);
  const congSuatData = [];

  for (let i = 0; i <= duongDayList.length; i += 20) {
    const promises = duongDayList.slice(i, i + 20).map(duongDay => {
      mapIDinhMuc[duongDay.asset_id] = duongDay.i_dinh_muc;
      mapLoaiDuongDay[duongDay.asset_id] = duongDay.loai_duong_day_id;
      mapTenDuongDay[duongDay.asset_id] = duongDay.ten_duong_day;

      const pmisId = duongDay.asset_id || duongDay.ma_duong_day;

      const queryPmiss = { ID: pmisId, TU_NGAY: startTime, DEN_NGAY: endTime };
      return getDataPmis(PMIS_SOAP_NAME.THONG_SO_BY_DUONG_DAY_ID, queryPmiss, 5000);
    });

    const dataPmis = await Promise.all(promises)
      .then(results => {
        return results
          .filter(result => result.success)
          .map(result => result.data).flat();
      });

    const resultGroupByAssetId = groupBy(dataPmis, 'ASSETID');
    Object.entries(resultGroupByAssetId).forEach(([key, value]) => {
      resultGroupByAssetId[key] = groupBy(value, 'THOI_GIAN');

      const resultGroupByThoiGian = groupBy(value, 'THOI_GIAN');
      Object.values(resultGroupByThoiGian).forEach(value => {
        const result = value.reduce((grouped, element) => {
          const thoiDiem = element.THOI_GIAN;
          grouped ||= {
            thoiDiem: element.THOI_GIAN,
            thoi_diem_thong_so: momentValid(thoiDiem) ? convertMoment(thoiDiem).toISOString() : null,
            asset_id: element.ASSETID,
            ten_duong_day: mapTenDuongDay[element.ASSETID],
            i_dinh_muc: mapIDinhMuc[element.ASSETID],
            loai_duong_day_id: mapLoaiDuongDay[element.ASSETID],
            thoi_gian_dong_bo: momentTimezone().tz('Etc/GMT-7'),
            u: '', i: '', p: '', q: '',
          };
          grouped[element.THONG_SO.toLowerCase()] = element.GIA_TRI;
          return grouped;
        }, null);
        congSuatData.push(result);
      });
    });
  }

  return congSuatData;
}
