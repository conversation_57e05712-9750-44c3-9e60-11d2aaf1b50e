import momentTimezone from 'moment-timezone';

import * as Service from './congSuatPmiss.service';

import CongSuatPmisModel from './congSuatPmiss.model';

import * as CongSuatPmisService from './congSuatPmiss.service';
import * as DuongDayService from '../../../TongKe/DuongDay/duongDay.service';
import { logSyncPmis } from '../../../TongKe/SyncPmis/syncPmis.service';
import * as DonViService from '../../../DonVi/donVi.service';

import * as controllerHelper from '../../../../helpers/controllerHelper';
import queryHelper from '../../../../helpers/queryHelper';
import * as responseHelper from '../../../../helpers/responseHelper';
import { floorMinutes, formatDatePmiss } from '../../../../common/formatUTCDateToLocalDate';
import { convertObject, extractKeys, groupBy } from '../../../../utils/dataconverter';
import { cloneObj } from '../../../../common/functionCommons';
import { momentValid } from '../../../../helpers/checkDataHelper';
import CommonError from '../../../../error/CommonError';

const searchLike = [];
const populateOpts = [];
const uniqueOpts = [];
const sortGetALl = { created_at: -1 };
const pupulateOptsGetAll = [];

export const findOne = controllerHelper.createFindOneFunction(CongSuatPmisModel, populateOpts);
export const remove = controllerHelper.createRemoveFunction(CongSuatPmisModel);
export const update = controllerHelper.createUpdateByIdFunction(CongSuatPmisModel, Service, populateOpts, uniqueOpts);
export const create = controllerHelper.createCreateFunction(CongSuatPmisModel, Service, populateOpts, uniqueOpts);


export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);

    const allDuongDay = await DuongDayService.getAll(
      { don_vi_id: criteria.don_vi_id, is_deleted: false },
      { ten_duong_day: 1, asset_id: 1, loai_duong_day_id: 1 },
    );

    const assetId = extractKeys(allDuongDay, 'asset_id').filter(Boolean);
    const congSuatData = await CongSuatPmisService.getAll(
      {
        $or: [
          { thoi_diem_thong_so: floorMinutes(criteria.thoi_diem_thong_so).toISOString() },
          { thoi_diem_thong_so: formatDatePmiss(floorMinutes(criteria.thoi_diem_thong_so)) },
        ],
        asset_id: assetId,
      },
      { asset_id: 1, thoi_diem_thong_so: 1, thoi_gian_dong_bo: 1, u: 1, i: 1, p: 1, q: 1 },
    );
    const congSuatByAssetId = convertObject(congSuatData, 'asset_id');
    const dataReturn = allDuongDay.map(dz => {

      const congSuat = congSuatByAssetId[dz.asset_id];
      return Object.assign({}, dz, congSuat);
    });

    return responseHelper.success(res, dataReturn);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function syncCongSuatThoiDiem(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;

    if (!momentValid(criteria.thoi_diem)) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }


    const allDonViInScope = await DonViService.getDonViInScope(criteria.don_vi_id);
    const timeNow = momentTimezone(criteria.thoi_diem).tz('Etc/GMT-7').set({ minute: 0, second: 0 });
    const startTime = momentTimezone(timeNow).tz('Etc/GMT-7')
      .subtract(1, 'minute')
      .subtract(24, 'hours')
      .format('MM/DD/YYYY HH:mm:ss');
    const endTime = momentTimezone(timeNow).tz('Etc/GMT-7').add(1, 'minute').format('MM/DD/YYYY HH:mm:ss');
    const allDuongDay = await DuongDayService.getAll({ don_vi_id: { $in: allDonViInScope }, is_deleted: false });

    const dataResponse = await Service.dataPmiss(allDuongDay, startTime, endTime);

    const thoiDiemThongSo = floorMinutes(criteria.thoi_diem).toISOString();
    const dataUpdate = cloneObj(dataResponse).filter(item => item.thoi_diem_thong_so === thoiDiemThongSo);
    const result = await CongSuatPmisModel.bulkWrite(
      dataUpdate.map((row) =>
        ({
          updateOne: {
            filter: { asset_id: row.asset_id, thoi_diem_thong_so: row.thoi_diem_thong_so },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );

    return responseHelper.success(res, dataUpdate);

  } catch (e) {
    console.log('e', e);
    return await logSyncPmis('DUONG_DAY', null, e);
  }
}
