import express from 'express';
import passport from 'passport';
import * as Controller from './congSuatPmiss.controller';

export const congSuatPmissRouter = express.Router();

congSuatPmissRouter.use(passport.authenticate('jwt', { session: false }));
congSuatPmissRouter
  .route('/')
  .get(Controller.getAll)
  .post(Controller.create);

congSuatPmissRouter
  .route('/thongsothoidiem')
  .get(Controller.syncCongSuatThoiDiem);

congSuatPmissRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);


