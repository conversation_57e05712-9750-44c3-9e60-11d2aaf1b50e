import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CONG_SUAT_PMISS, LOAI_DUONG_DAY } from '../../../../constant/dbCollections';

const schema = new Schema({
  thoi_diem_thong_so: String,
  thoi_gian_dong_bo: Date,
  asset_id: String,
  ma_duong_day: String,
  ten_duong_day: String,
  loai_duong_day_id: { type: Schema.Types.ObjectId, ref: LOAI_DUONG_DAY  },
  i: String,
  p: String,
  q: String,
  u: String,
  i_dinh_muc: String,

  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

// Optimized indexes for CongSuatPmiss queries
// Primary compound index for upsert operations (asset_id + thoi_diem_thong_so)
// Using background: true and sparse: true for better performance during index creation
schema.index({ 
  asset_id: 1, 
  thoi_diem_thong_so: 1 
}, { 
  background: true, 
  sparse: true,
  name: 'idx_asset_thoi_diem'
});

// Index for time-based queries and sorting - most frequently used
schema.index({ 
  thoi_diem_thong_so: 1, 
  thoi_gian_dong_bo: -1 
}, { 
  background: true,
  name: 'idx_time_based_queries'
});

// Index for soft delete with time filtering - high priority for data integrity
schema.index({ 
  is_deleted: 1, 
  thoi_diem_thong_so: 1,
  asset_id: 1
}, { 
  background: true,
  name: 'idx_soft_delete_time_asset'
});

// Index for duong_day queries with time
schema.index({ 
  ma_duong_day: 1, 
  thoi_diem_thong_so: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_duong_day_time'
});

// Index for loai_duong_day_id filtering with time
schema.index({ 
  loai_duong_day_id: 1, 
  thoi_diem_thong_so: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_loai_duong_day_time'
});

// Additional index for asset-based queries without time constraint
schema.index({ 
  asset_id: 1,
  is_deleted: 1,
  thoi_gian_dong_bo: -1
}, { 
  background: true,
  name: 'idx_asset_queries'
});

// Index for bulk operations and data synchronization
schema.index({ 
  thoi_gian_dong_bo: -1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_sync_operations'
});

// Index for upsert operations with time constraint (7 days filter)
// Using descending order for time fields to search from newest records first
schema.index({
  asset_id: 1,
  thoi_diem_thong_so: -1,
  created_at: -1
}, {
  background: true,
  name: 'idx_upsert_time_constraint'
});

schema.index({
  asset_id: 1,
  thoi_diem_thong_so: -1,
}, {
  background: true,
  name: 'idx_asset_thoi_diem_2',
});
export { schema as DocumentSchema };
export default mongoose.model(CONG_SUAT_PMISS, schema, CONG_SUAT_PMISS);
