import * as Service from './traoLuuCongSuat.service';
import * as TraoLuuCongSuatService from './traoLuuCongSuat.service';
import Model from './traoLuuCongSuat.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import queryHelper from '../../../helpers/queryHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import { floorMinutes } from '../../../common/formatUTCDateToLocalDate';
import * as DuongDayService from '../../TongKe/DuongDay/duongDay.service';
import moment from 'moment';
import PMIS_SOAP_NAME from '../../TongKe/SyncPmis/soapName';
import momentTimezone from 'moment-timezone';
import DUONG_DAY from '../../../constant/dbCollections';
import { cloneObj } from '../../../common/functionCommons';
import { getDataPmis, logSyncPmis } from '../../TongKe/SyncPmis/syncPmis.service';

const searchLike = [];
const populateOpts = [];
const uniqueOpts = [];
const sortGetALl = { created_at: -1 };
const pupulateOptsGetAll = [];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);


export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_phieu_mau']);
    const { criteria, options } = query;
    criteria.thoi_diem_thong_so.setTime(criteria.thoi_diem_thong_so.getTime() + 7 * 60 * 60 * 1000);
    const data = await Model.find({ thoi_diem_thong_so: { $eq: floorMinutes(criteria.thoi_diem_thong_so) } })
      .sort({ thoi_diem_thong_so: -1 }).limit(1).lean();
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function syncThongSoDuongDayPmis() {
  try {
    const allDuongDay = await DuongDayService.getAll({ is_deleted: false }, { ma_duong_day: 1, asset_id: 1 });
    const timeNow = moment().set({ minute: 0, second: 0 });
    const startTime = moment(timeNow).subtract(1, 'minute').format('MM/DD/YYYY HH:mm:ss');
    const endTime = moment(timeNow).add(1, 'minute').format('MM/DD/YYYY HH:mm:ss');

    const promises = allDuongDay.map(duongDay => {
      // const query = { ID: duongDay.asset_id, TU_NGAY: startTime, DEN_NGAY: endTime };
      const query = { THOI_GIAN: duongDay.asset_id, TU_NGAY: startTime, DEN_NGAY: endTime };
      return getDataPmis(PMIS_SOAP_NAME.THONG_SO_BY_DUONG_DAY_ID, query, 10000);
    });

    const dataPmis = await Promise.all(promises).then(results => {
      results = results.filter(result => !!result?.length);
      return results.map(result => {
        return result.reduce((prevValue, currentValue) => {
          if (currentValue) {
            prevValue.asset_id = currentValue.ASSETID;
            prevValue.thoi_diem_thong_so = prevValue.thoi_diem_thong_so || currentValue.THOI_GIAN;
            prevValue.thoi_gian_dong_bo = momentTimezone().tz('Etc/GMT-7');
            prevValue[currentValue.THONG_SO?.toLowerCase()] = currentValue.GIA_TRI;
          }
          return prevValue;
        }, null);
      });
    });
    if (!dataPmis.length) {
      await DUONG_DAY.bulkWrite(
        allDuongDay.map((row) =>
          ({
            updateOne: {
              filter: { _id: row?._id },
              update: { $set: { thoi_gian_dong_bo: momentTimezone().tz('Etc/GMT-7') } },
              upsert: false,
            },
          }),
        ),
      );
      await TraoLuuCongSuatService.updateTraoLuuCongSuat();
    } else {
      await DUONG_DAY.bulkWrite(
        cloneObj(dataPmis).map((row) =>
          ({
            updateOne: {
              filter: { asset_id: row.asset_id },
              update: { $set: row },
              upsert: false,
            },
          }),
        ),
      );

      await TraoLuuCongSuatService.updateTraoLuuCongSuat();
    }

  } catch (e) {
    console.log('e', e);
    return await logSyncPmis('DUONG_DAY', null, e);
  }
}
