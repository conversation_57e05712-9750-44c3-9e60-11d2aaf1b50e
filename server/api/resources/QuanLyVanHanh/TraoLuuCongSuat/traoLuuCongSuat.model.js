import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { TRAO_LUU_CONG_SUAT } from '../../../constant/dbCollections';

const schema = new Schema({
  thoi_diem_thong_so: Date,
  data_thoi_diem_thong_so: { type: Schema.Types.Mixed },

  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(TRAO_LUU_CONG_SUAT, schema, TRAO_LUU_CONG_SUAT);
