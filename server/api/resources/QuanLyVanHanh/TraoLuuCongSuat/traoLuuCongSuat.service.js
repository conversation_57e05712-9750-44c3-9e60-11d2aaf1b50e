import * as ValidatorHelper from '../../../helpers/validatorHelper';
import TRAO_LUU_CONG_SUAT from './traoLuuCongSuat.model';
import * as DonViService from '../../DonVi/donVi.service';
import { CAP_DON_VI } from '../../../constant/constant';
import * as DuongDayService from '../../TongKe/DuongDay/duongDay.service';
import { extractIds, groupByMultiKey } from '../../../utils/dataconverter';
import DUONG_DAY from '../../TongKe/DuongDay/duongDay.model';
import { floorMinutes } from '../../../common/formatUTCDateToLocalDate';

const Joi = require('joi');

const objSchema = Joi.object({});


export function getAll(query) {
  return TRAO_LUU_CONG_SUAT.find(query).lean();
}

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function updateTraoLuuCongSuat() {
  const allCongTy = await DonViService.getAll({ cap_don_vi: CAP_DON_VI.CONG_TY, is_deleted: false });

  const duongDayDongBoGanNhat = await DuongDayService.getAll({ don_vi_id: { $in: extractIds(allCongTy) } })
    .sort({ thoi_diem_thong_so: -1 }).limit(1);
  const thoiDiemThongSo = duongDayDongBoGanNhat[0]?.thoi_diem_thong_so;

  const allDuongDayCapCongTy = await DuongDayService.getAll(
    { thoi_diem_thong_so: thoiDiemThongSo, don_vi_id: { $in: extractIds(allCongTy) } },
    {
      ma_duong_day: 1,
      asset_id: 1,
      thoi_diem_thong_so: 1,
      ten_duong_day: 1,
      i: 1, p: 1, q: 1, u: 1,
      i_dinh_muc: 1,
      thoi_gian_dong_bo: 1,
      don_vi_id: 1,
      loai_duong_day_id: 1,
    },
  );
  const momentThoiDiem = new Date(thoiDiemThongSo);
  const existThoiDiem = await TRAO_LUU_CONG_SUAT.findOne({ thoi_diem_thong_so: momentThoiDiem });

  if (!existThoiDiem) {
    const data = { thoi_diem_thong_so: momentThoiDiem, data_thoi_diem_thong_so: allDuongDayCapCongTy };
    await TRAO_LUU_CONG_SUAT.create(data);
  } else {
    await TRAO_LUU_CONG_SUAT.findOneAndUpdate({ thoi_diem_thong_so: momentThoiDiem },
      { data_thoi_diem_thong_so: allDuongDayCapCongTy });
  }

}

export async function updateOneTraoLuuCongSuat(assetId) {
  const objDuongDay = await DUONG_DAY.findOne({ asset_id: assetId, is_deleted: false }).lean();
  if (!objDuongDay?.thoi_diem_thong_so) return;
  const momentThoiDiem = new Date(objDuongDay?.thoi_diem_thong_so);
  const existThoiDiem = await TRAO_LUU_CONG_SUAT.findOne({ thoi_diem_thong_so: momentThoiDiem }).lean();
  if (!existThoiDiem) {
    //Nếu chưa có report nào tại thời điểm thông số => tạo mới
    const data = { thoi_diem_thong_so: momentThoiDiem, data_thoi_diem_thong_so: [objDuongDay] };
    await TRAO_LUU_CONG_SUAT.create(data);
  } else {
    //Lọc dữ liệu thời điểm thông số không chứa đường dây mới đồng bộ
    let allDuongDayKhongDongBo = [];
    if (Array.isArray(existThoiDiem?.data_thoi_diem_thong_so)) {
      allDuongDayKhongDongBo = existThoiDiem?.data_thoi_diem_thong_so.filter(item => item?.asset_id !== objDuongDay?.asset_id);
    }
    const newDataDuongDayThoiDiemDongBo = [...allDuongDayKhongDongBo, objDuongDay];
    await TRAO_LUU_CONG_SUAT.findOneAndUpdate({ thoi_diem_thong_so: momentThoiDiem },
      { data_thoi_diem_thong_so: newDataDuongDayThoiDiemDongBo });
  }
}


export async function getDataTraoLuuForReport(criteria) {
  criteria.thoi_diem_thong_so.setTime(criteria.thoi_diem_thong_so.getTime() + 7 * 60 * 60 * 1000);

  const dataTraoLuu = await TRAO_LUU_CONG_SUAT.find({ thoi_diem_thong_so: { $eq: floorMinutes(criteria.thoi_diem_thong_so) } })
    .sort({ thoi_diem_thong_so: -1 }).limit(1).lean();

  const groupTraoLuu = groupByMultiKey(dataTraoLuu[0]?.data_thoi_diem_thong_so, ['loai_duong_day_id', 'don_vi_id']);
  return groupTraoLuu[criteria.loai_duong_day_id + criteria.don_vi_id];
}
