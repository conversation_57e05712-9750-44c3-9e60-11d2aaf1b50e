import express from 'express';
import passport from 'passport';
import * as Controller from './traoLuuCongSuat.controller';

export const traoLuuCongSuatRouter = express.Router();

traoLuuCongSuatRouter.use(passport.authenticate('jwt', { session: false }));
traoLuuCongSuatRouter
  .route('/')
  .get(Controller.getAll)
  .post(Controller.create);

traoLuuCongSuatRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);


