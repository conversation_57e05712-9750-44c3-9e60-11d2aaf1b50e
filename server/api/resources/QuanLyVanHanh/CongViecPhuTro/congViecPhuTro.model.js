import mongoose, { Schema } from 'mongoose';
import { CONG_VIEC_PHU_TRO, DANH_MUC_CONG_VIEC_PHU_TRO, PHIEU_GIAO_VIEC } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';
import { TRANG_THAI_THUC_HIEN } from '../../DanhMuc/TrangThaiXuLy';

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  cong_viec_id: { type: Schema.Types.ObjectId, ref: DANH_MUC_CONG_VIEC_PHU_TRO },
  khoi_luong: String,
  tinh_trang_thuc_hien: {
    type: String,
    enum: Object.keys(TRANG_THAI_THUC_HIEN),
    default: TRANG_THAI_THUC_HIEN.CHUA_THUC_HIEN.code,
  },
  ghi_chu: String,
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
// Optimized indexes for CongViecPhuTro queries
// Index for phieu_giao_viec_id queries with is_deleted filter
schema.index({ 
  phieu_giao_viec_id: 1, 
  is_deleted: 1 
}, { 
  background: true,
  name: 'idx_phieu_giao_viec_deleted'
});

// Index for cong_viec_id queries with is_deleted filter
schema.index({ 
  cong_viec_id: 1, 
  is_deleted: 1 
}, { 
  background: true,
  name: 'idx_cong_viec_deleted'
});

// Index for tinh_trang_thuc_hien filtering
schema.index({ 
  tinh_trang_thuc_hien: 1, 
  phieu_giao_viec_id: 1,
  is_deleted: 1 
}, { 
  background: true,
  name: 'idx_tinh_trang_phieu_deleted'
});

// Index for created_at with is_deleted for time-based queries
schema.index({ 
  created_at: 1, 
  is_deleted: 1 
}, { 
  background: true,
  name: 'idx_created_deleted'
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(CONG_VIEC_PHU_TRO, schema, CONG_VIEC_PHU_TRO);
