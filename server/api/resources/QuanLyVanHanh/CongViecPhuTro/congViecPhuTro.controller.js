import * as Service from './congViecPhuTro.service';
import Model from './congViecPhuTro.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import queryHelper from '../../../helpers/queryHelper';
import * as responseHelper from '../../../helpers/responseHelper';

const populateOpts = [
  { path: 'cong_viec_id' },
];

export const findOne = controllerHelper.createFindOneFunction(Model);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service);
export const create = controllerHelper.createCreateFunction(Model, Service);

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const data = await Model.find(criteria)
      .populate(populateOpts);

    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function updateMany(req, res) {
  try {
    const { body } = req;
    if (!Array.isArray(body)) return;
    let promiseArr = [], notValid = null;
    for (let i = 0; i < body.length; i++) {
      const { error, value } = Service.validate(body[i]);
      if (error) {
        notValid = error;
        break;
      }
      promiseArr.push(Model.findByIdAndUpdate(value._id, value));
    }
    if (notValid) return responseHelper.error(res, notValid, 400);
    await Promise.all(promiseArr);

    const data = await Model.find({ _id: body.map(x => x._id) }).populate(populateOpts);
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}
