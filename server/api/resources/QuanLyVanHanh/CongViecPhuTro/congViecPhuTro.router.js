import express from 'express';
import passport from 'passport';
import * as noiDungCongViecController from './congViecPhuTro.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import GiaoViecPermission from '../../RBAC/permissions/GiaoViecPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const congViecPhuTroRouter = express.Router();
congViecPhuTroRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
congViecPhuTroRouter.post('*', authorizationMiddleware([GiaoViecPermission.CREATE]));
congViecPhuTroRouter.put('*', authorizationMiddleware([GiaoViecPermission.UPDATE]));
congViecPhuTroRouter.delete('*', authorizationMiddleware([GiaoViecPermission.DELETE]));
congViecPhuTroRouter.route('/')
  .get(noiDungCongViecController.getAll)
  .post(noiDungCongViecController.create)
  .put(noiDungCongViecController.updateMany);

congViecPhuTroRouter
  .route('/:id')
  .get(noiDungCongViecController.findOne)
  .delete(noiDungCongViecController.remove)
  .put(noiDungCongViecController.update);
