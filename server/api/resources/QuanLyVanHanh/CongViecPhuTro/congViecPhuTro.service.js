import * as ValidatorHelper from '../../../helpers/validatorHelper';
import CONG_VIEC_PHU_TRO from './congViecPhuTro.model';

const Joi = require('joi');

const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return CONG_VIEC_PHU_TRO.create(value);
}

export function getAll(query, projection = {}) {
  return CONG_VIEC_PHU_TRO.find(query, projection).lean();
}

export function getOne(query, projection = {}) {
  return CONG_VIEC_PHU_TRO.findOne(query, projection).lean();
}

export async function updateAll(datUpdate) {
  for (const row of datUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await CONG_VIEC_PHU_TRO.findByIdAndUpdate(value._id, value);
  }
}

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  if (Array.isArray(data)) {
    let validateError = null;
    data.find(itemData => {
      const { value, error } = schema.validate(itemData, { allowUnknown: true, abortEarly: true });
      if (error) validateError = error;
      return error;
    });
    if (validateError && validateError.details) {
      return { validateError };
    }
    return { value: data };
  } else {
    const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
    if (error && error.details) {
      return { error };
    }
    return { value };
  }
}
