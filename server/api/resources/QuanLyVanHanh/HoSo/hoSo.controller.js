import * as responseAction from '../../../helpers/responseHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from './hoSo.service';
import { getAllHoSoExpandInScope } from './hoSo.service';
import Model from './hoSo.model';
import { createDataTree } from '../../../common/DataStructureHelper';
import { CAP_HO_SO } from '../../../constant/constant';

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id);
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const parentFile = await Model.findOne({ ho_so_id: id, is_deleted: false });
    if (parentFile) {
      return responseAction.error(res, { message: t('delete_profile_child_before_delete_profile_parent') }, 400);
    }
    const data = await Model.findOneAndUpdate({ _id: id, is_root: false }, { is_deleted: true }, { new: true });
    if (data.ho_so_id) {
      await Service.deleteIndex(data.ho_so_id);
    } else {
      await Service.deleteIndex();
    }
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    return responseAction.success(res, data);

  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    let query;
    if (value.ho_so_id) {
      query = { ho_so_id: value.ho_so_id, is_deleted: false, _id: { $ne: id } };
    } else {
      query = { cap_ho_so: CAP_HO_SO.CAP_1, is_deleted: false, _id: { $ne: id } };
    }
    const allBeforeInsert = await Service.getAll(query);
    const index = (value.thu_tu && value.thu_tu <= allBeforeInsert.length) ? value.thu_tu - 1 : allBeforeInsert.length;
    await Service.updateIndex(allBeforeInsert, index, value);
    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true })
      .populate({ path: 'ho_so_id', select: 'ten_ho_so' });
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function create(req, res) {

  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const dataAfterReplace = await Service.replaceThuTu(value);
    const data = await Model.create(dataAfterReplace);
    let dataRtn = await data
      .populate({ path: 'ho_so_id', select: 'ten_ho_so' }).execPopulate();
    return responseAction.success(res, dataRtn);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAllTree(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_ho_so']);
    const { criteria } = query;
    let doiTuongId = undefined;
    if (criteria.doi_tuong_id) {
      doiTuongId = criteria.doi_tuong_id;
      delete criteria.doi_tuong_id;
    }
    let allHoSo = await Model.find(criteria).lean();
    await Service.checkHavingFile(allHoSo, doiTuongId);
    if (req.query.tree) {
      allHoSo = await getAllHoSoExpandInScope(allHoSo);
    }
    const allHoSoTree = createDataTree(allHoSo, '_id', 'ho_so_id');
    responseHelper.success(res, allHoSoTree);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function getAllHoSo(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_ho_so']);
    const { criteria, options } = query;

    options.populate = [
      { path: 'ho_so_id', select: 'ten_ho_so' },
    ];
    options.sort = { ten_ho_so: 1 };
    const data = await Model.paginate(criteria, options);
    responseAction.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}
