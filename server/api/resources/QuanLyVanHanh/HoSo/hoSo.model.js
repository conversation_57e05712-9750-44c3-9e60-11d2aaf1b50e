import mongoose, { Schema } from 'mongoose';
import { HO_SO } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';
import { CAP_HO_SO } from '../../../constant/constant';

const schema = new Schema({
  ten_ho_so: { type: String, required: true, validate: /\S+/ },
  doi_tuong: [{ type: String, required: true }],
  cap_ho_so: {
    type: String,
    enum: Object.values(CAP_HO_SO),
    required: true,
  },
  ho_so_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: HO_SO,
  },
  thu_tu: { type: Number },
  is_ho_so_vi_tri: { type: Boolean, default: false },
  ten_thiet_bi: { type: String },
  is_root: { type: Boolean, default: false, select: false },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(HO_SO, schema, HO_SO);

