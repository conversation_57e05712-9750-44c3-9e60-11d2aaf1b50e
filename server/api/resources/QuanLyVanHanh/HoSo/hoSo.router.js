import express from 'express';
import passport from 'passport';
import * as hosoController from './hoSo.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import HoSoPermission from '../../RBAC/permissions/HoSoPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const hoSoRouter = express.Router();
hoSoRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
hoSoRouter.post('*', authorizationMiddleware([HoSoPermission.CREATE]));
hoSoRouter.put('*', authorizationMiddleware([HoSoPermission.UPDATE]));
hoSoRouter.delete('*', authorizationMiddleware([HoSoPermission.DELETE]));
hoSoRouter
  .route('/')
  .get(hosoController.getAllHoSo)
  .post(hosoController.create);

hoSoRouter
  .route('/tree')
  .get(hosoController.getAllTree);

hoSoRouter
  .route('/:id')
  .get(hosoController.findOne)
  .delete(hosoController.remove)
  .put(hosoController.update);
