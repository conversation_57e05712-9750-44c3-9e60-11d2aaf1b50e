import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { HO_SO, LINK_FILE, CONG_TRINH, VI_TRI } from '../../../../constant/dbCollections';

const schema = new Schema({
  ho_so_id: { type: Schema.Types.ObjectId, ref: HO_SO },
  doi_tuong_id: { type: Schema.Types.ObjectId, ref: CONG_TRINH },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  link_file: { type: String },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(LINK_FILE, schema, LINK_FILE);
