import express from 'express';
import passport from 'passport';
import * as linkFileController from './linkFile.controller';
import { authorizationMiddleware } from '../../../RBAC/middleware';
import HoSoPermission from '../../../RBAC/permissions/HoSoPermission';
import { loggerMiddleware } from '../../../../logs/middleware';

export const linkFileRouter = express.Router();
linkFileRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
linkFileRouter.post('*', authorizationMiddleware([HoSoPermission.CREATE]));
linkFileRouter.put('*', authorizationMiddleware([HoSoPermission.UPDATE]));
linkFileRouter.delete('*', authorizationMiddleware([HoSoPermission.DELETE]));
linkFileRouter
  .route('/')
  .post(linkFileController.create);

linkFileRouter
  .route('/')
  .get(linkFileController.getAll);

linkFileRouter
  .route('/:id')
  .get(linkFileController.findOne)
  .delete(linkFileController.remove)
  .put(linkFileController.update);
