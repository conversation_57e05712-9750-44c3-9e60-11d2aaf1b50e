import * as ValidatorHelper from '../../../../helpers/validatorHelper';
import LinkFileModel from './linkFile.model';
import createBaseService from '../../../../base/baseService';


const baseService = createBaseService(LinkFileModel);

export const createMulti = baseService.createMulti;
export const getAll = baseService.getAll;
export const getOne = baseService.getOne;
export const remove = baseService.remove;
export const distinctId = baseService.distinctId;
export const updateByQuery = baseService.updateByQuery;
export const updateByRows = baseService.updateByRows;
export const deleteAll = baseService.deleteAll;
export const updateAll = baseService.updateAll;

const Joi = require('joi');

const objSchema = Joi.object({
  ten_don_vi: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên đơn vị')),
  ma_don_vi: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã đơn vị')),
  don_vi_cha_id: Joi.string().messages(ValidatorHelper.messageDefine('Đơn vị cha')),
  duong_day_nong: Joi.string(),
  email: Joi.string(),
  fax: Joi.string(),
  dia_chi: Joi.string(),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}


