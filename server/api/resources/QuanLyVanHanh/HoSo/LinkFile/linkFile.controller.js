import * as Service from './linkFile.service';
import Model from './linkFile.model';
import * as controllerHelper from '../../../../helpers/controllerHelper';

const populateOpts = [
  { path: 'ho_so_id',  },
  { path: 'doi_tuong_id' },
];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, null, populateOpts);

