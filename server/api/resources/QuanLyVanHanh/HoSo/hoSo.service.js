import * as ValidatorHelper from '../../../helpers/validatorHelper';
import Model from './hoSo.model';
import * as TaiLieuService from '../../TaiLieu/taiLieu.service';
import * as LinkFileService from '../HoSo/LinkFile/linkFile.service';
import { CAP_HO_SO } from '../../../constant/constant';
import { insertObjToArr } from '../../../common/functionCommons';

function sortThuTu(a, b) {
  return a.thu_tu - b.thu_tu;
}

export function getAll(query) {
  return Model.find(query).lean();
}

export async function getAllHoSoExpandInScope(currentHoSoId) {
  const currentHoSo = await Model.findById(currentHoSoId);
  if (!currentHoSo) return [];
  let parentIs = [currentHoSo._id.toString()];
  let hoSoSet = new Set([...parentIs]);
  while (parentIs.length) {
    const children = await Model.find({ is_deleted: false, ho_so_id: { $in: parentIs } }).lean();
    parentIs = children.map(child => child._id.toString());
    hoSoSet = new Set([...hoSoSet, ...parentIs]);
  }
  return [...hoSoSet.values()];
}

export async function checkHavingFile(allHoSo, doiTuongId) {
  const allTaiLieu = await TaiLieuService.getAll({
    doi_tuong_id: doiTuongId,
    is_deleted: false,
  });
  const allLinkFile = await LinkFileService.getAll({
    doi_tuong_id: doiTuongId,
    is_deleted: false,
  });
  const mapHoSoId = {}, mapLinkFile = {};
  allTaiLieu.forEach(taiLieu => {
    mapHoSoId[taiLieu.ho_so_id] = taiLieu;
  });
  allLinkFile.forEach(link => {
    mapLinkFile[link.ho_so_id] = link;
  });

  allHoSo.forEach(hoSo => {
    if (mapLinkFile[hoSo._id]?.link_file) {
      hoSo.link_file = mapLinkFile[hoSo._id]?.link_file;
    }
    if (mapHoSoId[hoSo._id]?.file_id) {
      hoSo.file_id = mapHoSoId[hoSo._id]?.file_id;
    }
  });
}

const Joi = require('joi');

const objSchema = Joi.object({
  ten_don_vi: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên đơn vị')),
  ma_don_vi: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã đơn vị')),
  don_vi_cha_id: Joi.string().messages(ValidatorHelper.messageDefine('Đơn vị cha')),
  duong_day_nong: Joi.string(),
  email: Joi.string(),
  fax: Joi.string(),
  dia_chi: Joi.string(),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function updateAll(arrData) {
  await Model.bulkWrite(
    arrData.map((row) =>
      ({
        updateOne: {
          filter: { _id: row._id },
          update: { $set: row },
          upsert: false,
        },
      }),
    ),
  );
}

export async function createIndex(allData, index, data) {
  allData.sort(sortThuTu);
  const allHoSoAfterInsert = insertObjToArr(allData, data, index);
  allHoSoAfterInsert.forEach((hoSo, i) => {
    hoSo.thu_tu = i + 1;
  });
  const dataUpdate = allHoSoAfterInsert.filter(item => item._id);
  await updateAll(dataUpdate);
  return allHoSoAfterInsert.filter(item => !item._id)[0];
}

export async function updateIndex(allData, index, data) {
  allData.sort(sortThuTu);
  const allHoSoAfterInsert = insertObjToArr(allData, data, index);
  allHoSoAfterInsert.forEach((hoSo, i) => {
    hoSo.thu_tu = i + 1;
  });
  await updateAll(allHoSoAfterInsert);
}

export async function deleteIndex(hoSoId) {
  let allHoSoUpdate;
  if (hoSoId) {
    allHoSoUpdate = await Model.find({ ho_so_id: hoSoId, is_deleted: false }).lean();
  } else {
    allHoSoUpdate = await Model.find({ cap_ho_so: CAP_HO_SO.CAP_1, is_deleted: false }).lean();
  }
  allHoSoUpdate.sort(sortThuTu);
  allHoSoUpdate.forEach((hoSo, i) => {
    hoSo.thu_tu = i + 1;
  });
  await updateAll(allHoSoUpdate);
}

export async function replaceThuTu(data) {
  let query;
  if (data.ho_so_id) {
    query = { ho_so_id: data.ho_so_id, is_deleted: false, _id: { $ne: data._id } };
  } else {
    query = { cap_ho_so: CAP_HO_SO.CAP_1, is_deleted: false, _id: { $ne: data._id } };
  }
  const allHoSoCungCap = await Model.find(query).lean();
  const index = (data.thu_tu && data.thu_tu <= allHoSoCungCap.length) ? data.thu_tu - 1 : allHoSoCungCap.length;
  return await createIndex(allHoSoCungCap, index, data);
}
