import * as ValidatorHelper from '../../../helpers/validatorHelper';
import PHIEU_MAU from './phieuMau.model';
import { TAN_SUAT_LAP_LICH } from '../../DanhMuc/LoaiPhieuMau';
import moment from 'moment';
import * as PhieuGiaoViecService from '../PhieuGiaoViec/phieuGiaoViec.service';
import PhieuGiaoViec from '../PhieuGiaoViec/phieuGiaoViec.model';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import * as BienPhapAnToanCongViecService from '../BienPhapAnToanCongViec/bienPhapAnToanCongViec.service';
import * as DieuKienAnToanCongViecService from '../DieuKienAnToanCongViec/dieuKienAnToanCongViec.service';
import * as NguoiCongTacService from '../NguoiCongTac/nguoiCongTac.service';
import * as ViTriCongViecService from '../ViTriCongViec/viTriCongViec.service';
import * as TapTinService from '../TapTin/tapTin.service';
import * as NoiDungCongViecService from '../NoiDungCongViec/noiDungCongViec.service';

const Joi = require('joi');

const objSchema = Joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  if (Array.isArray(data)) {
    let validateError = null;
    data.find(itemData => {
      const { value, error } = schema.validate(itemData, { allowUnknown: true, abortEarly: true });
      if (error) validateError = error;
      return error;
    });
    if (validateError && validateError.details) {
      return { validateError };
    }
    return { value: data };
  } else {
    const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
    if (error && error.details) {
      return { error };
    }
    return { value };
  }
}

export function getAll(query, projection = {}) {
  return PHIEU_MAU.find(query, projection).lean();
}

export async function updateAll(dataUpdate) {
  for (const row of dataUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await PHIEU_MAU.findByIdAndUpdate(value._id, value);
  }
}

export async function autoLapLich() {
  const phieuMau = await PHIEU_MAU.find({ is_deleted: false }).lean();

  for (const item of phieuMau) {
    const tanSuatLapLich = item.tan_suat_lap_lich;
    const thoiGianLapLich = item.thoi_gian_lap_lich;
    const contentPhieuMau = item.content_phieu_mau;

    switch (tanSuatLapLich) {
      case TAN_SUAT_LAP_LICH.KHONG_LAP_LICH.code:
        break;
      case TAN_SUAT_LAP_LICH.HANG_NGAY.code:
        createDataFromPhieuMau(contentPhieuMau);
        break;
      case TAN_SUAT_LAP_LICH.HANG_TUAN.code:
        if (moment().day() === moment(thoiGianLapLich).day()) {
          createDataFromPhieuMau(contentPhieuMau);
        }
        break;
      case TAN_SUAT_LAP_LICH.HANG_THANG.code:
        if (moment().get('date') === moment(thoiGianLapLich).get('date')) {
          createDataFromPhieuMau(contentPhieuMau);
        }
        break;
    }

    await timeout(1000);
  }
}

function timeout(delay) {
  return new Promise(res => setTimeout(res, delay));
}

async function createDataFromPhieuMau(contentPhieuMau) {
  try {
    const dataPhieu = contentPhieuMau;
    let dataRequest = {};
    dataRequest.so_phieu = await PhieuGiaoViecService.generateSoPhieu(PhieuGiaoViec, dataPhieu?.don_vi_giao_phieu_id?._id);
    dataRequest.nguoi_cap_phieu_id = dataPhieu?.nguoi_cap_phieu_id?._id;
    dataRequest.chi_huy_xac_nhan_ket_qua = false;
    dataRequest.qua_thoi_gian_tiep_nhan = false;
    dataRequest.trang_thai_cong_viec = TRANG_THAI_PHIEU.DANG_TAO_PHIEU.code;
    dataRequest.ky_tao_phieu = false;
    dataRequest.ky_tiep_nhan = false;
    dataRequest.ky_khoa_phieu = false;
    dataRequest.nguoi_tao_huy_giao_phieu = false;
    dataRequest.loai_cong_viec = dataPhieu?.loai_cong_viec;
    dataRequest.noi_dung_cong_tac = dataPhieu?.noi_dung_cong_tac;
    dataRequest.tinh_trang_van_hanh_duong_day = dataPhieu?.tinh_trang_van_hanh_duong_day;
    dataRequest.don_vi_giao_phieu_id = dataPhieu?.don_vi_giao_phieu_id?._id;
    dataRequest.don_vi_cong_tac_id = dataPhieu?.don_vi_cong_tac_id?._id;
    dataRequest.pham_vi_bay_du_kien = dataPhieu?.pham_vi_bay_du_kien;
    dataRequest.chi_huy_truc_tiep_id = dataPhieu?.don_vi_chi_huy_truc_tiep_id?._id;
    dataRequest.created_at = new Date();
    dataRequest.updated_at = new Date();
    dataRequest.noi_dung_cong_viec = dataPhieu?.noi_dung_cong_viec;
    dataRequest.may_do_id = dataPhieu?.may_do_id?._id;
    dataRequest.chuyen_de_id = dataPhieu?.chuyen_de_id?._id;

    dataRequest.su_dung_drone = dataPhieu?.su_dung_drone;
    dataRequest.chi_huy_truc_tiep_id = dataPhieu?.chi_huy_truc_tiep_id?._id;
    dataRequest.thoi_gian_cong_tac_bat_dau = dataPhieu?.thoi_gian_cong_tac_bat_dau;
    dataRequest.thoi_gian_cong_tac_ket_thuc = dataPhieu?.thoi_gian_cong_tac_ket_thuc;

    if (dataPhieu?.duong_day_ids) {
      let duong_day_ids_arr = [];
      dataPhieu?.duong_day_ids.map(item => {
        duong_day_ids_arr.push(item._id);
      });
      dataRequest.duong_day_ids = duong_day_ids_arr;
    }

    const result = await PhieuGiaoViecService.create(dataRequest);
    if (!result) return;

    const {
      bien_phap_an_toan_cong_viec,
      dieu_kien_an_toan_cong_viec,
      nguoi_cong_tac,
      khoang_cot_cong_viec,
      vi_tri_cong_viec,
      file_phuong_an_thuc_hien,
      noi_dung_cong_viec,
    } = dataPhieu;

    if (bien_phap_an_toan_cong_viec) {
      bien_phap_an_toan_cong_viec.map(item => {
        delete item._id;
        item.phieu_giao_viec_id = result._id;
      });
      await BienPhapAnToanCongViecService.create(bien_phap_an_toan_cong_viec);
    }

    if (dieu_kien_an_toan_cong_viec) {
      dieu_kien_an_toan_cong_viec.map(item => {
        delete item._id;
        item.phieu_giao_viec_id = result._id;
      });
      await DieuKienAnToanCongViecService.create(dieu_kien_an_toan_cong_viec);
    }

    if (nguoi_cong_tac) {
      nguoi_cong_tac.map(item => {
        delete item._id;
        item.phieu_giao_viec_id = result._id;
      });
      await NguoiCongTacService.create(nguoi_cong_tac);
    }

    if (khoang_cot_cong_viec) {
      khoang_cot_cong_viec.map(item => {
        delete item._id;
        item.phieu_giao_viec_id = result._id;
      });
      await ViTriCongViecService.create(khoang_cot_cong_viec);
    }

    if (vi_tri_cong_viec) {
      vi_tri_cong_viec.map(item => {
        delete item._id;
        item.phieu_giao_viec_id = result._id;
      });
      await ViTriCongViecService.create(vi_tri_cong_viec);
    }

    if (file_phuong_an_thuc_hien) {
      file_phuong_an_thuc_hien.map(item => {
        delete item._id;
        item.phieu_giao_viec_id = result._id;
      });
      await TapTinService.create(file_phuong_an_thuc_hien);
    }

    if (noi_dung_cong_viec) {
      noi_dung_cong_viec.map(item => {
        delete item._id;
        item.phieu_giao_viec_id = result._id;
      });
      await NoiDungCongViecService.create(noi_dung_cong_viec);
    }

  } catch (err) {
    console.log(err);
  }
}
