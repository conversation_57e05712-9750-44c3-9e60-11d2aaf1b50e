import express from 'express';
import passport from 'passport';
import * as phieuMauController from './phieuMau.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import GiaoViecPermission from '../../RBAC/permissions/GiaoViecPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const phieuMauRouter = express.Router();
phieuMauRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
phieuMauRouter.post('*', authorizationMiddleware([GiaoViecPermission.CREATE]));
phieuMauRouter.put('*', authorizationMiddleware([GiaoViecPermission.UPDATE]));
phieuMauRouter.delete('*', authorizationMiddleware([GiaoViecPermission.DELETE]));
phieuMauRouter.route('/')
  .get(phieuMauController.getAll)
  .post(phieuMauController.create);

phieuMauRouter
  .route('/:id')
  .get(phieuMauController.findOne)
  .delete(phieuMauController.remove)
  .put(phieuMauController.update);
