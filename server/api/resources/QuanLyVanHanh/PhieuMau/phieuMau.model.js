import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { PHIEU_MAU, USER } from '../../../constant/dbCollections';
import { LOAI_PHIEU_MAU, TAN_SUAT_LAP_LICH } from '../../DanhMuc/LoaiPhieuMau';

const schema = new Schema({
  ten_phieu_mau: String,
  mo_ta_phieu: String,
  loai_phieu_mau: {
    type: String,
    enum: Object.keys(LOAI_PHIEU_MAU),
  },
  thoi_gian_lap_lich: { type: Date },
  tan_suat_lap_lich: {
    type: String,
    enum: Object.keys(TAN_SUAT_LAP_LICH),
  },
  content_phieu_mau: { type: Schema.Types.Mixed },
  nguoi_tao_id: { type: Schema.Types.ObjectId, ref: USER },
  nguoi_chinh_sua: { type: Schema.Types.ObjectId, ref: USER },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(PHIEU_MAU, schema, PHIEU_MAU);
