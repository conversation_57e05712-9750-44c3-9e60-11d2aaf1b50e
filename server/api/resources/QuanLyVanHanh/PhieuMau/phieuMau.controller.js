import * as Service from './phieuMau.service';
import Model from './phieuMau.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import queryHelper from '../../../helpers/queryHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import * as DonViService from '../../DonVi/donVi.service';
import * as VanHanhService from '../../TongKe/VanHanh/vanHanh.service';

// export const findOne = controllerHelper.createFindOneFunction(Model);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service);
export const create = controllerHelper.createCreateFunction(Model, Service);

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_phieu_mau']);
    const { criteria, options } = query;
    if(criteria.loai_cong_viec){
      criteria['content_phieu_mau.loai_cong_viec'] = criteria.loai_cong_viec;
      delete criteria.loai_cong_viec;
    }
    criteria['content_phieu_mau.don_vi_giao_phieu_id._id'] = await DonViService.getDonViQuery(req, null);
    if (!options.hasOwnProperty('sort')) options.sort = { created_at: -1 };
    options.populate = [
      { path: 'nguoi_tao_id' }
    ];
    const data = await Model.paginate(criteria, options);

    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOne({ _id: id, is_deleted: false }).lean();
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    for(const item of data.content_phieu_mau?.khoang_cot_cong_viec){
      let arrDuongDayId = [];
      const vanhanh = await VanHanhService.getAll({
        vi_tri_id: item.khoang_cot_id?.vi_tri_id,
        is_deleted: false,
      });
      vanhanh.map(vh => {
        if(!arrDuongDayId.includes(vh.duong_day_id?.toString())){
          arrDuongDayId.push(vh.duong_day_id?.toString());
        }
      })

      item['khoang_cot_id']['duong_day_id'] = arrDuongDayId;
    }

    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}
