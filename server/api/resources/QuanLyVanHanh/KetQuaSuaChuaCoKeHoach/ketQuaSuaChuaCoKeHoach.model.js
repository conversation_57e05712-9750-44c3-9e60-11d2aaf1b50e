import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { KET_QUA_KIEM_TRA, KET_QUA_SUA_CHUA_CO_KE_HOACH, PHIEU_GIAO_VIEC, USER } from '../../../constant/dbCollections';
import { TRANG_THAI_XU_LY } from '../../DanhMuc/TrangThaiXuLy';

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  ton_tai_id: { type: Schema.Types.ObjectId, ref: KET_QUA_KIEM_TRA },
  tinh_trang_xu_ly: {
    type: String,
    enum: Object.keys(TRANG_THAI_XU_LY),
    default: TRANG_THAI_XU_LY.CHUA_XU_LY.code,
  },
  khoi_luong: String,
  ghi_chu: String,
  nguoi_tao: { type: Schema.Types.ObjectId, ref: USER },
  nguoi_chinh_sua: { type: Schema.Types.ObjectId, ref: USER },
  thoi_gian_cap_nhat: { type: Date, default: Date.now },
  is_deleted: { type: Boolean, default: false },
  ke_hoach_phe_duyet: { type: String },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized compound indexes for KetQuaSuaChuaCoKeHoach queries
// Using background: true for better performance during index creation

// Primary index for phieu_giao_viec_id filtering with deletion status
schema.index({ 
  phieu_giao_viec_id: 1, 
  is_deleted: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  name: 'idx_phieu_giao_viec_deleted_time'
});

// Index for tinh_trang_xu_ly filtering with work ticket reference
schema.index({ 
  tinh_trang_xu_ly: 1, 
  phieu_giao_viec_id: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_tinh_trang_phieu_deleted'
});

// Index for ton_tai_id queries with processing status
schema.index({ 
  ton_tai_id: 1, 
  tinh_trang_xu_ly: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_ton_tai_tinh_trang_deleted'
});

// Index for ke_hoach_phe_duyet filtering
schema.index({ 
  ke_hoach_phe_duyet: 1, 
  phieu_giao_viec_id: 1,
  is_deleted: 1
}, { 
  background: true,
  sparse: true,
  name: 'idx_ke_hoach_phieu_deleted'
});

// Index for nguoi_tao queries with time sorting
schema.index({ 
  nguoi_tao: 1, 
  thoi_gian_tao: -1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_nguoi_tao_time_deleted'
});

// Index for thoi_gian_cap_nhat queries
schema.index({ 
  thoi_gian_cap_nhat: -1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_cap_nhat_deleted'
});

// Additional optimized index for getAll function performance
// Critical index for ton_tai_id queries with time sorting (used in getAll loop)
schema.index({ 
  ton_tai_id: 1, 
  is_deleted: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  name: 'idx_ton_tai_deleted_time_optimized'
});

// Index for bulk operations and data synchronization
schema.index({ 
  thoi_gian_tao: -1,
  is_deleted: 1,
  phieu_giao_viec_id: 1
}, { 
  background: true,
  name: 'idx_sync_operations_phieu'
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(KET_QUA_SUA_CHUA_CO_KE_HOACH, schema, KET_QUA_SUA_CHUA_CO_KE_HOACH);
