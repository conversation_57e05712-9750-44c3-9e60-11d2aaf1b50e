import * as Validator<PERSON>elper from '../../../helpers/validatorHelper';
import KET_QUA_SUA_CHUA_CO_KE_HOACH from './ketQuaSuaChuaCoKeHoach.model';
import { LOAI_CONG_VIEC } from '../../DanhMuc/LoaiCongViec';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import * as PhieuGiaoViecService from '../PhieuGiaoViec/phieuGiaoViec.service';
import * as KetQuaSuaChuaKhongKeHoachService from '../KetQuaSuaChuaKhongKeHoach/ketQuaSuaChuaKhongKeHoach.service';
import { extractIds } from '../../../utils/dataconverter';
import { TRANG_THAI_XU_LY, KE_HOACH_PHE_DUYET } from '../../DanhMuc/TrangThaiXuLy';
import { addIndexToListData } from '../../../common/functionCommons';
import * as DonViService from '../../DonVi/donVi.service';
import quanLyVanHanhCommons from '../quanLyVanHanhCommons';

const Joi = require('joi');

const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return KET_QUA_SUA_CHUA_CO_KE_HOACH.create(value);
}

export function getAll(query, projection = {}) {
  return KET_QUA_SUA_CHUA_CO_KE_HOACH.find(query, projection).lean();
}

export function aggregate(pipeline = [], cb) {
  return KET_QUA_SUA_CHUA_CO_KE_HOACH.aggregate(pipeline, cb);
}

export function getOne(query) {
  return KET_QUA_SUA_CHUA_CO_KE_HOACH.findOne(query).lean();
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await KET_QUA_SUA_CHUA_CO_KE_HOACH.findByIdAndUpdate(value._id, value);
  }
}

export async function updateTonTai(phieuGiaoViecId, ketQuaIds) {
  const allKetQua = await KET_QUA_SUA_CHUA_CO_KE_HOACH.find({
    phieu_giao_viec_id: phieuGiaoViecId,
    _id: { $nin: ketQuaIds },
    is_deleted: false,
  }).lean();
  await KET_QUA_SUA_CHUA_CO_KE_HOACH.bulkWrite(
    allKetQua.map((row) =>
      ({
        updateOne: {
          filter: { _id: row._id },
          update: { $set: { is_deleted: true } },
          upsert: false,
        },
      }),
    ),
  );
}

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};

function formatSuaChuaCoKeHoach(tonTai) {
  return {
    loai_cong_viec: LOAI_CONG_VIEC.CO_KE_HOACH.name,
    chi_huy_truc_tiep_id: tonTai.phieu_giao_viec_id?.chi_huy_truc_tiep_id,
    phieu_giao_viec_id: tonTai.phieu_giao_viec_id,
    ton_tai_id: tonTai.ton_tai_id,
    khoang_cot_id: tonTai.ton_tai_id?.khoang_cot_id?.ten_khoang_cot || tonTai.ton_tai_id?.vi_tri_id?.ten_vi_tri,
    // tieu_chi_id: tonTai.ton_tai_id?.tieu_chi_id?.ten_tieu_chi,
    ten_tieu_chi: tonTai.ton_tai_id?.tieu_chi_id?.ten_tieu_chi,
    khoi_luong: tonTai.khoi_luong || tonTai.ton_tai_id?.khoi_luong,
    don_vi_tinh: tonTai.don_vi_tinh,
    ghi_chu: tonTai.ghi_chu,
    ngay_thuc_hien: tonTai.phieu_giao_viec_id?.thoi_gian_ky_tiep_nhan,
    ngay_ket_thuc: tonTai.phieu_giao_viec_id?.thoi_gian_chi_huy_xac_nhan,
    so_phieu: tonTai.phieu_giao_viec_id?.so_phieu,
    ke_hoach_phe_duyet: KE_HOACH_PHE_DUYET[tonTai.ton_tai_id?.ke_hoach_phe_duyet]?.label,
    thoi_gian_tao: tonTai.thoi_gian_tao,
    duong_day_id: tonTai.ton_tai_id?.duong_day_id,
  };
}

function formatSuaChuaKoKeHoach(congViec) {
  return {
    loai_cong_viec: LOAI_CONG_VIEC.KHONG_CO_KE_HOACH.name,
    chi_huy_truc_tiep_id: congViec.phieu_giao_viec_id?.chi_huy_truc_tiep_id,
    phieu_giao_viec_id: congViec.phieu_giao_viec_id,
    khoang_cot_id: congViec.vi_tri_id?.ten_vi_tri || congViec.khoang_cot_id?.ten_khoang_cot,
    // tieu_chi_id: congViec.cong_viec_id?.ten_cong_viec,
    ten_cong_viec: congViec.cong_viec_id?.ten_cong_viec,
    khoi_luong: congViec.khoi_luong,
    ghi_chu: congViec.ghi_chu,
    don_vi_tinh: congViec.cong_viec_id?.don_vi_tinh,
    ngay_thuc_hien: congViec.phieu_giao_viec_id?.thoi_gian_ky_tiep_nhan,
    ngay_ket_thuc: congViec.phieu_giao_viec_id?.thoi_gian_chi_huy_xac_nhan,
    so_phieu: congViec.phieu_giao_viec_id?.so_phieu,
    duong_day_id: congViec.duong_day_id,
    thoi_gian_tao: congViec.thoi_gian_tao,
  };
}

export async function bangTongHopKetQuaSuaChuaCoKeHoach(req, criteria) {
  criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code;

  // criteria.don_vi_giao_phieu_id = criteria.don_vi_id;
  criteria.don_vi_giao_phieu_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);

  const duongDayQuery = criteria.duong_day_id;
  const congViecQuery = criteria.cong_viec_id;
  const tieuChiQuery = criteria.tieu_chi_id;

  delete criteria.duong_day_id;
  delete criteria.don_vi_id;
  delete criteria.cong_viec_id;
  delete criteria.tieu_chi_id;

  const allPhieu = await PhieuGiaoViecService.getAll(criteria);
  const allPhieuId = extractIds(allPhieu);

  let allKetQuaCoKeHoach = [];

  // ko query có kế hoạch khi tìm kiếm theo công việc sửa chữa
  if (!congViecQuery) {
    allKetQuaCoKeHoach = await getAll(
      {
        phieu_giao_viec_id: allPhieuId,
        tinh_trang_xu_ly: TRANG_THAI_XU_LY.DA_XU_LY.code,
        is_deleted: false,
      })
      .populate([
        {
          path: 'phieu_giao_viec_id',
          populate: [
            { path: 'don_vi_giao_phieu_id', populate: 'don_vi_cha_id' },
            { path: 'duong_day_id chi_huy_truc_tiep_id' },
          ],
        },
        {
          path: 'ton_tai_id',
          populate: [
            {
              path: 'tieu_chi_id vi_tri_id', select: 'ten_tieu_chi tieu_chi_cha_id noi_dung_kiem_tra_id',
              populate: [
                {
                  path: 'tieu_chi_cha_id', select: 'ten_tieu_chi noi_dung_kiem_tra_id',
                  populate: { path: 'noi_dung_kiem_tra_id', select: 'ten_noi_dung' },
                },
                { path: 'noi_dung_kiem_tra_id', select: 'ten_noi_dung' },
              ],
            },
            { path: 'khoang_cot_id vi_tri_id' },
            { path: 'duong_day_id', select: 'ten_duong_day' },
          ],
        },
      ]);

    if (tieuChiQuery) {
      allKetQuaCoKeHoach = allKetQuaCoKeHoach.filter(ketQua => ketQua.ton_tai_id.tieu_chi_id._id.toString() === tieuChiQuery);
    }
  }

  let allKetQuaKoKeHoach = [];
  if (!tieuChiQuery) {
    const koKeHoachQuery = { phieu_giao_viec_id: allPhieuId, is_deleted: false };

    if (congViecQuery) {
      koKeHoachQuery.cong_viec_id = congViecQuery;
    }

    allKetQuaKoKeHoach = await KetQuaSuaChuaKhongKeHoachService.getAll(koKeHoachQuery)
      .populate([
        {
          path: 'phieu_giao_viec_id',
          populate: [
            { path: 'don_vi_giao_phieu_id', populate: 'don_vi_cha_id' },
            { path: 'duong_day_id chi_huy_truc_tiep_id' },
          ],
        },
        { path: 'cong_viec_id' },
        { path: 'vi_tri_id', select: 'ten_vi_tri' },
        { path: 'khoang_cot_id', select: 'ten_khoang_cot' },
        { path: 'duong_day_id', select: 'ten_duong_day' },
      ]);
  }


  return addIndexToListData([
    ...allKetQuaCoKeHoach.map(formatSuaChuaCoKeHoach),
    ...allKetQuaKoKeHoach.map(formatSuaChuaKoKeHoach),
  ].sort((a, b) => b.thoi_gian_tao - a.thoi_gian_tao));
}
