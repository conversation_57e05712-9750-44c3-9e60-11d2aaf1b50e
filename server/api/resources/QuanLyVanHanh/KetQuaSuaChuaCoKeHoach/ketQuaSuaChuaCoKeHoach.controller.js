import moment from 'moment';
import i18next from 'i18next';

import * as Service from './ketQuaSuaChuaCoKeHoach.service';
import * as HieuChinhTonTaiService from '../PhieuGiaoViec/HieuChinhTonTai/hieuChinhTonTai.service';

import Model from './ketQuaSuaChuaCoKeHoach.model';
import DuongDayModel from '../../TongKe/DuongDay/duongDay.model';

import * as controllerHelper from '../../../helpers/controllerHelper';
import queryHelper from '../../../helpers/queryHelper';

import { formatDate, formatDateTime } from '../../../common/formatUTCDateToLocalDate';
import * as generateFile from '../../Report/GenerateFile/generate.controller';
import * as responseHelper from '../../../helpers/responseHelper';
import CommonError from '../../../error/CommonError';
import * as fileUtils from '../../../utils/fileUtils';

import { TEMPLATES_DIRS } from '../../../constant/constant';
import quanLyVanHanhCommons from '../quanLyVanHanhCommons';

const populateOpts = [
  {
    path: 'ton_tai_id', populate: {
      path: 'tieu_chi_id vi_tri_id khoang_cot_id',
      select: 'ten_tieu_chi noi_dung_kiem_tra_id ten_vi_tri thu_tu ten_khoang_cot',
      populate: { path: 'noi_dung_kiem_tra_id', select: 'ten_noi_dung' },
    },
  },
];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts);

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const data = await Model.find(criteria).collation({ locale: 'vi' })
      .populate(populateOpts)
      .lean();
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    for (let i = 0; i < data.length; i++) {
      const tonTaiId = await HieuChinhTonTaiService.getDetailHieuChinh([data[i].ton_tai_id]);
      data[i].ton_tai_id = tonTaiId[0];
    }

    return responseHelper.success(res, data);
  } catch (err) {
    console.log('err', err);
    return responseHelper.error(res, err);
  }
}

export async function getAllByDuongDayId(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;
    criteria = quanLyVanHanhCommons.buildThoiGianCongTacQuery(criteria);

    const data = await Service.bangTongHopKetQuaSuaChuaCoKeHoach(req, criteria);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function downloadBieuTheoDoiSuaChua(req, res) {
  const { t } = req;
  const query = queryHelper.extractQueryParam(req);
  let { criteria } = query;

  criteria = quanLyVanHanhCommons.buildThoiGianCongTacQuery(criteria);

  const ketQuaSuaChua = await Service.bangTongHopKetQuaSuaChuaCoKeHoach(req, criteria);
  ketQuaSuaChua.map(ketQua => {
    ketQua.mot_vi_tri = ketQua.khoang_cot_id?.length === 1;
    ketQua.ngay_thuc_hien = ketQua.ngay_thuc_hien ? formatDate(ketQua.ngay_thuc_hien) : '';
    ketQua.ngay_ket_thuc = ketQua.ngay_ket_thuc ? formatDate(ketQua.ngay_ket_thuc) : '';
    return ketQua;
  });
  let data = {};
  const duongDay = await DuongDayModel.find({ _id: criteria.duong_day_ids });
  data.tu_ngay = criteria.thoi_gian_cong_tac_bat_dau.$gte ? formatDateTime(criteria.thoi_gian_cong_tac_bat_dau.$gte) : '';

  const denNgayData = criteria.thoi_gian_cong_tac_bat_dau.$lt
    ? moment(criteria.thoi_gian_cong_tac_bat_dau.$lt).subtract(1, 'minute')
    : null;

  data.den_ngay = denNgayData ? formatDateTime(denNgayData) : '';
  data.don_vi_id = ketQuaSuaChua[0]?.phieu_giao_viec_id.don_vi_giao_phieu_id;
  data.ten_duong_day = duongDay[0]?.ten_duong_day;
  data.tu_vi_tri = duongDay[0]?.tu_vi_tri;
  data.den_vi_tri = duongDay[0]?.den_vi_tri;
  data.sua_chua = ketQuaSuaChua;

  const templateFilePath = fileUtils.getFilePath('bieu_mau_theo_doi_sua_chua.docx', TEMPLATES_DIRS.BIEU_MAU);
  const outputFileName = `${i18next.t('theo_doi_sua_chua_bao_duong')}.docx`;

  generateFile.generateDocument(res, data, templateFilePath, outputFileName);
}
