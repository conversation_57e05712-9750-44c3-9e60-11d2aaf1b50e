import express from 'express';
import passport from 'passport';
import * as Controller from './ketQuaSuaChuaCoKeHoach.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import WorkPermission from '../../RBAC/permissions/WorkPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const ketQuaSuaChuaCoKeHoachRouter = express.Router();

ketQuaSuaChuaCoKeHoachRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
ketQuaSuaChuaCoKeHoachRouter.post('*', authorizationMiddleware([WorkPermission.CREATE]));
ketQuaSuaChuaCoKeHoachRouter.put('*', authorizationMiddleware([WorkPermission.UPDATE]));
ketQuaSuaChuaCoKeHoachRouter.delete('*', authorizationMiddleware([WorkPermission.DELETE]));
ketQuaSuaChuaCoKeHoachRouter.route('/')
  .get(Controller.getAll)
  .post(Controller.create);

ketQuaSuaChuaCoKeHoachRouter
  .route('/duongday')
  .get(Controller.getAllByDuongDayId);

ketQuaSuaChuaCoKeHoachRouter
  .route('/download')
  .get(Controller.downloadBieuTheoDoiSuaChua);

ketQuaSuaChuaCoKeHoachRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);


