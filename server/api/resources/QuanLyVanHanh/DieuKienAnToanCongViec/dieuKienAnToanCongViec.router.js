import express from 'express';
import passport from 'passport';
import * as bienPhapAnToanCongViecController from './dieuKienAnToanCongViec.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import GiaoViecPermission from '../../RBAC/permissions/GiaoViecPermission';

export const dieuKienAnToanCongViecRouter = express.Router();
dieuKienAnToanCongViecRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
dieuKienAnToanCongViecRouter.post('*', authorizationMiddleware([GiaoViecPermission.CREATE]));
dieuKienAnToanCongViecRouter.put('*', authorizationMiddleware([GiaoViecPermission.UPDATE]));
dieuKienAnToanCongViecRouter.delete('*', authorizationMiddleware([GiaoViecPermission.DELETE]));
dieuKienAnToanCongViecRouter.route('/')
  .get(bienPhapAnToanCongViecController.getAll)
  .post(bienPhapAnToanCongViecController.create);

dieuKienAnToanCongViecRouter
  .route('/:id')
  .get(bienPhapAnToanCongViecController.findOne)
  .delete(bienPhapAnToanCongViecController.remove)
  .put(bienPhapAnToanCongViecController.update);
