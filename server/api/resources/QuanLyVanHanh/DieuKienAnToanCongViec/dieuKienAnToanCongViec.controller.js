import * as Service from './dieuKienAnToanCongViec.service';
import Model from './dieuKienAnToanCongViec.model';
import * as controllerHelper from '../../../helpers/controllerHelper';

export const findOne = controllerHelper.createFindOneFunction(Model);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service);
export const create = controllerHelper.createCreateFunction(Model, Service);
export const getAll = controllerHelper.createGetAllFunction(Model);
