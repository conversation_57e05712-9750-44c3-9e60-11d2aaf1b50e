import * as ValidatorHelper from '../../../helpers/validatorHelper';
import NGUOI_CONG_TAC from './nguoiCongTac.model';

const Joi = require('joi');

const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return NGUOI_CONG_TAC.create(value);
}

export function getAll(query, projection = {}) {
  return NGUOI_CONG_TAC.find(query, projection).lean();
}

export function count(query) {
  return NGUOI_CONG_TAC.count(query).lean();
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await NGUOI_CONG_TAC.findByIdAndUpdate(value._id, value);
  }
}

export function xoaXacNhanKetQua(phieuGiaoViecId) {
  return NGUOI_CONG_TAC.updateMany({ phieu_giao_viec_id: phieuGiaoViecId }, {
    xac_nhan_ket_qua: false,
    thoi_gian_xac_nhan: null,
  });
}

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  if (Array.isArray(data)) {
    let validateError = null;
    data.find(itemData => {
      const { value, error } = schema.validate(itemData, { allowUnknown: true, abortEarly: true });
      if (error) validateError = error;
      return error;
    });
    if (validateError && validateError.details) {
      return { validateError };
    }
    return { value: data };
  } else {
    const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
    if (error && error.details) {
      return { error };
    }
    return { value };
  }
}
