import * as Service from './nguoiCongTac.service';
import Model from './nguoiCongTac.model';
import * as controllerHelper from '../../../helpers/controllerHelper';

const populateOpts = [
  { path: 'user_id', select: 'full_name username phone bac_an_toan' },
  { path: 'don_vi_id', select: 'ten_don_vi' },
];

export const findOne = controllerHelper.createFindOneFunction(Model);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service);
export const getAll = controllerHelper.createGetAllFunction(Model, [], populateOpts);
