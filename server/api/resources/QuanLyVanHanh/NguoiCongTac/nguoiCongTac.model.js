import mongoose, { Schema } from 'mongoose';
import { DON_VI, NGUOI_CONG_TAC, PHIEU_GIAO_VIEC, USER } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';
import UserService from '../../User/user.service';

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  user_id: { type: Schema.Types.ObjectId, ref: USER },
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  thoi_gian_vao: { type: Date },
  xac_nhan_vao: { type: Boolean, default: false },
  thoi_gian_ra: { type: Date },
  xac_nhan_ra: { type: Boolean, default: false },
  nguoi_tao: { type: Schema.Types.ObjectId, ref: USER },
  nguoi_chinh_sua: { type: Schema.Types.ObjectId, ref: USER },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});


schema.pre('save', async function(next) {
  let user = this;
  user = await formatDonVi(user);
  next();
});
schema.pre('findOneAndUpdate', async function(next) {
  this._update = await formatDonVi(this._update);
  next();
});

async function formatDonVi(user) {
  const { user_id } = user;
  const userData = await UserService.getById(user_id, { don_vi_id: 1 });
  if (userData?.don_vi_id) {
    user.don_vi_id = userData.don_vi_id;
  }
  return user;
}

// Optimized indexes for NguoiCongTac queries
// Using background: true and sparse: true for better performance during index creation

// Primary compound index for phieu_giao_viec_id with soft delete (most common query)
schema.index({ 
  phieu_giao_viec_id: 1, 
  is_deleted: 1 
}, { 
  background: true,
  name: 'idx_phieu_giao_viec_deleted'
});

// Index for user_id queries with soft delete
schema.index({ 
  user_id: 1, 
  is_deleted: 1,
  created_at: -1
}, { 
  background: true,
  name: 'idx_user_deleted_time'
});

// Index for don_vi_id filtering
schema.index({ 
  don_vi_id: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_don_vi_deleted'
});

// Compound index for complex queries (user + phieu_giao_viec)
schema.index({ 
  user_id: 1,
  phieu_giao_viec_id: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_user_phieu_deleted'
});

// Index for time-based queries
schema.index({ 
  created_at: -1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_time_deleted'
});

// Index for attendance tracking (vao/ra queries)
schema.index({ 
  phieu_giao_viec_id: 1,
  thoi_gian_vao: 1,
  thoi_gian_ra: 1
}, { 
  background: true,
  name: 'idx_attendance_tracking'
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(NGUOI_CONG_TAC, schema, NGUOI_CONG_TAC);
