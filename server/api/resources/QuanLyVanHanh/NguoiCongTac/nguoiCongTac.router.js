import express from 'express';
import passport from 'passport';
import * as nguoiCongTacController from './nguoiCongTac.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import GiaoViecPermission from '../../RBAC/permissions/GiaoViecPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const nguoiCongTacRouter = express.Router();
nguoiCongTacRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
nguoiCongTacRouter.post('*', authorizationMiddleware([GiaoViecPermission.CREATE]));
nguoiCongTacRouter.put('*', authorizationMiddleware([GiaoViecPermission.UPDATE]));
nguoiCongTacRouter.delete('*', authorizationMiddleware([GiaoViecPermission.DELETE]));
nguoiCongTacRouter.route('/')
  .get(nguoiCongTacController.getAll)
  .post(nguoiCongTacController.create);

nguoiCongTacRouter
  .route('/:id')
  .get(nguoiCongTacController.findOne)
  .delete(nguoiCongTacController.remove)
  .put(nguoiCongTacController.update);
