import * as ValidatorHelper from '../../../helpers/validatorHelper';
import CongTrinhCongViecModel from './congTrinhCongViec.model';
import createBaseService from '../../../base/baseService';

const Joi = require('joi');

const objSchema = Joi.object({});


const baseService = createBaseService(CongTrinhCongViecModel);

export const createMulti = baseService.createMulti;
export const getAll = baseService.getAll;
export const getOne = baseService.getOne;
export const remove = baseService.remove;
export const distinctId = baseService.distinctId;
export const updateByQuery = baseService.updateByQuery;
export const updateByRows = baseService.updateByRows;
export const deleteAll = baseService.deleteAll;

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return CongTrinhCongViecModel.create(value);
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await CongTrinhCongViecModel.findByIdAndUpdate(value._id, value);
  }
}

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  if (Array.isArray(data)) {
    let validateError = null;
    data.find(itemData => {
      const { value, error } = schema.validate(itemData, { allowUnknown: true, abortEarly: true });
      if (error) validateError = error;
      return error;
    });
    if (validateError && validateError.details) {
      return { validateError };
    }
    return { value: data };
  } else {
    const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
    if (error && error.details) {
      return { error };
    }
    return { value };
  }
}
