import express from 'express';
import passport from 'passport';
import * as Controller from './congTrinhCongViec.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import GiaoViecPermission from '../../RBAC/permissions/GiaoViecPermission';

export const congTrinhCongViecRouter = express.Router();
congTrinhCongViecRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
congTrinhCongViecRouter.post('*', authorizationMiddleware([GiaoViecPermission.CREATE]));
congTrinhCongViecRouter.put('*', authorizationMiddleware([GiaoViecPermission.UPDATE]));
congTrinhCongViecRouter.delete('*', authorizationMiddleware([GiaoViecPermission.DELETE]));
congTrinhCongViecRouter.route('/')
  .get(Controller.getAll)
  .post(Controller.create);

congTrinhCongViecRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
