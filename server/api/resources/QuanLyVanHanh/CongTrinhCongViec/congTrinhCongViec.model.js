import mongoose, { Schema } from 'mongoose';
import { CONG_TRINH, CONG_TRINH_CONG_VIEC, PHIEU_GIAO_VIEC } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  cong_trinh_id: { type: Schema.Types.ObjectId, ref: CONG_TRINH },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(CONG_TRINH_CONG_VIEC, schema, CONG_TRINH_CONG_VIEC);
