import express from 'express';
import passport from 'passport';

import * as Controller from './ketQuaDoNhietDoComposite.controller';

import WorkPermission from '../../RBAC/permissions/WorkPermission';

import { authorizationMiddleware } from '../../RBAC/middleware';
import { loggerMiddleware } from '../../../logs/middleware';

export const ketQuaDoNhietDoCompositeRouter = express.Router();


ketQuaDoNhietDoCompositeRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

ketQuaDoNhietDoCompositeRouter.post('*', authorizationMiddleware([WorkPermission.CREATE]));
ketQuaDoNhietDoCompositeRouter.put('*', authorizationMiddleware([WorkPermission.UPDATE]));
ketQuaDoNhietDoCompositeRouter.delete('*', authorizationMiddleware([WorkPermission.DELETE]));

ketQuaDoNhietDoCompositeRouter
  .route('/phieugiaoviec/:id')
  .get(Controller.getByPhieuGiaoViec)
  .post(Controller.createPhieu);
