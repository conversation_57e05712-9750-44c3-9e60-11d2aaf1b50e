import * as responseHelper from '../../../helpers/responseHelper';

import Model from './ketQuaDoNhietDoComposite.model';
import * as KetQuaDoNhietDoCompositeService from './ketQuaDoNhietDoComposite.service';

export async function createPhieu(req, res) {
  try {
    const { id } = req.params;

    const arrData = req.body.map(ketQua => {
      ketQua.phieu_giao_viec_id = id;
      ketQua.nguoi_chinh_sua = req.user._id;
      ketQua.is_deleted = false;
      return ketQua;
    });

    const result = await Model.bulkWrite(
      arrData.map((row) => ({
        updateOne: {
          filter: {
            vi_tri_id: row.vi_tri_id,
            phieu_giao_viec_id: row.phieu_giao_viec_id,
            duong_day_id: row.duong_day_id,
            pha: row.pha,
            huong_do: row.huong_do,
          },
          update: { $set: row },
          upsert: true,
        },
      })),
    );

    if (!result.result.ok) {
      return responseHelper.error(res);
    }

    const ketQuaDo = await KetQuaDoNhietDoCompositeService
      .getAll({ phieu_giao_viec_id: id, is_deleted: false })
      .populate({ path: 'duong_day_id' });

    return responseHelper.success(res, ketQuaDo);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getByPhieuGiaoViec(req, res) {
  try {
    const { id } = req.params;
    // const ketQuaDo = await Model.find({ phieu_giao_viec_id: id, is_deleted: false }).lean();
    const ketQuaDo = await KetQuaDoNhietDoCompositeService
      .getAll({ phieu_giao_viec_id: id, is_deleted: false })
      .populate({ path: 'duong_day_id' });


    return responseHelper.success(res, ketQuaDo);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}
