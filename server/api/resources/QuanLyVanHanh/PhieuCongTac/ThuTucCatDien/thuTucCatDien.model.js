import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DON_VI, DUONG_DAY, PHIEU_CONG_TAC, THU_TUC_CAT_DIEN } from '../../../../constant/dbCollections';

const schema = new Schema({
  phieu_cong_tac_id: { type: Schema.Types.ObjectId, ref: PHIEU_CONG_TAC },
  duong_day_id: { type: Schema.Types.ObjectId, ref: DUONG_DAY },

  thoi_gian_cat_dien: { type: Date },
  nguoi_cho_phep: { type: String },
  da_cat_dien: { type: Boolean, default: false },

  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },

  thoi_gian_ban_giao: { type: Date },
  da_ban_giao: { type: Boolean, default: false },

  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
    updatedAt: 'thoi_gian_cap_nhat',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(THU_TUC_CAT_DIEN, schema, THU_TUC_CAT_DIEN);
