import express from 'express';
import passport from 'passport';
import * as controller from './thuTucCatDien.controller';
import { loggerMiddleware } from '../../../../logs/middleware';

export const thuTucCatDienRouter = express.Router();
thuTucCatDienRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
thuTucCatDienRouter.route('/')
  .get(controller.getAll)
  .post(controller.create);

thuTucCatDienRouter
  .route('/:id/thoigian')
  .put(controller.updateThoiGian);

thuTucCatDienRouter
  .route('/:id/xacnhan')
  .put(controller.xacNhanThucHien);

thuTucCatDienRouter
  .route('/:id/huyxacnhan')
  .put(controller.huyXacNhanThucHien);

thuTucCatDienRouter
  .route('/:id')
  .get(controller.findOne)
  .delete(controller.remove)
  .put(controller.update);
