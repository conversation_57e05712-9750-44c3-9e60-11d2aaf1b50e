import * as ValidatorHelper from '../../../helpers/validatorHelper';
import PHIEU_CONG_TAC from './phieuCongTac.model';
import * as DonViService from '../../DonVi/donVi.service';
import queryHelper from '../../../helpers/queryHelper';
import * as DieuKienTienHanhService from './DieuKienTienHanh/dieuKienTienHanh.service';
import * as thuTucCatDienService from './ThuTucCatDien/thuTucCatDien.service';
import * as thuTucTiepDatService from './ThuTucTiepDat/thuTucTiepDat.service';
import * as thuTucRaoChanBienBaoService from './ThuTucRaoChanBienBao/thuTucRaoChanBienBao.service';
import * as thuTucPhamViLamViecService from './ThuTucPhamViLamViec/thuTucPhamViLamViec.service';
import * as thuTucCanhBaoChiDanService from './ThuTucCanhBaoChiDan/thuTucCanhBaoChiDan.service';
import * as bienPhapAnToanBoSungService from './BienPhapAnToanBoSung/bienPhapAnToanBoSung.service';
import * as danhSachNhanVienCongTacService from './DanhSachNhanVienCongTac/danhSachNhanVienCongTac.service';
import * as congTacHangNgayService from './CongTacHangNgay/congTacHangNgay.service';
import * as phieuCongTacNgoaiService from './PhieuCongTacNgoai/phieuCongTacNgoai.service';
import * as ToCongTacService from '../NguoiCongTac/nguoiCongTac.service';
import * as ViTriCongViecService from '../ViTriCongViec/viTriCongViec.service';
import { addIndexToListData } from '../../../common/functionCommons';
import { formatDateTime, formatTimeDate } from '../../../common/formatUTCDateToLocalDate';


export async function updateAll(dataUpdate) {
  const results = [];
  for (const row of dataUpdate) {
    try {
      const { error, value } = validate(row);
      if (!error) {
        const updated = await PHIEU_CONG_TAC.findByIdAndUpdate(value._id, value, { new: true });
        if (updated) results.push(updated);
      }
    } catch {
    }
  }
  return results;
}

async function getSoPhieuTrongThang(Model, don_vi_giao_phieu_id) {
  const date = new Date();
  const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
  return await Model.count({ don_vi_giao_phieu_id: don_vi_giao_phieu_id, created_at: { $gte: firstDay } });
}

export async function generateSoPhieu(Model, don_vi_giao_phieu_id) {
  const soPhieuGanNhat = await getSoPhieuTrongThang(Model, don_vi_giao_phieu_id);
  const currentDate = new Date();
  const namHienTai = currentDate.getFullYear();
  const thangHienTai = currentDate.getMonth() + 1;
  const soThuTuTrongThang = soPhieuGanNhat + 1;
  return `${soThuTuTrongThang}/${thangHienTai}/${namHienTai}`;
}

export function getAll(query, projection = {}) {
  return PHIEU_CONG_TAC.find(query, projection).lean();
}

export async function getOneById(query) {
  const data = await PHIEU_CONG_TAC.findById(query)
    .populate({
      path: 'nguoi_nhan_phieu_id giam_sat_an_toan_id lanh_dao_cong_viec_id nguoi_cho_phep_id nguoi_cap_phieu_id',
      select: 'full_name username phone bac_an_toan chu_ky_id role_id alias',
      populate: { path: 'role_id', select: 'name' },
    })
    .populate({
      path: 'phieu_giao_viec_id', populate: [
        { path: 'chi_huy_truc_tiep_id', populate: { path: 'role_id', select: 'name' } },
        { path: 'duong_day_ids', select: 'ten_duong_day' },
      ],
    })
    .populate({
      path: 'don_vi_giao_phieu_id',
      populate: { path: 'don_vi_cha_id', populate: { path: 'don_vi_cha_id' } },
    })
    .lean();

  async function getNhanVienDonViCongTac() {
    const soLuongNhanVien = await ToCongTacService
      .count({ phieu_giao_viec_id: data.phieu_giao_viec_id._id, is_deleted: false });
    data.nhan_vien_don_vi_cong_tac = soLuongNhanVien + ' người - Thuộc: ' + data.don_vi_giao_phieu_id?.ten_don_vi;
  }

  async function getDiaDiemCongTac() {
    const viTriCongViec = await ViTriCongViecService
      .getAll({ phieu_giao_viec_id: data.phieu_giao_viec_id._id, is_deleted: false })
      .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' })
      .populate({ path: 'khoang_cot_id', select: 'ten_khoang_cot' });
    let viTri, khoangCot;
    viTriCongViec.map(item => {
      viTri = [viTri, item.vi_tri_id?.ten_vi_tri].join(' ');
      khoangCot = [khoangCot, item.khoang_cot_id?.ten_khoang_cot].join(' ');
    });
    data.vi_tri = viTri.trim();
    data.khoang_cot = khoangCot.trim();
    data.duong_day = addIndexToListData(data.phieu_giao_viec_id.duong_day_ids);
  }

  async function getThuTucDieuKien() {
    data.dieu_kien_tien_hanh_id = await DieuKienTienHanhService
      .getAll({ phieu_cong_tac_id: data._id, is_deleted: false })
      .populate({ path: 'duong_day_id', select: 'ten_duong_day' })
      .populate({ path: 'dieu_kien_id' });
    data.dieu_kien_tien_hanh_id.map((item, index) => {
      item.stt = index + 1;
      item.ten_duong_day = item.duong_day_id?.ten_duong_day;
      item.thoi_gian = formatTimeDate(item.thoi_gian);
    });
  }

  async function getThuTucCatDien() {
    data.thu_tuc_cat_dien_id = await thuTucCatDienService
      .getAll({ phieu_cong_tac_id: data._id, is_deleted: false })
      .populate({ path: 'duong_day_id', select: 'ten_duong_day' })
      .populate({ path: 'nguoi_ban_giao_id', select: 'full_name username phone bac_an_toan' })
      .populate({ path: 'don_vi_id' });
    data.thu_tuc_cat_dien_id.map((item, index) => {
      item.stt = index + 1;
      item.ten_duong_day = item.duong_day_id?.ten_duong_day;
      item.thoi_gian_cat_dien = formatTimeDate(item.thoi_gian_cat_dien);
      item.thoi_gian_ban_giao = formatTimeDate(item.thoi_gian_ban_giao);
      item.da_cat_dien = item.da_cat_dien ? 'X' : '';
      item.da_ban_giao = item.da_ban_giao ? 'X' : '';
    });
  }

  async function getThuTucTiepDat() {
    data.thu_tuc_tiep_dat_id = await thuTucTiepDatService
      .getAll({ phieu_cong_tac_id: data._id, is_deleted: false })
      .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' })
      .populate({ path: 'nguoi_ban_giao_id', select: 'full_name username phone bac_an_toan' })
      .populate({ path: 'don_vi_id' });
    data.thu_tuc_tiep_dat_id.map((item, index) => {
      item.stt = index + 1;
      item.ten_vi_tri = item.vi_tri_id?.ten_vi_tri;
      item.thoi_gian = formatTimeDate(item.thoi_gian);
      item.da_thuc_hien = item.da_thuc_hien ? 'X' : '';
    });
  }

  async function getThuTucRaoChanBienBao() {
    data.thu_tuc_rao_chan_bien_bao_id = await thuTucRaoChanBienBaoService
      .getAll({ phieu_cong_tac_id: data._id, is_deleted: false })
      .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });
    data.thu_tuc_rao_chan_bien_bao_id.map((item, index) => {
      item.stt = index + 1;
      item.ten_vi_tri = item.vi_tri_id?.ten_vi_tri;
      item.thoi_gian = formatTimeDate(item.thoi_gian);
      item.da_thuc_hien = item.da_thuc_hien ? 'X' : '';
    });
  }

  async function getThuTucPhamViLamViec() {
    data.thu_tuc_pham_vi_lam_viec_id = await thuTucPhamViLamViecService
      .getAll({ phieu_cong_tac_id: data._id, is_deleted: false })
      .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });
    data.thu_tuc_pham_vi_lam_viec_id.map((item, index) => {
      item.stt = index + 1;
      item.ten_vi_tri = item.vi_tri_id?.ten_vi_tri;
      item.da_thuc_hien = item.da_thuc_hien ? 'X' : '';
    });
  }

  async function getThuTucCanhBaoChiDan() {
    data.thu_tuc_canh_bao_chi_dan_id = await thuTucCanhBaoChiDanService
      .getAll({ phieu_cong_tac_id: data._id, is_deleted: false });
    data.thu_tuc_canh_bao_chi_dan_id.map((item, index) => {
      item.stt = index + 1;
      item.da_thuc_hien = item.da_thuc_hien ? 'X' : '';
    });
  }

  async function getBienPhapAnToanBoSung() {
    data.bien_phap_an_toan_bo_sung_id = await bienPhapAnToanBoSungService
      .getAll({ phieu_cong_tac_id: data._id, is_deleted: false })
      .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });
    data.bien_phap_an_toan_bo_sung_id.map((item, index) => {
      item.stt = index + 1;
      item.da_thuc_hien = item.da_thuc_hien ? 'X' : '';
      item.thoi_gian = formatTimeDate(item.thoi_gian);
    });
  }

  async function getDanhSachNhanVien() {
    data.danh_sach_nhan_vien_id = await danhSachNhanVienCongTacService
      .getAll({ phieu_giao_viec_id: data.phieu_giao_viec_id, is_deleted: false })
      .populate({ path: 'user_id', select: 'full_name username phone bac_an_toan' });

    data.danh_sach_nhan_vien_id.map((item, index) => {
      item.stt = index + 1;
      item.ten_nhan_vien = item.user_id?.full_name;
      item.xac_nhan_vao = item.xac_nhan_vao ? 'Đã ký' : 'Chưa ký';
      item.xac_nhan_ra = item.xac_nhan_ra ? 'Đã ký' : 'Chưa ký';
      item.thoi_gian_vao = formatTimeDate(item.thoi_gian_vao);
      item.thoi_gian_ra = formatTimeDate(item.thoi_gian_ra);
    });

  }

  async function getCongTacHangNgay() {
    data.cong_tac_hang_ngay_id = await congTacHangNgayService
      .getAll({ phieu_cong_tac_id: data._id, is_deleted: false })
      .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });
    data.cong_tac_hang_ngay_id.map((item, index) => {
      item.stt = index + 1;
      item.ten_vi_tri = item.vi_tri_id?.ten_vi_tri;
      item.nguoi_cho_phep_ky = item.nguoi_cho_phep_ky ? 'Đã ký' : 'Chưa ký';
      item.chi_huy_ky = item.chi_huy_ky ? 'Đã ký' : 'Chưa ký';
      item.thoi_gian_bat_dau = item.thoi_gian_bat_dau ? formatTimeDate(item.thoi_gian_bat_dau) : '';
      item.thoi_gian_ket_thuc = item.thoi_gian_ket_thuc ? formatTimeDate(item.thoi_gian_ket_thuc) : '';
    });
  }

  async function getPhieuCongTacNgoai() {
    data.phieu_cong_tac_ngoai_id = await phieuCongTacNgoaiService
      .getOne({ phieu_cong_tac_id: data._id, is_deleted: false });
  }

  const promises = [
    getDiaDiemCongTac(),
    getNhanVienDonViCongTac(),
    getThuTucDieuKien(),
    getThuTucCatDien(),
    getThuTucTiepDat(),
    getThuTucRaoChanBienBao(),
    getThuTucPhamViLamViec(),
    getThuTucCanhBaoChiDan(),
    getBienPhapAnToanBoSung(),
    getDanhSachNhanVien(),
    getCongTacHangNgay(),
    getPhieuCongTacNgoai(),
  ];
  await Promise.all(promises);
  data.thoi_gian_cong_tac_bat_dau = data.phieu_giao_viec_id.thoi_gian_cong_tac_bat_dau ? formatDateTime(data.phieu_giao_viec_id.thoi_gian_cong_tac_bat_dau) : '';
  data.thoi_gian_cong_tac_ket_thuc = data.phieu_giao_viec_id.thoi_gian_cong_tac_ket_thuc ? formatDateTime(data.phieu_giao_viec_id.thoi_gian_cong_tac_ket_thuc) : '';
  data.thoi_gian_cap_phieu = formatDateTime(data.thoi_gian_cap_phieu);
  data.thoi_gian_bat_dau = formatDateTime(data.thoi_gian_bat_dau);
  data.thoi_gian_ket_thuc = formatDateTime(data.thoi_gian_ket_thuc);
  data.thoi_gian_khoa_phieu = formatDateTime(data.thoi_gian_khoa_phieu);
  data.thoi_gian_cho_phep_cong_tac = formatDateTime(data.thoi_gian_cho_phep_cong_tac);
  data.thoi_gian_hoan_thanh = data.thoi_gian_hoan_thanh ? formatDateTime(data.thoi_gian_hoan_thanh) : '';
  data.da_thuc_hien_bien_phap_an_toan = data.da_thuc_hien_bien_phap_an_toan ? 'X' : '';
  data.can_lam_them_bien_phap_an_toan = data.can_lam_them_bien_phap_an_toan ? 'X' : '';

  return data;
}

export function getOne(query, projection = {}) {
  return PHIEU_CONG_TAC.findOne(query, projection).lean();
}

export function count(query) {
  return PHIEU_CONG_TAC.count(query);
}

export async function buildQuery(req) {
  const query = queryHelper.extractQueryParam(req, ['so_phieu']);
  const { criteria, options } = query;
  criteria.don_vi_giao_phieu_id = await DonViService.getDonViQuery(req, criteria.don_vi_giao_phieu_id);
  criteria.tu_ngay ? (criteria.den_ngay ? (criteria.created_at = {
        $gte: criteria.tu_ngay,
        $lte: criteria.den_ngay,
      }, criteria.tu_ngay = null, criteria.den_ngay = null)
      : (criteria.created_at = { $gte: criteria.tu_ngay }, criteria.tu_ngay = null))
    : (criteria.den_ngay ? (criteria.created_at = { $lte: criteria.den_ngay }, criteria.den_ngay = null) : null);
  return { criteria, options };
}

const Joi = require('joi');

const objSchema = Joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

