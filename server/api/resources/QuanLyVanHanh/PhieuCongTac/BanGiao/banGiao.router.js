import express from 'express';
import passport from 'passport';
import * as controller from './banGiao.controller';
import { loggerMiddleware } from '../../../../logs/middleware';

export const banGiaoRouter = express.Router();
banGiaoRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

banGiaoRouter
  .route('/:id/xacnhan')
  .put(controller.xacNhanThucHien);

banGiaoRouter
  .route('/:id/huyxacnhan')
  .put(controller.huyXacNhanThucHien);

banGiaoRouter
  .route('/:id')
  .get(controller.findOne)
  .delete(controller.remove)
  .put(controller.update);
