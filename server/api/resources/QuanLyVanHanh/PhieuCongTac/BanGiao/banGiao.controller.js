import * as Service from './banGiao.service';
import Model from '../ThuTucCatDien/thuTucCatDien.model';
import * as controllerHelper from '../../../../helpers/controllerHelper';
import * as responseAction from '../../../../helpers/responseHelper';
import * as responseHelper from '../../../../helpers/responseHelper';
import CommonError from '../../../../error/CommonError';

const populateOpts = [
  { path: 'duong_day_id', select: 'ten_duong_day' },
  { path: 'don_vi_id' },
  { path: 'nguoi_ban_giao_id', select: 'full_name username phone bac_an_toan' },
];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, null, populateOpts);

export async function xacNhanThucHien(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return res.status(400).json(error.details);

    const dataUpdate = {
      da_ban_giao: true,
      thoi_gian_ban_giao: value.thoi_gian_ban_giao || new Date(),
    };
    const data = await Model.findOneAndUpdate({ _id: id }, dataUpdate, { new: true })
      .populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function huyXacNhanThucHien(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { da_ban_giao: false }, { new: true })
      .populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}
