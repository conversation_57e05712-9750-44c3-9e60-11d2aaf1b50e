import * as Service from './congTacHangNgay.service';
import Model from './congTacHangNgay.model';
import * as controllerHelper from '../../../../helpers/controllerHelper';
import * as responseAction from '../../../../helpers/responseHelper';
import * as responseHelper from '../../../../helpers/responseHelper';
import CommonError from '../../../../error/CommonError';

const populateOpts = [
  { path: 'vi_tri_id', select: 'ten_vi_tri' },
];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, null, populateOpts);

export async function xacNhanThucHien(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { da_thuc_hien: true }, { new: true })
      .populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function huyXacNhanThucHien(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { da_thuc_hien: false }, { new: true })
      .populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function chiHuyKy(req, res) {
  return await kyXacNhan(req, res, 'chi_huy_ky', true);
}

export async function chiHuyHuyKy(req, res) {
  return await kyXacNhan(req, res, 'chi_huy_ky', false);
}

export async function nguoiChoPhepKy(req, res) {
  return await kyXacNhan(req, res, 'nguoi_cho_phep_ky', true);
}

export async function nguoiChoPhepHuyKy(req, res) {
  return await kyXacNhan(req, res, 'nguoi_cho_phep_ky', false);
}

export async function kyXacNhan(req, res, signType, value = true) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { [signType]: value }, { new: true })
      .populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}
