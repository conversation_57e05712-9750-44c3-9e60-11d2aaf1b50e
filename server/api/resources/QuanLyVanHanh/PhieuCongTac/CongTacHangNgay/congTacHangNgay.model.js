import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CONG_TAC_HANG_NGAY, PHIEU_CONG_TAC, VI_TRI } from '../../../../constant/dbCollections';

const schema = new Schema({
  phieu_cong_tac_id: { type: Schema.Types.ObjectId, ref: PHIEU_CONG_TAC },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  thoi_gian_bat_dau: { type: Date },
  thoi_gian_ket_thuc: { type: Date },
  chi_huy_ky: { type: Boolean, default: false },
  nguoi_cho_phep_ky: { type: Boolean, default: false },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
    updatedAt: 'thoi_gian_cap_nhat',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(CONG_TAC_HANG_NGAY, schema, CONG_TAC_HANG_NGAY);
