import express from 'express';
import passport from 'passport';
import * as controller from './congTacHangNgay.controller';
import { loggerMiddleware } from '../../../../logs/middleware';

export const congTacHangNgayRouter = express.Router();
congTacHangNgayRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
congTacHangNgayRouter.route('/')
  .get(controller.getAll)
  .post(controller.create);

congTacHangNgayRouter
  .route('/:id/chihuyky')
  .put(controller.chiHuyKy);

congTacHangNgayRouter
  .route('/:id/chihuyhuyky')
  .put(controller.chiHuyHuyKy);

congTacHangNgayRouter
  .route('/:id/nguoichophepky')
  .put(controller.nguoiChoPhepKy);

congTacHangNgayRouter
  .route('/:id/nguoichophephuyky')
  .put(controller.nguoiChoPhepHuyKy);

congTacHangNgayRouter
  .route('/:id')
  .get(controller.findOne)
  .delete(controller.remove)
  .put(controller.update);
