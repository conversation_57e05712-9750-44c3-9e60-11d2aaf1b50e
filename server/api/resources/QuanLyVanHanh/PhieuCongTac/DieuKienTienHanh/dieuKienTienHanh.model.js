import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DIEU_KIEN_AN_TOAN, DIEU_KIEN_TIEN_HANH, DUONG_DAY, PHIEU_CONG_TAC } from '../../../../constant/dbCollections';

const schema = new Schema({
  phieu_cong_tac_id: { type: Schema.Types.ObjectId, ref: PHIEU_CONG_TAC },
  duong_day_id: { type: Schema.Types.ObjectId, ref: DUONG_DAY },
  dieu_kien_id: { type: Schema.Types.ObjectId, ref: DIEU_KIEN_AN_TOAN },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
    updatedAt: 'thoi_gian_cap_nhat',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(DIEU_KIEN_TIEN_HANH, schema, DIEU_KIEN_TIEN_HANH);
