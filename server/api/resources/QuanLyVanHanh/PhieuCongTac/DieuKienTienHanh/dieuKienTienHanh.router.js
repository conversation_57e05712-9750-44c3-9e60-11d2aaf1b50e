import express from 'express';
import passport from 'passport';
import * as controller from './dieuKienTienHanh.controller';
import { loggerMiddleware } from '../../../../logs/middleware';

export const dieuKienTienHanhRouter = express.Router();
dieuKienTienHanhRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
dieuKienTienHanhRouter.route('/')
  .get(controller.getAll)
  .post(controller.create);

dieuKienTienHanhRouter
  .route('/:id')
  .get(controller.findOne)
  .delete(controller.remove)
  .put(controller.update);
