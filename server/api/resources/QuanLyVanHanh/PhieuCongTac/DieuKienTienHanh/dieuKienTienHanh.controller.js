import * as Service from './dieuKienTienHanh.service';
import Model from './dieuKienTienHanh.model';
import * as controllerHelper from '../../../../helpers/controllerHelper';

const populateOpts = [
  { path: 'dieu_kien_id' },
  { path: 'duong_day_id', select: 'ten_duong_day' },
];
export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, null, populateOpts);
