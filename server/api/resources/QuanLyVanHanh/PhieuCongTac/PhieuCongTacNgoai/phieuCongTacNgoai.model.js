import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { PHIEU_CONG_TAC, PHIEU_CONG_TAC_NGOAI } from '../../../../constant/dbCollections';


const schema = new Schema({
  phieu_cong_tac_id: { type: Schema.Types.ObjectId, ref: PHIEU_CONG_TAC },
  file_name: { type: String },
  hash_name: { type: String },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
    updatedAt: 'thoi_gian_cap_nhat',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(PHIEU_CONG_TAC_NGOAI, schema, PHIEU_CONG_TAC_NGOAI);
