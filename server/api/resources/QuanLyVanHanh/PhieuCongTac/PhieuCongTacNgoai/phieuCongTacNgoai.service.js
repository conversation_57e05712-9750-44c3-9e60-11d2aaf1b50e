import * as ValidatorHelper from '../../../../helpers/validatorHelper';
import Model from './phieuCongTacNgoai.model';

const Joi = require('joi');

export function getAll(query) {
  return Model.find(query).lean();
}

export function getOne(query, projection = {}) {
  return Model.findOne(query, projection).lean();
}

export function findOneAndUpdate(query, update) {
  return Model.findOneAndUpdate(query, update).lean();
}


export async function createMulti(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    return Model.create(value);
  }
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await Model.findByIdAndUpdate(value._id, value);
  }
}

export async function deleteAll(data) {
  for (const row of data) {
    const { error, value } = validate(row);
    if (error) throw error;
    await Model.findByIdAndUpdate(value._id, { is_deleted: true });
  }
}

const objSchema = Joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

