import express from 'express';
import passport from 'passport';
import * as tapTinController from './phieuCongTacNgoai.controller';
import { checkTempFolder, multipartMiddleware } from '../../../../utils/fileUtils';
import { loggerMiddleware } from '../../../../logs/middleware';

export const phieuCongTacNgoaiRouter = express.Router();

phieuCongTacNgoaiRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

phieuCongTacNgoaiRouter
  .route('/')
  .post(checkTempFolder, multipartMiddleware, tapTinController.createPhieuCongTacNgoai);
