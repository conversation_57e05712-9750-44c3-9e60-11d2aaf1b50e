import path from 'path';
import Model from './phieuCongTacNgoai.model';
import * as responseAction from '../../../../helpers/responseHelper';
import * as fileUtils from '../../../../utils/fileUtils';

export async function createPhieuCongTacNgoai(req, res) {
  try {
    let value = req.body;
    const { phieu_cong_tac_id } = req.body;
    const oldFile = await Model.find({ phieu_cong_tac_id, is_deleted: false }).lean();
    await Model.updateMany({ _id: oldFile.map(x => x._id) }, { is_deleted: true });

    for (let i = 0; i < oldFile.length; i++) {
      const item = oldFile[i];
      fileUtils.deleteFile(path.join(fileUtils.filesDir, item.hash_name));
    }

    let filePath = req.files.file.path;
    const hashName = fileUtils.createUniqueFileName(filePath);
    await fileUtils.createByName(filePath, hashName);
    value.hash_name = hashName;
    value.file_name = req.files.file.name;
    const data = await Model.create(value);
    return responseAction.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}
