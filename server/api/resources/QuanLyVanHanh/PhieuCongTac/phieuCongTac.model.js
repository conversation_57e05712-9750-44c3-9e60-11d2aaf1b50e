import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DON_VI, PHIEU_CONG_TAC, PHIEU_GIAO_VIEC, USER } from '../../../constant/dbCollections';

const DON_VI_THUC_HIEN = {
  TRONG_EVN: { code: 'TRONG_EVN', label: 'Đơn vị trong EVNNPT' },
  NGOAI_EVN: { code: 'NGOAI_EVN', label: 'Đơn vị ngoài EVNNPT' },
};

const schema = new Schema({
  so_phieu: { type: String, required: true, validate: /\S+/ },
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  don_vi_giao_phieu_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  lanh_dao_cong_viec_id: { type: Schema.Types.ObjectId, ref: USER },
  giam_sat_an_toan_id: { type: Schema.Types.ObjectId, ref: USER },
  nguoi_cho_phep_id: { type: Schema.Types.ObjectId, ref: USER },

  don_vi_thuc_hien: {
    type: String,
    enum: Object.keys(DON_VI_THUC_HIEN),
    required: true,
    default: DON_VI_THUC_HIEN.TRONG_EVN.code,
  },

  xac_nhan_cap_phieu: { type: Boolean, default: false },
  thoi_gian_cap_phieu: { type: Date },
  nguoi_cap_phieu_id: { type: Schema.Types.ObjectId, ref: USER },

  cho_phep_cong_tac: { type: Boolean, default: false },
  thoi_gian_cho_phep_cong_tac: { type: Date },
  thoi_gian_ky_cho_phep: { type: Date },

  da_thuc_hien_bien_phap_an_toan: { type: Boolean, default: false },
  can_lam_them_bien_phap_an_toan: { type: Boolean, default: false },

  lanh_dao_xac_nhan_tiep_nhan: { type: Boolean, default: false },
  chi_huy_xac_nhan_tiep_nhan: { type: Boolean, default: false },
  giam_sat_xac_nhan_tiep_nhan: { type: Boolean, default: false },

  tiep_nhan_lam_viec: { type: Boolean, default: false },

  thoi_gian_bat_dau: { type: Date },
  thoi_gian_ket_thuc: { type: Date },


  ket_thuc_cong_tac: { type: Boolean, default: false },
  // chi_huy_xac_nhan_ket_thuc: { type: Boolean, default: false },
  giam_sat_xac_nhan_ket_thuc: { type: Boolean, default: false },
  lanh_dao_xac_nhan_ket_thuc: { type: Boolean, default: false },
  thoi_gian_khoa_phieu: { type: Date },
  xac_nhan_khoa_phieu: { type: Boolean, default: false },

  thoi_gian_hoan_thanh: { type: Date },
  hoan_thanh_cong_tac: { type: Boolean, default: false },

  huy_phieu: { type: Boolean, default: false },
  ly_do_huy_phieu: { type: String },
  nguoi_huy_phieu_id: { type: Schema.Types.ObjectId, ref: USER },
  thoi_gian_huy_phieu: { type: Date },

  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
    updatedAt: 'thoi_gian_cap_nhat',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized indexes for PHIEU_CONG_TAC queries
// Using background: true for better performance during index creation

// Primary index for phieu_giao_viec_id queries with soft delete
// Used in phieuGiaoViec.service.js getPhieuCongTac function
schema.index({ 
  phieu_giao_viec_id: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_phieu_giao_viec_deleted'
});

// Index for so_phieu searches with soft delete
schema.index({ 
  so_phieu: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_so_phieu_deleted'
});

// Index for don_vi_giao_phieu_id queries
schema.index({ 
  don_vi_giao_phieu_id: 1, 
  is_deleted: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  name: 'idx_don_vi_giao_phieu_deleted_time'
});

// Index for workflow status queries
schema.index({ 
  xac_nhan_cap_phieu: 1,
  cho_phep_cong_tac: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_workflow_status_deleted'
});

// Index for time-based queries
schema.index({ 
  thoi_gian_bat_dau: 1,
  thoi_gian_ket_thuc: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_time_range_deleted'
});

// Index for completion status queries
schema.index({ 
  hoan_thanh_cong_tac: 1,
  ket_thuc_cong_tac: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_completion_status_deleted'
});

// Index for cancelled tickets
schema.index({ 
  huy_phieu: 1,
  is_deleted: 1,
  thoi_gian_huy_phieu: -1
}, { 
  background: true,
  sparse: true,
  name: 'idx_huy_phieu_deleted_time'
});

// Index for user-based queries (lanh_dao, giam_sat, nguoi_cho_phep)
schema.index({ 
  lanh_dao_cong_viec_id: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_lanh_dao_deleted'
});

schema.index({ 
  giam_sat_an_toan_id: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_giam_sat_deleted'
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(PHIEU_CONG_TAC, schema, PHIEU_CONG_TAC);
