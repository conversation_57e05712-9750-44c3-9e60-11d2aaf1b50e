import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { PHIEU_CONG_TAC, THU_TUC_CANH_BAO_CHI_DAN } from '../../../../constant/dbCollections';

const schema = new Schema({
  phieu_cong_tac_id: { type: Schema.Types.ObjectId, ref: PHIEU_CONG_TAC },
  canh_bao_tai_nan: { type: String },
  chi_dan_bien_phap_an_toan: { type: String },
  da_thuc_hien: { type: Boolean, default: false },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
    updatedAt: 'thoi_gian_cap_nhat',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(THU_TUC_CANH_BAO_CHI_DAN, schema, THU_TUC_CANH_BAO_CHI_DAN);
