import express from 'express';
import passport from 'passport';
import * as controller from './thuTucCanhBaoChiDan.controller';
import { loggerMiddleware } from '../../../../logs/middleware';

export const thuTucCanhBaoChiDanRouter = express.Router();
thuTucCanhBaoChiDanRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
thuTucCanhBaoChiDanRouter.route('/')
  .get(controller.getAll)
  .post(controller.create);

thuTucCanhBaoChiDanRouter
  .route('/:id/xacnhan')
  .put(controller.xacNhanThucHien);

thuTucCanhBaoChiDanRouter
  .route('/:id/huyxacnhan')
  .put(controller.huyXacNhanThucHien);

thuTucCanhBaoChiDanRouter
  .route('/:id')
  .get(controller.findOne)
  .delete(controller.remove)
  .put(controller.update);
