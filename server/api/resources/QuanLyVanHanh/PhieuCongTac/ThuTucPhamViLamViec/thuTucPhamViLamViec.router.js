import express from 'express';
import passport from 'passport';
import * as controller from './thuTucPhamViLamViec.controller';
import { loggerMiddleware } from '../../../../logs/middleware';

export const thuTucPhamViLamViecRouter = express.Router();
thuTucPhamViLamViecRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
thuTucPhamViLamViecRouter.route('/')
  .get(controller.getAll)
  .post(controller.create);

thuTucPhamViLamViecRouter
  .route('/:id')
  .get(controller.findOne)
  .delete(controller.remove)
  .put(controller.update);

thuTucPhamViLamViecRouter
  .route('/:id/xacnhan')
  .put(controller.xacNhanThucHien);

thuTucPhamViLamViecRouter
  .route('/:id/huyxacnhan')
  .put(controller.huyXacNhanThucHien);
