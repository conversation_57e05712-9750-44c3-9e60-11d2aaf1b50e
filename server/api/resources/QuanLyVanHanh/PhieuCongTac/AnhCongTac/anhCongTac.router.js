import express from 'express';
import passport from 'passport';
import * as Controller from './anhCongTac.controller';
import { checkTempFolder, multipartMiddleware } from '../../../../utils/fileUtils';
import { loggerMiddleware } from '../../../../logs/middleware';

export const anhCongTacRouter = express.Router();
anhCongTacRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
anhCongTacRouter.route('/')
  .get(Controller.getAll)
  .post(checkTempFolder, multipartMiddleware, Controller.create)
  .delete(Controller.removeMulti);

anhCongTacRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
