import * as ValidatorHelper from '../../../../helpers/validatorHelper';
import ANH_CONG_TAC from './anhCongTac.model';


const Joi = require('joi');

const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return ANH_CONG_TAC.create(value);
}

export function getAll(query) {
  return ANH_CONG_TAC.find(query).lean();
}

export function getById(id, projection = {}) {
  return ANH_CONG_TAC.findById(id, projection).lean();
}

export async function updateAll(dataUpdate) {
  for (const row of dataUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await ANH_CONG_TAC.findByIdAndUpdate(value._id, value);
  }
}

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};
