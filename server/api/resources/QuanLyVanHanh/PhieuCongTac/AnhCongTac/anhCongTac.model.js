import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { ANH_CONG_TAC, PHIEU_CONG_TAC } from '../../../../constant/dbCollections';

export const LOAI_CONG_TAC = {
  BIEN_PHAP_AN_TOAN: 'BIEN_PHAP_AN_TOAN',
  LAM_THEM_BIEN_PHAP_AN_TOAN: 'LAM_THEM_BIEN_PHAP_AN_TOAN',
  RUT_BIEN_PHAP_AN_TOAN: 'RUT_BIEN_PHAP_AN_TOAN',
};

const schema = new Schema({
    phieu_cong_tac_id: { type: Schema.Types.ObjectId, ref: PHIEU_CONG_TAC },
    loai_cong_tac: {
      type: String,
      enum: Object.keys(LOAI_CONG_TAC),
    },
    image_id: { type: String, require: true },
    thumbnail_id: { type: String, require: true },
    is_deleted: { type: Boolean, default: false, select: true },
    folder_path: { type: String },
  },
  {
    timestamps: {
      createdAt: 'thoi_gian_tao',
      updatedAt: 'thoi_gian_cap_nhat',
    },
    collation: { locale: 'vi' },
    versionKey: false,
  });
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(ANH_CONG_TAC, schema, ANH_CONG_TAC);
