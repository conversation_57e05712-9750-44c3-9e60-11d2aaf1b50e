import express from 'express';
import passport from 'passport';
import * as controller from './thuTucTiepDat.controller';
import { loggerMiddleware } from '../../../../logs/middleware';

export const thuTucTiepDatRouter = express.Router();
thuTucTiepDatRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
thuTucTiepDatRouter.route('/')
  .get(controller.getAll)
  .post(controller.create);

thuTucTiepDatRouter
  .route('/:id/thoigian')
  .put(controller.updateThoiGian);

thuTucTiepDatRouter
  .route('/:id/xacnhan')
  .put(controller.xacNhanThucHien);

thuTucTiepDatRouter
  .route('/:id/huyxacnhan')
  .put(controller.huyXacNhanThucHien);

thuTucTiepDatRouter
  .route('/:id')
  .get(controller.findOne)
  .delete(controller.remove)
  .put(controller.update);
