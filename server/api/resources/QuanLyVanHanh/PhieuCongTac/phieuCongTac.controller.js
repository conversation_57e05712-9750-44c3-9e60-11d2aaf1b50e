import * as responseHelper from '../../../helpers/responseHelper';
import * as Service from './phieuCongTac.service';
import * as DieuKienTienHanhService from './DieuKienTienHanh/dieuKienTienHanh.service';
import * as ThuTucCatDienService from './ThuTucCatDien/thuTucCatDien.service';
import * as ThuTucTiepDatService from './ThuTucTiepDat/thuTucTiepDat.service';
import * as ThuTucRaoChanBienBaoService from './ThuTucRaoChanBienBao/thuTucRaoChanBienBao.service';
import * as ThuTucPhamViLamViecService from './ThuTucPhamViLamViec/thuTucPhamViLamViec.service';
import * as ThuTucCanhBaoChiDanService from './ThuTucCanhBaoChiDan/thuTucCanhBaoChiDan.service';
import * as BienPhapAnToanBoSungService from './BienPhapAnToanBoSung/bienPhapAnToanBoSung.service';
import * as DanhSachNhanVienCongTacService from './DanhSachNhanVienCongTac/danhSachNhanVienCongTac.service';
import * as CongTacHangNgayService from './CongTacHangNgay/congTacHangNgay.service';
import * as PhieuCongTacNgoaiService from './PhieuCongTacNgoai/phieuCongTacNgoai.service';
import * as AnhCongTacService from './AnhCongTac/anhCongTac.service';
import path from 'path';
import { STORE_DIRS } from '../../../constant/constant';
import { getFileDirByMonth } from '../../../utils/fileUtils';

import Model from './phieuCongTac.model';
import CommonError from '../../../error/CommonError';
import * as functionCommons from '../../../common/functionCommons';
import { LOAI_CONG_TAC } from './AnhCongTac/anhCongTac.model';
import Error from '../../RBAC/Error';
import { extractIds } from '../../../utils/dataconverter';

const populateOpts = [
  { path: 'don_vi_giao_phieu_id' },
  { path: 'lanh_dao_cong_viec_id', select: 'full_name username role_id phone bac_an_toan' },
  { path: 'giam_sat_an_toan_id', select: 'full_name username role_id phone bac_an_toan' },
  { path: 'nguoi_cho_phep_id', select: 'full_name username role_id phone bac_an_toan don_vi_id' },
  { path: 'nguoi_cap_phieu_id', select: 'full_name username role_id phone bac_an_toan' },
];

async function findOneById(id) {
  const data = await Model.findById(id)
    .populate({ path: 'nguoi_nhan_phieu_id', select: 'full_name username phone bac_an_toan' })
    .populate({ path: 'nguoi_cap_phieu_id', select: 'full_name username' })
    .populate({
      path: 'don_vi_giao_phieu_id',
      populate: { path: 'don_vi_cha_id', populate: { path: 'don_vi_cha_id' } },
    })
    .lean();
  if (!data) {
    return null;
  }
  return data;
}

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await findOneById(id);
    if (!data) {
      return responseHelper.error(res, null, 404, '');
    }
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseHelper.error(res, null, 404, '');
    }
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}


export async function update(req, res) {

  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true }).populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    responseHelper.error(res, err);
  }
}

async function getDataThuTuc(data) {
  async function getThuTucDieuKien() {
    data.dieu_kien_tien_hanh_id = await DieuKienTienHanhService
      .getAll({ phieu_cong_tac_id: data._id, is_deleted: false })
      .populate({ path: 'duong_day_id', select: 'ten_duong_day' })
      .populate({ path: 'dieu_kien_id' });
  }

  async function getThuTucCatDien() {
    data.thu_tuc_cat_dien_id = await ThuTucCatDienService
      .getAll({ phieu_cong_tac_id: data._id, is_deleted: false })
      .populate({ path: 'duong_day_id', select: 'ten_duong_day' })
      .populate({ path: 'nguoi_ban_giao_id', select: 'full_name username phone bac_an_toan' })
      .populate({ path: 'don_vi_id' });
  }

  async function getThuTucTiepDat() {
    data.thu_tuc_tiep_dat_id = await ThuTucTiepDatService
      .getAll({ phieu_cong_tac_id: data._id, is_deleted: false })
      .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' })
      .populate({ path: 'nguoi_ban_giao_id', select: 'full_name username phone bac_an_toan' })
      .populate({ path: 'don_vi_id' });
  }

  async function getThuTucRaoChanBienBao() {
    data.thu_tuc_rao_chan_bien_bao_id = await ThuTucRaoChanBienBaoService
      .getAll({ phieu_cong_tac_id: data._id, is_deleted: false })
      .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });
  }

  async function getThuTucPhamViLamViec() {
    data.thu_tuc_pham_vi_lam_viec_id = await ThuTucPhamViLamViecService
      .getAll({ phieu_cong_tac_id: data._id, is_deleted: false })
      .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });
  }

  async function getThuTucCanhBaoChiDan() {
    data.thu_tuc_canh_bao_chi_dan_id = await ThuTucCanhBaoChiDanService
      .getAll({ phieu_cong_tac_id: data._id, is_deleted: false });
  }

  async function getBienPhapAnToanBoSung() {
    data.bien_phap_an_toan_bo_sung_id = await BienPhapAnToanBoSungService
      .getAll({ phieu_cong_tac_id: data._id, is_deleted: false })
      .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });
  }

  async function getDanhSachNhanVien() {
    data.danh_sach_nhan_vien_id = await DanhSachNhanVienCongTacService
      .getAll({ phieu_giao_viec_id: data.phieu_giao_viec_id, is_deleted: false })
      .populate({ path: 'user_id', select: 'full_name username phone bac_an_toan' })
      .populate({ path: 'don_vi_id', select: 'ten_don_vi' });
  }

  async function getCongTacHangNgay() {
    data.cong_tac_hang_ngay_id = await CongTacHangNgayService
      .getAll({ phieu_cong_tac_id: data._id, is_deleted: false })
      .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });
  }

  async function getPhieuCongTacNgoai() {
    data.phieu_cong_tac_ngoai_id = await PhieuCongTacNgoaiService
      .getOne({ phieu_cong_tac_id: data._id, is_deleted: false });
  }

  async function getAnhCongViec() {
    const imageData = await AnhCongTacService.getAll({ phieu_cong_tac_id: data._id, is_deleted: false });
    data.anh_bien_phap_an_toan_id = imageData.filter(image => image.loai_cong_tac === LOAI_CONG_TAC.BIEN_PHAP_AN_TOAN);
    data.anh_lam_them_bien_phap_an_toan_id = imageData.filter(image => image.loai_cong_tac === LOAI_CONG_TAC.LAM_THEM_BIEN_PHAP_AN_TOAN);
    data.anh_rut_bien_phap_an_toan_id = imageData.filter(image => image.loai_cong_tac === LOAI_CONG_TAC.RUT_BIEN_PHAP_AN_TOAN);
  }

  const promises = [
    getThuTucDieuKien(),
    getThuTucCatDien(),
    getThuTucTiepDat(),
    getThuTucRaoChanBienBao(),
    getThuTucPhamViLamViec(),
    getThuTucCanhBaoChiDan(),
    getBienPhapAnToanBoSung(),
    getDanhSachNhanVien(),
    getCongTacHangNgay(),
    getPhieuCongTacNgoai(),
    getAnhCongViec(),
  ];
  await Promise.all(promises);
  return data;
}

export async function getByPhieuGiaoViecId(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOne({ phieu_giao_viec_id: id }).populate(populateOpts).lean();
    if (!data) {
      return responseHelper.error(res, 404, '');
    }
    const dataReturn = await getDataThuTuc(data);
    return responseHelper.success(res, dataReturn);
  } catch (err) {
    console.error(err);
    responseHelper.error(res, err);
  }
}

export async function create(req, res) {

  try {
    req.body.so_phieu = await Service.generateSoPhieu(Model, req.body.don_vi_giao_phieu_id);
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error);
    let data = await Model.create(value);
    data = data.toObject();
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseHelper.error(res, err, 500);
  }
}


export async function xacNhanKiemTraBienPhapAnToan(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { da_thuc_hien_bien_phap_an_toan: true },
      {
        new: true,
        projection: { da_thuc_hien_bien_phap_an_toan: 1 },
      });
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    responseHelper.error(res, err);
  }
}

export async function huyXacNhanKiemTraBienPhapAnToan(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { da_thuc_hien_bien_phap_an_toan: false },
      {
        new: true,
        projection: { da_thuc_hien_bien_phap_an_toan: 1 },
      });
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    responseHelper.error(res, err);
  }
}


export async function xacNhanCanLamThemBienPhapAnToan(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { can_lam_them_bien_phap_an_toan: true },
      {
        new: true,
        projection: { can_lam_them_bien_phap_an_toan: 1 },
      });
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    responseHelper.error(res, err);
  }
}

export async function huyXacNhanCanLamThemBienPhapAnToan(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { can_lam_them_bien_phap_an_toan: false },
      {
        new: true,
        projection: { can_lam_them_bien_phap_an_toan: 1 },
      });
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    responseHelper.error(res, err);
  }
}

export async function updateThoiGianBatDau(req, res) {
  const { thoi_gian_bat_dau } = req.body;
  return await updateThoiGian(req, res, 'thoi_gian_bat_dau', thoi_gian_bat_dau);
}

export async function updateThoiGianKetThuc(req, res) {
  const { thoi_gian_ket_thuc } = req.body;
  return await updateThoiGian(req, res, 'thoi_gian_ket_thuc', thoi_gian_ket_thuc);
}

export async function thoiGianHoanThanh(req, res) {
  const { thoi_gian_hoan_thanh } = req.body;
  return await updateThoiGian(req, res, 'thoi_gian_hoan_thanh', thoi_gian_hoan_thanh);
}

export async function updateThoiGianKhoaPhieu(req, res) {
  const { thoi_gian_khoa_phieu } = req.body;
  return await updateThoiGian(req, res, 'thoi_gian_khoa_phieu', thoi_gian_khoa_phieu);
}

export async function updateThoiGian(req, res, key, value) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { [key]: value },
      {
        new: true,
        projection: { [key]: 1 },
      });
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    responseHelper.error(res, err);
  }
}

// xac nhan tiep nhan
export async function lanhDaoXacNhanTiepNhan(req, res) {
  return await kyXacNhan(req, res, 'lanh_dao_xac_nhan_tiep_nhan', true);
}

export async function lanhDaoHuyXacNhanTiepNhan(req, res) {
  return await kyXacNhan(req, res, 'lanh_dao_xac_nhan_tiep_nhan', false);
}

export async function chiHuyXacNhanTiepNhan(req, res) {
  return await kyXacNhan(req, res, 'chi_huy_xac_nhan_tiep_nhan', true);
}

export async function chiHuyHuyXacNhanTiepNhan(req, res) {
  return await kyXacNhan(req, res, 'chi_huy_xac_nhan_tiep_nhan', false);
}

export async function giamSatXacNhanTiepNhan(req, res) {
  return await kyXacNhan(req, res, 'giam_sat_xac_nhan_tiep_nhan', true);
}

export async function giamSatHuyXacNhanTiepNhan(req, res) {
  return await kyXacNhan(req, res, 'giam_sat_xac_nhan_tiep_nhan', false);
}

export async function tiepNhanLamViec(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const currentData = await Model.findById(id);
    if (!currentData.da_thuc_hien_bien_phap_an_toan) {
      return responseHelper.error(res, { message: t('not_adequate_correct_safety_measure') });
    }
    if (!currentData.can_lam_them_bien_phap_an_toan) {
      return responseHelper.error(res, { message: t('not_take_more_safety_measure') });
    }

    const checkBienPhapBoSung = await BienPhapAnToanBoSungService
      .getOne({ phieu_cong_tac_id: id, da_thuc_hien: false, is_deleted: false });
    if (checkBienPhapBoSung) {
      return responseHelper.error(res, { message: t('not_complete_safety_measure_grounding') });
    }

    return await updateDataObject(req, res,
      { tiep_nhan_lam_viec: true, thoi_gian_bat_dau: new Date() },
      { tiep_nhan_lam_viec: 1, thoi_gian_bat_dau: 1 });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function huyTiepNhanLamViec(req, res) {
  return await updateDataObject(req, res,
    { tiep_nhan_lam_viec: false, thoi_gian_bat_dau: null },
    { tiep_nhan_lam_viec: 1, thoi_gian_bat_dau: 1 });
  // return await kyXacNhan(req, res, 'tiep_nhan_lam_viec', false);
}

export async function hoanThanhCongTac(req, res) {
  return await updateDataObject(req, res,
    { hoan_thanh_cong_tac: true, thoi_gian_hoan_thanh: new Date() },
    { hoan_thanh_cong_tac: 1, thoi_gian_hoan_thanh: 1 });
}

export async function huyHoanThanhCongTac(req, res) {
  return await updateDataObject(req, res,
    { hoan_thanh_cong_tac: false, thoi_gian_hoan_thanh: null },
    { hoan_thanh_cong_tac: 1, thoi_gian_hoan_thanh: 1 });
}


// xac nhan tiep nhan

// export async function chiHuyXacNhanKetThuc(req, res) {
//   return await kyXacNhan(req, res, 'chi_huy_xac_nhan_ket_thuc', true);
// }
//
// export async function chiHuyHuyXacNhanKetThuc(req, res) {
//   return await kyXacNhan(req, res, 'chi_huy_xac_nhan_ket_thuc', false);
// }

export async function ketThucCongTac(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const currentData = await Model.findById(id);
    const checkNhanVienXacNhan = await DanhSachNhanVienCongTacService
      .getOne(
        {
          phieu_giao_viec_id: currentData.phieu_giao_viec_id,
          xac_nhan_vao: false,
          is_deleted: false,
        },
        { _id: 1 },
      );
    if (checkNhanVienXacNhan) {
      return responseHelper.error(res, { message: t('employee_not_confirm') });
    }
    const checkCongTac = await CongTacHangNgayService
      .getOne(
        {
          phieu_cong_tac_id: id, is_deleted: false,
          $or: [{ chi_huy_ky: false }, { nguoi_cho_phep_ky: false }],
        },
        { _id: 1 },
      );
    if (checkCongTac) {
      return responseHelper.error(res, { message: t('daily_work_not_confirm') });
    }

    return await updateDataObject(req, res,
      { ket_thuc_cong_tac: true, thoi_gian_ket_thuc: new Date() },
      { ket_thuc_cong_tac: 1, thoi_gian_ket_thuc: 1 });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function huyKetThucCongTac(req, res) {
  return await updateDataObject(req, res,
    { ket_thuc_cong_tac: false, thoi_gian_ket_thuc: null },
    { ket_thuc_cong_tac: 1, thoi_gian_ket_thuc: 1 });
}

export async function lanhDaoXacNhanKetThuc(req, res) {
  return await kyXacNhan(req, res, 'lanh_dao_xac_nhan_ket_thuc', true);
}

export async function lanhDaoHuyXacNhanKetThuc(req, res) {
  return await kyXacNhan(req, res, 'lanh_dao_xac_nhan_ket_thuc', false);
}

export async function xacNhanKhoaPhieu(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const checkBanGiao = await ThuTucCatDienService
      .getOne({ phieu_cong_tac_id: id, da_ban_giao: false, is_deleted: false }, { _id: 1 });
    if (checkBanGiao) {
      return responseHelper.error(res, { message: t('handover_not_complete') });
    }

    return await updateDataObject(req, res,
      { xac_nhan_khoa_phieu: true, thoi_gian_khoa_phieu: new Date() },
      { xac_nhan_khoa_phieu: 1, thoi_gian_khoa_phieu: 1 });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function huyXacNhanKhoaPhieu(req, res) {
  return await updateDataObject(req, res,
    { xac_nhan_khoa_phieu: false, thoi_gian_khoa_phieu: null },
    { xac_nhan_khoa_phieu: 1, thoi_gian_khoa_phieu: 1 });
}

export async function huyPhieu(req, res) {
  try {
    const { id } = req.params;
    const currentData = await Model.findById(id, { xac_nhan_cap_phieu: 1 }).lean();
    if (currentData.xac_nhan_cap_phieu) {
      return responseHelper.error(res, Error.INSUFFICIENT_PERMISSION);
    }
    const { value } = Service.validate(req.body);
    return await updateDataObject(req, res,
      {
        huy_phieu: true,
        ly_do_huy_phieu: value.ly_do_huy_phieu,
        nguoi_huy_phieu_id: req.user._id,
        thoi_gian_huy_phieu: new Date(),
      },
      { huy_phieu: 1, ly_do_huy_phieu: 1, nguoi_huy_phieu_id: 1, thoi_gian_huy_phieu: 1 });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function lyDoHuyPhieu(req, res) {
  try {
    const { id } = req.params;
    const currentData = await Model.findById(id, { huy_phieu: 1 }).lean();
    if (!currentData.huy_phieu) {
      return responseHelper.error(res, Error.INSUFFICIENT_PERMISSION);
    }
    const { value } = Service.validate(req.body);
    return await updateDataObject(req, res,
      { ly_do_huy_phieu: value.ly_do_huy_phieu },
      { ly_do_huy_phieu: 1 });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function kyXacNhan(req, res, signType, value = true) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { [signType]: value },
      {
        new: true,
        projection: { [signType]: 1 },
      });
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseHelper.error(res, err, 500);
  }
}

export async function updateMobile(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    const dataUpdated = await Model.findOneAndUpdate({ _id: id }, value,
      { new: true, lean: true })
      .populate(populateOpts);
    if (!dataUpdated) {
      return responseHelper.error(res, null, 404);
    }

    function filterData(dataInput) {
      const dataCreate = dataInput?.filter(item => !item._id) || [];
      const dataUpdate = dataInput?.filter(item => item._id && !item.delete) || [];
      const dataDelete = dataInput?.filter(item => item._id && item.delete) || [];
      return { dataCreate, dataUpdate, dataDelete };
    }

    async function updateThuTucDieuKien() {
      const { dataCreate, dataUpdate, dataDelete } = filterData(req.body.dieu_kien_tien_hanh_id);
      await DieuKienTienHanhService.createMulti(dataCreate);
      await DieuKienTienHanhService.updateAll(dataUpdate);
      await DieuKienTienHanhService.deleteAll(dataDelete);
    }

    async function updateThuTucCatDien() {
      const { dataCreate, dataUpdate, dataDelete } = filterData(req.body.thu_tuc_cat_dien_id);

      const currentCatDien = await ThuTucCatDienService.getAll({
        _id: extractIds(dataUpdate),
        $or: [
          { nguoi_cho_phep: { $exists: false } },
          { don_vi_id: { $exists: false } },
        ],
      }, { _id: 1 });

      dataUpdate.forEach(catDien => {
        const current = currentCatDien.find(item => item._id.toString() === catDien._id.toString());
        if (catDien.da_cat_dien) {
          if (!current?.nguoi_cho_phep) {
            catDien.nguoi_cho_phep = dataUpdated.nguoi_cho_phep_id?.full_name || dataUpdated.nguoi_cho_phep_id?.username;
          }
          if (!current?.don_vi_id) {
            catDien.don_vi_id = dataUpdated.nguoi_cho_phep_id?.don_vi_id;
          }
        }
      });

      await ThuTucCatDienService.createMulti(dataCreate);
      await ThuTucCatDienService.updateAll(dataUpdate);
      await ThuTucCatDienService.deleteAll(dataDelete);
    }

    async function updateThuTucTiepDat() {
      const { dataCreate, dataUpdate, dataDelete } = filterData(req.body.thu_tuc_tiep_dat_id);

      const currentTiepDat = await ThuTucTiepDatService.getAll({
        _id: extractIds(dataUpdate),
        $or: [
          { nguoi_ban_giao: { $exists: false } },
          { don_vi_id: { $exists: false } },
        ],
      }, { _id: 1 });

      dataUpdate.forEach(catDien => {
        const current = currentTiepDat.find(item => item._id.toString() === catDien._id.toString());
        if (catDien.da_thuc_hien) {
          if (!current?.nguoi_ban_giao) {
            catDien.nguoi_ban_giao = dataUpdated.nguoi_cho_phep_id?.full_name || dataUpdated.nguoi_cho_phep_id?.username;
          }
          if (!current?.don_vi_id) {
            catDien.don_vi_id = dataUpdated.nguoi_cho_phep_id?.don_vi_id;
          }
        }
      });

      await ThuTucTiepDatService.createMulti(dataCreate);
      await ThuTucTiepDatService.updateAll(dataUpdate);
      await ThuTucTiepDatService.deleteAll(dataDelete);
    }

    async function updateThuTucRaoChanBienBao() {
      const { dataCreate, dataUpdate, dataDelete } = filterData(req.body.thu_tuc_rao_chan_bien_bao_id);
      await ThuTucRaoChanBienBaoService.createMulti(dataCreate);
      await ThuTucRaoChanBienBaoService.updateAll(dataUpdate);
      await ThuTucRaoChanBienBaoService.deleteAll(dataDelete);
    }

    async function updateThuTucPhamViLamViec() {
      const { dataCreate, dataUpdate, dataDelete } = filterData(req.body.thu_tuc_pham_vi_lam_viec_id);
      await ThuTucPhamViLamViecService.createMulti(dataCreate);
      await ThuTucPhamViLamViecService.updateAll(dataUpdate);
      await ThuTucPhamViLamViecService.deleteAll(dataDelete);
    }

    async function updateThuTucCanhBaoChiDan() {
      const { dataCreate, dataUpdate, dataDelete } = filterData(req.body.thu_tuc_canh_bao_chi_dan_id);
      await ThuTucCanhBaoChiDanService.createMulti(dataCreate);
      await ThuTucCanhBaoChiDanService.updateAll(dataUpdate);
      await ThuTucCanhBaoChiDanService.deleteAll(dataDelete);
    }

    async function updateBienPhapAnToanBoSung() {
      const { dataCreate, dataUpdate, dataDelete } = filterData(req.body.bien_phap_an_toan_bo_sung_id);
      await BienPhapAnToanBoSungService.createMulti(dataCreate);
      await BienPhapAnToanBoSungService.updateAll(dataUpdate);
      await BienPhapAnToanBoSungService.deleteAll(dataDelete);
    }

    async function updateDanhSachNhanVien() {
      const { dataCreate, dataUpdate, dataDelete } = filterData(req.body.danh_sach_nhan_vien_id);
      await DanhSachNhanVienCongTacService.createMulti(dataCreate);
      await DanhSachNhanVienCongTacService.updateAll(dataUpdate);
      await DanhSachNhanVienCongTacService.deleteAll(dataDelete);
    }

    async function updateCongTacHangNgay() {
      const { dataCreate, dataUpdate, dataDelete } = filterData(req.body.cong_tac_hang_ngay_id);
      await CongTacHangNgayService.createMulti(dataCreate);
      await CongTacHangNgayService.updateAll(dataUpdate);
      await CongTacHangNgayService.deleteAll(dataDelete);
    }

    async function updatePhieuCongTacNgoai() {
      const { dataCreate, dataUpdate, dataDelete } = filterData(req.body.phieu_cong_tac_ngoai_id);
      await PhieuCongTacNgoaiService.createMulti(dataCreate);
      await PhieuCongTacNgoaiService.updateAll(dataUpdate);
      await PhieuCongTacNgoaiService.deleteAll(dataDelete);
    }

    const promises = [
      updateThuTucDieuKien(),
      updateThuTucCatDien(),
      updateThuTucTiepDat(),
      updateThuTucRaoChanBienBao(),
      updateThuTucPhamViLamViec(),
      updateThuTucCanhBaoChiDan(),
      updateBienPhapAnToanBoSung(),
      updateDanhSachNhanVien(),
      updateCongTacHangNgay(),
      updatePhieuCongTacNgoai(),
    ];
    await Promise.all(promises);

    const dataReturn = await getDataThuTuc(dataUpdated);
    // console.log('dataReturn', dataReturn);
    return responseHelper.success(res, dataReturn);
  } catch (err) {
    console.log(err);
    responseHelper.error(res, err);
  }
}


export async function choPhepVaTiepNhanLamViec(req, res) {
  return await updateDataObject(req, res,
    {
      tiep_nhan_lam_viec: true,
      cho_phep_cong_tac: true,
      thoi_gian_cho_phep_cong_tac: new Date(),
    },
    { tiep_nhan_lam_viec: 1, cho_phep_cong_tac: 1, thoi_gian_cho_phep_cong_tac: 1 });
}

export async function huyChoPhepVaTiepNhanLamViec(req, res) {
  return await updateDataObject(req, res,
    {
      tiep_nhan_lam_viec: false,
      cho_phep_cong_tac: false,
      thoi_gian_cho_phep_cong_tac: null,
    },
    { tiep_nhan_lam_viec: 1, cho_phep_cong_tac: 1, thoi_gian_cho_phep_cong_tac: 1 });
}

export async function xacNhanCapPhieu(req, res) {
  return await updateDataObject(req, res,
    {
      xac_nhan_cap_phieu: true,
      thoi_gian_cap_phieu: new Date(),
      nguoi_cap_phieu_id: req.user._id,
    },
    { xac_nhan_cap_phieu: 1, thoi_gian_cap_phieu: 1, nguoi_cap_phieu_id: 1 });
}

export async function huyCapPhieu(req, res) {
  return await updateDataObject(req, res,
    {
      xac_nhan_cap_phieu: false,
      thoi_gian_cap_phieu: null,
      nguoi_cap_phieu_id: null,
    },
    { xac_nhan_cap_phieu: 1, thoi_gian_cap_phieu: 1, nguoi_cap_phieu_id: 1 });
}

export async function choPhepCongTac(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const checkCatDien = await ThuTucCatDienService
      .getOne({ phieu_cong_tac_id: id, da_cat_dien: false, is_deleted: false }, { _id: 1 });
    if (checkCatDien) {
      return responseHelper.error(res, { message: t('not_complete_power_cut') });
    }

    const checkTiepDat = await ThuTucTiepDatService
      .getOne({ phieu_cong_tac_id: id, da_thuc_hien: false, is_deleted: false }, { _id: 1 });
    if (checkTiepDat) {
      return responseHelper.error(res, { message: t('not_complete_grounding') });
    }

    const checkRaoChan = await ThuTucRaoChanBienBaoService
      .getOne({ phieu_cong_tac_id: id, da_thuc_hien: false, is_deleted: false }, { _id: 1 });
    if (checkRaoChan) {
      return responseHelper.error(res, { message: t('not_complete_barriers_signs') });
    }

    const checkPhamViLamViec = await ThuTucPhamViLamViecService
      .getOne({ phieu_cong_tac_id: id, da_thuc_hien: false, is_deleted: false }, { _id: 1 });
    if (checkPhamViLamViec) {
      return responseHelper.error(res, { message: t('not_complete_scope_job') });
    }

    const checkCanhBao = await ThuTucCanhBaoChiDanService
      .getOne({ phieu_cong_tac_id: id, da_thuc_hien: false, is_deleted: false }, { _id: 1 });
    if (checkCanhBao) {
      return responseHelper.error(res, { message: t('not_complete_warnings_instructions') });
    }

    return await updateDataObject(req, res,
      { cho_phep_cong_tac: true, thoi_gian_cho_phep_cong_tac: new Date() },
      { cho_phep_cong_tac: 1, thoi_gian_cho_phep_cong_tac: 1 });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function huyChoPhepCongTac(req, res) {
  return await updateDataObject(req, res,
    { cho_phep_cong_tac: false, thoi_gian_cho_phep_cong_tac: null },
    { cho_phep_cong_tac: 1, thoi_gian_cho_phep_cong_tac: 1 });
}

export async function updateDataObject(req, res, objectData, projection = {}) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, objectData, { new: true, projection })
      .populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseHelper.error(res, err, 500);
  }
}


export async function anhBienPhapAnToan(req, res) {
  const { error } = Service.validate(req.body);
  if (error) return responseHelper.error(res, error, 400);
  const { id } = req.params;
  req.body.phieu_cong_tac_id = id;
  req.body.loai_cong_tac = LOAI_CONG_TAC.BIEN_PHAP_AN_TOAN;
  return await createImage(req, res);
}

export async function anhLamThemBienPhapAnToan(req, res) {
  const { error } = Service.validate(req.body);
  if (error) return responseHelper.error(res, error, 400);
  const { id } = req.params;
  req.body.phieu_cong_tac_id = id;
  req.body.loai_cong_tac = LOAI_CONG_TAC.LAM_THEM_BIEN_PHAP_AN_TOAN;
  return await createImage(req, res);
}

export async function anhRutBienPhapAnToan(req, res) {
  const { error } = Service.validate(req.body);
  if (error) return responseHelper.error(res, error, 400);
  const { id } = req.params;
  req.body.phieu_cong_tac_id = id;
  req.body.loai_cong_tac = LOAI_CONG_TAC.RUT_BIEN_PHAP_AN_TOAN;
  return await createImage(req, res);
}

export async function createImage(req, res) {
  try {
    //find folder_path by month
    const sub_folder_path = path.join(STORE_DIRS.IMAGE);
    const folder_path = getFileDirByMonth(sub_folder_path);

    const { imageId, thumbnailId } = await functionCommons.generateImage(req, false, folder_path);
    const body = req.body;
    body.image_id = imageId;
    body.thumbnail_id = thumbnailId;
    body.folder_path = folder_path;
    const data = await AnhCongTacService.create(body);
    const createdData = await AnhCongTacService.getById(data._id);
    return responseHelper.success(res, createdData);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

