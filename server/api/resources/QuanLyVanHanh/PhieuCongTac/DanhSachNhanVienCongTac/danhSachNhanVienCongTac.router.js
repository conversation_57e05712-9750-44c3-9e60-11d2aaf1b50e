import express from 'express';
import passport from 'passport';
import * as controller from './danhSachNhanVienCongTac.controller';
import { loggerMiddleware } from '../../../../logs/middleware';

export const danhSach<PERSON>hanVienCongTacRouter = express.Router();
danhSachNhanVienCongTacRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
danhSachNhanVienCongTacRouter.route('/')
  .get(controller.getAll)
  .post(controller.create);

danhSachNhanVienCongTacRouter.route('/multi')
  .post(controller.createMulti);

danhSachNhanVienCongTacRouter
  .route('/:id/xacnhanvao')
  .put(controller.kyXacNhanVaoViTri);

danhSachNhanVienCongTacRouter
  .route('/:id/huyxacnhanvao')
  .put(controller.huyKyXacNhanVaoViTri);

danhSachNhanVienCongTacRouter
  .route('/:id/xacnhanra')
  .put(controller.kyXacNhanRaKhoiViTri);

danhSachNhanVienCongTacRouter
  .route('/:id/huyxacnhanra')
  .put(controller.huyKyXacNhanRaKhoiViTri);

danhSachNhanVienCongTacRouter
  .route('/:id')
  .get(controller.findOne)
  .delete(controller.remove)
  .put(controller.update);
