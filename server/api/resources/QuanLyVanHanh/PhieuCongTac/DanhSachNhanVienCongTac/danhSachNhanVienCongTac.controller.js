import * as PhieuCongTacService from '../phieuCongTac.service';
import * as Service from './danhSachNhanVienCongTac.service';
import * as controllerHelper from '../../../../helpers/controllerHelper';
import * as responseAction from '../../../../helpers/responseHelper';
import * as responseHelper from '../../../../helpers/responseHelper';
import CommonError from '../../../../error/CommonError';
import NGUOI_CONG_TAC from '../../NguoiCongTac/nguoiCongTac.model';
import Error from '../../../RBAC/Error';

const populateOpts = [
  { path: 'user_id', select: 'full_name username phone bac_an_toan' },
  { path: 'don_vi_id', select: 'ten_don_vi' },
];

export const findOne = controllerHelper.createFindOneFunction(NGUOI_CONG_TAC, populateOpts);
export const update = controllerHelper.createUpdateByIdFunction(NGUOI_CONG_TAC, Service, populateOpts);
export const create = controllerHelper.createCreateFunction(NGUOI_CONG_TAC, Service, populateOpts);
export const getAll = controllerHelper.createGetAllFunction(NGUOI_CONG_TAC, null, populateOpts);

export async function remove(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const currentData = await Service.getOne({ _id: id }, { xac_nhan_vao: 1 });
    if (currentData.xac_nhan_vao) {
      return responseHelper.error(res, { message: t('cant_delete_employee_confirm') });
    }
    const data = await NGUOI_CONG_TAC.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function createMulti(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    value.forEach(item => {
      item.nguoi_tao = req.user._id;
      item.nguoi_chinh_sua = req.user._id;
    });
    const data = await NGUOI_CONG_TAC.create(value);
    const dataReturn = await NGUOI_CONG_TAC.populate(data, populateOpts);
    return responseAction.success(res, dataReturn);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

async function checkDangTiepNhan(req) {
  const { id } = req.params;
  const currentData = await Service.getOne({ _id: id }, { phieu_giao_viec_id: 1 });
  if (!currentData) {
    return { success: false, message: CommonError.NOT_FOUND };
  }
  const phieuCongTacData = await PhieuCongTacService.getOne({ phieu_giao_viec_id: currentData.phieu_giao_viec_id });
  if (phieuCongTacData.ket_thuc_cong_tac || !phieuCongTacData.tiep_nhan_lam_viec) {
    return { success: false, message: Error.INSUFFICIENT_PERMISSION };
  }
  return { success: true };
}

export async function kyXacNhanVaoViTri(req, res) {
  const { error, value } = Service.validate(req.body);
  if (error) return res.status(400).json(error.details);
  const dataUpdate = { xac_nhan_vao: true, thoi_gian_vao: value.thoi_gian_vao || new Date() };
  return await kyXacNhan(req, res, dataUpdate);
}

export async function huyKyXacNhanVaoViTri(req, res) {
  const { error } = Service.validate(req.body);
  if (error) return res.status(400).json(error.details);
  const dataUpdate = { xac_nhan_vao: false, thoi_gian_vao: null };
  return await kyXacNhan(req, res, dataUpdate);
}

export async function kyXacNhanRaKhoiViTri(req, res) {
  const { id } = req.params;
  const { error, value } = Service.validate(req.body);
  if (error) return res.status(400).json(error.details);
  const currentData = await Service.getOne({ _id: id }, { xac_nhan_vao: 1 });
  if (!currentData.xac_nhan_vao) {
    return responseHelper.error(res, { message: t('not_confirm_work_position_not_confirm_leave_work_position') });
  }

  const dataUpdate = { xac_nhan_ra: true, thoi_gian_ra: value.thoi_gian_ra || new Date() };
  return await kyXacNhan(req, res, dataUpdate);
}

export async function huyKyXacNhanRaKhoiViTri(req, res) {
  const { error } = Service.validate(req.body);
  if (error) return res.status(400).json(error.details);
  const dataUpdate = { xac_nhan_ra: false, thoi_gian_ra: null };
  return await kyXacNhan(req, res, dataUpdate);
}

export async function kyXacNhan(req, res, dataUpdate) {
  try {
    const { id } = req.params;
    const isDangTiepNhan = await checkDangTiepNhan(req);
    if (!isDangTiepNhan.success) {
      return responseAction.error(res, isDangTiepNhan.message);
    }
    const data = await NGUOI_CONG_TAC.findOneAndUpdate({ _id: id }, dataUpdate, { new: true })
      .populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}
