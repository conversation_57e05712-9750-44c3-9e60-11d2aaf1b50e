import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { PHIEU_CONG_TAC, THU_TUC_RAO_CHAN_BIEN_BAO, VI_TRI } from '../../../../constant/dbCollections';

const schema = new Schema({
  phieu_cong_tac_id: { type: Schema.Types.ObjectId, ref: PHIEU_CONG_TAC },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  thoi_gian: { type: Date },
  rao_chan_bien_bao: { type: String },
  da_thuc_hien: { type: Boolean, default: false },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
    updatedAt: 'thoi_gian_cap_nhat',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(THU_TUC_RAO_CHAN_BIEN_BAO, schema, THU_TUC_RAO_CHAN_BIEN_BAO);
