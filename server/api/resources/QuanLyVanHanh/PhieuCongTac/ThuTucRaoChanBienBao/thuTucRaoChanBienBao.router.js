import express from 'express';
import passport from 'passport';
import * as controller from './thuTucRaoChanBienBao.controller';
import { loggerMiddleware } from '../../../../logs/middleware';

export const thuTucRaoChanBienBaoRouter = express.Router();
thuTucRaoChanBienBaoRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
thuTucRaoChanBienBaoRouter.route('/')
  .get(controller.getAll)
  .post(controller.create);

thuTucRaoChanBienBaoRouter
  .route('/:id/thoigian')
  .put(controller.updateThoiGian);

thuTucRaoChanBienBaoRouter
  .route('/:id/xacnhan')
  .put(controller.xacNhanThucHien);

thuTucRaoChanBienBaoRouter
  .route('/:id/huyxacnhan')
  .put(controller.huyXacNhanThucHien);

thuTucRaoChanBienBaoRouter
  .route('/:id')
  .get(controller.findOne)
  .delete(controller.remove)
  .put(controller.update);
