import * as ValidatorHelper from '../../../../helpers/validatorHelper';
import THU_TUC_RAO_CHAN_BIEN_BAO from './thuTucRaoChanBienBao.model';

const Joi = require('joi');

const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return THU_TUC_RAO_CHAN_BIEN_BAO.create(value);
}

export async function createMulti(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    return THU_TUC_RAO_CHAN_BIEN_BAO.create(value);
  }
}

export async function getOne(query, projection = {}) {
  return THU_TUC_RAO_CHAN_BIEN_BAO.findOne(query, projection).lean();
}

export function getAll(query, projection = {}) {
  return THU_TUC_RAO_CHAN_BIEN_BAO.find(query, projection).lean();
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await THU_TUC_RAO_CHAN_BIEN_BAO.findByIdAndUpdate(value._id, value);
  }
}

export async function deleteAll(data) {
  for (const row of data) {
    const { error, value } = validate(row);
    if (error) throw error;
    await THU_TUC_RAO_CHAN_BIEN_BAO.findByIdAndUpdate(value._id, { is_deleted: true });
  }
}

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  if (Array.isArray(data)) {
    let validateError = null;
    data.find(itemData => {
      const { value, error } = schema.validate(itemData, { allowUnknown: true, abortEarly: true });
      if (error) validateError = error;
      return error;
    });
    if (validateError && validateError.details) {
      return { validateError };
    }
    return { value: data };
  } else {
    const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
    if (error && error.details) {
      return { error };
    }
    return { value };
  }
}
