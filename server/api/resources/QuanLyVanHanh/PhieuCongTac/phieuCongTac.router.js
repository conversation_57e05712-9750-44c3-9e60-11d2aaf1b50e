import express from 'express';
import passport from 'passport';
import * as Controller from './phieuCongTac.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import GiaoViecPermission from '../../RBAC/permissions/GiaoViecPermission';
import { bodyInjectionMiddleware } from '../../../middlewares/bodyInjectionMiddleware';
import { checkTempFolder, multipartMiddleware } from '../../../utils/fileUtils';
import { loggerMiddleware } from '../../../logs/middleware';
import { huyPhieu, lyDoHuyPhieu } from './phieuCongTac.controller';

export const phieuCongTacRouter = express.Router();
phieuCongTacRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

const authorizationPost = authorizationMiddleware([GiaoViecPermission.UPDATE]);
phieuCongTacRouter
  .route('/:id/anhbienphapantoan').post(authorizationPost, checkTempFolder, multipartMiddleware, Controller.anhBienPhapAnToan);
phieuCongTacRouter
  .route('/:id/anhlamthembienphapantoan').post(authorizationPost, checkTempFolder, multipartMiddleware, Controller.anhLamThemBienPhapAnToan);
phieuCongTacRouter
  .route('/:id/anhrutbienphapantoan').post(authorizationPost, checkTempFolder, multipartMiddleware, Controller.anhRutBienPhapAnToan);

phieuCongTacRouter.post('*', authorizationMiddleware([GiaoViecPermission.CREATE]), bodyInjectionMiddleware);
phieuCongTacRouter.get('*', authorizationMiddleware([GiaoViecPermission.READ]));
phieuCongTacRouter.put('*', authorizationMiddleware([GiaoViecPermission.UPDATE]));
phieuCongTacRouter.delete('*', authorizationMiddleware([GiaoViecPermission.DELETE]));
phieuCongTacRouter
  .route('/')
  .post(Controller.create);

phieuCongTacRouter
  .route('/phieugiaoviec/:id')
  .get(Controller.getByPhieuGiaoViecId);

phieuCongTacRouter
  .route('/:id/xacnhankiemtrabienphapantoan')
  .put(Controller.xacNhanKiemTraBienPhapAnToan);
phieuCongTacRouter
  .route('/:id/huyxacnhankiemtrabienphapantoan')
  .put(Controller.huyXacNhanKiemTraBienPhapAnToan);
phieuCongTacRouter
  .route('/:id/xacnhancanlamthembienphapantoan')
  .put(Controller.xacNhanCanLamThemBienPhapAnToan);
phieuCongTacRouter
  .route('/:id/huyxacnhancanlamthembienphapantoan')
  .put(Controller.huyXacNhanCanLamThemBienPhapAnToan);

phieuCongTacRouter
  .route('/:id/thoigianbatdau')
  .put(Controller.updateThoiGianBatDau);
phieuCongTacRouter
  .route('/:id/thoigianketthuc')
  .put(Controller.updateThoiGianKetThuc);
phieuCongTacRouter
  .route('/:id/thoigianhoanthanh')
  .put(Controller.thoiGianHoanThanh);
phieuCongTacRouter
  .route('/:id/thoigiankhoaphieu')
  .put(Controller.updateThoiGianKhoaPhieu);


phieuCongTacRouter
  .route('/:id/lanhdaoxacnhantiepnhan')
  .put(Controller.lanhDaoXacNhanTiepNhan);
phieuCongTacRouter
  .route('/:id/lanhdaohuyxacnhantiepnhan')
  .put(Controller.lanhDaoHuyXacNhanTiepNhan);

phieuCongTacRouter
  .route('/:id/chihuyxacnhantiepnhan')
  .put(Controller.chiHuyXacNhanTiepNhan);
phieuCongTacRouter
  .route('/:id/chihuyhuyxacnhantiepnhan')
  .put(Controller.chiHuyHuyXacNhanTiepNhan);

phieuCongTacRouter
  .route('/:id/giamsatxacnhantiepnhan')
  .put(Controller.giamSatXacNhanTiepNhan);
phieuCongTacRouter
  .route('/:id/giamsathuyxacnhantiepnhan')
  .put(Controller.giamSatHuyXacNhanTiepNhan);

phieuCongTacRouter
  .route('/:id/tiepnhanlamviec').put(Controller.tiepNhanLamViec);
phieuCongTacRouter
  .route('/:id/huytiepnhanlamviec').put(Controller.huyTiepNhanLamViec);

phieuCongTacRouter
  .route('/:id/chophepvatiepnhanlamviec').put(Controller.choPhepVaTiepNhanLamViec);
phieuCongTacRouter
  .route('/:id/huychophepvatiepnhanlamviec').put(Controller.huyChoPhepVaTiepNhanLamViec);

phieuCongTacRouter
  .route('/:id/hoanthanhcongtac')
  .put(Controller.hoanThanhCongTac);
phieuCongTacRouter
  .route('/:id/huyhoanthanhcongtac')
  .put(Controller.huyHoanThanhCongTac);


// phieuCongTacRouter
//   .route('/:id/chihuyxacnhanketthuc').put(Controller.chiHuyXacNhanKetThuc);
// phieuCongTacRouter
//   .route('/:id/chihuyhuyxacnhanketthuc').put(Controller.chiHuyHuyXacNhanKetThuc);

phieuCongTacRouter
  .route('/:id/ketthuccongtac')
  .put(Controller.ketThucCongTac);
phieuCongTacRouter
  .route('/:id/huyketthuccongtac')
  .put(Controller.huyKetThucCongTac);

phieuCongTacRouter
  .route('/:id/lanhdaoxacnhanketthuc')
  .put(Controller.lanhDaoXacNhanKetThuc);
phieuCongTacRouter
  .route('/:id/lanhdaohuyxacnhanketthuc')
  .put(Controller.lanhDaoHuyXacNhanKetThuc);

phieuCongTacRouter
  .route('/:id/xacnhankhoaphieu')
  .put(Controller.xacNhanKhoaPhieu);
phieuCongTacRouter
  .route('/:id/huyXacNhanKhoaPhieu')
  .put(Controller.huyXacNhanKhoaPhieu);

phieuCongTacRouter
  .route('/:id/huyphieu')
  .put(Controller.huyPhieu);

phieuCongTacRouter
  .route('/:id/lydohuyphieu')
  .put(Controller.lyDoHuyPhieu);

phieuCongTacRouter
  .route('/:id/xacnhancapphieu').put(Controller.xacNhanCapPhieu);
phieuCongTacRouter
  .route('/:id/huycapphieu').put(Controller.huyCapPhieu);
phieuCongTacRouter
  .route('/:id/chophepcongtac').put(Controller.choPhepCongTac);
phieuCongTacRouter
  .route('/:id/huychophepcongtac').put(Controller.huyChoPhepCongTac);

phieuCongTacRouter
  .route('/:id/updatemobile')
  .put(Controller.updateMobile);

phieuCongTacRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
