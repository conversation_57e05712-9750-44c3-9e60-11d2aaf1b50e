import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { BIEN_PHAP_AN_TOAN_BO_SUNG, PHIEU_CONG_TAC, USER, VI_TRI } from '../../../../constant/dbCollections';

const schema = new Schema({
  phieu_cong_tac_id: { type: Schema.Types.ObjectId, ref: PHIEU_CONG_TAC },
  thoi_gian: { type: Date },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  bien_phap_an_toan: { type: String },
  da_thuc_hien: { type: Boolean, default: false },
  nguoi_tao: { type: Schema.Types.ObjectId, ref: USER },
  nguoi_chinh_sua: { type: Schema.Types.ObjectId, ref: USER },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
    updatedAt: 'thoi_gian_cap_nhat',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(BIEN_PHAP_AN_TOAN_BO_SUNG, schema, BIEN_PHAP_AN_TOAN_BO_SUNG);
