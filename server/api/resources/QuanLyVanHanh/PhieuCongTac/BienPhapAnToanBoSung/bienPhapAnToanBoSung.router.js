import express from 'express';
import passport from 'passport';
import * as controller from './bienPhapAnToanBoSung.controller';
import { loggerMiddleware } from '../../../../logs/middleware';

export const bienPhapAnToanBoSungRouter = express.Router();
bienPhapAnToanBoSungRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
bienPhapAnToanBoSungRouter.route('/')
  .get(controller.getAll)
  .post(controller.create);

bienPhapAnToanBoSungRouter
  .route('/:id/thoigian')
  .put(controller.updateThoiGianXuLy);

bienPhapAnToanBoSungRouter
  .route('/:id/xacnhan')
  .put(controller.xacNhanThucHien);

bienPhapAnToanBoSungRouter
  .route('/:id/huyxacnhan')
  .put(controller.huyXacNhanThucHien);

bienPhapAnToanBoSungRouter
  .route('/:id')
  .get(controller.findOne)
  .delete(controller.remove)
  .put(controller.update);
