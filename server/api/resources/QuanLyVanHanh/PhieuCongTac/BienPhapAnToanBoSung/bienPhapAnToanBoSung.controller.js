import * as Service from './bienPhapAnToanBoSung.service';
import Model from './bienPhapAnToanBoSung.model';
import * as controllerHelper from '../../../../helpers/controllerHelper';
import * as responseAction from '../../../../helpers/responseHelper';
import * as responseHelper from '../../../../helpers/responseHelper';
import CommonError from '../../../../error/CommonError';

const populateOpts = [
  { path: 'vi_tri_id', select: 'ten_vi_tri' },
];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, null, populateOpts);

export async function xacNhanThuc<PERSON>ien(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return res.status(400).json(error.details);
    const dataUpdate = {
      da_thuc_hien: true,
      thoi_gian: value.thoi_gian || new Date(),
    };
    const data = await Model.findOneAndUpdate({ _id: id }, dataUpdate, { new: true })
      .populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function huyXacNhanThucHien(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { da_thuc_hien: false }, { new: true })
      .populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function updateThoiGianXuLy(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const data = await Model.findOneAndUpdate({ _id: id }, { thoi_gian: value.thoi_gian }, { new: true })
      .populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}
