import * as ValidatorHelper from '../../../helpers/validatorHelper';
import KET_QUA_SUA_CHUA_KHONG_KE_HOACH from './ketQuaSuaChuaKhongKeHoach.model';

const Joi = require('joi');

const objSchema = Joi.object({});

export async function createMany(dataList = []) {
  if (!Array.isArray(dataList)) {
    throw new Error('Input must be an array');
  }

  const validRecords = [];

  for (const data of dataList) {
    const { error, value } = validate(data);
    if (!error) {
      validRecords.push(value);
    }
  }

  return KET_QUA_SUA_CHUA_KHONG_KE_HOACH.insertMany(validRecords);
}

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return KET_QUA_SUA_CHUA_KHONG_KE_HOACH.create(value);
}

export function getAll(query, projection = {}) {
  return KET_QUA_SUA_CHUA_KHONG_KE_HOACH.find(query, projection).lean();
}

export function aggregate(pipeline = [], cb) {
  return KET_QUA_SUA_CHUA_KHONG_KE_HOACH.aggregate(pipeline, cb);
}

export async function updateAll(dataUpdate) {
  const results = [];
  for (const row of dataUpdate) {
    try {
      const { error, value } = validate(row);
      if (!error) {
        const updated = await KET_QUA_SUA_CHUA_KHONG_KE_HOACH.findByIdAndUpdate(value._id, value, { new: true });
        if (updated) results.push(updated);
      }
    } catch {
    }
  }
  return results;
}

export async function findByIdAndUpdate(data) {
  return KET_QUA_SUA_CHUA_KHONG_KE_HOACH.findByIdAndUpdate(data._id, data, { new: true });
}

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};
