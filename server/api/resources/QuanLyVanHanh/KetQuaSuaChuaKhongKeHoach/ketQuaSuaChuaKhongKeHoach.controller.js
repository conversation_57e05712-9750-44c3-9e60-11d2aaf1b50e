import * as Service from './ketQuaSuaChuaKhongKeHoach.service';
import Model from './ketQuaSuaChuaKhongKeHoach.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';

const populateOpts = [
  { path: 'anh_vi_tris', populate: { path: 'nguoi_tao', select: 'full_name' } },
  { path: 'vi_tri_id', select: 'ten_vi_tri' },
  { path: 'duong_day_id' },
  { path: 'khoang_cot_id', populate: 'vi_tri_ket_thuc_id vi_tri_bat_dau_id' },
  { path: 'cong_viec_id' },
];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts);

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const data = await Model.find(criteria)
      .populate(populateOpts);
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}
