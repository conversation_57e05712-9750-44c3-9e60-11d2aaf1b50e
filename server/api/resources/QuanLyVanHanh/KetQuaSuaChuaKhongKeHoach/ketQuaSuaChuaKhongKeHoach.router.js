import express from 'express';
import passport from 'passport';
import * as Controller from './ketQuaSuaChuaKhongKeHoach.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import WorkPermission from '../../RBAC/permissions/WorkPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const ketQuaSuaChuaKhongKeHoachRouter = express.Router();
ketQuaSuaChuaKhongKeHoachRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
ketQuaSuaChuaKhongKeHoachRouter.post('*', authorizationMiddleware([WorkPermission.CREATE]));
ketQuaSuaChuaKhongKeHoachRouter.put('*', authorizationMiddleware([WorkPermission.UPDATE]));
ketQuaSuaChuaKhongKeHoachRouter.delete('*', authorizationMiddleware([WorkPermission.DELETE]));
ketQuaSuaChuaKhongKeHoachRouter.route('/')
  .get(Controller.getAll)
  .post(Controller.create);

ketQuaSuaChuaKhongKeHoachRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
