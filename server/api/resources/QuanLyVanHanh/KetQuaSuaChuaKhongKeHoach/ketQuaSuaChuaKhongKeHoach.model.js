import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import {
  ANH_VI_TRI,
  DANH_MUC_CONG_VIEC,
  KET_QUA_SUA_CHUA_KHONG_KE_HOACH,
  PHIEU_GIAO_VIEC,
  USER,
  VI_TRI,
  KHOANG_COT, DUONG_DAY,
} from '../../../constant/dbCollections';

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  duong_day_id: [{ type: Schema.Types.ObjectId, ref: DUONG_DAY }],
  khoang_cot_id: { type: Schema.Types.ObjectId, ref: KHOANG_COT },
  cong_viec_id: { type: Schema.Types.ObjectId, ref: DANH_MUC_CONG_VIEC },
  noi_dung_chi_tiet: String,
  nguoi_tao: { type: Schema.Types.ObjectId, ref: USER },
  nguoi_chinh_sua: { type: Schema.Types.ObjectId, ref: USER },
  anh_vi_tris: [{ type: Schema.Types.ObjectId, ref: ANH_VI_TRI }],
  khoi_luong: { type: Number },
  thoi_gian_cap_nhat: { type: Date, default: Date.now },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized compound indexes for KetQuaSuaChuaKhongKeHoach queries
// Using background: true for better performance during index creation

// Primary index for phieu_giao_viec_id filtering with deletion status
schema.index({ 
  phieu_giao_viec_id: 1, 
  is_deleted: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  name: 'idx_phieu_giao_viec_deleted_time'
});

// Index for duong_day_id filtering (array field)
schema.index({ 
  duong_day_id: 1, 
  phieu_giao_viec_id: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_duong_day_phieu_deleted'
});

// Index for vi_tri_id queries with work ticket reference
schema.index({ 
  vi_tri_id: 1, 
  phieu_giao_viec_id: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_vi_tri_phieu_deleted'
});

// Index for khoang_cot_id queries with work ticket reference
schema.index({ 
  khoang_cot_id: 1, 
  phieu_giao_viec_id: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_khoang_cot_phieu_deleted'
});

// Index for cong_viec_id filtering
schema.index({ 
  cong_viec_id: 1, 
  is_deleted: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  name: 'idx_cong_viec_deleted_time'
});

// Index for nguoi_tao queries with time sorting
schema.index({ 
  nguoi_tao: 1, 
  thoi_gian_tao: -1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_nguoi_tao_time_deleted'
});

// Index for thoi_gian_cap_nhat queries
schema.index({ 
  thoi_gian_cap_nhat: -1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_cap_nhat_deleted'
});

// Compound index for location-based queries (vi_tri or khoang_cot)
schema.index({ 
  vi_tri_id: 1, 
  khoang_cot_id: 1,
  is_deleted: 1
}, { 
  background: true,
  sparse: true,
  name: 'idx_vi_tri_khoang_cot_deleted'
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(KET_QUA_SUA_CHUA_KHONG_KE_HOACH, schema, KET_QUA_SUA_CHUA_KHONG_KE_HOACH);
