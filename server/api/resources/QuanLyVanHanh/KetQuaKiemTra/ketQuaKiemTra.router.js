import express from 'express';
import passport from 'passport';
import * as Controller from './ketQuaKiemTra.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import WorkPermission from '../../RBAC/permissions/WorkPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const ketQuaKiemTraRouter = express.Router();
ketQuaKiemTraRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
ketQuaKiemTraRouter.post('*', authorizationMiddleware([WorkPermission.CREATE]));
ketQuaKiemTraRouter.put('*', authorizationMiddleware([WorkPermission.UPDATE]));
ketQuaKiemTraRouter.delete('*', authorizationMiddleware([WorkPermission.DELETE]));
ketQuaKiemTraRouter.route('/')
  .get(Controller.getAll)
  .post(Controller.create);

ketQuaKiemTraRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
