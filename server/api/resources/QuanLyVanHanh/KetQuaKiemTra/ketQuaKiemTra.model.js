import mongoose, { Schem<PERSON> } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import {
  ANH_VI_TRI,
  BAT_THUONG_PHAT_HIEN,
  CACH_DIEN,
  COT_DIEN,
  DAY_CAP_QUANG,
  DAY_CHONG_SET,
  DAY_DAN,
  DON_VI,
  DUONG_DAY,
  GIAO_CHEO,
  KET_QUA_KIEM_TRA,
  KHOANG_COT,
  PHIEU_GIAO_VIEC,
  TIEP_DAT,
  TIEU_CHI,
  TON_TAI_CAP_TREN,
  USER,
  VI_TRI,
} from '../../../constant/dbCollections';
import { TRANG_THAI_XU_LY } from '../../DanhMuc/TrangThaiXuLy';
import { NHOM_CONG_VIEC } from '../../DanhMuc/LoaiCongViec';

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  khoang_cot_id: { type: Schema.Types.ObjectId, ref: KHOANG_COT },
  nguoi_tao: { type: Schema.Types.ObjectId, ref: USER },
  nguoi_chinh_sua: { type: Schema.Types.ObjectId, ref: USER },
  ref: { type: Schema.Types.ObjectId, ref: BAT_THUONG_PHAT_HIEN },
  anh_vi_tris: [{ type: Schema.Types.ObjectId, ref: ANH_VI_TRI }],
  from_ai: { type: Boolean, default: false },

  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  duong_day_id: [{ type: Schema.Types.ObjectId, ref: DUONG_DAY }],

  tieu_chi_id: { type: Schema.Types.ObjectId, ref: TIEU_CHI, required: true },

  // nguoi phan cong kiem tra
  khoi_luong: { type: String },
  noi_dung_chi_tiet: { type: String },
  de_xuat_to_cong_tac: { type: String },
  thoi_han_xu_ly_to_cong_tac: { type: Date },
  khoang_cach_nga_do_gan_nhat: { type: Number },         // nhập khi tiêu chí đánh giá là quản lý cây cao

  // doi ttd
  de_xuat_cap_doi: { type: String },
  thoi_gian_de_xuat_cap_doi: { type: Date },
  thoi_han_xu_ly_cap_doi: { type: Date },

  //ttd
  ke_hoach_phe_duyet: { type: String },
  thoi_gian_phe_duyet: { type: Date },
  thoi_han_xu_ly: { type: Date },

  tinh_trang_xu_ly: {
    type: String,
    enum: Object.keys(TRANG_THAI_XU_LY),
  },
  thoi_gian_cap_nhat: { type: Date, default: Date.now },

  cach_dien_id: { type: Schema.Types.ObjectId, ref: CACH_DIEN },
  day_dan_id: { type: Schema.Types.ObjectId, ref: DAY_DAN },
  day_chong_set_id: { type: Schema.Types.ObjectId, ref: DAY_CHONG_SET },
  day_cap_quang_id: { type: Schema.Types.ObjectId, ref: DAY_CAP_QUANG },
  cot_dien_id: { type: Schema.Types.ObjectId, ref: COT_DIEN },
  giao_cheo_id: { type: Schema.Types.ObjectId, ref: GIAO_CHEO },
  tiep_dat_id: { type: Schema.Types.ObjectId, ref: TIEP_DAT },

  ton_tai_cap_tren_id: { type: Schema.Types.ObjectId, ref: TON_TAI_CAP_TREN },
  nhom_cong_viec: {
    type: String,
    enum: Object.keys(NHOM_CONG_VIEC),
  },

  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
// Optimized compound indexes for KET_QUA_KIEM_TRA queries
// Using background: true for better performance during index creation

// Primary compound index for phieu_giao_viec_id with soft delete (most common query)
schema.index({ 
  phieu_giao_viec_id: 1, 
  is_deleted: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  name: 'idx_phieu_giao_viec_deleted_time'
});

// Index for tieu_chi_id queries with work ticket reference
schema.index({ 
  tieu_chi_id: 1, 
  phieu_giao_viec_id: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_tieu_chi_phieu_deleted'
});

// Index for vi_tri_id queries with location filtering
schema.index({ 
  vi_tri_id: 1, 
  khoang_cot_id: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_vi_tri_khoang_cot_deleted'
});

// Index for duong_day_id filtering (array field)
schema.index({ 
  duong_day_id: 1, 
  don_vi_id: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_duong_day_don_vi_deleted'
});

// Index for tinh_trang_xu_ly filtering
schema.index({ 
  tinh_trang_xu_ly: 1, 
  phieu_giao_viec_id: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_tinh_trang_phieu_deleted'
});

// Index for nguoi_tao queries with time sorting
schema.index({ 
  nguoi_tao: 1, 
  thoi_gian_tao: -1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_nguoi_tao_time_deleted'
});

// Index for nhom_cong_viec filtering
schema.index({ 
  nhom_cong_viec: 1, 
  is_deleted: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  sparse: true,
  name: 'idx_nhom_cong_viec_deleted_time'
});

// Index for anh_vi_tris array queries
schema.index({ 
  anh_vi_tris: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_anh_vi_tris_deleted'
});

// Index for ref (BAT_THUONG_PHAT_HIEN) queries
schema.index({ 
  ref: 1, 
  is_deleted: 1
}, { 
  background: true,
  sparse: true,
  name: 'idx_ref_deleted'
});

// Index for thoi_gian_cap_nhat queries
schema.index({ 
  thoi_gian_cap_nhat: -1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_cap_nhat_deleted'
});

// Additional optimized indexes for getAll function performance
// Index for default sorting (thoi_gian_tao desc, don_vi_id asc) with soft delete
schema.index({ 
  is_deleted: 1,
  thoi_gian_tao: -1, 
  don_vi_id: 1
}, { 
  background: true,
  name: 'idx_deleted_time_donvi_sort'
});

// Index for time range queries (tu_ngay/den_ngay filtering)
schema.index({ 
  thoi_gian_tao: 1, 
  is_deleted: 1,
  phieu_giao_viec_id: 1
}, { 
  background: true,
  name: 'idx_time_range_deleted_phieu'
});

// Index for phuong_phap_thuc_hien filtering via phieu_giao_viec_id
schema.index({ 
  phieu_giao_viec_id: 1, 
  is_deleted: 1,
  don_vi_id: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  name: 'idx_phieu_deleted_donvi_time'
});

// Index for ton_tai_cap_tren_id queries with soft delete
schema.index({ 
  ton_tai_cap_tren_id: 1, 
  is_deleted: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  sparse: true,
  name: 'idx_ton_tai_cap_tren_deleted_time'
});

// Index for from_ai filtering with time sorting
schema.index({ 
  from_ai: 1, 
  is_deleted: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  name: 'idx_from_ai_deleted_time'
});

// Compound index for complex filtering scenarios
schema.index({ 
  is_deleted: 1,
  don_vi_id: 1,
  duong_day_id: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  name: 'idx_deleted_donvi_duongday_time'
});

schema.plugin(mongoosePaginate);

export { schema as DocumentSchema };
export default mongoose.model(KET_QUA_KIEM_TRA, schema, KET_QUA_KIEM_TRA);
