import * as Service from './ketQuaKiemTra.service';
import Model from './ketQuaKiemTra.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import { findOneById } from '../../../helpers/controllerHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';

const populateOpts = [
  { path: 'vi_tri_id', select: 'ten_vi_tri' },
  { path: 'duong_day_id', populate: 'duong_day_cu_id' },
  { path: 'vi_tri_id' },
  { path: 'khoang_cot_id', populate: 'vi_tri_bat_dau_id vi_tri_ket_thuc_id' },
  { path: 'anh_vi_tris' },
  { path: 'tieu_chi_id', populate: 'tieu_chi_cha_id' },
  { path: 'cach_dien_id' },
  { path: 'day_dan_id' },
  { path: 'day_chong_set_id' },
  { path: 'day_cap_quang_id' },
  { path: 'cot_dien_id' },
  { path: 'giao_cheo_id' },
  { path: 'tiep_dat_id' },
  { path: 'ton_tai_cap_tren_id', populate: 'don_vi_id duong_day_id nguoi_tao_id' },
  { path: 'ref', select: 'ai_status' },
];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);

export async function update(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    if (!value.vi_tri_id) {
      return responseHelper.error(res, {
        success: false,
        message: 'Vị trí không được bỏ trống',
      }, 400);
    }
    if (!value.thoi_gian_cap_nhat) {
      value.thoi_gian_cap_nhat = Date.now();
    }
    value.nguoi_chinh_sua = req.user._id;
    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true });
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    const updatedData = await findOneById(Model, id, populateOpts, true);
    return responseHelper.success(res, updatedData);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const { t } = req;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    if (!value.vi_tri_id) {
      return responseHelper.error(res, { success: false, message: t('location_cant_empty') }, 400);
    }

    value.nguoi_tao = req.user._id;
    value.nguoi_chinh_sua = req.user._id;
    const data = await Model.create(value);
    const createdData = await findOneById(Model, data._id, populateOpts, true);
    return responseHelper.success(res, createdData);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const data = await Model.find(criteria)
      .populate(populateOpts);
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}
