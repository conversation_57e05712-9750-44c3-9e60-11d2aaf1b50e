import moment from 'moment';
import i18next from 'i18next';

import * as ValidatorHelper from '../../../helpers/validatorHelper';
import { sendEmail } from '../../../utils/mailHelper';
import { formatDate } from '../../../common/formatUTCDateToLocalDate';
import { downlineData } from '../../../common/functionCommons';
import { getConfig } from '../../../../config/config';

import KetQuaKiemTraModel from './ketQuaKiemTra.model';
import DuongDayModel from '../../TongKe/DuongDay/duongDay.model';
import CaiDatVanHanhModel from '../../CaiDatVanHanh/caiDatVanHanh.model';
import UserModel from '../../User/user.model';

import { DON_VI } from '../../../constant/dbCollections';

import { DE_XUAT_XU_LY } from '../../DanhMuc/DeXuatXuLy';
import { KE_HOACH_PHE_DUYET, TRANG_THAI_XU_LY } from '../../DanhMuc/TrangThaiXuLy';
import { NOTIFICATION_ACTION, NOTIFICATION_TYPE } from '../../Notification/notification.constants';

import { extractIds, extractKeys, groupBy } from '../../../utils/dataconverter';

import * as KhoangCotService from '../../TongKe/KhoangCot/khoangCot.service';
import * as NotificationService from '../../Notification/notification.service';

import createBaseService from '../../../base/baseService';

const config = getConfig(process.env.NODE_ENV);

const Joi = require('joi');

const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return KetQuaKiemTraModel.create(value);
}
const baseService = createBaseService(KetQuaKiemTraModel);

export const createMulti = baseService.createMulti;
export const getAll = baseService.getAll;
export const getById = baseService.getById;
export const getOne = baseService.getOne;
export const getByIdAndUpdate = baseService.getByIdAndUpdate;


export function paginate(criteria, options = {}) {
  return KetQuaKiemTraModel.paginate(criteria, options);
}

export async function update(query, data) {
  return KetQuaKiemTraModel.update(query, data);
}

export async function updateMany(query, data) {
  return KetQuaKiemTraModel.updateMany(query, data);
}

export async function findByIdAndUpdate(data) {
  return KetQuaKiemTraModel.findByIdAndUpdate(data._id, data, { new: true });
}


export async function createMany(dataList = []) {
  if (!Array.isArray(dataList)) {
    throw new Error('Input must be an array');
  }

  const validRecords = [];

  for (const data of dataList) {
    const { error, value } = validate(data);
    if (!error) {
      validRecords.push(value);
    }
  }

  return KetQuaKiemTraModel.insertMany(validRecords);
}


export async function updateAll(dataUpdate, options = {}) {
  options.new = true;
  const results = [];
  for (const row of dataUpdate) {
    try {
      const { error, value } = validate(row);
      if (!error) {
        const updated = await KetQuaKiemTraModel.findByIdAndUpdate(value._id, value, options);
        if (updated) results.push(updated);
      }
    } catch {
    }
  }
  return results;
}

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};

export async function bangThongKeTheoDoiTonTai(criteria) {
  const dataThongKeTontai = await getAll(criteria)
    .populate([
      {
        path: 'duong_day_id',
        select: 'ten_duong_day duong_day_cu_id',
        populate: { path: 'duong_day_cu_id', select: 'ten_duong_day' },
      },
      {
        path: 'don_vi_id',
        select: 'ten_don_vi don_vi_cha_id',
        populate: { path: 'don_vi_cha_id', select: 'ten_don_vi' },
      },
      { path: 'phieu_giao_viec_id', populate: { path: 'chuyen_de_id', select: 'ten_chuyen_de' } },
      { path: 'vi_tri_id', select: 'ten_vi_tri' },
      { path: 'khoang_cot_id', select: 'ten_khoang_cot' },
      {
        path: 'tieu_chi_id', select: 'noi_dung_kiem_tra_id ten_tieu_chi tieu_chi_cha_id',
        populate: [
          { path: 'noi_dung_kiem_tra_id', select: 'ten_noi_dung' },
          { path: 'tieu_chi_cha_id', select: 'ten_tieu_chi' },
        ],
      },
      { path: 'nguoi_tao', select: 'full_name' },
    ]).sort({ thoi_gian_tao: -1 });


  function convertTonTaiToRows(tonTai, index) {
    let tenDuongDay;
    tonTai.duong_day_id?.forEach(duongday => {
      tenDuongDay = downlineData(tenDuongDay, `- ${duongday.ten_duong_day}`);
      if (duongday.duong_day_cu_id) {
        tenDuongDay = downlineData(tenDuongDay, `(Tách từ đường dây: ${duongday.duong_day_cu_id.ten_duong_day})`);
      }
    });

    return {
      stt: index + 1,
      duong_day_id: tenDuongDay,
      ten_chuyen_de: tonTai.phieu_giao_viec_id?.chuyen_de_id?.ten_chuyen_de,
      khoi_luong: tonTai.khoi_luong,
      nguoi_tao: tonTai.nguoi_tao?.full_name,
      ten_vi_tri: tonTai.vi_tri_id?.ten_vi_tri,
      khoang_cot_id: tonTai.khoang_cot_id?.ten_khoang_cot,
      don_vi_id: tonTai.don_vi_id,
      thoi_gian_tao: tonTai.thoi_gian_tao ? formatDate(tonTai.thoi_gian_tao) : '',
      thoi_gian_cap_nhat: tonTai.thoi_gian_cap_nhat ? formatDate(tonTai.thoi_gian_cap_nhat) : '',
      thoi_gian_de_xuat_cap_doi: tonTai.thoi_gian_de_xuat_cap_doi ? formatDate(tonTai.thoi_gian_de_xuat_cap_doi) : '',
      thoi_han_xu_ly_cap_doi: tonTai.thoi_han_xu_ly_cap_doi ? formatDate(tonTai.thoi_han_xu_ly_cap_doi) : '',
      thoi_gian_phe_duyet: tonTai.thoi_gian_phe_duyet ? formatDate(tonTai.thoi_gian_phe_duyet) : '',
      thoi_han_xu_ly: tonTai.thoi_han_xu_ly ? formatDate(tonTai.thoi_han_xu_ly) : '',
      thoi_han_xu_ly_to_cong_tac: tonTai.thoi_han_xu_ly_to_cong_tac ? formatDate(tonTai.thoi_han_xu_ly_to_cong_tac) : '',
      phieu_giao_viec_id: tonTai.phieu_giao_viec_id,
      noi_dung_chi_tiet: tonTai.noi_dung_chi_tiet,
      de_xuat_to_cong_tac: tonTai.de_xuat_to_cong_tac,
      de_xuat_cap_doi: DE_XUAT_XU_LY[tonTai.de_xuat_cap_doi]?.label,
      tinh_trang_xu_ly: TRANG_THAI_XU_LY[tonTai.tinh_trang_xu_ly]?.label,
      ke_hoach_phe_duyet: KE_HOACH_PHE_DUYET[tonTai.ke_hoach_phe_duyet]?.label,
      tieu_chi_id: tonTai.tieu_chi_id,
      thoi_gian_giao: tonTai.ket_qua_sua_chua?.thoi_gian_giao_phieu ? formatDate(tonTai.ket_qua_sua_chua?.thoi_gian_giao_phieu) : '',
      thoi_gian_hoan_thanh: tonTai.ket_qua_sua_chua?.thoi_gian_xac_nhan_khoa_phieu ? formatDate(tonTai.ket_qua_sua_chua?.thoi_gian_xac_nhan_khoa_phieu) : '',
    };
  }

  return dataThongKeTontai.map(convertTonTaiToRows);
}


export async function updateDuongDayId(duongDayCuId, vanHanh) {
  function changeDuongDayId(arrayData, oldId) {
    let resultArr = arrayData.filter(item => item.toString() !== oldId.toString());
    return [...resultArr, vanHanh.duong_day_id];
  }

  const khoangCots = await KhoangCotService.getAll({ vi_tri_id: vanHanh.vi_tri_id, is_deleted: false });

  const tonTais = await KetQuaKiemTraModel.find({
    $or: [{ vi_tri_id: vanHanh.vi_tri_id }, { khoang_cot_id: { $in: extractIds(khoangCots) } }],
    tinh_trang_xu_ly: TRANG_THAI_XU_LY.CHUA_XU_LY.code,
    duong_day_id: duongDayCuId,
  });

  KetQuaKiemTraModel.bulkWrite(
    tonTais.map((tonTai) =>
      ({
        updateOne: {
          filter: { _id: tonTai._id },
          update: { $set: { duong_day_id: changeDuongDayId(tonTai.duong_day_id, duongDayCuId) } },
          upsert: true,
        },
      }),
    ),
  );
}

export async function updateDuongDayIds(vanHanh) {
  const duongDayData = await DuongDayModel.findById(vanHanh.duong_day_id).lean();
  if (duongDayData.duong_day_cu_id) {
    const khoangCots = await KhoangCotService.getAll({ vi_tri_id: vanHanh.vi_tri_id, is_deleted: false });

    const tonTais = await KetQuaKiemTraModel.find({
      $or: [{ vi_tri_id: vanHanh.vi_tri_id }, { khoang_cot_id: { $in: extractIds(khoangCots) } }],
      tinh_trang_xu_ly: TRANG_THAI_XU_LY.CHUA_XU_LY.code,
      duong_day_id: duongDayData.duong_day_cu_id,
    });

    KetQuaKiemTraModel.bulkWrite(
      tonTais.map((tonTai) =>
        ({
          updateOne: {
            filter: { _id: tonTai._id },
            update: { $set: { duong_day_id: [...tonTai.duong_day_id, vanHanh.duong_day_id] } },
            upsert: true,
          },
        }),
      ),
    );
  }
}

async function sendEmailToUser(userData, tonTaiData) {
  if (!tonTaiData?.length) return;

  const mailOptions = {
    from: `Hệ thống Quản lý đường dây truyền tải điện <${config.mail.auth.user}>`,
    to: userData.email,
    subject: 'Tồn tại chưa xử lý',
    html: `<h2>Xin chào ${userData.full_name}</h2>
           <div>Đơn vị <strong>${userData.don_vi_id.ten_don_vi}</strong> của bạn hiện có <strong>${tonTaiData.length}</strong> tồn tại chưa xử lý</div>
           <div>Vui lòng kiểm tra tại 
           <a href="${config.host_admin}/theo-doi-xu-ly-ton-tai?don_vi_giao_phieu_id=${userData.don_vi_id?._id}" style="font-weight: 700">đây</a>
           </div>`,
  };

  return sendEmail(mailOptions);
}


export async function checkTonTai() {
  const caiDat = await CaiDatVanHanhModel.findOne({}, {
    vai_tro_nhan_thong_bao_ton_tai_id: 1,
    ngay_thong_bao_ton_tai: 1,
  }).sort({ created_at: -1 });

  if (parseInt(moment().format('DD')) !== parseInt(caiDat.ngay_thong_bao_ton_tai)) return;

  const tonTaiChuaXuLy = await KetQuaKiemTraModel.find({
      tinh_trang_xu_ly: { $ne: TRANG_THAI_XU_LY.DA_XU_LY.code },
      don_vi_id: { $exists: true },
      is_deleted: false,
    },
    { _id: 1, don_vi_id: 1 },
  ).lean();

  if (!tonTaiChuaXuLy.length) return;

  const tonTaiGroupByDonVi = groupBy(tonTaiChuaXuLy, 'don_vi_id');
  const donViId = extractKeys(tonTaiChuaXuLy, 'don_vi_id');

  const allUser = await UserModel.find(
    { role_id: { $in: caiDat.vai_tro_nhan_thong_bao_ton_tai_id }, don_vi_id: donViId, active: true },
    { full_name: 1, email: 1, don_vi_id: 1 })
    .populate({ path: 'don_vi_id', select: 'ten_don_vi' })
    .lean();

  if (!allUser?.length) return;

  for (let i = 0; i < allUser.length; i++) {
    const user = allUser[i];
    console.log(`---- Đang xử lý user [${i}]: ${user.email}`);

    if (user.don_vi_id?._id) {
      const data = tonTaiGroupByDonVi[user.don_vi_id._id];
      if (!data?.length) {
        console.log('❌ Không có tồn tại');
        continue;
      }

      try {
        NotificationService.notification(
          NOTIFICATION_TYPE.SYSTEM_TO_USER,
          DON_VI,
          user.don_vi_id,
          [user._id],
          NOTIFICATION_ACTION.TON_TAI_CHUA_XU_LY,
          { so_luong_ton_tai: data.length },
          'TonTaiChuaXyLy',
          i18next.t,
        );

        await sendEmailToUser(user, data);

        console.log(`✅ Gửi mail xong cho ${user.email}, chờ 200ms...`);
        await new Promise((r) => setTimeout(r, 200));
      } catch (err) {
        console.error('‼️ Lỗi khi gửi mail cho', user.email, err);
      }
    }
  }

  console.log('✅ Đã xử lý xong tất cả user.');

}
