import * as ValidatorHelper from '../../../helpers/validatorHelper';
import THIET_BI_PHAT_HIEN from './thietBiPhatHien.model';

const Joi = require('joi');

const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return THIET_BI_PHAT_HIEN.create(value);
}

export function getAll(query, projection = {}) {
  return THIET_BI_PHAT_HIEN.find(query, projection).lean();
}

export function getOne(query, projection = {}) {
  return THIET_BI_PHAT_HIEN.findOne(query, projection).lean();
}

export async function updateAll(dataUpdate) {
  for (const row of dataUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await THIET_BI_PHAT_HIEN.findByIdAndUpdate(value._id, value);
  }
}

export async function deleteAll(data) {
  for (const row of data) {
    const { error, value } = validate(row);
    if (error) throw error;
    await THIET_BI_PHAT_HIEN.findByIdAndUpdate(value._id, { is_deleted: true });
  }
}

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};
