import express from 'express';
import passport from 'passport';
import * as Controller from './thietBiPhatHien.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import WorkPermission from '../../RBAC/permissions/WorkPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const thietBiPhatHienRouter = express.Router();
thietBiPhatHienRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
thietBiPhatHienRouter.post('*', authorizationMiddleware([WorkPermission.CREATE]));
thietBiPhatHienRouter.put('*', authorizationMiddleware([WorkPermission.UPDATE]));
thietBiPhatHienRouter.delete('*', authorizationMiddleware([WorkPermission.DELETE]));
thietBiPhatHienRouter.route('/')
  .get(Controller.getAll)
  .post(Controller.create);

thietBiPhatHienRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
