import * as Service from './thietBiPhatHien.service';
import Model from './thietBiPhatHien.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import * as responseAction from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';

const populateOpts = [
  { path: 'dm_thiet_bi_id' },
  { path: 'anh_vi_tri_id' },
  { path: 'nguoi_chinh_sua', select: 'full_name avatar' }
];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts);

export async function update(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    // value.from_ai = false;
    if (error) return responseAction.error(res, error, 400);
    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true })
      .populate('dm_thiet_bi_id')
      .populate('anh_vi_tri_id');
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_loai']);
    const { criteria } = query;

    const data = await Model.find(criteria)
      .populate('dm_thiet_bi_id')
      .populate('anh_vi_tri_id')
      .populate({ path: 'nguoi_chinh_sua', select: 'full_name avatar' });

    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}
