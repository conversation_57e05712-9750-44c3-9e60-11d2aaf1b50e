import mongoose, { Schema } from 'mongoose';
import { ANH_VI_TRI, DM_THIET_BI, THIET_BI_PHAT_HIEN, USER } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
    dm_thiet_bi_id: { type: Schema.Types.ObjectId, ref: DM_THIET_BI },
    anh_vi_tri_id: { type: Schema.Types.ObjectId, ref: ANH_VI_TRI },
    ghi_chu: String,
    height: { type: Number, default: null },
    width: { type: Number, default: null },
    x: { type: Number, default: null },
    y: { type: Number, default: null },
    polygons: [{ type: Object, default: null }],
    from_ai: { type: Boolean, default: false },
    ai_status: { type: String, default: "CHUA_XAC_NHAN" },
    is_deleted: { type: Boolean, default: false },
    nguoi_tao: { type: Schema.Types.ObjectId, ref: USER },
    nguoi_chinh_sua: { type: Schema.Types.ObjectId, ref: USER },
  },
  {
    timestamps: {
      createdAt: 'thoi_gian_tao',
      updatedAt: 'thoi_gian_cap_nhat',
    },
    collation: { locale: 'vi' },
    versionKey: false,
  });

// Thêm index cho các trường tìm kiếm phổ biến
schema.index({ anh_vi_tri_id: 1 });
schema.index({ anh_vi_tri_id: 1, is_deleted: 1 });

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(THIET_BI_PHAT_HIEN, schema, THIET_BI_PHAT_HIEN);
