import momentTimezone from 'moment-timezone';

import { extractIds, extractKeys } from '../../utils/dataconverter';
import * as VanHanhService from '../TongKe/VanHanh/vanHanh.service';
import * as DuongDayService from '../TongKe/DuongDay/duongDay.service';
import * as ViTriService from '../TongKe/ViTri/viTri.service';
import { RETURN_TYPE } from '../../constant/constant';
import { momentValid } from '../../helpers/checkDataHelper';

momentTimezone.tz.setDefault('Asia/Ho_Chi_Minh');

function buildThoiGianCongTacQuery(criteria, defaultValue = true) {
  // chuyển start & end ngày về range đúng (startOf -> startOf +1 ngày)
  const buildDateRange = (from, to) => {
    const start = momentTimezone(from).startOf('day');
    const end = momentTimezone(to).add(1, 'day').startOf('day');
    return { $gte: start.toDate(), $lt: end.toDate() };
  };

  // Ưu tiên xử lý date_filter nếu có
  if (criteria.date_filter && !criteria.date_filter?.hasOwnProperty('$exists')) {
    criteria.thoi_gian_cong_tac_bat_dau = buildDateRange(criteria.date_filter, criteria.date_filter);
    delete criteria.date_filter;
    return criteria;
  }

  const hasTuNgay = momentValid(criteria.tu_ngay) && !criteria.tu_ngay?.hasOwnProperty('$exists');
  const hasDenNgay = momentValid(criteria.den_ngay) && !criteria.den_ngay?.hasOwnProperty('$exists');

  const tuNgay = hasTuNgay ? new Date(criteria.tu_ngay) : null;
  const denNgay = hasDenNgay ? new Date(criteria.den_ngay) : null;

  if (hasTuNgay && hasDenNgay) {
    criteria.thoi_gian_cong_tac_bat_dau = buildDateRange(tuNgay, denNgay);
  } else if (hasTuNgay) {
    const start = momentTimezone(tuNgay).startOf('day');
    criteria.thoi_gian_cong_tac_bat_dau = { $gte: start.toDate() };
  } else if (hasDenNgay) {
    const end = momentTimezone(denNgay).add(1, 'day').startOf('day');
    criteria.thoi_gian_cong_tac_bat_dau = { $lt: end.toDate() };
  }

  if (criteria.created_at) {
    criteria.thoi_gian_cong_tac_bat_dau = buildDateRange(
      criteria.created_at.$gte,
      criteria.created_at.$lte,
    );
    delete criteria.created_at;
  }

  // Nếu không có filter, mặc định tháng hiện tại
  if (!criteria.thoi_gian_cong_tac_bat_dau && defaultValue) {
    const now = momentTimezone();
    criteria.thoi_gian_cong_tac_bat_dau = {
      $gte: now.startOf('month').toDate(),
      $lt: now.endOf('month').toDate(),
    };
  }

  delete criteria.tu_ngay;
  delete criteria.den_ngay;

  return criteria;
}


async function getDuongDayByViTri(viTriData, projection = {}) {
  if (!Array.isArray(viTriData) || !viTriData?.length) return [];
  const viTriId = extractIds(viTriData);

  const vanHanhData = await VanHanhService.getAll(
    { is_deleted: false, vi_tri_id: viTriId },
    { duong_day_id: 1, vi_tri_id: 1 },
  );
  const duongDayId = extractKeys(vanHanhData, 'duong_day_id');
  return DuongDayService.getAll({ _id: duongDayId, is_deleted: false }, projection);
}

async function getViTriByDuongDay(duongDayData, resultType = RETURN_TYPE.FULL, projection = {}) {
  if (!duongDayData) return [];
  const duongDayList = Array.isArray(duongDayData) ? duongDayData : [duongDayData];
  const viTriId = await VanHanhService.distinctField(
    'vi_tri_id',
    { is_deleted: false, duong_day_id: duongDayList },
    { duong_day_id: 1, vi_tri_id: 1 },
  );

  switch (resultType) {
    case RETURN_TYPE.ID:
      return viTriId;
    case RETURN_TYPE.FULL:
      return ViTriService.getAll({ _id: viTriId }, projection);
    default:
      return [];
  }
}

export default { buildThoiGianCongTacQuery, getDuongDayByViTri, getViTriByDuongDay };
