import express from 'express';
import passport from 'passport';
import * as noiDungCongViecController from './noiDungCongViec.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import GiaoViecPermission from '../../RBAC/permissions/GiaoViecPermission';

export const noiDungCongViecRouter = express.Router();
noiDungCongViecRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
noiDungCongViecRouter.post('*', authorizationMiddleware([GiaoViecPermission.CREATE]));
noiDungCongViecRouter.put('*', authorizationMiddleware([GiaoViecPermission.UPDATE]));
noiDungCongViecRouter.delete('*', authorizationMiddleware([GiaoViecPermission.DELETE]));
noiDungCongViecRouter.route('/')
  .get(noiDungCongViecController.getAll)
  .post(noiDungCongViecController.create);

noiDungCongViecRouter
  .route('/:id')
  .get(noiDungCongViecController.findOne)
  .delete(noiDungCongViecController.remove)
  .put(noiDungCongViecController.update);
