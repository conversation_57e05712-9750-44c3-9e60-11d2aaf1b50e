import mongoose, { Schema } from 'mongoose';
import { DANH_MUC_CONG_VIEC, NOI_DUNG_CONG_VIEC, PHIEU_GIAO_VIEC } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  cong_viec_id: { type: Schema.Types.ObjectId, ref: DANH_MUC_CONG_VIEC },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized indexes for NOI_DUNG_CONG_VIEC queries
// Primary compound index for phieu_giao_viec_id with soft delete (most common query)
schema.index({ 
  phieu_giao_viec_id: 1, 
  is_deleted: 1,
  created_at: -1
}, { 
  background: true,
  name: 'idx_phieu_giao_viec_deleted_time'
});

// Index for cong_viec_id queries with work ticket reference
schema.index({ 
  cong_viec_id: 1, 
  phieu_giao_viec_id: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_cong_viec_phieu_deleted'
});

// Index for work content filtering by cong_viec_id
schema.index({ 
  cong_viec_id: 1, 
  is_deleted: 1,
  created_at: -1
}, { 
  background: true,
  name: 'idx_cong_viec_deleted_time'
});

// Index for time-based queries with soft delete
schema.index({ 
  created_at: -1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_time_deleted'
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(NOI_DUNG_CONG_VIEC, schema, NOI_DUNG_CONG_VIEC);
