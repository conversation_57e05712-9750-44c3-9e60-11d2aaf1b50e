import * as ValidatorHelper from '../../../helpers/validatorHelper';
import BAT_THUONG_PHAT_HIEN, { AI_STATUS } from './batThuongPhatHien.model';
import * as KetQuaKiemTraService from '../KetQuaKiemTra/ketQuaKiemTra.service';
import PHIEU_GIAO_VIEC from '../PhieuGiaoViec/phieuGiaoViec.model';
import TIEU_CHI from '../../DanhMuc/NoiDungKiemTra/TieuChi/tieuChi.model';
import { extractIds } from '../../../utils/dataconverter';

const Joi = require('joi');

const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return BAT_THUONG_PHAT_HIEN.create(value);
}

export async function findOneById(id) {
  return BAT_THUONG_PHAT_HIEN.findById(id)
    .populate({ path: 'dm_thiet_bi_id' })
    .populate({ path: 'anh_vi_tri_id' })
    .populate({
      path: 'tinh_trang_kbt_id', populate: {
        path: 'tieu_chi_id',
      },
    });
}

async function checkValidData(batThuong) {
  const tieuChi = batThuong.tinh_trang_kbt_id.tieu_chi_id;
  const anhViTri = batThuong.anh_vi_tri_id;
  if (!tieuChi || tieuChi.is_deleted) {
    return false;
  }
  const tieuChiData = await TIEU_CHI.findById(tieuChi._id).populate('noi_dung_kiem_tra_id tieu_chi_cha_id').lean();
  if (tieuChiData) {
    if (!tieuChiData.noi_dung_kiem_tra_id || tieuChiData.noi_dung_kiem_tra_id.is_deleted) return false;
  } else {
    return false;
  }
  const phieuGiaoViec = await PHIEU_GIAO_VIEC.findById(anhViTri.phieu_giao_viec_id, 'chuyen_de_id')
    .populate({ path: 'chuyen_de_id', select: 'noi_dung_kiem_tra_id' }).lean();
  if (phieuGiaoViec.chuyen_de_id) {
    const noiDungKiemTraIds = phieuGiaoViec.chuyen_de_id.noi_dung_kiem_tra_id;
    if (!noiDungKiemTraIds) return false;
    const tieuChiKiemTras = await TIEU_CHI.find({
      noi_dung_kiem_tra_id: { $in: noiDungKiemTraIds },
      is_deleted: false,
    }, '_id').lean();
    const tieuChiKiemTrasIds = extractIds(tieuChiKiemTras);
    if (!tieuChiKiemTrasIds.includes(tieuChi._id.toString())) return false;
  }
  return true;
}

export async function updateKetQuaKiemTra(batThuong) {
  // if (batThuong.from_ai) return;
  if (!await checkValidData(batThuong)) {
    return null;
  }
  const tieuChi = batThuong.tinh_trang_kbt_id.tieu_chi_id;
  const anhViTri = batThuong.anh_vi_tri_id;
  const phieuGiaoViec = await PHIEU_GIAO_VIEC.findById(anhViTri?.phieu_giao_viec_id).lean();
  if (tieuChi && !tieuChi.is_deleted) {
    const dataKetQua = {
      // duong_day_id: batThuong?.duong_day_id,
      phieu_giao_viec_id: anhViTri?.phieu_giao_viec_id,
      don_vi_id: phieuGiaoViec?.don_vi_cong_tac_id,
      vi_tri_id: anhViTri.vi_tri_id,
      khoang_cot_id: anhViTri.khoang_cot_id,
      tieu_chi_id: tieuChi._id,
      noi_dung_chi_tiet: batThuong.ghi_chu || '',
      nguoi_tao: batThuong.nguoi_tao,
      nguoi_chinh_sua: batThuong.nguoi_chinh_sua,
      anh_vi_tris: [anhViTri._id],
      from_ai: batThuong.from_ai,
      ref: batThuong._id,
    };
    const existKetQuaKiemTra = await KetQuaKiemTraService.getAll({ ref: batThuong._id });
    if (!existKetQuaKiemTra || existKetQuaKiemTra.length === 0) {
      dataKetQua.is_deleted = dataKetQua.from_ai && batThuong.ai_status === AI_STATUS.AI_TU_CHOI;
      return KetQuaKiemTraService.create(dataKetQua); // Trả về bản ghi vừa tạo
    } else {
      dataKetQua._id = existKetQuaKiemTra[0]._id;
      dataKetQua.is_deleted = batThuong.from_ai
        ? batThuong.ai_status !== AI_STATUS.AI_XAC_NHAN
        : batThuong.is_deleted;

      return KetQuaKiemTraService.updateAll([dataKetQua]);
    }
  } else {
    await KetQuaKiemTraService.update({ ref: batThuong._id }, { is_deleted: true });
    return { deleted: true };
  }
}

export function getAll(query, projection = {}) {
  return BAT_THUONG_PHAT_HIEN.find(query, projection).lean();
}

export function getOne(query, projection = {}) {
  return BAT_THUONG_PHAT_HIEN.findOne(query, projection).lean();
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await BAT_THUONG_PHAT_HIEN.findByIdAndUpdate(value._id, value);
  }
}

export async function deleteAll(data) {
  for (const row of data) {
    const { error, value } = validate(row);
    if (error) throw error;
    await BAT_THUONG_PHAT_HIEN.findByIdAndUpdate(value._id, { is_deleted: true });
  }
}

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};
