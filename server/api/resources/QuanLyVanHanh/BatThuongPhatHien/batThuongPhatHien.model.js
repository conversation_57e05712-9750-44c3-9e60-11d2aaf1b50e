import mongoose, { Schema } from 'mongoose';
import {
  ANH_VI_TRI,
  BAT_THUONG_PHAT_HIEN,
  DM_THIET_BI,
  THIET_BI_PHAT_HIEN,
  TINH_TRANG_KBT,
  USER,
} from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

export const AI_STATUS = {
  CHUA_XAC_NHAN: 'CHUA_XAC_NHAN',
  AI_TU_CHOI: 'AI_TU_CHOI',
  AI_XAC_NHAN: 'AI_XAC_NHAN',
};

const schema = new Schema({
    dm_thiet_bi_id: { type: Schema.Types.ObjectId, ref: DM_THIET_BI },
    anh_vi_tri_id: { type: Schema.Types.ObjectId, ref: ANH_VI_TRI },
    thiet_bi_phat_hien_id: { type: Schema.Types.ObjectId, ref: THIET_BI_PHAT_HIEN },
    tinh_trang_kbt_id: { type: Schema.Types.ObjectId, ref: TINH_TRANG_KBT },
    ghi_chu: String,
    height: { type: Number, default: null },
    width: { type: Number, default: null },
    x: { type: Number, default: null },
    y: { type: Number, default: null },
    polygons: { type: [Array], default: null },
    from_ai: { type: Boolean, default: false },
    ai_status: {
      type: String,
      enum: Object.values(AI_STATUS),
      default: AI_STATUS.CHUA_XAC_NHAN,
    },
    checked: { type: Boolean, default: false },
    is_deleted: { type: Boolean, default: false },
    ref: { type: Schema.Types.ObjectId, ref: BAT_THUONG_PHAT_HIEN },
    nguoi_tao: { type: Schema.Types.ObjectId, ref: USER },
    nguoi_chinh_sua: { type: Schema.Types.ObjectId, ref: USER },
  },
  {
    timestamps: {
      createdAt: 'thoi_gian_tao',
      updatedAt: 'thoi_gian_cap_nhat',
    },
    collation: { locale: 'vi' },
    versionKey: false,
  });

// Thêm index cho các trường tìm kiếm phổ biến
schema.index({ anh_vi_tri_id: 1 });
schema.index({ anh_vi_tri_id: 1, is_deleted: 1 });
schema.index({ from_ai: 1 });

schema.plugin(mongoosePaginate);

schema.indexes({
  anh_vi_tri_id: 1,
  is_deleted: 1,
});


export { schema as DocumentSchema };
export default mongoose.model(BAT_THUONG_PHAT_HIEN, schema, BAT_THUONG_PHAT_HIEN);
