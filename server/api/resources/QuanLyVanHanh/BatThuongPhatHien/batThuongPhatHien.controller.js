import * as controllerHelper from '../../../helpers/controllerHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';
import CommonError from '../../../error/CommonError';

import BatThuongPhatHienModel from './batThuongPhatHien.model';
import * as Service from './batThuongPhatHien.service';
import * as PhieuGiaoViecService from '../PhieuGiaoViec/phieuGiaoViec.service';

import { KIEM_TRA, LOAI_CONG_VIEC } from '../../DanhMuc/LoaiCongViec';


const populateOpts = [
  { path: 'dm_thiet_bi_id' },
  { path: 'anh_vi_tri_id' },
  {
    path: 'tinh_trang_kbt_id', populate: {
      path: 'tieu_chi_id',
    },
  },
  { path: 'nguoi_chinh_sua', select: 'full_name avatar' },
];

async function handleCreatedOrUpdate(batThuong) {
  // if (batThuong.from_ai) return;
  await Service.updateKetQuaKiemTra(batThuong);
}

export const findOne = controllerHelper.createFindOneFunction(BatThuongPhatHienModel, populateOpts);
export const remove = async (req, res) => {
  try {
    const { id } = req.params;
    const data = await BatThuongPhatHienModel.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    const updatedData = await controllerHelper.findOneById(BatThuongPhatHienModel, id, populateOpts, true);
    handleCreatedOrUpdate(updatedData);
    return responseHelper.success(res, updatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
};

export async function update(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    value.nguoi_chinh_sua = req.user._id;
    if (!value.thoi_gian_cap_nhat) {
      value.thoi_gian_cap_nhat = Date.now();
    }
    const data = await BatThuongPhatHienModel.findOneAndUpdate({ _id: id }, value, { new: true });
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    const updatedData = await controllerHelper.findOneById(BatThuongPhatHienModel, id, populateOpts, true);

    await Service.updateKetQuaKiemTra(Object.assign({}, updatedData, { nguoi_tao: req.user._id }));

    return responseHelper.success(res, updatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    value.nguoi_tao = req.user._id;
    value.nguoi_chinh_sua = req.user._id;
    const data = await BatThuongPhatHienModel.create(value);
    const createdData = await controllerHelper.findOneById(BatThuongPhatHienModel, data._id, populateOpts, true);

    if (createdData?.anh_vi_tri_id?.phieu_giao_viec_id) {
      const phieuCurrent = await PhieuGiaoViecService.getById(createdData.anh_vi_tri_id.phieu_giao_viec_id, { loai_cong_viec: 1 });
      const loaiCv = LOAI_CONG_VIEC[phieuCurrent.loai_cong_viec]?.type;

      if (loaiCv === KIEM_TRA) {
        await Service.updateKetQuaKiemTra(Object.assign({}, createdData, { duong_day_id: value.duong_day_id }));
      }
    }
    return responseHelper.success(res, createdData);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

// export const update = controllerHelper.createUpdateByIdFunction(BatThuongPhatHienModel, Service, populateOpts, null, handleCreatedOrUpdate);
// export const create = controllerHelper.createCreateFunction(BatThuongPhatHienModel, Service, populateOpts, null, handleCreatedOrUpdate);

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, null, true, false);
    const { criteria, options } = query;
    const data = await BatThuongPhatHienModel.find(criteria)
      .populate('dm_thiet_bi_id')
      .populate('tinh_trang_kbt_id')
      .populate({ path: 'nguoi_chinh_sua', select: 'full_name avatar' });
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}
