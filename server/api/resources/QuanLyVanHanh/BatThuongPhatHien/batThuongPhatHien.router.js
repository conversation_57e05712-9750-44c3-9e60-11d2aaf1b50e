import express from 'express';
import passport from 'passport';
import * as Controller from './batThuongPhatHien.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import WorkPermission from '../../RBAC/permissions/WorkPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const batThuongPhatHienRouter = express.Router();
batThuongPhatHienRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
batThuongPhatHienRouter.post('*', authorizationMiddleware([WorkPermission.CREATE]));
batThuongPhatHienRouter.put('*', authorizationMiddleware([WorkPermission.UPDATE]));
batThuongPhatHienRouter.delete('*', authorizationMiddleware([WorkPermission.DELETE]));
batThuongPhatHienRouter.route('/')
  .get(Controller.getAll)
  .post(Controller.create);

batThuongPhatHienRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
