import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import {
  CONG_VIEC_PHAT_SINH,
  DANH_MUC_CONG_VIEC,
  DUONG_DAY, VI_TRI, KHOANG_COT,
  PHIEU_GIAO_VIEC,
  USER,
} from '../../../constant/dbCollections';

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  duong_day_id: { type: Schema.Types.ObjectId, ref: DUONG_DAY },
  vi_tri_id: [{ type: Schema.Types.ObjectId, ref: VI_TRI }],
  khoang_cot_id: [{ type: Schema.Types.ObjectId, ref: KHOANG_COT }],
  cong_viec_id: { type: Schema.Types.ObjectId, ref: <PERSON><PERSON><PERSON>_MUC_CONG_VIEC, required: true },
  khoi_luong: { type: String },
  ghi_chu: { type: String },

  nguoi_tao: { type: Schema.Types.ObjectId, ref: USER },
  nguoi_chinh_sua: { type: Schema.Types.ObjectId, ref: USER },

  is_clone: { type: Boolean, default: false, select: false },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
    updatedAt: 'thoi_gian_cap_nhat',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(CONG_VIEC_PHAT_SINH, schema, CONG_VIEC_PHAT_SINH);
