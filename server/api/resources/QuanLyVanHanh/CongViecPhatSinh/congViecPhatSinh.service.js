import * as ValidatorHelper from '../../../helpers/validatorHelper';
import CONG_VIEC_PHAT_SINH from './congViecPhatSinh.model';
import KET_QUA_KIEM_TRA from '../../../constant/dbCollections';

const Joi = require('joi');

const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return CONG_VIEC_PHAT_SINH.create(value);
}

export async function createMany(dataList = []) {
  if (!Array.isArray(dataList)) {
    throw new Error('Input must be an array');
  }

  const validRecords = [];

  for (const data of dataList) {
    const { error, value } = validate(data);
    if (!error) {
      validRecords.push(value);
    }
  }

  return CONG_VIEC_PHAT_SINH.insertMany(validRecords);
}

export function aggregate(pipeline = [], cb) {
  return CONG_VIEC_PHAT_SINH.aggregate(pipeline, cb);
}

export function getOne(query, projection = {}) {
  return CONG_VIEC_PHAT_SINH.findOne(query, projection).lean();
}

export function getAll(query, projection = {}) {
  return CONG_VIEC_PHAT_SINH.find(query, projection).lean();
}

export async function update(query, data) {
  return CONG_VIEC_PHAT_SINH.update(query, data);
}

export async function updateAll(dataUpdate, options = {}) {
  options.new = true;
  const results = [];
  for (const row of dataUpdate) {
    try {
      const { error, value } = validate(row);
      if (!error) {
        const updated = await CONG_VIEC_PHAT_SINH.findByIdAndUpdate(value._id, value, options);
        if (updated) results.push(updated);
      }
    } catch {
    }
  }
  return results;
}

export async function findByIdAndUpdate(data) {
  return CONG_VIEC_PHAT_SINH.findByIdAndUpdate(data._id, data, { new: true });
}

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};
