import express from 'express';
import passport from 'passport';
import * as Controller from './congViecPhatSinh.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import WorkPermission from '../../RBAC/permissions/WorkPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const congViecPhatSinhRouter = express.Router();
congViecPhatSinhRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
congViecPhatSinhRouter.post('*', authorizationMiddleware([WorkPermission.CREATE]));
congViecPhatSinhRouter.put('*', authorizationMiddleware([WorkPermission.UPDATE]));
congViecPhatSinhRouter.delete('*', authorizationMiddleware([WorkPermission.DELETE]));
congViecPhatSinhRouter.route('/')
  .get(Controller.getAll)
  .post(Controller.create);

congViecPhatSinhRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
