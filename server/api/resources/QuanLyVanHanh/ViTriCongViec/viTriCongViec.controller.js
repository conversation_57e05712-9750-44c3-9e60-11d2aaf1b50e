import * as Service from './viTriCongViec.service';
import Model from './viTriCongViec.model';
import * as controllerHelper from '../../../helpers/controllerHelper';

const populateOpts = [
  { path: 'vi_tri_id' },
  { path: 'khoang_cot_id' },
];
export const findOne = controllerHelper.createFindOneFunction(Model);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service);
export const create = controllerHelper.createCreateFunction(Model, Service);
export const getAll = controllerHelper.createGetAllFunction(Model, null, populateOpts);
