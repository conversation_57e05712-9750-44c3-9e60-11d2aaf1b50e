import * as ValidatorHelper from '../../../helpers/validatorHelper';
import VI_TRI_CONG_VIEC from './viTriCongViec.model';
import ViTriCongViecModel from './viTriCongViec.model';
import { TRANG_THAI_HOAN_THANH } from '../../DanhMuc/TrangThaiHoanThanh';
import { VI_TRI } from '../../../constant/dbCollections';

import { LOAI_VI_TRI } from '../../TongKe/ViTri/viTri.model';
import createBaseService from '../../../base/baseService';

const Joi = require('joi');

const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return ViTriCongViecModel.create(value);
}

const baseService = createBaseService(ViTriCongViecModel)
export const createMulti = baseService.createMulti;
export const getAll = baseService.getAll;
export const getOne = baseService.getOne;
export const remove = baseService.remove;
export const distinctId = baseService.distinctId;
export const distinctField = baseService.distinctField;
export const updateByQuery = baseService.updateByQuery;
export const updateByRows = baseService.updateByRows;
export const deleteAll = baseService.deleteAll;
export const updateAll = baseService.updateAll;


export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  if (Array.isArray(data)) {
    let validateError = null;
    data.find(itemData => {
      const { value, error } = schema.validate(itemData, { allowUnknown: true, abortEarly: true });
      if (error) validateError = error;
      return error;
    });
    if (validateError && validateError.details) {
      return { validateError };
    }
    return { value: data };
  } else {
    const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
    if (error && error.details) {
      return { error };
    }
    return { value };
  }
}

export function reportViTriCongViecByDonVi(queryAggregate) {

  return VI_TRI_CONG_VIEC.aggregate([
    {
      $match: {
        ...queryAggregate, trang_thai_hoan_thanh: TRANG_THAI_HOAN_THANH.DA_HOAN_THANH,
      },
    },
    {
      // Lấy thông tin từ vi_tri_id - loai_vi_tri
      $lookup: {
        from: VI_TRI,
        localField: 'vi_tri_id',
        foreignField: '_id',
        as: 'vi_tri',
      },
    },
    {
      $unwind: {
        path: '$vi_tri',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $group: {
        _id: {
          phieu_giao_viec_id: '$phieu_giao_viec_id',
        },
        so_luong_vi_tri: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $not: ['$khoang_cot_id'] }, // không có khoảng cột
                  { $ne: ['$vi_tri.loai_vi_tri', LOAI_VI_TRI.XA_POOCTIC] }, // không phải loại xà pooc-tic
                ],
              },
              1.0,
              0.0,
            ],
          },
        },
        so_luong_khoang_cot: {
          $sum: {
            $cond: [
              { $not: ['$vi_tri_id'] }, // không có vị trí → tức là khoảng cột
              1.0,
              0.0,
            ],
          },
        },
      },
    },
    {
      $set: {
        phieu_giao_viec_id: '$_id.phieu_giao_viec_id',
      },
    },
    {
      $lookup: {
        from: 'PhieuGiaoViec',
        localField: 'phieu_giao_viec_id',
        foreignField: '_id',
        as: 'phieugiaoviecs',
      },
    },
    {
      $unwind: { path: '$phieugiaoviecs' },
    },
    {
      $set: {
        don_vi_id: '$phieugiaoviecs.don_vi_giao_phieu_id',
      },
    },
    {
      $group: {
        _id: {
          don_vi_id: '$don_vi_id',
        },
        so_luong_vi_tri: {
          $sum: '$so_luong_vi_tri',
        },
        so_luong_khoang_cot: {
          $sum: '$so_luong_khoang_cot',
        },
      },
    },
    {
      $set: {
        don_vi_id: '$_id.don_vi_id',
      },
    },
  ]);
}

export async function tongHopPhamViBayKiemTraTruyenThong(queryAggregate) {
  return VI_TRI_CONG_VIEC.aggregate([
    {
      $match: {
        ...queryAggregate, trang_thai_hoan_thanh: TRANG_THAI_HOAN_THANH.DA_HOAN_THANH,
        khoang_cot_id: { $exists: true },
      },
    },
    {
      $lookup: {
        from: 'KhoangCot',
        localField: 'khoang_cot_id',
        foreignField: '_id',
        as: 'khoangcots',
      },
    },
    {
      $unwind: { path: '$khoangcots' },
    },
    {
      $set: {
        chieu_dai: '$khoangcots.chieu_dai',
      },
    },
    {
      $group: {
        _id: {
          phieu_giao_viec_id: '$phieu_giao_viec_id',
        },
        chieu_dai: {
          // $sum: { $multiply: ['$chieu_dai', '$so_mach'] },
          $sum: {
            $cond: [
              {
                $not: [
                  '$so_mach',
                ],
              },
              '$chieu_dai',
              { $multiply: ['$chieu_dai', '$so_mach'] },
            ],
          },
        },
      },
    },
    {
      $set: {
        phieu_giao_viec_id: '$_id.phieu_giao_viec_id',
      },
    },
    {
      $lookup: {
        from: 'PhieuGiaoViec',
        localField: 'phieu_giao_viec_id',
        foreignField: '_id',
        as: 'phieugiaoviecs',
      },
    },
    {
      $project: {
        _id: 1,
        chieu_dai: 1,
        'phieugiaoviecs.don_vi_giao_phieu_id': 1,
      },
    },
    {
      $unwind: { path: '$phieugiaoviecs' },
    },
    {
      $set: {
        don_vi_id: '$phieugiaoviecs.don_vi_giao_phieu_id',
      },
    },
    {
      $group: {
        _id: {
          don_vi_id: '$don_vi_id',
        },
        pham_vi_thuc_hien: {
          $sum: '$chieu_dai',
        },
      },
    },
    {
      $set: {
        don_vi_id: '$_id.don_vi_id',
      },
    },
  ]);

}

export function allViTriKhoangCotCongViecByDonViInScope(queryAggregate) {

  return VI_TRI_CONG_VIEC.aggregate([
    {
      $match: {
        ...queryAggregate, trang_thai_hoan_thanh: TRANG_THAI_HOAN_THANH.DA_HOAN_THANH,
      },
    },
    {
      $group: {
        _id: {
          don_vi_id: '$don_vi_i',
        },
        so_luong_vi_tri: {
          $sum: {
            $cond: [
              {
                $not: [
                  '$khoang_cot_id',
                ],
              },
              1.0,
              0.0,
            ],
          },
        },
        so_luong_khoang_cot: {
          $sum: {
            $cond: [
              {
                $not: [
                  '$vi_tri_id',
                ],
              },
              1.0,
              0.0,
            ],
          },
        },
      },
    },
    {
      $set: {
        phieu_giao_viec_id: '$_id.phieu_giao_viec_id',
      },
    },
  ]);
}

