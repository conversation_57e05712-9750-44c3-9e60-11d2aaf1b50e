import express from 'express';
import passport from 'passport';
import * as vitricongviecController from './viTriCongViec.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import GiaoViecPermission from '../../RBAC/permissions/GiaoViecPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const viTriCongViecRouter = express.Router();
viTriCongViecRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
viTriCongViecRouter.post('*', authorizationMiddleware([GiaoViecPermission.CREATE]));
viTriCongViecRouter.put('*', authorizationMiddleware([GiaoViecPermission.UPDATE]));
viTriCongViecRouter.delete('*', authorizationMiddleware([GiaoViecPermission.DELETE]));
viTriCongViecRouter.route('/')
  .get(vitricongviecController.getAll)
  .post(vitricongviecController.create);

viTriCongViecRouter
  .route('/:id')
  .get(vitricongviecController.findOne)
  .delete(vitricongviecController.remove)
  .put(vitricongviecController.update);
