import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { PHIEU_GIAO_VIEC, VI_TRI, VI_TRI_CONG_VIEC, KHOANG_COT } from '../../../constant/dbCollections';
import { TRANG_THAI_HOAN_THANH } from '../../DanhMuc/TrangThaiHoanThanh';

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  khoang_cot_id: { type: Schema.Types.ObjectId, ref: KHOANG_COT },
  so_mach: { type: Number },
  trang_thai_hoan_thanh: {
    type: String,
    enum: Object.values(TRANG_THAI_HOAN_THANH),
    default: TRANG_THAI_HOAN_THANH.CHUA_HOAN_THANH,
  },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Existing compound index
schema.index({ phieu_giao_viec_id: 1, vi_tri_id: 1, khoang_cot_id: 1 });

// Optimized indexes for the slow query with $or condition
// Index for vi_tri_id queries with is_deleted filter
schema.index({ 
  vi_tri_id: 1, 
  is_deleted: 1 
});

// Index for khoang_cot_id queries with is_deleted filter
schema.index({ 
  khoang_cot_id: 1, 
  is_deleted: 1 
});

// Compound index for projection fields optimization
schema.index({ 
  is_deleted: 1, 
  vi_tri_id: 1, 
  khoang_cot_id: 1, 
  phieu_giao_viec_id: 1 
});

// Index for trang_thai_hoan_thanh queries
schema.index({ 
  trang_thai_hoan_thanh: 1, 
  is_deleted: 1 
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(VI_TRI_CONG_VIEC, schema, VI_TRI_CONG_VIEC);
