import express from 'express';
import passport from 'passport';

import * as Controller from './ketQuaDoCorocam.controller';

import WorkPermission from '../../RBAC/permissions/WorkPermission';

import { authorizationMiddlewareMultiRequired } from '../../RBAC/middleware';
import { loggerMiddleware } from '../../../logs/middleware';

export const ketQuaDoCorocamRouter = express.Router();

ketQuaDoCorocamRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

ketQuaDoCorocamRouter
  .route('/phieugiaoviec/:id')
  .get(Controller.getByPhieuGiaoViec)
  .post(
    authorizationMiddlewareMultiRequired([[WorkPermission.CREATE], [WorkPermission.UPDATE]]),
    Controller.createPhieu
  );

