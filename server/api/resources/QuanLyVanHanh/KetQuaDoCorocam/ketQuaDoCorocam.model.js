import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { KET_QUA_DO_COROCAM, PHIEU_GIAO_VIEC, USER, VI_TRI } from '../../../constant/dbCollections';
import { KET_LUAN } from '../../../constant/constant';

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC, required: true },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  huong_do: { type: String },
  pha: { type: String },
  nhiet_do_moi_truong: { type: String },
  dong_tai: { type: String },
  nguoi_do:  { type: String },
  thoi_gian_do:{ type: Date },
  gia_tri_do: { type: String },
  danh_gia: { type: String, enum: Object.keys(KET_LUAN) },
  ghi_chu: { type: String },

  nguoi_chinh_sua: { type: Schema.Types.ObjectId, ref: USER },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
    updatedAt: 'thoi_gian_cap_nhat',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(KET_QUA_DO_COROCAM, schema, KET_QUA_DO_COROCAM);
