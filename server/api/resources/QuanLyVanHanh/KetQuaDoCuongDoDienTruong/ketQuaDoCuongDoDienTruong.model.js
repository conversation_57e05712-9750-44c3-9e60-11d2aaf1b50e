import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import {
  KET_QUA_DO_CUONG_DO_DIEN_TRUONG,
  KHOANG_COT,
  PHIEU_GIAO_VIEC,
  USER,
  VI_TRI,
} from '../../../constant/dbCollections';

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC, required: true },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  khoang_cot_id: { type: Schema.Types.ObjectId, ref: KHOANG_COT },
  thoi_tiet: { type: String },
  nhiet_do_moi_truong: { type: String },
  khoang_cach_pha_dat: { type: String },
  cuong_do_lan_mot: { type: String },
  cuong_do_lan_hai: { type: String },
  cuong_do_lan_ba: { type: String },
  cuong_do_trung_binh: { type: String },
  thoi_gian_lam_viec: { type: String },
  nguoi_do: { type: String },
  thoi_gian_do: { type: Date },

  nguoi_chinh_sua: { type: Schema.Types.ObjectId, ref: USER },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
    updatedAt: 'thoi_gian_cap_nhat',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(KET_QUA_DO_CUONG_DO_DIEN_TRUONG, schema, KET_QUA_DO_CUONG_DO_DIEN_TRUONG);
