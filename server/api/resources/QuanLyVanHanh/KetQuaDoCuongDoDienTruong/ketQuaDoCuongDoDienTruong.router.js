import express from 'express';
import passport from 'passport';

import * as Controller from './ketQuaDoCuongDoDienTruong.controller';

import WorkPermission from '../../RBAC/permissions/WorkPermission';

import { authorizationMiddleware } from '../../RBAC/middleware';
import { loggerMiddleware } from '../../../logs/middleware';

export const ketQuaDoCuongDoDienTruongRouter = express.Router();


ketQuaDoCuongDoDienTruongRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

ketQuaDoCuongDoDienTruongRouter.post('*', authorizationMiddleware([WorkPermission.CREATE]));
ketQuaDoCuongDoDienTruongRouter.put('*', authorizationMiddleware([WorkPermission.UPDATE]));
ketQuaDoCuongDoDienTruongRouter.delete('*', authorizationMiddleware([WorkPermission.DELETE]));

ketQuaDoCuongDoDienTruongRouter
  .route('/phieugiaoviec/:id')
  .get(Controller.getByPhieuGiaoViec)
  .post(Controller.createPhieu);
