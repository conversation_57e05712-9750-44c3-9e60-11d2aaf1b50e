import * as ValidatorHelper from '../../../helpers/validatorHelper';
import BIEN_PHAP_AN_TOAN_CONG_VIEC from './bienPhapAnToanCongViec.model';

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return BIEN_PHAP_AN_TOAN_CONG_VIEC.create(value);
}

export function getAll(query) {
  return BIEN_PHAP_AN_TOAN_CONG_VIEC.find(query).lean();
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await BIEN_PHAP_AN_TOAN_CONG_VIEC.findByIdAndUpdate(value._id, value);
  }
}

const Joi = require('joi');

const objSchema = Joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  if (Array.isArray(data)) {
    let validateError = null;
    data.find(itemData => {
      const { value, error } = schema.validate(itemData, { allowUnknown: true, abortEarly: true });
      if (error) validateError = error;
      return error;
    });
    if (validateError && validateError.details) {
      return { validateError };
    }
    return { value: data };
  } else {
    const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
    if (error && error.details) {
      return { error };
    }
    return { value };
  }
}
