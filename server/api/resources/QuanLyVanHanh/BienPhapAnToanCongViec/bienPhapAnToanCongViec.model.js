import mongoose, { Schema } from 'mongoose';
import { BIEN_PHAP_AN_TOAN, BIEN_PHAP_AN_TOAN_CONG_VIEC, PHIEU_GIAO_VIEC } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  bien_phap_an_toan_id: { type: Schema.Types.ObjectId, ref: BIEN_PHAP_AN_TOAN },
  ghi_chu: { type: String },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
// Optimized indexes for BIEN_PHAP_AN_TOAN_CONG_VIEC queries
// Using background: true for better performance during index creation

// Primary index for phieu_giao_viec_id queries with soft delete
// Used in phieuGiaoViec.service.js updateBienPhapAnToan function
schema.index({ 
  phieu_giao_viec_id: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_phieu_giao_viec_deleted'
});

// Index for bien_phap_an_toan_id queries with soft delete
schema.index({ 
  bien_phap_an_toan_id: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_bien_phap_an_toan_deleted'
});

// Compound index for phieu_giao_viec_id and bien_phap_an_toan_id queries
schema.index({ 
  phieu_giao_viec_id: 1, 
  bien_phap_an_toan_id: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_phieu_bien_phap_deleted'
});

// Index for time-based queries
schema.index({ 
  created_at: -1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_created_deleted'
});

// Index for ghi_chu text searches (if needed)
schema.index({ 
  ghi_chu: 'text', 
  is_deleted: 1
}, { 
  background: true,
  sparse: true,
  name: 'idx_ghi_chu_text_deleted'
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(BIEN_PHAP_AN_TOAN_CONG_VIEC, schema, BIEN_PHAP_AN_TOAN_CONG_VIEC);
