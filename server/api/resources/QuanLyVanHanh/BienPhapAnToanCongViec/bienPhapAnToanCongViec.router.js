import express from 'express';
import passport from 'passport';
import * as bienPhapAnToanCongViecController from './bienPhapAnToanCongViec.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import GiaoViecPermission from '../../RBAC/permissions/GiaoViecPermission';

export const bienPhapAnToanCongViecRouter = express.Router();
bienPhapAnToanCongViecRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
bienPhapAnToanCongViecRouter.post('*', authorizationMiddleware([GiaoViecPermission.CREATE]));
bienPhapAnToanCongViecRouter.put('*', authorizationMiddleware([GiaoViecPermission.UPDATE]));
bienPhapAnToanCongViecRouter.delete('*', authorizationMiddleware([GiaoViecPermission.DELETE]));
bienPhapAnToanCongViecRouter.route('/')
  .get(bienPhapAnToanCongViecController.getAll)
  .post(bienPhapAnToanCongViecController.create);

bienPhapAnToanCongViecRouter
  .route('/:id')
  .get(bienPhapAnToanCongViecController.findOne)
  .delete(bienPhapAnToanCongViecController.remove)
  .put(bienPhapAnToanCongViecController.update);
