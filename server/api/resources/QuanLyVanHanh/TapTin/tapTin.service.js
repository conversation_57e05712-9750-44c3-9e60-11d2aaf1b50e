import * as ValidatorHelper from '../../../helpers/validatorHelper';
import Model from './tapTin.model';

const Joi = require('joi');

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return Model.create(value);
}

export function getAll(query) {
  return Model.find(query).lean();
}

export function getOne(query, projection = {}) {
  return Model.findOne(query, projection).lean();
}

export function findOneAndUpdate(query, update) {
  return Model.findOneAndUpdate(query, update).lean();
}

const objSchema = Joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  if (Array.isArray(data)) {
    let validateError = null;
    data.find(itemData => {
      const { value, error } = schema.validate(itemData, { allowUnknown: true, abortEarly: true });
      if (error) validateError = error;
      return error;
    });
    if (validateError && validateError.details) {
      return { validateError };
    }
    return { value: data };
  } else {
    const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
    if (error && error.details) {
      return { error };
    }
    return { value };
  }
}

