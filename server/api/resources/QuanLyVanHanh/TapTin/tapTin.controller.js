import * as responseAction from '../../../helpers/responseHelper';
import * as fileUtils from '../../../utils/fileUtils';
import { deleteFile, getFilePath } from '../../../utils/fileUtils';
import Model, { TAP_TIN_TYPE } from './tapTin.model';
import * as Service from './tapTin.service';
import * as controllerHelper from '../../../helpers/controllerHelper';

export const findOne = controllerHelper.createFindOneFunction(Model);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service);
export const getAll = controllerHelper.createGetAllFunction(Model, ['file_name']);
export const create = controllerHelper.createCreateFunction(Model, Service);

export async function createPhuongAnThucHien(req, res) {
  try {
    let value = req.body;
    let filePath = req.files.file.path;
    const hashName = fileUtils.createUniqueFileName(filePath);
    await fileUtils.createByName(filePath, hashName);
    value.type = TAP_TIN_TYPE.PHUONG_AN_THUC_HIEN;
    value.hash_name = hashName;
    value.file_name = req.files.file.name;
    const data = await Model.create(value);
    return responseAction.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function deletePhuongAnThucHien(req, res) {
  return deleteBase(req, res, TAP_TIN_TYPE.PHUONG_AN_THUC_HIEN);
}

/////

async function getOneBase(req, res, fileType) {
  try {
    const data = await Service.getOne({
      phieu_giao_viec_id: req.query.phieu_giao_viec_id,
      type: fileType,
      is_deleted: false,
    });
    return responseAction.success(res, data);
  } catch (err) {
    console.error(err);
    return res.status(500).send(err);
  }
}

async function createBase(req, res, fileType) {
  try {
    const { phieu_giao_viec_id } = req.body;
    await Model.update(
      { phieu_giao_viec_id, type: fileType, is_deleted: false },
      { is_deleted: true },
      { multi: true },
    );
    let value = req.body;
    let filePath = req.files.file.path;
    const hashName = fileUtils.createUniqueFileName(filePath);
    await fileUtils.createByName(filePath, hashName);
    value.type = fileType;
    value.hash_name = hashName;
    value.file_name = req.files.file.name;
    const data = await Model.create(value);
    return responseAction.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function deleteBase(req, res, fileType) {
  try {
    const { id } = req.params;

    const check = Service.getOne({ _id: id, type: fileType }, { _id: 1 });
    if (!check) {
      return responseAction.error(res, 404, '');
    }
    const data = await Model.update({ _id: id }, { is_deleted: true });
    return responseAction.success(res, data);
  } catch (err) {
    console.error(err);
    return res.status(500).send(err);
  }
}
