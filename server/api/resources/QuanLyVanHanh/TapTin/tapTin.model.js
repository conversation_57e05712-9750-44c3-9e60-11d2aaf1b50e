import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { TAP_TIN, PHIEU_GIAO_VIEC } from '../../../constant/dbCollections';

export const TAP_TIN_TYPE = {
  PHUONG_AN_THUC_HIEN: 'PHUONG_AN_THUC_HIEN',
};

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  type: {
    type: String,
    enum: Object.keys(TAP_TIN_TYPE),
    default: TAP_TIN_TYPE.PHUONG_AN_THUC_HIEN
  },
  file_name: { type: String },
  hash_name: { type: String },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
    updatedAt: 'thoi_gian_cap_nhat',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized indexes for TAP_TIN queries
// Using background: true for better performance during index creation

// Primary index for phieu_giao_viec_id queries with soft delete and type filtering
// Used in phieuGiaoViec.service.js updateTepTin function
schema.index({ 
  phieu_giao_viec_id: 1, 
  is_deleted: 1,
  type: 1
}, { 
  background: true,
  name: 'idx_phieu_deleted_type'
});

// Index for phieu_giao_viec_id with time sorting
schema.index({ 
  phieu_giao_viec_id: 1, 
  is_deleted: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  name: 'idx_phieu_deleted_time'
});

// Index for type filtering with soft delete
schema.index({ 
  type: 1, 
  is_deleted: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  name: 'idx_type_deleted_time'
});

// Index for file_name searches
schema.index({ 
  file_name: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_filename_deleted'
});

// Index for hash_name queries
schema.index({ 
  hash_name: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_hashname_deleted'
});

schema.plugin(mongoosePaginate);
export default mongoose.model(TAP_TIN, schema, TAP_TIN);
