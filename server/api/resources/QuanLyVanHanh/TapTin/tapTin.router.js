import express from 'express';
import passport from 'passport';
import * as tapTinController from './tapTin.controller';
import { checkTempFolder, multipartMiddleware } from '../../../utils/fileUtils';
import { loggerMiddleware } from '../../../logs/middleware';

export const tapTinRouter = express.Router();

tapTinRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

tapTinRouter
  .route('/')
  .post(tapTinController.create);

tapTinRouter
  .route('/phuonganthuchien')
  .post(checkTempFolder, multipartMiddleware, tapTinController.createPhuongAnThucHien);

tapTinRouter
  .route('/phuonganthuchien/:id')
  .delete(tapTinController.deletePhuongAnThucHien);
