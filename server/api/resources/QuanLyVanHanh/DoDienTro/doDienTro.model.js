import mongoose, { Schem<PERSON> } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DIA_HINH, DO_DIEN_TRO, PHIEU_GIAO_VIEC, USER, VI_TRI } from '../../../constant/dbCollections';
import { KET_LUAN } from '../../../constant/constant';

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI, required: true },
  chieu_cao_cot: { type: Number },
  loai_tiep_dia: { type: String },
  tri_so_tia_tiep_dia: { type: Object },
  dien_tro_he_thong: { type: Number },
  dien_tro_dat: { type: Number },
  tro_suat_cua_dat: { type: Number },
  dia_hinh_id: { type: Schema.Types.ObjectId, ref: DIA_HINH },
  dien_tro_theo_quy_pham: { type: Number },
  ngay_do: { type: Date },
  huong_do: { type: String },
  dien_tro_dat_quy_pham: { type: String },
  ket_luan: { type: String, enum: Object.keys(KET_LUAN) },
  nguoi_tao_id: { type: Schema.Types.ObjectId, ref: USER },
  nguoi_chinh_sua_id: { type: Schema.Types.ObjectId, ref: USER },
  ghi_chu: { type: String },
  is_deleted: { type: Boolean, default: false },
  is_acceptance_data : { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(DO_DIEN_TRO, schema, DO_DIEN_TRO);

