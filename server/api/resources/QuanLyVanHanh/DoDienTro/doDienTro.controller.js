import * as responseHelper from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';
import * as LoaiTiepDiaService from '../../DanhMuc/LoaiTiepDat/loaiTiepDat.service';
import * as DiaHinhService from '../../DanhMuc/DiaHinh/diaHinh.service';
import * as Service from './doDienTro.service';
import DuongDayModel from '../../TongKe/DuongDay/duongDay.model';
import Model from './doDienTro.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import { Types } from 'mongoose';
import { generateDocument } from '../../Report/GenerateFile/generate.controller';
import { formatToDateDetail } from '../../../common/formatUTCDateToLocalDate';
import { TIA_TIEP_DIA } from './doDienTro.constants';
import { getFilePath } from '../../../utils/fileUtils';
import { TEMPLATES_DIRS } from '../../../constant/constant';
import CommonError from '../../../error/CommonError';
import * as DuongDayService from '../../TongKe/DuongDay/duongDay.service';
import { extractIds, convertObject } from '../../../utils/dataconverter';
import * as TiepDatService from '../../TongKe/TiepDat/tiepDat.service';
import * as CotDienService from '../../TongKe/CotDien/cotDien.service';
import { isValidObjectId } from '../../../common/functionCommons';

const populateOpts = [
  { path: 'dia_hinh_id' },
  { path: 'loai_tiep_dia_id' },
];

export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service);
export const createOrUpdate = async function createOrUpdate(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    if (!Array.isArray(req.body)) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    const phieuGiaoViecId = req.body[0].phieu_giao_viec_id;
    req.body.forEach(row => {
      if (!isValidObjectId(row.dia_hinh_id)) row.dia_hinh_id = null;

      if (row._id) {
        row.nguoi_chinh_sua_id = req.user._id;
      } else {
        row._id = Types.ObjectId();
        row.nguoi_tao_id = req.user._id;
        row.nguoi_chinh_sua_id = req.user._id;
        row.is_deleted = false;
      }
    });

    const result = await Model.bulkWrite(
      req.body.map((row) =>
        ({
          updateOne: {
            filter: { _id: row._id },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );

    const updatedData = await Model.find({ phieu_giao_viec_id: phieuGiaoViecId, is_deleted: false })
      .populate('vi_tri_id')
      .populate({ path: 'nguoi_tao_id', select: 'full_name username phone bac_an_toan' })
      .populate({ path: 'nguoi_chinh_sua_id', select: 'full_name username phone bac_an_toan' })
      .lean();
    return responseHelper.success(res, updatedData);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
};

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id)
      .populate({ path: 'dia_hinh_id', select: 'ten_dia_hinh' })
      .populate({ path: 'loai_tiep_dia_id', select: 'ten_tiep_dia' })
      .lean();
    data.dia_hinh = await DiaHinhService.findOne({ dia_hinh_id: data._id, is_deleted: false });

    data.loai_tiep_dia = await LoaiTiepDiaService.findOne({ loai_tiep_dia_id: data._id, is_deleted: false });
    if (!data) {
      return responseHelper.error(res, 404, '');
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const data = await Model.find(criteria)
      .populate('vi_tri_id')
      .populate({ path: 'nguoi_tao_id', select: 'full_name username phone bac_an_toan' })
      .populate({ path: 'nguoi_chinh_sua_id', select: 'full_name username phone bac_an_toan' });

    if (!data) {
      return responseHelper.error(res, 404, '');
    }

    responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function getAllByDuongDayId(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const data = await Service.bangTongHopDoDienTro(req, criteria);
    responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function downloadBieuMauDoDienTro(req, res) {
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  const ketQuaDoDienTro = await Service.bangTongHopDoDienTro(req, criteria);
  let soLuongTia = 1;
  ketQuaDoDienTro.forEach(ketqua => {
    TIA_TIEP_DIA.forEach((tiaTiepDia, index) => {
      if (ketqua[tiaTiepDia.code]) {
        soLuongTia = soLuongTia > index + 1 ? soLuongTia : index + 1;
      }
    });
  });
  let data = {};
  const duongDay = await DuongDayModel.find({ _id: criteria.duong_day_ids });

  data.ten_duong_day = duongDay[0].ten_duong_day;
  data.do_dien_tro = ketQuaDoDienTro;

  const templateFilePath = getFilePath(`bieu_mau_do_dien_tro_${soLuongTia}.xlsx`, TEMPLATES_DIRS.BIEU_MAU);
  const outputFileName = 'Kết quả đo điện trở.xlsx';
  generateDocument(res, data, templateFilePath, outputFileName);
}


export async function getAllDataNghiemThu(req, res) {
  try {
    const { t } = req;
    const { criteria } = queryHelper.extractQueryParam(req);

    if (!criteria.duong_day_id) {
      return responseHelper.error(res, { message: t('DUONG_DAY_KHONG_DUOC_DE_TRONG') }, 400);
    }

    const allViTriByDuongDay = await DuongDayService.getViTriOnlyOnceDuongDay(req, criteria.duong_day_id);
    const allViTri = extractIds(allViTriByDuongDay);

    const tiepDatData = await TiepDatService.getAll(
      { vi_tri_id: { $in: allViTri }, is_deleted: false },
      { ten_tiep_dat: 1, ma_tiep_dat: 1, vi_tri_id: 1, ma_hieu_noi_dat: 1, vat_lieu_tia: 1, so_tia: 1, so_tiep_dat: 1 },
    );

    const cotDienData = await CotDienService.getAll(
      { vi_tri_id: allViTri, is_deleted: false },
      { chieu_cao: 1, ten_cot_dien: 1, ma_cot_dien: 1, vi_tri_id: 1 },
    );

    const query = {
      vi_tri_id: allViTri,
      is_acceptance_data: true,
      is_deleted: false,
    };

    const doDienTroData = await Model.find(query)
      .populate('nguoi_tao_id')
      .lean();

    const doDienTroGroupByViTri = convertObject(doDienTroData, 'vi_tri_id');

    const dienTroNghiemThu = allViTriByDuongDay.map(viTri => {
      viTri.tiep_dat = tiepDatData.find(tiepDat => tiepDat.vi_tri_id.toString() === viTri._id.toString());
      viTri.cot_dien = cotDienData.find(cotDien => cotDien.vi_tri_id.toString() === viTri._id.toString());
      return { ...doDienTroGroupByViTri[viTri._id], vi_tri_id: viTri };
    });

    return responseHelper.success(res, dienTroNghiemThu);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function createOrUpdateDataNghiemThu(req, res) {
  try {

    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    if (!isValidObjectId(value.dia_hinh_id)) value.dia_hinh_id = null;
    if (!value.ket_luan) delete value.ket_luan;

    value.nguoi_chinh_sua_id = req.user._id;
    value.is_acceptance_data = true;

    let data;
    if (value._id) {
      data = await Model.findOneAndUpdate({ _id: value._id }, value, { new: true });
    } else {
      value.nguoi_tao_id = req.user._id;
      data = await Model.create(value);

    }
    if (!data) return responseHelper.error(res, CommonError.NOT_FOUND);

    const doDienTroData = await Model.findById(data._id)
      .populate('nguoi_tao_id')
      .lean();

    return responseHelper.success(res, doDienTroData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function getDataNghiemThuByViTri(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOne({ vi_tri_id: id, is_deleted: false, is_acceptance_data: true })
      .populate({ path: 'dia_hinh_id', select: 'ten_dia_hinh' })
      .populate({ path: 'loai_tiep_dia_id', select: 'ten_tiep_dia' })
      .lean();

    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}
