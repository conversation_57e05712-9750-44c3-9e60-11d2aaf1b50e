import express from 'express';
import passport from 'passport';
import * as ketQuaDoDienController from './doDienTro.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import WorkPermission from '../../RBAC/permissions/WorkPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const doDienTroRouter = express.Router();
doDienTroRouter
  .route('/download')
  .get(ketQuaDoDienController.downloadBieuMauDoDienTro);

doDienTroRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
doDienTroRouter.post('*', authorizationMiddleware([WorkPermission.CREATE]));
doDienTroRouter.put('*', authorizationMiddleware([WorkPermission.UPDATE]));
doDienTroRouter.delete('*', authorizationMiddleware([WorkPermission.DELETE]));
doDienTroRouter
  .route('/')
  .get(ketQuaDoDienController.getAll)
  .post(ketQuaDoDienController.create);

doDienTroRouter
  .route('/createorupdate')
  .post(ketQuaDoDienController.createOrUpdate);

doDienTroRouter
  .route('/duongday')
  .get(ketQuaDoDienController.getAllByDuongDayId);

doDienTroRouter
  .route('/nghiemthu')
  .get(ketQuaDoDienController.getAllDataNghiemThu)
  .put(ketQuaDoDienController.createOrUpdateDataNghiemThu);

doDienTroRouter
  .route('/nghiemthu/vitri/:id')
  .get(ketQuaDoDienController.getDataNghiemThuByViTri);

doDienTroRouter
  .route('/:id')
  .get(ketQuaDoDienController.findOne)
  .delete(ketQuaDoDienController.remove)
  .put(ketQuaDoDienController.update);
