import * as ValidatorHelper from '../../../helpers/validatorHelper';
import DO_DIEN_TRO from './doDienTro.model';
import * as PhieuGiaoViecService from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';
import { LOAI_CONG_VIEC } from '../../DanhMuc/LoaiCongViec';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import * as CotDienService from '../../TongKe/CotDien/cotDien.service';
import * as TiepDatService from '../../TongKe/TiepDat/tiepDat.service';
import { KET_LUAN } from '../../../constant/constant';
import { formatDate } from '../../../common/formatUTCDateToLocalDate';
import * as DuongDayService from '../../TongKe/DuongDay/duongDay.service';
import { extractIds } from '../../../utils/dataconverter';

export function getAll(query, projection = {}) {
  return DO_DIEN_TRO.find(query, projection).lean();
}

export async function updateAll(dataUpdate) {
  for (const row of dataUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await DO_DIEN_TRO.findByIdAndUpdate(value._id, value);
  }
}

const Joi = require('joi');

const objSchema = Joi.object({
  huong_do: Joi.string().required().messages(ValidatorHelper.messageDefine('Tia số một')),
  tia_so_mot: Joi.number().required().messages(ValidatorHelper.messageDefine('Tia số một')),
  tia_so_hai: Joi.number().required().messages(ValidatorHelper.messageDefine('Tia số hai')),
  tia_so_ba: Joi.number().required().messages(ValidatorHelper.messageDefine('Tia số ba')),
  tia_so_bon: Joi.number().required().messages(ValidatorHelper.messageDefine('Tia số bốn')),
  dien_tro: Joi.number().required().messages(ValidatorHelper.messageDefine('Điện trở')),
  rdat: Joi.number().required().messages(ValidatorHelper.messageDefine('Điện trở')),
  chieu_cao_cot: Joi.number().required().messages(ValidatorHelper.messageDefine('Chiều cao cột')),
  ghi_chu: Joi.string(),
  nguoi_do: Joi.string(),
  vi_tri_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Vị trí')),
  dia_hinh_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Địa hình')),
  loai_tiep_dia_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Tiếp địa')),
});

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};

export async function bangTongHopDoDienTro(req, criteria) {
  criteria.loai_cong_viec = LOAI_CONG_VIEC.DO_DIEN_TRO_TIEP_DIA.code;
  criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code;
  criteria.duong_day_ids = criteria.duong_day_id;
  delete criteria.duong_day_id;
  delete criteria.created_at;
  const allPhieuGiaoViecIds = extractIds(await PhieuGiaoViecService.getAllPopulate(criteria));
  const allViTriByDuongDayIds = (await DuongDayService.getViTriByDuongDay(req, criteria.duong_day_ids)).vi_tri_ids;
  const allDoDienTro = await getAll({
    phieu_giao_viec_id: { $in: allPhieuGiaoViecIds },
    vi_tri_id: { $in: allViTriByDuongDayIds },
    is_deleted: false,
  }).populate({ path: 'vi_tri_id phieu_giao_viec_id nguoi_tao_id dia_hinh_id' });

  const viTriIds = allDoDienTro.map(item => item.vi_tri_id?._id);
  const cotDienData = await CotDienService.getAll({ vi_tri_id: viTriIds, is_deleted: false });
  const tiepDatData = await TiepDatService.getAll({ vi_tri_id: viTriIds, is_deleted: false });
  const mapCotDien = {}, mapTiepDat = {};
  cotDienData.forEach(cotDien => {
    mapCotDien[cotDien.vi_tri_id] = cotDien;
  });
  tiepDatData.forEach(tiepDat => {
    mapTiepDat[tiepDat.vi_tri_id] = tiepDat;
  });
  allDoDienTro.forEach(doDienTro => {
    doDienTro.chieu_cao_cot = mapCotDien[doDienTro.vi_tri_id?._id]?.chieu_cao;
    doDienTro.loai_tiep_dia = mapTiepDat[doDienTro.vi_tri_id?._id]?.ten_tiep_dat;
  });

  function convertDataToRows(phieuDoDienTro, index) {
    return {
      stt: index + 1,
      ten_vi_tri: phieuDoDienTro.vi_tri_id?.ten_vi_tri,
      chieu_cao_cot: phieuDoDienTro.chieu_cao_cot,
      loai_tiep_dia: phieuDoDienTro.loai_tiep_dia,
      tia_so_mot: phieuDoDienTro.tri_so_tia_tiep_dia?.tia_so_mot,
      tia_so_hai: phieuDoDienTro.tri_so_tia_tiep_dia?.tia_so_hai,
      tia_so_ba: phieuDoDienTro.tri_so_tia_tiep_dia?.tia_so_ba,
      tia_so_bon: phieuDoDienTro.tri_so_tia_tiep_dia?.tia_so_bon,
      tia_so_nam: phieuDoDienTro.tri_so_tia_tiep_dia?.tia_so_nam,
      tia_so_sau: phieuDoDienTro.tri_so_tia_tiep_dia?.tia_so_sau,
      tia_so_bay: phieuDoDienTro.tri_so_tia_tiep_dia?.tia_so_bay,
      tia_so_tam: phieuDoDienTro.tri_so_tia_tiep_dia?.tia_so_tam,
      tia_so_chin: phieuDoDienTro.tri_so_tia_tiep_dia?.tia_so_chin,
      tia_so_muoi: phieuDoDienTro.tri_so_tia_tiep_dia?.tia_so_muoi,
      tia_so_muoi_mot: phieuDoDienTro.tri_so_tia_tiep_dia?.tia_so_muoi_mot,
      tia_so_muoi_hai: phieuDoDienTro.tri_so_tia_tiep_dia?.tia_so_muoi_hai,
      tia_so_muoi_ba: phieuDoDienTro.tri_so_tia_tiep_dia?.tia_so_muoi_ba,
      tia_so_muoi_bon: phieuDoDienTro.tri_so_tia_tiep_dia?.tia_so_muoi_bon,
      tia_so_muoi_nam: phieuDoDienTro.tri_so_tia_tiep_dia?.tia_so_muoi_nam,
      dac_diem_dia_hinh: phieuDoDienTro.vi_tri_id?.dac_diem_dia_hinh,
      nguoi_do: phieuDoDienTro.nguoi_tao_id?.full_name,
      dien_tro_he_thong: phieuDoDienTro.dien_tro_he_thong,
      dien_tro_dat: phieuDoDienTro.dien_tro_dat,
      tro_suat_cua_dat: phieuDoDienTro.tro_suat_cua_dat,
      dien_tro_theo_quy_pham: phieuDoDienTro.dien_tro_theo_quy_pham,
      ngay_do: phieuDoDienTro.ngay_do ? formatDate(phieuDoDienTro.ngay_do) : '',
      ten_dia_hinh: phieuDoDienTro.dia_hinh_id?.ten_dia_hinh,
      ket_luan: KET_LUAN?.[phieuDoDienTro.ket_luan]?.label,
      ghi_chu: phieuDoDienTro.ghi_chu,
      huong_do: phieuDoDienTro.huong_do,
      dien_tro_dat_quy_pham: phieuDoDienTro.dien_tro_dat_quy_pham,
    };
  }

  return allDoDienTro.map(convertDataToRows);
}
