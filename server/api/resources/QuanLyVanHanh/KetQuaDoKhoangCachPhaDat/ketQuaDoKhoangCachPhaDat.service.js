import * as ValidatorHelper from '../../../helpers/validatorHelper';
import KET_QUA_DO_KHOANG_CACH_PHA_DAT from './ketQuaDoKhoangCachPhaDat.model';
import { LOAI_CONG_VIEC } from '../../DanhMuc/LoaiCongViec';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import * as PhieuGiaoViecService from '../PhieuGiaoViec/phieuGiaoViec.service';
import * as DuongDayService from '../../TongKe/DuongDay/duongDay.service';
import * as KhoangCotService from '../../TongKe/KhoangCot/khoangCot.service';
import { formatDate } from '../../../common/formatUTCDateToLocalDate';
import { GIAI_PHAP_XU_LY_PHA_DAT_THAP } from '../../DanhMuc/DeXuatXuLy';
import { extractIds } from '../../../utils/dataconverter';

const Joi = require('joi');
const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return KET_QUA_DO_KHOANG_CACH_PHA_DAT.create(value);
}

export function getAll(query, projection = {}) {
  return KET_QUA_DO_KHOANG_CACH_PHA_DAT.find(query, projection).lean();
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await KET_QUA_DO_KHOANG_CACH_PHA_DAT.findByIdAndUpdate(value._id, value);
  }
}

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};

export async function bangTongHopDoPhaDat(req, criteria) {
  criteria.loai_cong_viec = LOAI_CONG_VIEC.DO_KHOANG_CACH_PHA_DAT.code;
  criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code;

  const allViTriByDuongDayIds = (await DuongDayService.getViTriByDuongDay(req, criteria.duong_day_id)).vi_tri_ids;
  const allKhoangCot = await KhoangCotService.getAll({ vi_tri_id: { $in: allViTriByDuongDayIds } });
  const allKhoangCotIds = extractIds(allKhoangCot);

  criteria.duong_day_ids = criteria.duong_day_id;
  delete criteria.duong_day_id;

  const allPhieuGiaoViecIds = extractIds(await PhieuGiaoViecService.getAllPopulate(criteria));
  const allDoPhaDat = await getAll({
    phieu_giao_viec_id: { $in: allPhieuGiaoViecIds }, is_deleted: false,
    khoang_cot_id: { $in: allKhoangCotIds },
  }).populate('vi_tri_id  khoang_cot_id')
    .populate({
      path: 'phieu_giao_viec_id',
      populate: [{ path: 'don_vi_giao_phieu_id', populate: 'don_vi_cha_id' }, { path: 'duong_day_id duong_day_ids' }],
    });

  function convertDataToRows(phieuDoPhaDat, index) {
    let deXuatXuLy = GIAI_PHAP_XU_LY_PHA_DAT_THAP[phieuDoPhaDat?.de_xuat_xu_ly]?.label;
    return {
      stt: index + 1,
      khoang_cot_id: phieuDoPhaDat.khoang_cot_id,
      dong_tai_khi_do: phieuDoPhaDat.dong_tai_khi_do,
      nhiet_do_moi_truong: phieuDoPhaDat.nhiet_do_moi_truong,
      khoang_cach_pha_dat: phieuDoPhaDat.khoang_cach_pha_dat,
      khoang_cach_theo_quy_pham: phieuDoPhaDat.khoang_cach_theo_quy_pham,
      ngay_do: phieuDoPhaDat.ngay_do ? formatDate(phieuDoPhaDat.ngay_do) : '',
      de_xuat_xu_ly: phieuDoPhaDat.de_xuat_xu_ly,
      de_xuat: deXuatXuLy,
      dac_diem_khu_vuc: phieuDoPhaDat.dac_diem_khu_vuc,
      ghi_chu: phieuDoPhaDat.ghi_chu,
      nguoi_tao: phieuDoPhaDat.nguoi_tao,
      nguoi_chinh_sua: phieuDoPhaDat.nguoi_chinh_sua,
      don_vi_giao_phieu_id: phieuDoPhaDat.phieu_giao_viec_id?.don_vi_giao_phieu_id,
      duong_day_id: phieuDoPhaDat.phieu_giao_viec_id?.duong_day_ids,
    };
  }

  return allDoPhaDat.map(convertDataToRows);
}
