import * as Service from './ketQuaDoKhoangCachPhaDat.service';
import Model from './ketQuaDoKhoangCachPhaDat.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import queryHelper from '../../../helpers/queryHelper';
import * as responseAction from '../../../helpers/responseHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import { Types } from 'mongoose';
import { generateDocument } from '../../Report/GenerateFile/generate.controller';
import { formatToDateDetail } from '../../../common/formatUTCDateToLocalDate';
import { getFilePath } from '../../../utils/fileUtils';
import { TEMPLATES_DIRS } from '../../../constant/constant';

const populateOpts = [
  { path: 'khoang_cot_id', populate: 'vi_tri_bat_dau_id vi_tri_ket_thuc_id' },
];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts);

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const data = await Model.find(criteria)
      .populate(populateOpts);

    if (!data) {
      return responseAction.error(res, 404, '');
    }
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export const createOrUpdate = async function createOrUpdate(req, res) {
  try {
    const { error } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    if (!Array.isArray(req.body)) {
      return responseHelper.error(res, null, 400);
    }
    const phieuGiaoViecId = req.body[0].phieu_giao_viec_id;
    req.body.forEach(row => {
      if (row._id) {
        row.nguoi_chinh_sua_id = req.user._id;
      } else {
        row._id = Types.ObjectId();
        row.nguoi_tao = req.user._id;
        row.nguoi_chinh_sua_id = req.user._id;
        row.is_deleted = false;
      }
    });
    await Model.bulkWrite(
      req.body.map((row) =>
        ({
          updateOne: {
            filter: { _id: row._id },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
    const updatedData = await Model.find({ phieu_giao_viec_id: phieuGiaoViecId, is_deleted: false })
      .populate(populateOpts)
      .populate({ path: 'nguoi_tao_id', select: 'full_name username phone bac_an_toan' })
      .populate({ path: 'nguoi_chinh_sua_id', select: 'full_name username phone bac_an_toan' })
      .lean();
    return responseHelper.success(res, updatedData);
  } catch (err) {
    console.log('err', err);
    return responseHelper.error(res, err, 500);
  }
};

export async function getAllByDuongDayId(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const data = await Service.bangTongHopDoPhaDat(req, criteria);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function downloadBieuMauDoPhaDat(req, res) {
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  const ketQuaDoPhaDat = await Service.bangTongHopDoPhaDat(req, criteria);
  let data = {};
  data.nam = formatToDateDetail(criteria.created_at.$lte).nam;
  data.don_vi_id = ketQuaDoPhaDat[0].don_vi_giao_phieu_id;
  data.ten_duong_day = ketQuaDoPhaDat[0].duong_day_id[0]?.ten_duong_day;
  data.tu_vi_tri = ketQuaDoPhaDat[0].duong_day_id[0]?.tu_vi_tri;
  data.den_vi_tri = ketQuaDoPhaDat[0].duong_day_id[0]?.den_vi_tri;
  data.do_pha_dat = ketQuaDoPhaDat;

  const templateFilePath = getFilePath('bieu_mau_do_khoang_cach_pha_dat.docx', TEMPLATES_DIRS.BIEU_MAU);
  const outputFileName = 'Kết quả đo khoảng cách pha đất.docx';
  generateDocument(res, data, templateFilePath, outputFileName);
}
