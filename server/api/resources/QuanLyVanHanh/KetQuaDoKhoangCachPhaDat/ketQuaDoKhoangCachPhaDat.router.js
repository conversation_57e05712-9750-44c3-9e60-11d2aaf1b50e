import express from 'express';
import passport from 'passport';
import * as Controller from './ketQuaDoKhoangCachPhaDat.controller';
import { authorizationMiddleware, authorizationMiddlewareMultiRequired } from '../../RBAC/middleware';
import WorkPermission from '../../RBAC/permissions/WorkPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const ketQuaDoKhoangCachPhaDatRouter = express.Router();

ketQuaDoKhoangCachPhaDatRouter
  .route('/download')
  .get(Controller.downloadBieuMauDoPhaDat);

ketQuaDoKhoangCachPhaDatRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

ketQuaDoKhoangCachPhaDatRouter
  .route('/createorupdate')
  .post(authorizationMiddlewareMultiRequired([[WorkPermission.CREATE], [WorkPermission.UPDATE]]), Controller.createOrUpdate);

ketQuaDoKhoangCachPhaDatRouter.post('*', authorizationMiddleware([WorkPermission.CREATE]));
ketQuaDoKhoangCachPhaDatRouter.put('*', authorizationMiddleware([WorkPermission.UPDATE]));
ketQuaDoKhoangCachPhaDatRouter.delete('*', authorizationMiddleware([WorkPermission.DELETE]));
ketQuaDoKhoangCachPhaDatRouter.route('/')
  .get(Controller.getAll)
  .post(Controller.create);

ketQuaDoKhoangCachPhaDatRouter
  .route('/duongday')
  .get(Controller.getAllByDuongDayId);

ketQuaDoKhoangCachPhaDatRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
