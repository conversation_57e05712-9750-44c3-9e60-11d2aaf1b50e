import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { KET_QUA_DO_KHOANG_CACH_PHA_DAT, PHIEU_GIAO_VIEC, USER, KHOANG_COT } from '../../../constant/dbCollections';
import { KET_LUAN } from '../../../constant/constant';

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC, required: true },
  khoang_cot_id: { type: Schema.Types.ObjectId, ref: KHOANG_COT },
  khoang_cach_pha_dat: { type: String },
  dong_tai_khi_do: { type: String },
  ngay_do: { type: Date },
  nhiet_do_moi_truong: { type: String },
  khoang_cach_theo_quy_pham: { type: String },
  dac_diem_khu_vuc: { type: String },
  de_xuat_xu_ly: { type: String },
  ket_luan: { type: String, enum: Object.keys(KET_LUAN) },
  ghi_chu: { type: String },
  nguoi_tao: { type: Schema.Types.ObjectId, ref: USER },
  nguoi_chinh_sua: { type: Schema.Types.ObjectId, ref: USER },
  thoi_gian_cap_nhat: { type: Date, default: Date.now },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(KET_QUA_DO_KHOANG_CACH_PHA_DAT, schema, KET_QUA_DO_KHOANG_CACH_PHA_DAT);
