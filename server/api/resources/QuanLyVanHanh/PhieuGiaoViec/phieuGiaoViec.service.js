import * as ValidatorHelper from '../../../helpers/validatorHelper';
import queryHelper from '../../../helpers/queryHelper';
import { createNotFoundError } from '../../../helpers/errorHelper';

import { formatUnique, groupBy } from '../../../common/functionCommons';
import quanLyVanHanhCommons from '../quanLyVanHanhCommons';

import PHIEU_GIAO_VIEC from './phieuGiaoViec.model';
import VI_TRI from '../../TongKe/ViTri/viTri.model';
import KHOANG_COT from '../../TongKe/KhoangCot/khoangCot.model';
import { TAP_TIN_TYPE } from '../TapTin/tapTin.model';
import VAN_HANH from '../../TongKe/VanHanh/vanHanh.model';
import KET_QUA_KIEM_TRA from '../KetQuaKiemTra/ketQuaKiemTra.model';
import CongViecPhuTroModel from '../CongViecPhuTro/congViecPhuTro.model';
import SuaChuaCoKeHoachModel from '../KetQuaSuaChuaCoKeHoach/ketQuaSuaChuaCoKeHoach.model';
import ViTriCongViecModel from '../ViTriCongViec/viTriCongViec.model';

import { CAP_DON_VI, PHUONG_PHAP_THUC_HIEN } from '../../../constant/constant';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import { TRANG_THAI_HOAN_THANH } from '../../DanhMuc/TrangThaiHoanThanh';
import {
  CONG_TAC_PHU_TRO,
  DO_THONG_SO,
  KIEM_TRA,
  LOAI_CONG_VIEC,
  NHOM_CONG_VIEC,
  SUA_CHUA_BAO_DUONG,
} from '../../DanhMuc/LoaiCongViec';
import { TRANG_THAI_THUC_HIEN, TRANG_THAI_XU_LY } from '../../DanhMuc/TrangThaiXuLy';
import { extractIds, extractKeyObjectIds, extractObjectIds } from '../../../utils/dataconverter';

import * as UserService from '../../User/user.service';
import * as NguoiCongTacService from '../NguoiCongTac/nguoiCongTac.service';
import * as KetQuaKiemTraService from '../KetQuaKiemTra/ketQuaKiemTra.service';
import * as PhieuCongTacService from '../PhieuCongTac/phieuCongTac.service';
import * as HieuChinhTonTaiService from './HieuChinhTonTai/hieuChinhTonTai.service';
import * as TapTinService from '../TapTin/tapTin.service';
import * as BienPhapAnToanCongViecService from '../BienPhapAnToanCongViec/bienPhapAnToanCongViec.service';
import * as DieuKienAnToanCongViecService from '../DieuKienAnToanCongViec/dieuKienAnToanCongViec.service';
import * as NoiDungCongViecService from '../NoiDungCongViec/noiDungCongViec.service';
import * as VanHanhService from '../../TongKe/VanHanh/vanHanh.service';
import * as CauHinhBayService from '../../CauHinhBay/cau_hinh_bay.service';
import * as CotDienService from '../../TongKe/CotDien/cotDien.service';
import * as TiepDatService from '../../TongKe/TiepDat/tiepDat.service';
import * as DayChongSetService from '../../TongKe/DayChongSet/dayChongSet.service';
import * as DayCapQuangService from '../../TongKe/DayCapQuang/dayCapQuang.service';
import * as GieoCheoService from '../../TongKe/GiaoCheo/giaoCheo.service';
import * as ThongTinQuanLyService from '../../ThongTinQuanLy/thongTinQuanLy.service';
import * as ViTriCongViecService from '../ViTriCongViec/viTriCongViec.service';
import * as ketQuaSuaChuaCoKeHoachService from '../KetQuaSuaChuaCoKeHoach/ketQuaSuaChuaCoKeHoach.service';
import * as KetQuaSuaChuaCoKeHoachService from '../KetQuaSuaChuaCoKeHoach/ketQuaSuaChuaCoKeHoach.service';
import * as CongViecPhuTroService from '../CongViecPhuTro/congViecPhuTro.service';
import * as DonViService from '../../DonVi/donVi.service';
import * as CaiDatVanHanhService from '../../CaiDatVanHanh/caiDatVanHanh.service';


async function getSoPhieuTrongThang(Model, don_vi_giao_phieu_id) {
  const date = new Date();
  const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
  return await Model.count({ don_vi_giao_phieu_id: don_vi_giao_phieu_id, created_at: { $gte: firstDay } });
}

export async function generateSoPhieu(Model, don_vi_giao_phieu_id) {
  const soPhieuGanNhat = await getSoPhieuTrongThang(Model, don_vi_giao_phieu_id);
  const currentDate = new Date();
  const namHienTai = currentDate.getFullYear();
  const thangHienTai = currentDate.getMonth() + 1;
  const soThuTuTrongThang = soPhieuGanNhat + 1;
  return `${soThuTuTrongThang}/${thangHienTai}/${namHienTai}`;
}

export async function generateSoPhieuCongTac(Model, donViGiaoPhieuId) {

  async function getSoPhieuCongTacTrongNam(Model, donViGiaoPhieuId) {
    const date = new Date();
    const firstDay = new Date(date.getFullYear(), 0, 1);
    return await Model.count({ don_vi_giao_phieu_id: donViGiaoPhieuId, thoi_gian_tao: { $gte: firstDay } });
  }

  function getFirstCharacterEachWord(input) {
    if (typeof input !== 'string') return '';
    return input.split(' ').map(word => word[0]).join('').toUpperCase();
  }

  const soPhieuGanNhat = await getSoPhieuCongTacTrongNam(Model, donViGiaoPhieuId);
  const donVi = await DonViService.getById(donViGiaoPhieuId, { ten_don_vi: 1, don_vi_cha_id: 1 })
    .populate({ path: 'don_vi_cha_id', select: 'ten_don_vi' });
  const currentDate = new Date();
  const namHienTai = currentDate.getFullYear();
  const soThuTuTrongThang = soPhieuGanNhat + 1;
  return `${soThuTuTrongThang}/${namHienTai}/${getFirstCharacterEachWord(donVi.ten_don_vi)}-${getFirstCharacterEachWord(donVi.don_vi_cha_id.ten_don_vi)}`;
}

export function getAll(query, projection = {}) {
  return PHIEU_GIAO_VIEC.find(query, projection).lean();
}

export function getAllPopulate(query, projection = {}) {
  return PHIEU_GIAO_VIEC.find(query, projection)
    .populate({ path: 'nguoi_cap_phieu_id', select: 'full_name username phone bac_an_toan' })
    .populate({ path: 'chi_huy_truc_tiep_id', select: 'full_name username phone bac_an_toan' })
    .populate({ path: 'don_vi_cong_tac_id', populate: 'don_vi_cha_id' })
    .lean();
}

export function getById(id, projection = {}) {
  return PHIEU_GIAO_VIEC.findById(id, projection).lean();
}

export function count(query) {
  return PHIEU_GIAO_VIEC.count(query);
}

export async function updateAll(dataUpdate) {
  const results = [];
  for (const row of dataUpdate) {
    try {
      const { error, value } = validate(row);
      if (!error) {
        const updated = await PHIEU_GIAO_VIEC.findByIdAndUpdate(value._id, value, { new: true });
        if (updated) results.push(updated);
      }
    } catch {
    }
  }
  return results;
}

export async function buildQuery(req, keepIdObject = false) {
  const query = queryHelper.extractQueryParam(req, ['so_phieu']);
  let { criteria, options } = query;
  criteria.don_vi_giao_phieu_id = await DonViService.getDonViQuery(req, criteria.don_vi_giao_phieu_id, keepIdObject);
  criteria = quanLyVanHanhCommons.buildThoiGianCongTacQuery(criteria, false);
  return { criteria, options };
}

export async function buildQueryNhanVien(req, criteria) {
  const tenNhanVien = criteria.ten_nhan_vien;
  delete criteria.ten_nhan_vien;
  if (tenNhanVien) {
    const donViId = await DonViService.getDonViQuery(req);
    const allUser = await UserService.getAll(
      { full_name: { '$regex': tenNhanVien, '$options': 'i' }, don_vi_id: donViId, is_deleted: false },
      { _id: 1, full_name: 1 });
    const allUserId = extractObjectIds(allUser);
    const allNguoiCongTac = await NguoiCongTacService.getAll(
      { user_id: allUserId, is_deleted: false },
      { phieu_giao_viec_id: 1 });
    criteria['$or'] = [
      { _id: { $in: extractKeyObjectIds(allNguoiCongTac, 'phieu_giao_viec_id') } },
      { chi_huy_truc_tiep_id: { $in: allUserId } },
    ];
  }
  return criteria;
}

export async function getDataPhieuCongTac(phieuGiaoViecData) {
  if (!Array.isArray(phieuGiaoViecData)) return phieuGiaoViecData;
  const phieuGiaoViecId = extractIds(phieuGiaoViecData);
  const allPhieuCongTac = await PhieuCongTacService.getAll({ phieu_giao_viec_id: phieuGiaoViecId });
  phieuGiaoViecData.forEach(phieuGiaoViec => {
    phieuGiaoViec.phieu_cong_tac_id = allPhieuCongTac.find(phieuCongTac => phieuCongTac.phieu_giao_viec_id.toString() === phieuGiaoViec._id.toString());
  });
  return phieuGiaoViecData;
}


const Joi = require('joi');

const objSchema = Joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return PHIEU_GIAO_VIEC.create(value);
}

export async function bangTongHopPhieuGiaoViec(query) {
  const allPhieuGiaoViec = await getAll(query);
  const phieuGiaoViecIds = await PHIEU_GIAO_VIEC.distinct('_id', query);

  const viTriCongViec = await ViTriCongViecService.getAll({ phieu_giao_viec_id: phieuGiaoViecIds })
    .select('trang_thai_hoan_thanh phieu_giao_viec_id');
  const groupViTriCongViecByPhieuId = groupBy(viTriCongViec, 'phieu_giao_viec_id');

  const ketQuaSuaChuaCoKeHoach = await ketQuaSuaChuaCoKeHoachService.getAll({ phieu_giao_viec_id: { $in: phieuGiaoViecIds } })
    .select('phieu_giao_viec_id tinh_trang_xu_ly');
  const groupKetQuaSuaChuaByPhieuId = groupBy(ketQuaSuaChuaCoKeHoach, 'phieu_giao_viec_id');

  const congViecPhuTro = await CongViecPhuTroService.getAll({ phieu_giao_viec_id: { $in: phieuGiaoViecIds } })
    .select('phieu_giao_viec_id tinh_trang_thuc_hien');
  const groupCongViecPhuTroByPhieuId = groupBy(congViecPhuTro, 'phieu_giao_viec_id');

  allPhieuGiaoViec.forEach(item => {
    item.vi_tri_cong_viec = groupViTriCongViecByPhieuId[item._id]?.map(vtri => vtri.trang_thai_hoan_thanh);
    item.ket_qua_sua_chua_co_ke_hoach = groupKetQuaSuaChuaByPhieuId[item._id]?.map(ketQua => ketQua.tinh_trang_xu_ly);
    item.cong_viec_phu_tro = groupCongViecPhuTroByPhieuId[item._id]?.map(congViec => congViec.tinh_trang_thuc_hien);
  });

  function convertToBangRows(phieu) {
    let chuaHoanThanh = phieu.vi_tri_cong_viec?.includes(TRANG_THAI_HOAN_THANH.CHUA_HOAN_THANH)
      || phieu.ket_qua_sua_chua_co_ke_hoach?.includes(TRANG_THAI_XU_LY.CHUA_XU_LY.code)
      || phieu.cong_viec_phu_tro?.includes(TRANG_THAI_THUC_HIEN.CHUA_THUC_HIEN.code);
    return {
      _id: phieu._id,
      chi_huy_truc_tiep_id: phieu.chi_huy_truc_tiep_id?._id,
      ten_chi_huy: phieu.chi_huy_truc_tiep_id?.full_name,
      thoi_gian_cong_tac_bat_dau: phieu.thoi_gian_cong_tac_bat_dau,
      thoi_gian_cong_tac_ket_thuc: phieu.thoi_gian_cong_tac_ket_thuc,
      hoan_thanh_cong_viec: phieu.vi_tri_cong_viec?.includes(TRANG_THAI_HOAN_THANH.DA_HOAN_THANH),
      chua_hoan_thanh_cong_viec: chuaHoanThanh,
      hoan_thanh_dung_han: phieu.thoi_gian_xac_nhan_khoa_phieu <= phieu.thoi_gian_cong_tac_ket_thuc,
      don_vi_giao_phieu_id: phieu.don_vi_giao_phieu_id,
      loai_cong_viec: LOAI_CONG_VIEC[phieu.loai_cong_viec]?.type,
      trang_thai_cong_viec: phieu.trang_thai_cong_viec,
      chi_huy_xac_nhan_ket_qua: phieu.chi_huy_xac_nhan_ket_qua,
    };
  }

  return allPhieuGiaoViec.map(convertToBangRows);
}

export async function thongKeCongViecHoanThanh(query) {
  const allPhieuGiaoViec = await PHIEU_GIAO_VIEC.find(query).select('loai_cong_viec ');
  const mapNhomCongViec = {};
  allPhieuGiaoViec.map(phieu => {
    const loaiCongViec = LOAI_CONG_VIEC[phieu.loai_cong_viec]?.type;
    !mapNhomCongViec[loaiCongViec] && (mapNhomCongViec[loaiCongViec] = []);
    mapNhomCongViec[loaiCongViec] && mapNhomCongViec[loaiCongViec].push(phieu._id);
  });

  let allKiemTra, allCoKeHoach, allDoThongSo, allCongViecPhuTro, allKhongKeHoach;
  let kiemTraCHT, coKeHoachCHT, doThongSoCHT, congViecPhuTroCHT, khongKeHoachCHT;
  let dataResponse = [];

  function pushToResult(nhomCongViec, tongSoLuong, chuaHoanThanh, key) {
    dataResponse.push({
      key: key,
      nhom_cong_viec: NHOM_CONG_VIEC[nhomCongViec]?.code,
      so_luong_cong_viec: tongSoLuong,
      so_luong_chua_hoan_thanh: chuaHoanThanh,
    });
  }

  async function getKiemTra() {
    const phieuKiemTra = mapNhomCongViec[KIEM_TRA];
    allKiemTra = await ViTriCongViecModel.distinct('phieu_giao_viec_id', { phieu_giao_viec_id: { $in: phieuKiemTra } });
    kiemTraCHT = await ViTriCongViecModel.distinct('phieu_giao_viec_id', {
      phieu_giao_viec_id: { $in: phieuKiemTra }, trang_thai_hoan_thanh: TRANG_THAI_HOAN_THANH.CHUA_HOAN_THANH,
    });
    pushToResult(KIEM_TRA, allKiemTra.length, kiemTraCHT.length, 1);
  }

  async function getSuaChuaBaoDuong() {
    const phieuSuaChua = mapNhomCongViec[SUA_CHUA_BAO_DUONG];

    allCoKeHoach = await SuaChuaCoKeHoachModel.distinct(
      'phieu_giao_viec_id',
      { phieu_giao_viec_id: { $in: phieuSuaChua }, is_deleted: false },
    );

    coKeHoachCHT = await SuaChuaCoKeHoachModel.distinct(
      'phieu_giao_viec_id',
      {
        phieu_giao_viec_id: { $in: phieuSuaChua },
        tinh_trang_xu_ly: TRANG_THAI_XU_LY.CHUA_XU_LY.code,
        is_deleted: false,
      },
    );

    allKhongKeHoach = await ViTriCongViecModel.distinct(
      'phieu_giao_viec_id',
      { phieu_giao_viec_id: { $in: phieuSuaChua }, is_deleted: false },
    );
    khongKeHoachCHT = await ViTriCongViecModel.distinct(
      'phieu_giao_viec_id',
      {
        phieu_giao_viec_id: { $in: phieuSuaChua },
        trang_thai_hoan_thanh: TRANG_THAI_HOAN_THANH.CHUA_HOAN_THANH,
        is_deleted: false,
      },
    );

    const tong = formatUnique([...allKhongKeHoach, ...allCoKeHoach]).length;
    const chuaHoanThanh = formatUnique([...coKeHoachCHT, ...khongKeHoachCHT]).length;

    pushToResult(SUA_CHUA_BAO_DUONG, tong, chuaHoanThanh, 2);
  }

  async function getDoThongSo() {
    const phieuDoThongSo = mapNhomCongViec[DO_THONG_SO];
    allDoThongSo = await ViTriCongViecModel.distinct(
      'phieu_giao_viec_id',
      { phieu_giao_viec_id: { $in: phieuDoThongSo }, is_deleted: false },
    );
    doThongSoCHT = await ViTriCongViecModel.distinct(
      'phieu_giao_viec_id',
      {
        phieu_giao_viec_id: { $in: phieuDoThongSo },
        trang_thai_hoan_thanh: TRANG_THAI_HOAN_THANH.CHUA_HOAN_THANH,
        is_deleted: false,
      });
    pushToResult(DO_THONG_SO, allDoThongSo.length, doThongSoCHT.length, 3);
  }

  async function getCongViecPhuTro() {
    const congTacPhuTro = mapNhomCongViec[CONG_TAC_PHU_TRO];
    allCongViecPhuTro = await CongViecPhuTroModel.distinct(
      'phieu_giao_viec_id',
      { phieu_giao_viec_id: { $in: congTacPhuTro }, is_deleted: false },
    );

    congViecPhuTroCHT = await CongViecPhuTroModel.distinct(
      'phieu_giao_viec_id',
      {
        phieu_giao_viec_id: { $in: congTacPhuTro },
        tinh_trang_thuc_hien: TRANG_THAI_THUC_HIEN.CHUA_THUC_HIEN.code,
        is_deleted: false,
      });

    console.log('allCongViecPhuTro', allCongViecPhuTro);

    pushToResult(CONG_TAC_PHU_TRO, allCongViecPhuTro.length, congViecPhuTroCHT.length, 4);
  }

  const allPromise = [
    getKiemTra(),
    getSuaChuaBaoDuong(),
    getDoThongSo(),
    getCongViecPhuTro(),
  ];
  await Promise.all(allPromise);
  return dataResponse.sort((a, b) => a.key - b.key);
}

export async function tongHopPhieuSuDungThietBiBay(criteria) {

  if (criteria.phuong_phap_thuc_hien === PHUONG_PHAP_THUC_HIEN.KIEM_TRA_TRUYEN_THONG) {
    criteria = {
      ...criteria,
      $or: [
        { su_dung_drone: false },
        { phuong_phap_thuc_hien: PHUONG_PHAP_THUC_HIEN.KIEM_TRA_TRUYEN_THONG },
      ],
    };
  } else {
    criteria = {
      ...criteria,
      $or: [
        {
          $and: [
            { su_dung_drone: true },
            { phuong_phap_thuc_hien: { $exists: false } },
          ],
        },
        { phuong_phap_thuc_hien: { $in: [PHUONG_PHAP_THUC_HIEN.BAY_TU_DONG, PHUONG_PHAP_THUC_HIEN.BAY_THU_CONG] } },
      ],
    };
  }
  delete criteria.su_dung_drone;
  delete criteria.phuong_phap_thuc_hien;
  criteria.don_vi_giao_phieu_id = criteria.don_vi_id;
  criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code;
  if (criteria.loai_kiem_tra) {
    criteria.loai_cong_viec = criteria.loai_kiem_tra;
    delete criteria.loai_kiem_tra;
  } else {
    const loaiCongViec = Object.values(LOAI_CONG_VIEC).filter(congViec => congViec.type === KIEM_TRA);
    const loaiCongViecCodes = loaiCongViec.map(loai => loai.code);
    criteria.loai_cong_viec = { $in: loaiCongViecCodes };
  }
  delete criteria.don_vi_id;
  if (criteria.duong_day_id) {
    criteria.duong_day_ids = criteria.duong_day_id;
    delete criteria.duong_day_id;
  }

  const allPhieuGiaoViec = await PHIEU_GIAO_VIEC.find(criteria)
    .populate({ path: 'duong_day_ids', select: 'ten_duong_day' })
    .populate({ path: 'don_vi_giao_phieu_id', populate: { path: 'don_vi_cha_id', select: 'ten_don_vi' } })
    .lean();

  let nguoiCongTac, viTriCongViec, khoangCotCongViec, ketQuaKiemTra, khoiLuongQuanLy = 0, khoiLuongQuanLyQ = 0;

  async function getNguoiCongTac() {
    nguoiCongTac = await NguoiCongTacService.getAll({
      phieu_giao_viec_id: { $in: extractIds(allPhieuGiaoViec) },
      is_deleted: false,
    });
  }

  async function getViTriCongViec() {
    let viTriCriteria = {
      phieu_giao_viec_id: { $in: extractIds(allPhieuGiaoViec) },
      vi_tri_id: { $exists: true },
      khoang_cot_id: { $exists: false },
      trang_thai_hoan_thanh: TRANG_THAI_HOAN_THANH.DA_HOAN_THANH,
      is_deleted: false,
    };
    if (criteria.duong_day_ids) {
      const viTriIds = await VAN_HANH.distinct(
        'vi_tri_id', { duong_day_id: criteria.duong_day_ids, is_deleted: false },
      );
      viTriCriteria.vi_tri_id = { $in: viTriIds };
    }
    viTriCongViec = await ViTriCongViecService.getAll(viTriCriteria, { vi_tri_id: 1, phieu_giao_viec_id: 1 })
      .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });
  }

  async function getKhoangCotCongViec() {
    let khoangCotCriteria = {
      phieu_giao_viec_id: { $in: extractIds(allPhieuGiaoViec) },
      vi_tri_id: { $exists: false },
      khoang_cot_id: { $exists: true },
      is_deleted: false,
      trang_thai_hoan_thanh: TRANG_THAI_HOAN_THANH.DA_HOAN_THANH,
    };
    if (criteria.duong_day_ids) {
      let khoangCotIds = await KHOANG_COT.distinct(
        '_id',
        { duong_day_id: criteria.duong_day_ids, is_deleted: false },
      );
      khoangCotCriteria.khoang_cot_id = { $in: khoangCotIds };
    }
    khoangCotCongViec = await ViTriCongViecService.getAll(khoangCotCriteria,
      { khoang_cot_id: 1, phieu_giao_viec_id: 1 })
      .populate({ path: 'khoang_cot_id', select: 'ten_khoang_cot chieu_dai so_mach' });
  }

  async function getKetQuaKiemTra() {
    ketQuaKiemTra = await KetQuaKiemTraService.getAll({
      phieu_giao_viec_id: { $in: extractIds(allPhieuGiaoViec) },
      is_deleted: false,
    }).populate([
      { path: 'vi_tri_id', select: 'ten_vi_tri' },
      { path: 'khoang_cot_id', select: 'ten_khoang_cot' },
      { path: 'duong_day_id', select: 'ten_duong_day' },
      {
        path: 'tieu_chi_id',
        populate: 'tieu_chi_cha_id',
      }]).select('vi_tri_id khoang_cot_id duong_day_id phieu_giao_viec_id');

    ketQuaKiemTra.forEach(ketqua => {
      ketqua.noi_dung_kiem_tra = ketqua.tieu_chi_id?.noi_dung_kiem_tra_id;
    });
  }

  async function getKhoiLuongQuanLy() {
    khoiLuongQuanLy = await Promise.all(criteria.don_vi_giao_phieu_id.map(async dv => {
      const viTriIds = await VI_TRI.distinct('_id', { don_vi_id: dv, is_deleted: false });
      if (!viTriIds.length) return 0;

      const khoangCot = await ThongTinQuanLyService.khoiLuongQuanLyByMotDonVi({
        vi_tri_id: { $in: viTriIds }, is_deleted: false,
      });

      return khoangCot.length > 0 ? khoangCot[0].khoi_luong_quan_ly / 1000 : 0;
    }));
    khoiLuongQuanLy = khoiLuongQuanLy.reduce((a, b) => a + b, 0);
  }

  const allPromise = [
    getNguoiCongTac(),
    getViTriCongViec(),
    getKhoangCotCongViec(),
    getKetQuaKiemTra(),
    getKhoiLuongQuanLy(),
  ];

  await Promise.all(allPromise);

  const groupNguoiCongTacByPhieuId = groupBy(nguoiCongTac, 'phieu_giao_viec_id');
  const groupViTriCongViecByPhieuId = groupBy(viTriCongViec, 'phieu_giao_viec_id');
  const groupKhoangCotCongViecByPhieuId = groupBy(khoangCotCongViec, 'phieu_giao_viec_id');
  const groupKetQuaByPhieuId = groupBy(ketQuaKiemTra, 'phieu_giao_viec_id');

  allPhieuGiaoViec.forEach(element => {
    element.so_luong_nguoi_tham_gia = groupNguoiCongTacByPhieuId[element._id]?.length + 1 || 1;
    element.vi_tri_cong_viec = groupViTriCongViecByPhieuId[element._id] || [];
    element.khoang_cot_cong_viec = groupKhoangCotCongViecByPhieuId[element._id] || [];
    element.ket_qua_kiem_tra = groupKetQuaByPhieuId[element._id] || [];
  });

  // Group theo loại công việc
  const groupByLoaiCongViec = groupBy(allPhieuGiaoViec, 'loai_cong_viec');

  let loaiCongViecKiemTra = [];
  Object.keys(LOAI_CONG_VIEC).filter(key => {
    if (LOAI_CONG_VIEC[key]?.type === KIEM_TRA) {
      loaiCongViecKiemTra.push({
        type: LOAI_CONG_VIEC[key].name,
        so_luong: groupByLoaiCongViec[key]?.length || 0,
      });
    }
  });
  // Thống kê dữ liệu bay
  let thoiGianBay = 0, soViTri = 0, soKhoangCot = 0, phamViBayDaThucHien = 0;
  allPhieuGiaoViec.forEach(item => {
    thoiGianBay += (item.thoi_gian_bay || 0);
    soViTri += (item.vi_tri_cong_viec.length || 0);
    soKhoangCot += (item.khoang_cot_cong_viec.length || 0);
    phamViBayDaThucHien += item.pham_vi_bay_da_thuc_hien || 0;
  });
  const thongKeDulieuBay = {
    thoi_gian_bay: thoiGianBay,
    so_vi_tri: soViTri,
    so_khoang_cot: soKhoangCot,
    pham_vi_bay_da_thuc_hien: Math.round(phamViBayDaThucHien) / 1000,
    khoi_luong_quan_ly: Math.round(khoiLuongQuanLy * 100) / 100,
  };

  allPhieuGiaoViec.sort(function(a, b) {
    return new Date(b.created_at) - new Date(a.created_at);
  });
  return {
    data_table: allPhieuGiaoViec,
    data_pie_chart: loaiCongViecKiemTra,
    data_statistical: thongKeDulieuBay,
  };
}

export function getOne(query, projection = {}) {
  return PHIEU_GIAO_VIEC.findOne(query, projection).lean();
}

export async function findOneById(id) {
  const data = await PHIEU_GIAO_VIEC.findOne({ _id: id, is_deleted: false })
    .populate({
      path: 'chi_huy_truc_tiep_id',
      select: 'full_name username phone bac_an_toan don_vi_id',
      populate: { path: 'don_vi_id', select: 'ten_don_vi' },
    })
    .populate({ path: 'don_vi_chi_huy_truc_tiep_id', select: 'ten_don_vi' })
    .populate({ path: 'nguoi_cap_phieu_id', select: 'full_name username' })
    .populate({ path: 'nguoi_cho_phep_id', select: 'full_name username' })
    .populate({ path: 'nguoi_giao_phieu_id', select: 'full_name username' })
    .populate({ path: 'nguoi_tiep_nhan_id', select: 'full_name username' })
    .populate({ path: 'nguoi_khoa_phieu_id', select: 'full_name username' })
    .populate({ path: 'nguoi_xac_nhan_khoa_id', select: 'full_name username' })
    .populate({
      path: 'don_vi_cong_tac_id don_vi_giao_phieu_id',
      populate: { path: 'don_vi_cha_id', populate: { path: 'don_vi_cha_id' } },
    })
    .populate({ path: 'may_do_id', populate: { path: 'may_do_id' } })
    .populate({ path: 'chuyen_de_id', select: 'ten_chuyen_de noi_dung_kiem_tra_id' })
    .populate({ path: 'ndkt_drone_id', select: 'ten_ndkt_drone noi_dung_kiem_tra_id' })
    .populate({ path: 'duong_day_ids' })
    .lean();
  if (!data) {
    throw createNotFoundError('Phiếu không tồn tại');
  }
  const allDuongDayInScopeIds = extractIds(data.duong_day_ids);

  async function updateTepTin() {
    const fileTapTin = await TapTinService.getAll({ phieu_giao_viec_id: id, is_deleted: false });
    data.file_phuong_an_thuc_hien = await fileTapTin.filter(file => file.type === TAP_TIN_TYPE.PHUONG_AN_THUC_HIEN);
  }

  async function updateBienPhapAnToan() {
    data.bien_phap_an_toan_cong_viec = await BienPhapAnToanCongViecService
      .getAll({ phieu_giao_viec_id: data._id, is_deleted: false })
      .populate('bien_phap_an_toan_id');
  }

  async function updateDieuKienAnToan() {
    data.dieu_kien_an_toan_cong_viec = await DieuKienAnToanCongViecService
      .getAll({ phieu_giao_viec_id: data._id, is_deleted: false })
      .populate('dieu_kien_an_toan_id');
  }

  async function updateToCongTac() {
    data.nguoi_cong_tac = await NguoiCongTacService
      .getAll({ phieu_giao_viec_id: data._id, is_deleted: false })
      .populate({ path: 'user_id', select: 'full_name username phone bac_an_toan' })
      .populate({ path: 'don_vi_id', select: 'ten_don_vi' });
  }

  async function updateNoiDungCongViec() {
    data.noi_dung_cong_viec = await NoiDungCongViecService
      .getAll({ phieu_giao_viec_id: data._id, is_deleted: false })
      .populate('cong_viec_id');
  }

  async function updateCongViecPhuTro() {
    data.cong_viec_phu_tro = await CongViecPhuTroService
      .getAll({ phieu_giao_viec_id: data._id, is_deleted: false })
      .populate('cong_viec_id');
  }

  async function updateThongTinKhoangCot() {
    data.khoang_cot_cong_viec = await ViTriCongViecService
      .getAll({
        phieu_giao_viec_id: data._id,
        vi_tri_id: { $exists: false },
        khoang_cot_id: { $exists: true },
        is_deleted: false,
      })
      .populate({
        path: 'khoang_cot_id',
        populate: { path: 'vi_tri_bat_dau_id vi_tri_ket_thuc_id vi_tri_id', select: 'ten_vi_tri kinh_do vi_do' },
      });
    const viTriIds = data.khoang_cot_cong_viec.map(congViec => congViec.khoang_cot_id?.vi_tri_id._id);
    const vanHanhData = await VanHanhService.findAllInOne({
      duong_day_id: allDuongDayInScopeIds,
      vi_tri_id: viTriIds,
      is_deleted: false,
    });
    vanHanhData.forEach(vanHanh => {
      vanHanh.so_luong_day_tren_pha = vanHanh?.day_dan_id?.[0]?.so_luong_day;
    });
    const vanHanhGroupViTri = groupBy(vanHanhData, 'vi_tri_id');
    for (let i = 0; i < data.khoang_cot_cong_viec.length; i++) {
      const viTriId = data.khoang_cot_cong_viec[i]?.khoang_cot_id?.vi_tri_id?._id?.toString();
      if (viTriId) {
        data.khoang_cot_cong_viec[i].khoang_cot_id.vi_tri_id.van_hanh_id = vanHanhGroupViTri[viTriId];
      }
    }
  }

  async function updateThongTinViTri() {
    const viTriCongViec = await ViTriCongViecService
      .getAll({
        phieu_giao_viec_id: data._id,
        vi_tri_id: { $exists: true },
        khoang_cot_id: { $exists: false },
        is_deleted: false,
      })
      .populate({ path: 'vi_tri_id', select: 'ten_vi_tri kinh_do vi_do' });

    const viTriIds = extractIds(viTriCongViec.map(viTriCongViec => viTriCongViec.vi_tri_id));
    const cauHinhBays = await CauHinhBayService.getAll({ vi_tri_id: { $in: viTriIds } });
    const cauHinhBayGroupByVitri = groupBy(cauHinhBays, 'vi_tri_id');

    const vanHanhData = await VanHanhService.findAllInOne({
      duong_day_id: allDuongDayInScopeIds,
      vi_tri_id: viTriIds,
      is_deleted: false,
    });
    vanHanhData.forEach(vanHanh => {
      vanHanh.so_luong_day_tren_pha = vanHanh?.day_dan_id?.[0]?.so_luong_day;
    });

    const cotDienData = await CotDienService.getAll(
      { vi_tri_id: viTriIds, is_deleted: false },
      { chieu_cao: 1, ten_cot_dien: 1, ma_cot_dien: 1, vi_tri_id: 1, cong_dung_cot: 1 },
    );

    const tiepDatData = await TiepDatService.getAll(
      { vi_tri_id: { $in: viTriIds }, is_deleted: false },
      { ten_tiep_dat: 1, ma_tiep_dat: 1, vi_tri_id: 1, ma_hieu_noi_dat: 1, vat_lieu_tia: 1, so_tia: 1, so_tiep_dat: 1 },
    );
    const dayChongSetData = await DayChongSetService.getAll(
      { vi_tri_id: { $in: viTriIds }, is_deleted: false },
      { ma_day_chong_set: 1, ten_day_chong_set: 1, vi_tri_id: 1, vi_tri_dat: 1 },
    );
    const dayCapQuangData = await DayCapQuangService.getAll(
      { vi_tri_id: { $in: viTriIds }, is_deleted: false },
      { ten_day_cap_quang: 1, ma_day_cap_quang: 1, vi_tri_dat: 1, vi_tri_id: 1 },
    );
    const gieoCheoData = await GieoCheoService.getAll(
      { vi_tri_id: { $in: viTriIds }, is_deleted: false },
      {
        ten_giao_cheo: 1,
        ma_giao_cheo: 1,
        vi_tri_id: 1,
        khoang_cach_giao_cheo: 1,
        khoang_cach_diem_giao_cheo_den_vi_tri: 1,
      },
    );

    const vanHanhGroupViTri = groupBy(vanHanhData, 'vi_tri_id');
    const dayChongSetGroupViTri = groupBy(dayChongSetData, 'vi_tri_id');
    const dayCapQuangGroupViTri = groupBy(dayCapQuangData, 'vi_tri_id');
    const gieoCheoGroupViTri = groupBy(gieoCheoData, 'vi_tri_id');
    for (let i = 0; i < viTriCongViec.length; i++) {
      const viTriId = viTriCongViec[i].vi_tri_id?._id?.toString();
      viTriCongViec[i].vi_tri_id.cau_hinh_bay = cauHinhBayGroupByVitri[viTriId];
      viTriCongViec[i].vi_tri_id.tiep_dat = tiepDatData.find(tiepDat => tiepDat.vi_tri_id.toString() === viTriId);
      viTriCongViec[i].vi_tri_id.cot_dien = cotDienData.find(cotDien => cotDien.vi_tri_id.toString() === viTriId);

      viTriCongViec[i].vi_tri_id.tiep_dat_id = viTriCongViec[i].vi_tri_id.tiep_dat;
      viTriCongViec[i].vi_tri_id.cot_dien_id = viTriCongViec[i].vi_tri_id.cot_dien;
      viTriCongViec[i].vi_tri_id.day_chong_set_id = dayChongSetGroupViTri[viTriId];
      viTriCongViec[i].vi_tri_id.day_cap_quang_id = dayCapQuangGroupViTri[viTriId];
      viTriCongViec[i].vi_tri_id.giao_cheo_id = gieoCheoGroupViTri[viTriId];
      viTriCongViec[i].vi_tri_id.van_hanh_id = vanHanhGroupViTri[viTriId];
    }

    // sort vi tri
    const viTriGroupByDuongDay = {};
    const duongDayIds = data.duong_day_ids?.map(duongDay => duongDay._id);
    duongDayIds?.forEach(duongDayId => {
      viTriCongViec.map(doc => {
        doc.sttVanHanh = doc.vi_tri_id?.van_hanh_id?.find(vanHanh => vanHanh.duong_day_id?._id?.toString() === duongDayId?.toString())?.thu_tu;
        return doc;
      })
        .sort((a, b) => a.sttVanHanh - b.sttVanHanh)
        .forEach(doc => viTriGroupByDuongDay[doc._id] = doc);
    });
    data.vi_tri_cong_viec = Object.values(viTriGroupByDuongDay);
  }

  async function updateDanhSachTonTai() {
    const danhSachCongViec = await KetQuaSuaChuaCoKeHoachService.getAll({
      phieu_giao_viec_id: data._id,
      is_deleted: false,
    })
      .populate({
        path: 'ton_tai_id', select: '',
        populate: {
          path: 'tieu_chi_id vi_tri_id khoang_cot_id',
          select: 'ten_tieu_chi noi_dung_kiem_tra_id ten_vi_tri thu_tu ten_khoang_cot don_vi',
          populate: { path: 'noi_dung_kiem_tra_id', select: 'ten_noi_dung' },
        },
      })
      .lean();
    // console.log('danhSachTonTai', danhSachCongViec);
    // const tonTaiId = danhSachCongViec.map(congViec => congViec?.ton_tai_id?._id).filter(x => !!x);


    for (let i = 0; i < danhSachCongViec.length; i++) {
      const tonTaiId = await HieuChinhTonTaiService.getDetailHieuChinh([danhSachCongViec[i].ton_tai_id]);
      danhSachCongViec[i].ton_tai_id = tonTaiId[0];
    }

    // data.danh_sach_ton_tai = danhSachCongViec.map(async (congViec) => {
    //   const tonTaiId = await HieuChinhTonTaiService.getDetailHieuChinh([congViec.ton_tai_id]);
    //   congViec.ton_tai_id = tonTaiId[0];
    //   return congViec;
    // });
    data.danh_sach_ton_tai = danhSachCongViec;
  }

  async function getPhieuCongTac() {
    data.phieu_cong_tac_id = await PhieuCongTacService.getOne(
      { phieu_giao_viec_id: data._id, is_deleted: false },
      {
        hoan_thanh_cong_tac: 1,
        ket_thuc_cong_tac: 1,
        xac_nhan_khoa_phieu: 1,
        tiep_nhan_lam_viec: 1,
        cho_phep_cong_tac: 1,
        xac_nhan_cap_phieu: 1,
        huy_phieu: 1,
      });
  }

  const updatePromises = [
    getPhieuCongTac(),
    updateDanhSachTonTai(),
    updateThongTinViTri(),
    updateThongTinKhoangCot(),
    updateNoiDungCongViec(),
    updateCongViecPhuTro(),
    updateToCongTac(),
    updateDieuKienAnToan(),
    updateBienPhapAnToan(),
    updateTepTin(),
  ];
  await Promise.all(updatePromises);
  return data;
}

export async function updateTrangThaiKetQuaKiemTra(phieuGiaoViecData, trangThaiUpdate = null) {
  if ((phieuGiaoViecData.loai_cong_viec !== LOAI_CONG_VIEC.CO_KE_HOACH.code)
    && (phieuGiaoViecData.loai_cong_viec !== LOAI_CONG_VIEC.DUY_TU_KET_HOP_KHAC_PHUC.code)) return;

  const danhSachTonTai = Array.isArray(phieuGiaoViecData.danh_sach_ton_tai) ? phieuGiaoViecData.danh_sach_ton_tai : [];
  for (let i = 0; i < danhSachTonTai.length; i++) {
    await KET_QUA_KIEM_TRA.findOneAndUpdate(
      { _id: danhSachTonTai[i].ton_tai_id._id },
      { tinh_trang_xu_ly: trangThaiUpdate || danhSachTonTai[i].tinh_trang_xu_ly });
  }
}

export async function restoreTrangThaiXuLyTonTai(phieuIds) {
  const phieuSuaChuaBaoDuongIds = phieuIds;
  for (let i = 0; i < phieuSuaChuaBaoDuongIds.length; i++) {
    const phieuSuaChuaBaoDuong = await findOneById(phieuSuaChuaBaoDuongIds[i]);
    if (phieuSuaChuaBaoDuong) {
      await updateTrangThaiKetQuaKiemTra(phieuSuaChuaBaoDuong, TRANG_THAI_XU_LY.CHUA_XU_LY.code);
    }
  }
}

export async function huyPhieu() {
  const caiDatVanHanh = await CaiDatVanHanhService.getConfig();
  const kichHoatAutoHuyPhieu = caiDatVanHanh?.kich_hoat_auto_huy_phieu;

  if (!kichHoatAutoHuyPhieu) return;

  const dataPhieu = await PHIEU_GIAO_VIEC.find({
    is_deleted: false,
    thoi_gian_cong_tac_ket_thuc: { '$lte': new Date() },
    trang_thai_cong_viec: TRANG_THAI_PHIEU.GIAO_PHIEU.code,
  }).lean();

  await runUpdateTrangThaiTonTai();
  const danhSachCongViec = await KetQuaSuaChuaCoKeHoachService.getAll({
    phieu_giao_viec_id: extractIds(dataPhieu),
    is_deleted: false,
  }).populate({
    path: 'ton_tai_id', select: '',
    populate: {
      path: 'tieu_chi_id vi_tri_id khoang_cot_id',
      select: 'ten_tieu_chi noi_dung_kiem_tra_id ten_vi_tri thu_tu ten_khoang_cot don_vi',
      populate: { path: 'noi_dung_kiem_tra_id', select: 'ten_noi_dung' },
    },
  }).lean();

  for (let i = 0; i < danhSachCongViec.length; i++) {
    const tonTaiId = await HieuChinhTonTaiService.getDetailHieuChinh([danhSachCongViec[i].ton_tai_id]);
    danhSachCongViec[i].ton_tai_id = tonTaiId[0];
  }
  const groupByPhieuId = groupBy(danhSachCongViec, 'phieu_giao_viec_id');

  for (let i = 0; i < dataPhieu.length; i++) {
    let afterUpdatedData = await PHIEU_GIAO_VIEC.findOneAndUpdate({ _id: dataPhieu[i]._id },
      {
        trang_thai_cong_viec: TRANG_THAI_PHIEU.HUY_PHIEU.code,
        ly_do_huy_giao_phieu: 'Hủy phiếu do người chỉ huy không tiếp nhận phiếu',
        ly_do_huy_phieu: 'Hủy phiếu do người chỉ huy không tiếp nhận phiếu',
        qua_thoi_gian_tiep_nhan: true,
      }).lean();
    afterUpdatedData.danh_sach_ton_tai = groupByPhieuId[dataPhieu[i]._id];
    await updateTrangThaiKetQuaKiemTra(afterUpdatedData, TRANG_THAI_XU_LY.CHUA_XU_LY.code);
  }
}

async function runUpdateTrangThaiTonTai() {
  const phieuHuyDoQuaTg = await PHIEU_GIAO_VIEC.find({
    loai_cong_viec: { $in: ['CO_KE_HOACH', 'DUY_TU_KET_HOP_KHAC_PHUC'] },
    qua_thoi_gian_tiep_nhan: true,
    is_deleted: false,
  });

  const ketQuaCoKeHoach = await KetQuaSuaChuaCoKeHoachService.getAll({
    phieu_giao_viec_id: extractIds(phieuHuyDoQuaTg),
    is_deleted: false,
  });

  const dataRes = await KetQuaKiemTraService.updateMany(
    {
      _id: extractKeyObjectIds(ketQuaCoKeHoach, 'ton_tai_id'),
      tinh_trang_xu_ly: 'DANG_XU_LY',
      is_deleted: false,
    },
    { tinh_trang_xu_ly: 'CHUA_XU_LY' },
  );
}

export async function tongHopCongTacKiemTraTheoDonVi(req, criteria) {
  criteria.don_vi_giao_phieu_id = criteria.don_vi_id;
  if (!criteria.loai_cong_viec) {
    const loaiCongViec = Object.values(LOAI_CONG_VIEC).filter(congViec => congViec.type === KIEM_TRA);
    const loaiCongViecCodes = loaiCongViec.map(loai => loai.code);
    criteria.loai_cong_viec = { $in: loaiCongViecCodes };
  }
  delete criteria.don_vi_id;
  criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code;
  const [phieuBayTuDong, phieuBayThuCong, phieuKiemTraTruyenThong] = await Promise.all([
    PHIEU_GIAO_VIEC.distinct('_id', { ...criteria, phuong_phap_thuc_hien: PHUONG_PHAP_THUC_HIEN.BAY_TU_DONG }),
    PHIEU_GIAO_VIEC.distinct('_id', { ...criteria, phuong_phap_thuc_hien: PHUONG_PHAP_THUC_HIEN.BAY_THU_CONG }),
    PHIEU_GIAO_VIEC.distinct('_id', {
      ...criteria,
      phuong_phap_thuc_hien: PHUONG_PHAP_THUC_HIEN.KIEM_TRA_TRUYEN_THONG,
    }),
  ]);

  const mapDonViBayThuCong = {};
  const mapDonViBayTuDong = {};
  const mapPhamViBayTuDong = {};
  const mapPhamViBayThuCong = {};
  const mapDonViKiemTraTruyenThong = {};
  const mapPhamViTruyenThong = {};
  const mapKhoiLuongQuanLyDonVi = {};

  async function getViTriBayTuDong() {
    const vtcvBayTuDong = await ViTriCongViecService.reportViTriCongViecByDonVi({
      phieu_giao_viec_id: { $in: phieuBayTuDong },
      is_deleted: false,
    });
    vtcvBayTuDong.forEach(dv => mapDonViBayTuDong[dv.don_vi_id] = dv);
  }

  async function getViTriThuCong() {
    const vtcvBayThuCong = await ViTriCongViecService.reportViTriCongViecByDonVi({
      phieu_giao_viec_id: { $in: phieuBayThuCong },
      is_deleted: false,
    });
    vtcvBayThuCong.forEach(dv => mapDonViBayThuCong[dv.don_vi_id] = dv);
  }

  async function getViTriTruyenThong() {
    const vtcvKiemTraTruyenThong = await ViTriCongViecService.reportViTriCongViecByDonVi({
      phieu_giao_viec_id: { $in: phieuKiemTraTruyenThong },
      is_deleted: false,
    });
    vtcvKiemTraTruyenThong.forEach(dv => mapDonViKiemTraTruyenThong[dv.don_vi_id] = dv);
  }

  async function getPhamViBayThuCong() {
    const phamViBayThuCong = await tongHopPhamViBayTheoDonVi({ _id: { $in: phieuBayThuCong } });
    phamViBayThuCong.forEach(phieu => mapPhamViBayThuCong[phieu.don_vi_id] = phieu);
  }

  async function getPhamViBayTuDong() {
    const phamViBayTuDong = await tongHopPhamViBayTheoDonVi({ _id: { $in: phieuBayTuDong } });
    phamViBayTuDong.forEach(phieu => mapPhamViBayTuDong[phieu.don_vi_id] = phieu);
  }

  async function getPhamViBayTruyenThong() {
    const phamViKiemTraTruyenThong = await ViTriCongViecService.tongHopPhamViBayKiemTraTruyenThong({
      phieu_giao_viec_id: { $in: phieuKiemTraTruyenThong },
    });
    phamViKiemTraTruyenThong.forEach(dv => mapPhamViTruyenThong[dv.don_vi_id] = dv);
  }

  async function getKhoiLuongQuanLy() {
    const khoiLuongQuanLy = await Promise.all(criteria.don_vi_giao_phieu_id.map(async dv => {
      const viTriIds = await VI_TRI.distinct('_id', { don_vi_id: dv, is_deleted: false });
      if (!viTriIds.length) return { don_vi_id: dv, khoi_luong_quan_ly: 0 };

      const khoangCot = await ThongTinQuanLyService.khoiLuongQuanLyByMotDonVi({
        vi_tri_id: { $in: viTriIds }, is_deleted: false,
      });

      return {
        don_vi_id: dv,
        khoi_luong_quan_ly: khoangCot.length > 0 ? khoangCot[0].khoi_luong_quan_ly : 0,
      };
    }));

    khoiLuongQuanLy.forEach(item => {
      mapKhoiLuongQuanLyDonVi[item.don_vi_id] = item.khoi_luong_quan_ly;
    });
  }

  const allPromiseAggregate = [
    getViTriBayTuDong(),
    getViTriThuCong(),
    getViTriTruyenThong(),
    getPhamViBayThuCong(),
    getPhamViBayTuDong(),
    getPhamViBayTruyenThong(),
    getKhoiLuongQuanLy(),
  ];

  await Promise.all(allPromiseAggregate);

  let allDonVi = await DonViService.getAll({ _id: { $in: criteria.don_vi_giao_phieu_id }, is_deleted: false });

  allDonVi.forEach(element => {
    element.pham_vi_bay_tu_dong = mapPhamViBayTuDong[element._id]?.pham_vi_bay_da_thuc_hien || 0;
    element.thoi_gian_bay_tu_dong = mapPhamViBayTuDong[element._id]?.thoi_gian_bay || 0;

    element.pham_vi_bay_thu_cong = mapPhamViBayThuCong[element._id]?.pham_vi_bay_da_thuc_hien || 0;
    element.thoi_gian_bay_thu_cong = mapPhamViBayThuCong[element._id]?.thoi_gian_bay || 0;

    element.so_vi_tri_bay_tu_dong = mapDonViBayTuDong[element._id]?.so_luong_vi_tri || 0;
    element.so_khoang_cot_bay_tu_dong = mapDonViBayTuDong[element._id]?.so_luong_khoang_cot || 0;

    element.so_vi_tri_bay_thu_cong = mapDonViBayThuCong[element._id]?.so_luong_vi_tri || 0;
    element.so_khoang_cot_bay_thu_cong = mapDonViBayThuCong[element._id]?.so_luong_khoang_cot || 0;

    element.so_vi_tri_truyen_thong = mapDonViKiemTraTruyenThong[element._id]?.so_luong_vi_tri || 0;
    element.so_khoang_cot_truyen_thong = mapDonViKiemTraTruyenThong[element._id]?.so_luong_khoang_cot || 0;


    element.pham_vi_truyen_thong = mapPhamViTruyenThong[element._id]?.pham_vi_thuc_hien || 0;
    //Khối lượng quản lý tổng
    element.khoi_luong_quan_ly = mapKhoiLuongQuanLyDonVi[element._id] / 1000 || 0;
  });

  const updateDataForParent = (donVi, allDonVi) => {
    allDonVi
      .filter(dv => dv.don_vi_cha_id?.toString() === donVi._id?.toString())
      .forEach(child => {
        donVi.so_vi_tri_bay_tu_dong += child.so_vi_tri_bay_tu_dong || 0;
        donVi.so_khoang_cot_bay_tu_dong += child.so_khoang_cot_bay_tu_dong || 0;
        donVi.so_vi_tri_bay_thu_cong += child.so_vi_tri_bay_thu_cong || 0;
        donVi.so_khoang_cot_bay_thu_cong += child.so_khoang_cot_bay_thu_cong || 0;
        donVi.so_vi_tri_truyen_thong += child.so_vi_tri_truyen_thong || 0;
        donVi.so_khoang_cot_truyen_thong += child.so_khoang_cot_truyen_thong || 0;


        donVi.pham_vi_bay_tu_dong += child.pham_vi_bay_tu_dong || 0;
        donVi.thoi_gian_bay_tu_dong += child.thoi_gian_bay_tu_dong || 0;
        donVi.pham_vi_bay_thu_cong += child.pham_vi_bay_thu_cong || 0;
        donVi.thoi_gian_bay_thu_cong += child.thoi_gian_bay_thu_cong || 0;

        donVi.pham_vi_truyen_thong += child.pham_vi_truyen_thong || 0;
        donVi.khoi_luong_quan_ly += child.khoi_luong_quan_ly || 0;
      });
  };

  const updateDataForOrgUnit = (capDonVi, allDonVi) => {
    for (const donVi of allDonVi) {
      if (donVi.cap_don_vi === capDonVi) {
        updateDataForParent(donVi, allDonVi);
      }
    }
  };

  //Cộng kết quả của đơn vị con => đơn vị cha
  [CAP_DON_VI.TRUYEN_TAI_DIEN, CAP_DON_VI.CONG_TY, CAP_DON_VI.TONG_CONG_TY]
    .forEach(item => updateDataForOrgUnit(item, allDonVi));
  // Làm tròn
  allDonVi.forEach((donVi) => {
    donVi.pham_vi_bay_thu_cong = Math.round(donVi.pham_vi_bay_thu_cong / 10) / 100;
    donVi.pham_vi_bay_tu_dong = Math.round(donVi.pham_vi_bay_tu_dong / 10) / 100;
    donVi.thoi_gian_bay_tu_dong = Math.round(donVi.thoi_gian_bay_tu_dong * 100 / 60) / 100;
    donVi.thoi_gian_bay_thu_cong = Math.round(donVi.thoi_gian_bay_thu_cong * 100 / 60) / 100;
    donVi.pham_vi_truyen_thong = Math.round(donVi.pham_vi_truyen_thong / 10) / 100;
    donVi.khoi_luong_quan_ly = Math.round(donVi.khoi_luong_quan_ly * 100) / 100;
  });
  return allDonVi;
}

export async function tongHopCongTacKiemTraTheoDonViV1(req, criteria) {
  criteria.don_vi_giao_phieu_id = criteria.don_vi_id;
  if (!criteria.loai_cong_viec) {
    const loaiCongViec = Object.values(LOAI_CONG_VIEC).filter(congViec => congViec.type === KIEM_TRA);
    const loaiCongViecCodes = loaiCongViec.map(loai => loai.code);
    criteria.loai_cong_viec = { $in: loaiCongViecCodes };
  }
  delete criteria.don_vi_id;
  criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code;
  criteria.phuong_phap_thuc_hien = {
    $nin: [PHUONG_PHAP_THUC_HIEN.BAY_TU_DONG, PHUONG_PHAP_THUC_HIEN.BAY_THU_CONG, PHUONG_PHAP_THUC_HIEN.KIEM_TRA_TRUYEN_THONG],
  };
  let allPhieuSuDungDrone = await PHIEU_GIAO_VIEC.distinct('_id', { ...criteria, su_dung_drone: true });
  let allPhieuTruyenThong = await PHIEU_GIAO_VIEC.distinct('_id', { ...criteria, su_dung_drone: false });

  const mapDonViDrone = {};
  const mapPhieuGiaoViec = {};
  const mapDonViTruyenThong = {};
  const mapPhamViTruyenThong = {};
  const mapKhoiLuongQuanLyDonVi = {};

  async function getViTriDrone() {
    const viTriCongViecDrone = await ViTriCongViecService.reportViTriCongViecByDonVi({
      phieu_giao_viec_id: { $in: allPhieuSuDungDrone },
      is_deleted: false,
    });
    viTriCongViecDrone.forEach(dv => mapDonViDrone[dv.don_vi_id] = dv);
  }

  async function getViTriTruyenThong() {
    const viTriCongViecTruyenThong = await ViTriCongViecService.reportViTriCongViecByDonVi({
      phieu_giao_viec_id: { $in: allPhieuTruyenThong },
      is_deleted: false,
    });
    viTriCongViecTruyenThong.forEach(dv => mapDonViTruyenThong[dv.don_vi_id] = dv);
  }

  async function getPhamViBayDrone() {
    const phieuSuDungDrone = await tongHopPhamViBayTheoDonVi({ _id: { $in: allPhieuSuDungDrone } });
    phieuSuDungDrone.forEach(phieu => mapPhieuGiaoViec[phieu.don_vi_id] = phieu);
  }

  async function getPhamViBayTruyenThong() {
    const phamViTruyenThong = await ViTriCongViecService.tongHopPhamViBayKiemTraTruyenThong({
      phieu_giao_viec_id: { $in: allPhieuTruyenThong },
    });
    phamViTruyenThong.forEach(dv => mapPhamViTruyenThong[dv.don_vi_id] = dv);
  }

  async function getKhoiLuongQuanLy() {
    //all đơn vị và khối lượng quản lý theo đường dây
    const khoiLuongQuanLy = await Promise.all(criteria.don_vi_giao_phieu_id.map(async dv => {
      const viTriIds = await VI_TRI.distinct('_id', { don_vi_id: dv, is_deleted: false });
      if (!viTriIds.length) return { don_vi_id: dv, khoi_luong_quan_ly: 0 };

      const khoangCot = await ThongTinQuanLyService.khoiLuongQuanLyByMotDonVi({
        vi_tri_id: { $in: viTriIds }, is_deleted: false,
      });

      return {
        don_vi_id: dv,
        khoi_luong_quan_ly: khoangCot.length > 0 ? khoangCot[0].khoi_luong_quan_ly : 0,
      };
    }));

    khoiLuongQuanLy.forEach(item => {
      mapKhoiLuongQuanLyDonVi[item.don_vi_id] = item.khoi_luong_quan_ly;
    });
  }

  const allPromiseAggregate = [
    getViTriDrone(),
    getViTriTruyenThong(),
    getPhamViBayDrone(),
    getPhamViBayTruyenThong(),
    getKhoiLuongQuanLy(),
  ];

  await Promise.all(allPromiseAggregate);

  let allDonVi = await DonViService.getAll({ _id: { $in: criteria.don_vi_giao_phieu_id }, is_deleted: false });

  allDonVi.forEach(element => {
    element.so_vi_tri_drone = mapDonViDrone[element._id]?.so_luong_vi_tri || 0;
    element.so_khoang_cot_drone = mapDonViDrone[element._id]?.so_luong_khoang_cot || 0;
    element.so_vi_tri_truyen_thong = mapDonViTruyenThong[element._id]?.so_luong_vi_tri || 0;
    element.so_khoang_cot_truyen_thong = mapDonViTruyenThong[element._id]?.so_luong_khoang_cot || 0;
    element.pham_vi_bay = mapPhieuGiaoViec[element._id]?.pham_vi_bay_da_thuc_hien || 0;
    element.thoi_gian_bay = mapPhieuGiaoViec[element._id]?.thoi_gian_bay || 0;
    element.pham_vi_truyen_thong = mapPhamViTruyenThong[element._id]?.pham_vi_thuc_hien || 0;
    //Khối lượng quản lý tổng
    element.khoi_luong_quan_ly = mapKhoiLuongQuanLyDonVi[element._id] / 1000 || 0;
  });

  const updateDataForParent = (donVi, allDonVi) => {
    const allChild = allDonVi.filter(dv => dv.don_vi_cha_id?.toString() === donVi._id?.toString());
    allChild.forEach(child => {
      donVi.so_vi_tri_drone += child.so_vi_tri_drone || 0;
      donVi.so_khoang_cot_drone += child.so_khoang_cot_drone || 0;
      donVi.pham_vi_bay += child.pham_vi_bay || 0;
      donVi.thoi_gian_bay += child.thoi_gian_bay || 0;
      donVi.so_vi_tri_truyen_thong += child.so_vi_tri_truyen_thong || 0;
      donVi.so_khoang_cot_truyen_thong += child.so_khoang_cot_truyen_thong || 0;
      donVi.pham_vi_truyen_thong += child.pham_vi_truyen_thong || 0;
      donVi.khoi_luong_quan_ly += child.khoi_luong_quan_ly || 0;
    });
  };

  const updateDataForOrgUnit = (capDonVi, allDonVi) => {
    allDonVi.forEach(donVi => {
      if (donVi.cap_don_vi === capDonVi) updateDataForParent(donVi, allDonVi);
    });
  };

  //Cộng kết quả của đơn vị con => đơn vị cha
  [CAP_DON_VI.TRUYEN_TAI_DIEN, CAP_DON_VI.CONG_TY, CAP_DON_VI.TONG_CONG_TY]
    .forEach(item => updateDataForOrgUnit(item, allDonVi));
  // Làm tròn
  allDonVi.forEach((donVi) => {
    donVi.pham_vi_bay = Math.round(donVi.pham_vi_bay / 10) / 100;
    donVi.thoi_gian_bay = Math.round(donVi.thoi_gian_bay * 100 / 60) / 100;
    donVi.pham_vi_truyen_thong = Math.round(donVi.pham_vi_truyen_thong / 10) / 100;
    donVi.khoi_luong_quan_ly = Math.round(donVi.khoi_luong_quan_ly * 100) / 100;
  });
  return allDonVi;
}

export async function getTimePhieuGiaoViec(req, criteria) {
  criteria.don_vi_giao_phieu_id = criteria.don_vi_id;
  if (!criteria.loai_cong_viec) {
    const loaiCongViec = Object.values(LOAI_CONG_VIEC).filter(congViec => congViec.type === KIEM_TRA);
    const loaiCongViecCodes = loaiCongViec.map(loai => loai.code);
    criteria.loai_cong_viec = { $in: loaiCongViecCodes };
  }
  delete criteria.don_vi_id;
  criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code;
  criteria.phuong_phap_thuc_hien = {
    $nin: [PHUONG_PHAP_THUC_HIEN.BAY_TU_DONG, PHUONG_PHAP_THUC_HIEN.BAY_THU_CONG, PHUONG_PHAP_THUC_HIEN.KIEM_TRA_TRUYEN_THONG],
  };
  let phieuGiaoViec = await PHIEU_GIAO_VIEC.findOne(criteria).sort('-thoi_gian_xac_nhan_khoa_phieu');
  return phieuGiaoViec ? phieuGiaoViec.thoi_gian_xac_nhan_khoa_phieu : null;
}


export async function tongHopPhamViBayTheoDonVi(queryAggregate) {

  return PHIEU_GIAO_VIEC.aggregate([
    {
      $match: {
        ...queryAggregate,
      },
    },
    {
      $group: {
        _id: {
          don_vi_id: '$don_vi_giao_phieu_id',
        },
        thoi_gian_bay: {
          $sum: '$thoi_gian_bay',
        },
        pham_vi_bay_da_thuc_hien: {
          $sum: '$pham_vi_bay_da_thuc_hien',
        },
      },
    },
    {
      $set: {
        don_vi_id: '$_id.don_vi_id',
      },
    },
  ]);
}

export function tongHopPhieuGiaoViecChamTienDo(queryAggregate) {

  return PHIEU_GIAO_VIEC.aggregate([
    { $match: queryAggregate },
    {
      $group: {
        _id: {
          phieu_giao_viec_id: '$_id',
          trang_thai_cong_viec: '$trang_thai_cong_viec',
        },
        phieu_da_huy_do_tiep_nhan_cham: {
          $sum: {
            $cond: [
              {
                $and: [
                  {
                    $eq: [
                      '$qua_thoi_gian_tiep_nhan',
                      true,
                    ],
                  },
                  {
                    $eq: [
                      '$trang_thai_cong_viec',
                      'HUY_PHIEU',
                    ],
                  },
                ],
              },
              1.0,
              0.0,
            ],
          },
        },
        phieu_dang_thuc_hien_cham: {
          $sum: {
            $cond: [
              {
                $and: [
                  {
                    $ne: [
                      '$trang_thai_cong_viec',
                      'HUY_PHIEU',
                    ],
                  },
                  {
                    $ne: [
                      '$trang_thai_cong_viec',
                      'DANG_TAO_PHIEU',
                    ],
                  },
                  {
                    $ne: [
                      '$trang_thai_cong_viec',
                      'KHOA_PHIEU',
                    ],
                  },
                  {
                    $not: [
                      '$thoi_gian_khoa_phieu',
                    ],
                  },
                  {
                    $lt: [
                      '$thoi_gian_cong_tac_ket_thuc',
                      new Date(),
                    ],
                  },
                ],
              },
              1.0,
              0.0,
            ],
          },
        },
        phieu_dang_xac_nhan_khoa_cham: {
          $sum: {
            $cond: [
              {
                $and: [
                  {
                    $ne: [
                      '$trang_thai_cong_viec',
                      'HUY_PHIEU',
                    ],
                  },
                  {
                    $ne: [
                      '$trang_thai_cong_viec',
                      'DANG_TAO_PHIEU',
                    ],
                  },
                  {
                    $ne: [
                      '$trang_thai_cong_viec',
                      'XAC_NHAN_KHOA',
                    ],
                  },
                  {
                    $lt: [
                      '$thoi_gian_cong_tac_ket_thuc',
                      new Date(),
                    ],
                  },
                  {
                    $not: [
                      '$thoi_gian_xac_nhan_khoa_phieu',
                    ],
                  },
                ],
              },
              1.0,
              0.0,
            ],
          },
        },
      },
    },
    {
      $set: {
        phieu_giao_viec_id: '$_id.phieu_giao_viec_id',
        trang_thai_cong_viec: '$_id.trang_thai_cong_viec',
      },
    },
  ]);
}
