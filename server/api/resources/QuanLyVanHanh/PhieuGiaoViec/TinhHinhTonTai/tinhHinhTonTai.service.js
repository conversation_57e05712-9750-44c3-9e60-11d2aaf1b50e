import { extractKeys } from '../../../../utils/dataconverter';
import { TRANG_THAI_XU_LY } from '../../../DanhMuc/TrangThaiXuLy';
import { LOAI_CONG_VIEC } from '../../../DanhMuc/LoaiCongViec';
import * as PhieuGiaoViecService from '../phieuGiaoViec.service';
import * as ViTriCongViecService from '../../ViTriCongViec/viTriCongViec.service';
import * as KetQuaKiemTraService from '../../KetQuaKiemTra/ketQuaKiemTra.service';
import * as HieuChinhTonTaiService from '../HieuChinhTonTai/hieuChinhTonTai.service';


const projectionKetQua = {
  phieu_giao_viec_id: 1,
  ton_tai_cap_tren_id: 1,
  khoang_cot_id: 1,
  khoi_luong: 1,
  tieu_chi_id: 1,
  noi_dung_chi_tiet: 1,
  thoi_gian_tao: 1,
  tinh_trang_xu_ly: 1,
};
const populateKetQuaKiemTra = [
  { path: 'phieu_giao_viec_id', select: 'so_phieu' },
  { path: 'ton_tai_cap_tren_id' },
  {
    path: 'duong_day_id',
    select: 'ten_duong_day duong_day_cu_id',
    populate: { path: 'duong_day_cu_id', select: 'ten_duong_day' },
  },
  { path: 'vi_tri_id', select: 'ten_vi_tri' },
  { path: 'khoang_cot_id', select: 'ten_khoang_cot' },
  {
    path: 'tieu_chi_id', populate: [
      { path: 'tieu_chi_cha_id', select: 'ten_tieu_chi don_vi' },
      { path: 'noi_dung_kiem_tra_id', select: 'ten_noi_dung loai_noi_dung' },
    ],
  },
];

export async function getTinhHinhTonTaiByPhieuGiaoViec(id) {
  const phieuKiemTra = await PhieuGiaoViecService.getById(id, {
    thoi_gian_ky_tiep_nhan: 1,
    trang_thai_cong_viec: 1,
    loai_cong_viec: 1,
  });
  // if (phieuKiemTra.loai_cong_viec !== LOAI_CONG_VIEC.KIEM_TRA_DINH_KY_NGAY.code) return [];
  const allCongViec = await ViTriCongViecService.getAll({ phieu_giao_viec_id: id, is_deleted: false });

  const viTriId = extractKeys(allCongViec.filter(x => x.vi_tri_id), 'vi_tri_id');
  const khoangCotId = extractKeys(allCongViec.filter(x => x.khoang_cot_id), 'khoang_cot_id');

  let ketQuaChuaXuLy = [];
  ketQuaChuaXuLy = await KetQuaKiemTraService.getAll(
    {
      phieu_giao_viec_id: { $ne: id },
      $or: [
        { vi_tri_id: { $in: viTriId } },
        { khoang_cot_id: { $in: khoangCotId } },
      ],
      tinh_trang_xu_ly: TRANG_THAI_XU_LY.CHUA_XU_LY.code,
      is_deleted: false,
      thoi_gian_tao: { $lt: phieuKiemTra.thoi_gian_ky_tiep_nhan },
    },
    projectionKetQua,
  )
    .populate(populateKetQuaKiemTra);

  ketQuaChuaXuLy = await HieuChinhTonTaiService.getDetailHieuChinh(ketQuaChuaXuLy);

  const hieuChinhDaXuLy = await HieuChinhTonTaiService.getAll(
    { phieu_giao_viec_id: id, is_deleted: false },
    { ket_qua_kiem_tra_id: 1 });

  const ketQuaDaXuLyId = extractKeys(hieuChinhDaXuLy, 'ket_qua_kiem_tra_id');

  let ketQuaDaXuLy = await KetQuaKiemTraService.getAll(
    {
      _id: ketQuaDaXuLyId, tinh_trang_xu_ly: TRANG_THAI_XU_LY.DANG_XU_LY.code,
      is_deleted: false,
    },
    projectionKetQua)
    .populate(populateKetQuaKiemTra);

  ketQuaDaXuLy = await HieuChinhTonTaiService.getDetailHieuChinh(ketQuaDaXuLy);

  return [...ketQuaChuaXuLy, ...ketQuaDaXuLy];
}
