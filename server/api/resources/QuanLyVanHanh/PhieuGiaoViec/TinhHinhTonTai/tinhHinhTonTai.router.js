import express from 'express';
import passport from 'passport';
import * as Controller from './tinhHinhTonTai.controller';
import { loggerMiddleware } from '../../../../logs/middleware';

export const tinhHinhTonTaiRouter = express.Router();
tinhHinhTonTaiRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

tinhHinhTonTaiRouter
  .route('/phieugiaoviec/:id').get(Controller.getByPhieuGiaoViec);

