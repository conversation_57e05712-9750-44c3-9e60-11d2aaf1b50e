import * as responseAction from '../../../../helpers/responseHelper';
import Model from '../phieuGiaoViec.model';
import { LOAI_CONG_VIEC, SUA_CHUA_BAO_DUONG } from '../../../DanhMuc/LoaiCongViec';
import { buildQuery, buildQueryNhanVien } from '../phieuGiaoViec.service';
import * as PhieuGiaoViecService from '../phieuGiaoViec.service';
import NGUOI_CONG_TAC from '../../NguoiCongTac/nguoiCongTac.model';

export async function getAll(req, res) {
  try {
    const { criteria, options } = await buildQuery(req);
    if (!criteria.loai_cong_viec) {
      const loaiCongViec = Object.values(LOAI_CONG_VIEC).filter(congViec => congViec.type === SUA_CHUA_BAO_DUONG);
      const loaiCongViecCodes = loaiCongViec.map(loai => loai.code);
      criteria.loai_cong_viec = { $in: loaiCongViecCodes };
    }

    if (!options.hasOwnProperty('sort')) options.sort = { created_at: -1 };
    options.populate = [
      { path: 'don_vi_giao_phieu_id' },
      { path: 'nguoi_cap_phieu_id', select: 'full_name username phone bac_an_toan' },
      { path: 'chi_huy_truc_tiep_id', select: 'full_name' },
      { path: 'duong_day_ids', select: 'ten_duong_day' },
    ];
    const criteriaNhanVien = await buildQueryNhanVien(req, criteria);
    const data = await Model.paginate(criteriaNhanVien, options);
    for (const item of data?.docs) {
      const nguoiCongTac = await NGUOI_CONG_TAC.find({ phieu_giao_viec_id: item?._id, is_deleted: false }).populate({ path: 'user_id', select: 'full_name' }).lean();
      item["nguoi_cong_tac"] = nguoiCongTac;
    }
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    data.docs = await PhieuGiaoViecService.getDataPhieuCongTac(data.docs);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getAllMeCreated(req, res) {
  try {
    const { criteria, options } = await buildQuery(req);
    criteria.nguoi_cap_phieu_id = req.user._id;
    if (!criteria.loai_cong_viec) {
      const loaiCongViec = Object.values(LOAI_CONG_VIEC).filter(congViec => congViec.type === SUA_CHUA_BAO_DUONG);
      const loaiCongViecCodes = loaiCongViec.map(loai => loai.code);
      criteria.loai_cong_viec = { $in: loaiCongViecCodes };
    }
    if (!options.hasOwnProperty('sort')) options.sort = { created_at: -1 };
    options.populate = [
      { path: 'don_vi_giao_phieu_id' },
      { path: 'nguoi_cap_phieu_id', select: 'full_name username phone bac_an_toan' },
      { path: 'chi_huy_truc_tiep_id' },
      { path: 'duong_day_ids', select: 'ten_duong_day' },
    ];
    const data = await Model.paginate(criteria, options);
    for (const item of data?.docs) {
      const nguoiCongTac = await NGUOI_CONG_TAC.find({ phieu_giao_viec_id: item?._id, is_deleted: false }).populate({ path: 'user_id', select: 'full_name' }).lean();
      item["nguoi_cong_tac"] = nguoiCongTac;
    }
    responseAction.success(res, data);
  } catch (err) {
    console.log(err);
    responseAction.error(res, err);
  }
}
