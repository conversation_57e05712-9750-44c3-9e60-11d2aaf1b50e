import express from 'express';
import passport from 'passport';
import * as donviController from './phieuSuaChua.controller';
import { authorizationMiddleware } from '../../../RBAC/middleware';
import GiaoViecPermission from '../../../RBAC/permissions/GiaoViecPermission';
import { loggerMiddleware } from '../../../../logs/middleware';

export const phieuSuaChuaRouter = express.Router();
phieuSuaChuaRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
phieuSuaChuaRouter.post('*', authorizationMiddleware([GiaoViecPermission.CREATE]));
phieuSuaChuaRouter.get('*', authorizationMiddleware([GiaoViecPermission.READ]));
phieuSuaChuaRouter.put('*', authorizationMiddleware([GiaoViecPermission.UPDATE]));
phieuSuaChuaRouter.delete('*', authorizationMiddleware([GiaoViecPermission.DELETE]));
phieuSuaChuaRouter
  .route('/')
  .get(donviController.getAll);
phieuSuaChuaRouter
  .route('/mecreated')
  .get(donviController.getAllMeCreated);
