import express from 'express';
import passport from 'passport';
import * as donviController from './phieuDoLuong.controller';
import { authorizationMiddleware } from '../../../RBAC/middleware';
import GiaoViecPermission from '../../../RBAC/permissions/GiaoViecPermission';
import { loggerMiddleware } from '../../../../logs/middleware';

export const phieuDoLuongRouter = express.Router();
phieuDoLuongRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
phieuDoLuongRouter.post('*', authorizationMiddleware([GiaoViecPermission.CREATE]));
phieuDoLuongRouter.get('*', authorizationMiddleware([GiaoViecPermission.READ]));
phieuDoLuongRouter.put('*', authorizationMiddleware([GiaoViecPermission.UPDATE]));
phieuDoLuongRouter.delete('*', authorizationMiddleware([GiaoViecPermission.DELETE]));
phieuDoLuongRouter
  .route('/')
  .get(donviController.getAll);
phieuDoLuongRouter
  .route('/mecreated')
  .get(donviController.getAllMeCreated);
