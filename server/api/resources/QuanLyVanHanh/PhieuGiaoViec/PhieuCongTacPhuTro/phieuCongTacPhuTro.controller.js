import * as responseHelper from '../../../../helpers/responseHelper';

import PHIEU_GIAO_VIEC from '../phieuGiaoViec.model';

import { CONG_TAC_PHU_TRO, LOAI_CONG_VIEC } from '../../../DanhMuc/LoaiCongViec';

import { extractIds, extractKeys, groupBy } from '../../../../utils/dataconverter';

import { buildQuery, buildQueryNhanVien } from '../phieuGiaoViec.service';

import * as PhieuCongTacPhuTroService from './phieuCongTacPhuTro.service';
import * as NguoiCongTacService from '../../NguoiCongTac/nguoiCongTac.service';
import * as CongViecPhuTroService from '../../CongViecPhuTro/congViecPhuTro.service';


async function buildQueryPhuTro(req) {
  const { criteria, options } = await buildQuery(req);
  const congViecId = criteria.cong_viec_id;
  delete criteria.cong_viec_id;

  if (congViecId) {
    const congViec = await CongViecPhuTroService.getAll({ cong_viec_id: congViecId }, { phieu_giao_viec_id: 1 });
    criteria._id = extractKeys(congViec, 'phieu_giao_viec_id');
  }
  return { criteria, options };
}

async function addNguoiCongTac(phieuData) {
  const phieuId = extractIds(phieuData?.docs);
  const nguoiCongTacData = await NguoiCongTacService.getAll({ phieu_giao_viec_id: phieuId, is_deleted: false })
    .populate({ path: 'user_id', select: 'full_name' });

  const nguoiCongTacGroupByPhieu = groupBy(nguoiCongTacData, 'phieu_giao_viec_id');
  phieuData?.docs.forEach((doc) => {
    doc.nguoi_cong_tac = nguoiCongTacGroupByPhieu[doc?._id];
  });
}

export async function getAll(req, res) {
  try {
    const { criteria, options } = await buildQueryPhuTro(req);

    if (!criteria.loai_cong_viec) {
      const loaiCongViec = Object.values(LOAI_CONG_VIEC).filter(congViec => congViec.type === CONG_TAC_PHU_TRO).map(loai => loai.code);
      criteria.loai_cong_viec = { $in: loaiCongViec };
    }
    if (!options.hasOwnProperty('sort')) options.sort = { created_at: -1 };
    options.populate = [
      { path: 'don_vi_giao_phieu_id' },
      { path: 'nguoi_cap_phieu_id', select: 'full_name username phone bac_an_toan' },
      { path: 'chi_huy_truc_tiep_id', select: 'full_name' },
      // { path: 'duong_day_ids', select: 'ten_duong_day' },
    ];
    const criteriaNhanVien = await buildQueryNhanVien(req, criteria);
    const data = await PHIEU_GIAO_VIEC.paginate(criteriaNhanVien, options);

    await addNguoiCongTac(data);
    await PhieuCongTacPhuTroService.addCongViec(data.docs);

    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllMeCreated(req, res) {
  try {
    const { criteria, options } = await buildQueryPhuTro(req);

    criteria.nguoi_cap_phieu_id = req.user._id;
    if (!criteria.loai_cong_viec) {
      const loaiCongViec = Object.values(LOAI_CONG_VIEC).filter(congViec => congViec.type === CONG_TAC_PHU_TRO);
      const loaiCongViecCodes = loaiCongViec.map(loai => loai.code);
      criteria.loai_cong_viec = { $in: loaiCongViecCodes };
    }
    if (!options.hasOwnProperty('sort')) options.sort = { created_at: -1 };
    options.populate = [
      { path: 'don_vi_giao_phieu_id' },
      { path: 'nguoi_cap_phieu_id', select: 'full_name username phone bac_an_toan' },
      { path: 'chi_huy_truc_tiep_id' },
      { path: 'duong_day_ids', select: 'ten_duong_day' },
    ];
    const data = await PHIEU_GIAO_VIEC.paginate(criteria, options);

    await addNguoiCongTac(data);
    await PhieuCongTacPhuTroService.addCongViec(data.docs);

    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}
