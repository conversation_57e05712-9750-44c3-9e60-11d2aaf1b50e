import express from 'express';
import passport from 'passport';
import * as donviController from './phieuCongTacPhuTro.controller';
import { authorizationMiddleware } from '../../../RBAC/middleware';
import GiaoViecPermission from '../../../RBAC/permissions/GiaoViecPermission';
import { loggerMiddleware } from '../../../../logs/middleware';

export const phieuCongTacPhuTroRouter = express.Router();
phieuCongTacPhuTroRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
phieuCongTacPhuTroRouter.post('*', authorizationMiddleware([GiaoViecPermission.CREATE]));
phieuCongTacPhuTroRouter.get('*', authorizationMiddleware([GiaoViecPermission.READ]));
phieuCongTacPhuTroRouter.put('*', authorizationMiddleware([GiaoViecPermission.UPDATE]));
phieuCongTacPhuTroRouter.delete('*', authorizationMiddleware([GiaoViecPermission.DELETE]));
phieuCongTacPhuTroRouter
  .route('/')
  .get(donviController.getAll);
phieuCongTacPhuTroRouter
  .route('/mecreated')
  .get(donviController.getAllMeCreated);
