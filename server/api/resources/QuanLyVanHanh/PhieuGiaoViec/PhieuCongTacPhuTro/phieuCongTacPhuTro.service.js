import { extractIds, groupBy } from '../../../../utils/dataconverter';

import * as CongViecPhuTroService from '../../CongViecPhuTro/congViecPhuTro.service';


export async function addCongViec(phieuData) {
  const phieuId = extractIds(phieuData);
  const congViecData = await CongViecPhuTroService.getAll({ phieu_giao_viec_id: phieuId, is_deleted: false })
    .populate({ path: 'cong_viec_id' });

  const congViecGroupByPhieu = groupBy(congViecData, 'phieu_giao_viec_id');
  phieuData?.forEach((doc) => {
    doc.cong_viec_phu_tro = congViecGroupByPhieu[doc?._id];
  });
}
