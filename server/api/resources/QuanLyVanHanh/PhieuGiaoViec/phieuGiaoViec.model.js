import mongoose, { Schem<PERSON> } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CHUYEN_DE, DON_VI, DUONG_DAY, PHAN_BO_MAY_DO, PHIEU_GIAO_VIEC, USER, NDKT_DRONE } from '../../../constant/dbCollections';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import { LOAI_CONG_VIEC, THANG_KIEM_TRA } from '../../DanhMuc/LoaiCongViec';
import UserService from '../../User/user.service';
import { PHUONG_PHAP_THUC_HIEN } from '../../../constant/constant';
import { v4 as uuidv4 } from 'uuid';

const schema = new Schema({
  so_phieu: { type: String, required: true, validate: /\S+/ },
  don_vi_giao_phieu_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  nguoi_cap_phieu_id: { type: Schema.Types.ObjectId, ref: USER },
  su_dung_drone: { type: Boolean },
  ten_drone: { type: String },
  serial_drone: { type: String },
  thoi_gian_bay: { type: Number },
  pham_vi_bay_du_kien: { type: Number },
  pham_vi_bay_da_thuc_hien: { type: Number },
  xac_nhan_cap_phieu: { type: String },
  thoi_gian_tao_phieu: { type: Date },
  thoi_gian_giao_phieu: { type: Date },
  loai_cong_viec: {
    type: String,
    enum: Object.keys(LOAI_CONG_VIEC),
  },
  thang_kiem_tra: { type: String },
  quy_kiem_tra: { type: String },
  nam_kiem_tra: { type: String },

  tinh_trang_van_hanh_duong_day: { type: String },
  noi_dung_cong_tac: { type: String },

  don_vi_cong_tac_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  duong_day_id: { type: Schema.Types.ObjectId, ref: DUONG_DAY },
  duong_day_ids: [{ type: Schema.Types.ObjectId, ref: DUONG_DAY }],
  chi_huy_truc_tiep_id: { type: Schema.Types.ObjectId, ref: USER },
  don_vi_chi_huy_truc_tiep_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  chi_huy_xac_nhan_ket_qua: { type: Boolean, default: false },
  thoi_gian_chi_huy_xac_nhan: { type: Date },
  vi_tri_xac_nhan: { type: Schema.Types.Mixed },
  thoi_gian_luu_khoi_luong_cong_viec: { type: Date },

  thoi_gian_cong_tac_bat_dau: Date,
  thoi_gian_cong_tac_ket_thuc: Date,
  thoi_gian_khoa_phieu: Date,
  thoi_gian_xac_nhan_khoa_phieu: Date,
  qua_thoi_gian_tiep_nhan: { type: Boolean, default: false },

  thoi_gian_tu_choi_khoa_phieu: Date,
  ly_do_tu_choi_khoa_phieu: String,

  trang_thai_cong_viec: {
    type: String,
    enum: Object.keys(TRANG_THAI_PHIEU),
    default: TRANG_THAI_PHIEU.DANG_TAO_PHIEU.code,
  },
  ly_do_tu_choi: { type: String },
  thoi_gian_tiep_nhan: { type: Date },
  thoi_gian_tu_choi: { type: Date },

  ky_tao_phieu: { type: Boolean, default: false },
  thoi_gian_ky_tao_phieu: Date,
  nguoi_giao_phieu_id: { type: Schema.Types.ObjectId, ref: USER },

  ky_tiep_nhan: { type: Boolean, default: false },
  thoi_gian_ky_tiep_nhan: Date,
  nguoi_tiep_nhan_id: { type: Schema.Types.ObjectId, ref: USER },

  ky_khoa_phieu: { type: Boolean, default: false },
  thoi_gian_ky_khoa_phieu: Date,
  nguoi_khoa_phieu_id: { type: Schema.Types.ObjectId, ref: USER },

  nguoi_tao_huy_giao_phieu: { type: Boolean, default: false },
  ly_do_huy_giao_phieu: { type: String },

  ly_do_huy_phieu: { type: String },
  thoi_gian_huy_phieu: Date,

  nguoi_xac_nhan_khoa_id: { type: Schema.Types.ObjectId, ref: USER },
  nguoi_cho_phep_id: { type: Schema.Types.ObjectId, ref: USER },

  // do nhiet do tiep xuc
  chuyen_de_id: { type: Schema.Types.ObjectId, ref: CHUYEN_DE },
  may_do_id: { type: Schema.Types.ObjectId, ref: PHAN_BO_MAY_DO },

  gioi_han_ndkt_drone: { type: Boolean, default: false },
  ndkt_drone_id: { type: Schema.Types.ObjectId, ref: NDKT_DRONE },

  luu_y_cong_tac: { type: String },
  ket_qua_thuc_hien: { type: String },
  kien_nghi: { type: String, default: 'Không' },
  sync_time: String,
  sync_status: { type: String, default: 'synced' },

  phuong_phap_thuc_hien: {
    type: String,
    enum: Object.keys(PHUONG_PHAP_THUC_HIEN),
  },
  ly_do_cong_viec_chua_hoan_thanh: { type: String },

  nhan_viec_sau_du_kien: { type: Boolean, default: false },
  ly_do_nhan_viec_sau_du_kien: { type: String },
  uuid: { type: String, default: uuidv4 },

  is_deleted: { type: Boolean, default: false },
  created_by: { type: Schema.Types.ObjectId, ref: USER },
  updated_by: { type: Schema.Types.ObjectId, ref: USER },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.pre('save', async function(next) {
  let phieuGiaoViec = this;
  phieuGiaoViec = await formatDonViChiHuy(phieuGiaoViec);
  next();
});
schema.pre('findOneAndUpdate', async function(next) {
  this._update = await formatDonViChiHuy(this._update);
  next();
});

async function formatDonViChiHuy(phieuGiaoViec) {
  const { chi_huy_truc_tiep_id } = phieuGiaoViec;
  if (chi_huy_truc_tiep_id) {
    const userData = await UserService.getById(chi_huy_truc_tiep_id, { don_vi_id: 1 });
    if (userData?.don_vi_id) {
      phieuGiaoViec.don_vi_chi_huy_truc_tiep_id = userData.don_vi_id;
    }
  }
  return phieuGiaoViec;
}

// Optimized indexes for PhieuGiaoViec queries
// Using background: true and sparse: true for better performance during index creation

// Primary compound index for loai_cong_viec filtering (most common query)
schema.index({ 
  loai_cong_viec: 1, 
  created_at: -1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_loai_cong_viec_created_deleted'
});

// Index for don_vi_giao_phieu_id with time-based sorting
schema.index({ 
  don_vi_giao_phieu_id: 1, 
  created_at: -1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_don_vi_time_deleted'
});

// Index for nguoi_cap_phieu_id queries (for getAllMeCreated)
schema.index({ 
  nguoi_cap_phieu_id: 1, 
  loai_cong_viec: 1,
  created_at: -1
}, { 
  background: true,
  name: 'idx_nguoi_cap_phieu_loai_time'
});

// Index for chi_huy_truc_tiep_id queries (for getAllMe)
schema.index({ 
  chi_huy_truc_tiep_id: 1, 
  created_at: -1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_chi_huy_time_deleted'
});

// Index for trang_thai_cong_viec filtering
schema.index({ 
  trang_thai_cong_viec: 1, 
  don_vi_giao_phieu_id: 1,
  created_at: -1
}, { 
  background: true,
  name: 'idx_trang_thai_don_vi_time'
});

// Index for su_dung_drone filtering (for getAllWithDrone)
schema.index({ 
  su_dung_drone: 1, 
  don_vi_giao_phieu_id: 1,
  trang_thai_cong_viec: 1,
  chi_huy_truc_tiep_id: 1
}, { 
  background: true,
  name: 'idx_drone_don_vi_trang_thai_chi_huy'
});

// Index for uuid existence check (for drone queries)
schema.index({ 
  uuid: 1,
  don_vi_giao_phieu_id: 1
}, { 
  background: true,
  sparse: true,
  name: 'idx_uuid_don_vi'
});

// Index for time-based queries and sorting - most frequently used
schema.index({ 
  thoi_gian_cong_tac_bat_dau: 1,
  created_at: -1
}, { 
  background: true,
  name: 'idx_time_based_queries'
});

// Index for soft delete with general queries
schema.index({ 
  is_deleted: 1, 
  created_at: -1
}, { 
  background: true,
  name: 'idx_soft_delete_time'
});

// Compound index for complex queries with multiple filters
schema.index({ 
  don_vi_giao_phieu_id: 1,
  loai_cong_viec: 1,
  trang_thai_cong_viec: 1,
  created_at: -1
}, { 
  background: true,
  name: 'idx_complex_queries'
});

// Optimized index for findOneById query (_id + is_deleted)
schema.index({ 
  _id: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_findOneById_optimized'
});

// Index for UUID-based queries with soft delete
schema.index({ 
  uuid: 1,
  is_deleted: 1
}, { 
  background: true,
  sparse: true,
  name: 'idx_uuid_deleted'
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(PHIEU_GIAO_VIEC, schema, PHIEU_GIAO_VIEC);
