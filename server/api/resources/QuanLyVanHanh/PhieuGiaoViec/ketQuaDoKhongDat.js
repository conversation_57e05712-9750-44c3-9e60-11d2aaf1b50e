import * as DoDienTroService from '../DoDienTro/doDienTro.service';
import * as KetQuaDoNhietDoService from '../../KetQuaDoNhietDo/ketQuaDoNhietDo.service';
import * as ChiTietDoNhietDoService from '../../KetQuaDoNhietDo/ChiTietDoNhietDo/chiTietDoNhietDo.service';
import * as KetQuaDoKhoangCachPhaDatService
  from '../../QuanLyVanHanh/KetQuaDoKhoangCachPhaDat/ketQuaDoKhoangCachPhaDat.service';

import * as KetQuaKiemTraService from '../KetQuaKiemTra/ketQuaKiemTra.service';
import * as VanHanhService from '../../TongKe/VanHanh/vanHanh.service';

import * as KetQuaDoCorocamService from '../KetQuaDoCorocam/ketQuaDoCorocam.service';
import * as KetQuaDoNhietDoCompositeService
  from '../../QuanLyVanHanh/KetQuaDoNhietDoComposite/ketQuaDoNhietDoComposite.service';

import { KET_LUAN } from '../../../constant/constant';
import { extractIds, extractKeys, groupBy } from '../../../utils/dataconverter';

export async function getKetQuaDoDienTroKhongDat(phieuId) {

  const ketQuaDo = await DoDienTroService.getAll(
    { phieu_giao_viec_id: phieuId, ket_luan: KET_LUAN.KHONG_DAT.value, is_deleted: false },
  ).populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });

  if (!ketQuaDo?.length) return [];

  await themVanHanhChoViTri(ketQuaDo);

  return themTonTaiChoKetQua(phieuId, ketQuaDo);
}

export async function getKetQuaDoNhietDoTiepXucKhongDat(phieuId) {

  const allKetQuaDo = await KetQuaDoNhietDoService.getAll(
    { phieu_giao_viec_id: phieuId, is_deleted: false },
    { vi_tri_id: 1, khoang_cot_id: 1 },
  ).populate({ path: 'vi_tri_id', select: 'ten_vi_tri' })
    .populate({
      path: 'khoang_cot_id', select: 'ten_khoang_cot vi_tri_id',
      populate: { path: 'vi_tri_id', select: 'ten_vi_tri' },
    });

  const allKetQuaDoId = extractIds(allKetQuaDo);

  const allChiTiet = await ChiTietDoNhietDoService.getAll(
    { ket_qua_do_nhiet_do_id: allKetQuaDoId, danh_gia: KET_LUAN.KHONG_DAT.value, is_deleted: false },
    { ket_qua_do_nhiet_do_id: 1, danh_gia: 1 },
  );
  const ketQuaDoKhongDatId = extractKeys(allChiTiet, 'ket_qua_do_nhiet_do_id');

  const allKetQuaDoKhongDat = allKetQuaDo.filter(ketQua => ketQuaDoKhongDatId.includes(ketQua._id.toString()));

  if (!allKetQuaDoKhongDat?.length) return [];

  await themVanHanhChoViTri(allKetQuaDoKhongDat);

  return themTonTaiChoKetQua(phieuId, allKetQuaDoKhongDat);
}

export async function getKetQuaDoKhoangCachPhaDatKhongDat(phieuId) {
  const ketQuaDo = await KetQuaDoKhoangCachPhaDatService.getAll(
    { phieu_giao_viec_id: phieuId, ket_luan: KET_LUAN.KHONG_DAT.value, is_deleted: false },
    { khoang_cot_id: 1 },
  )
    .populate({
      path: 'khoang_cot_id', select: 'ten_khoang_cot vi_tri_id',
      populate: { path: 'vi_tri_id', select: 'ten_vi_tri' },
    });

  if (!ketQuaDo?.length) return [];

  await themVanHanhChoViTri(ketQuaDo);
  return themTonTaiChoKetQua(phieuId, ketQuaDo);
}

export async function getKetQuaDoCorocamKhongDat(phieuId) {

  const ketQuaDo = await KetQuaDoCorocamService.getAll(
    { phieu_giao_viec_id: phieuId, danh_gia: KET_LUAN.KHONG_DAT.value, is_deleted: false },
    { vi_tri_id: 1 },
  )
    .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });

  const viTriUnique = {};
  ketQuaDo.forEach(item => {
    const viTriId = item.vi_tri_id._id;
    viTriUnique[viTriId] = item.vi_tri_id;
  });


  const viTriList = Object.values(viTriUnique).map(viTri => ({ vi_tri_id: viTri }));
  await themVanHanhChoViTri(viTriList);
  return themTonTaiChoKetQua(phieuId, viTriList);
}

export async function getKetQuaDoCachDienCompositeKhongDat(phieuId) {

  const ketQuaDo = await KetQuaDoNhietDoCompositeService.getAll(
    { phieu_giao_viec_id: phieuId, danh_gia: KET_LUAN.KHONG_DAT.value, is_deleted: false },
    { vi_tri_id: 1 },
  )
    .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });

  const viTriUnique = {};
  ketQuaDo.forEach(item => {
    const viTriId = item.vi_tri_id._id;
    viTriUnique[viTriId] = item.vi_tri_id;
  });


  const viTriList = Object.values(viTriUnique).map(viTri => ({ vi_tri_id: viTri }));
  await themVanHanhChoViTri(viTriList);
  return themTonTaiChoKetQua(phieuId, viTriList);
}




async function themVanHanhChoViTri(ketQuaDo = []) {
  const viTriId = ketQuaDo.map(ketQua => {
    if (ketQua.vi_tri_id) return ketQua.vi_tri_id?._id;
    if (ketQua.khoang_cot_id) return ketQua.khoang_cot_id?.vi_tri_id?._id;
  });

  const allVanHanh = await VanHanhService.getAll(
    { vi_tri_id: viTriId, is_deleted: false },
    { vi_tri_id: 1, duong_day_id: 1, ten_van_hanh: 1 },
  ).populate({ path: 'duong_day_id', select: 'ten_duong_day' });

  const vanHanhGroupByViTri = groupBy(allVanHanh, 'vi_tri_id');
  ketQuaDo.forEach(ketQua => {
    if (ketQua.vi_tri_id?._id) {
      ketQua.vi_tri_id.van_hanh_id = vanHanhGroupByViTri[ketQua.vi_tri_id._id];
    }
    if (ketQua.khoang_cot_id?.vi_tri_id?._id) {
      ketQua.khoang_cot_id.vi_tri_id.van_hanh_id = vanHanhGroupByViTri[ketQua.khoang_cot_id.vi_tri_id._id];
    }
  });
}

async function themTonTaiChoKetQua(phieuId, ketQuaDo = []) {
  const allViTri = ketQuaDo.filter(viTriDo => !!viTriDo.vi_tri_id).pluck('vi_tri_id');
  const allKhoangCot = ketQuaDo.filter(viTriDo => !!viTriDo.khoang_cot_id).pluck('khoang_cot_id');

  const viTriId = extractIds(allViTri);
  const khoangCotId = extractIds(allKhoangCot);

  const tonTaiQuery = { phieu_giao_viec_id: phieuId, is_deleted: false };
  const orConditions = [];
  if (viTriId.length) orConditions.push({ vi_tri_id: viTriId });
  if (khoangCotId.length) orConditions.push({ khoang_cot_id: khoangCotId });

  if (orConditions.length === 1) {
    Object.assign(tonTaiQuery, orConditions[0]);
  } else if (orConditions.length > 1) {
    tonTaiQuery['$or'] = orConditions;
  }

  const tonTai = await KetQuaKiemTraService.getAll(tonTaiQuery)
    .populate({ path: 'duong_day_id', select: 'ten_duong_day' })
    .populate({ path: 'tieu_chi_id', populate: 'tieu_chi_cha_id' });

  const objTonTaiByViTri = groupBy(tonTai, 'vi_tri_id');
  const objTonTaiByKhoangCot = groupBy(tonTai, 'khoang_cot_id');

  const viTriList = allViTri.map(viTri => {
    return {
      vi_tri_id: viTri,
      ton_tai: objTonTaiByViTri[viTri._id] || [],
    };
  });
  const khoangCotList = allKhoangCot.map(khoangCot => {
    return {
      khoang_cot_id: khoangCot,
      ton_tai: objTonTaiByKhoangCot[khoangCot._id] || [],
    };
  });
  return [...viTriList, ...khoangCotList];
}

