import * as Service from './hieuChinhTonTai.service';
import Model from './hieuChinhTonTai.model';
import * as controllerHelper from '../../../../helpers/controllerHelper';

const populateOpts = [{ path: 'phieu_giao_viec_id', select: 'so_phieu' }];

export const remove = controllerHelper.createRemoveFunction(Model);
export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, [], populateOpts);
