import express from 'express';
import passport from 'passport';
import * as Controller from './hieuChinhTonTai.controller';
import { loggerMiddleware } from '../../../../logs/middleware';

export const hieuChinhTonTaiRouter = express.Router();
hieuChinhTonTaiRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
hieuChinhTonTaiRouter.route('/')
  .get(Controller.getAll)
  .post(Controller.create);

hieuChinhTonTaiRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
