import mongoose, { Schema } from 'mongoose';
import { KET_QUA_KIEM_TRA, PHIEU_GIAO_VIEC, HIEU_CHINH_TON_TAI, USER } from '../../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
    ket_qua_kiem_tra_id: { type: Schema.Types.ObjectId, ref: KET_QUA_KIEM_TRA, required: true },
    phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC, required: true },
    khoi_luong: { type: String },
    noi_dung_chi_tiet: { type: String },

    nguoi_tao: { type: Schema.Types.ObjectId, ref: USER },
    nguoi_chinh_sua: { type: Schema.Types.ObjectId, ref: USER },
    is_deleted: { type: <PERSON>olean, default: false, select: false },
  },
  {
    timestamps: {
      createdAt: 'thoi_gian_tao',
      updatedAt: 'thoi_gian_cap_nhat',
    },
    collation: { locale: 'vi' },
    versionKey: false,
  });

// Optimized compound indexes for HIEU_CHINH_TON_TAI queries
// Primary index for ket_qua_kiem_tra_id filtering with soft delete
schema.index({ 
  ket_qua_kiem_tra_id: 1, 
  is_deleted: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  name: 'idx_ket_qua_kiem_tra_deleted_time'
});

// Index for phieu_giao_viec_id queries with soft delete
schema.index({ 
  phieu_giao_viec_id: 1, 
  is_deleted: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  name: 'idx_phieu_giao_viec_deleted_time'
});

// Index for nguoi_tao queries with time sorting
schema.index({ 
  nguoi_tao: 1, 
  thoi_gian_tao: -1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_nguoi_tao_time_deleted'
});

// Index for time-based queries
schema.index({ 
  thoi_gian_cap_nhat: -1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_cap_nhat_deleted'
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(HIEU_CHINH_TON_TAI, schema, HIEU_CHINH_TON_TAI);
