import * as ValidatorHelper from '../../../../helpers/validatorHelper';
import HIEU_CHINH_TON_TAI from './hieuChinhTonTai.model';
import { extractIds } from '../../../../utils/dataconverter';

const Joi = require('joi');

const objSchema = Joi.object({
  ket_qua_kiem_tra_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Kết quả kiểm tra')),
  phieu_giao_viec_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Phiếu giao việc')),
});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return HIEU_CHINH_TON_TAI.create(value);
}

export function getAll(query, projection = {}) {
  return HIEU_CHINH_TON_TAI.find(query, projection).lean();
}

export async function getDetailHieuChinh(ketQuaData) {
  ketQuaData = ketQuaData.filter(ketQua => !!ketQua.phieu_giao_viec_id || !!ketQua.ton_tai_cap_tren_id);
  const ketQuaId = extractIds(ketQuaData);
  const hieuChinh = await HIEU_CHINH_TON_TAI.find({ ket_qua_kiem_tra_id: ketQuaId, is_deleted: false })
    .populate({ path: 'phieu_giao_viec_id', select: 'so_phieu' });
  return ketQuaData.map(ketQua => {
    ketQua.hieu_chinh_ton_tai_id = hieuChinh.filter(hieuChinh => hieuChinh.ket_qua_kiem_tra_id.toString() === ketQua._id.toString())
      .sort((a, b) => a.thoi_gian_tao - b.thoi_gian_tao);
    return ketQua;
  });
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await HIEU_CHINH_TON_TAI.findByIdAndUpdate(value._id, value);
  }
}

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};
