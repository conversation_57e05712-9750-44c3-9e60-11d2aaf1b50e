import express from 'express';
import passport from 'passport';

import * as Controller from './phieuGiaoViec.controller';

import { bodyInjectionMiddleware } from '../../../middlewares/bodyInjectionMiddleware';
import { authorizationMiddleware } from '../../RBAC/middleware';
import { loggerMiddleware } from '../../../logs/middleware';

import GiaoViecPermission from '../../RBAC/permissions/GiaoViecPermission';
import WorkPermission from '../../RBAC/permissions/WorkPermission';

export const phieuGiaoViecRouter = express.Router();
phieuGiaoViecRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

phieuGiaoViecRouter
  .route('/donvitest/huyphieu').put(Controller.huyPhieuDonViTest);

phieuGiaoViecRouter
  .route('/:id/ketquakiemtra/single')
  .put(authorizationMiddleware([WorkPermission.UPDATE]), Controller.updateKetQuaKiemTraSingle)
  .post(authorizationMiddleware([WorkPermission.CREATE]), Controller.updateKetQuaKiemTraSingle);

phieuGiaoViecRouter
  .route('/:id/ketquakiemtra/multi')
  .post(authorizationMiddleware([WorkPermission.CREATE]), Controller.createKetQuaKiemTraMulti);

phieuGiaoViecRouter
  .route('/:id/congviecphatsinh')
  .put(authorizationMiddleware([WorkPermission.UPDATE]), Controller.updateCongViecPhatSinh);

phieuGiaoViecRouter
  .route('/:id/congviecphatsinh/single')
  .put(authorizationMiddleware([WorkPermission.UPDATE]), Controller.updateCongViecPhatSinhSingle)
  .post(authorizationMiddleware([WorkPermission.CREATE]), Controller.updateCongViecPhatSinhSingle);

phieuGiaoViecRouter
  .route('/:id/ketquakiemtra')
  .put(authorizationMiddleware([WorkPermission.UPDATE]), Controller.updateKetQuaKiemTra);

phieuGiaoViecRouter
  .route('/:id/ketquaxuly')
  .put(authorizationMiddleware([WorkPermission.UPDATE]), Controller.updateKetQuaXuLy);

phieuGiaoViecRouter
  .route('/:id/ketquaxuly/single')
  .put(authorizationMiddleware([WorkPermission.UPDATE]), Controller.updateKetQuaXuLySingle)
  .post(authorizationMiddleware([WorkPermission.CREATE]), Controller.updateKetQuaXuLySingle);

phieuGiaoViecRouter
  .route('/:id/ketquasuachuacokehoach')
  .put(authorizationMiddleware([WorkPermission.UPDATE]), Controller.updateKetQuaSuaChuaCoKeHoach);

phieuGiaoViecRouter
  .route('/:id/chihuyxacnhanketqua')
  .put(authorizationMiddleware([WorkPermission.CREATE]), Controller.chiHuyXacNhanKetQua);

phieuGiaoViecRouter
  .route('/:id/khoiluongcongviec')
  .put(authorizationMiddleware([WorkPermission.UPDATE]), Controller.updateKhoiLuongCongViec);

phieuGiaoViecRouter.route('/:id/khoaphieu').put(Controller.khoaPhieu);

phieuGiaoViecRouter.post('*', authorizationMiddleware([GiaoViecPermission.CREATE]), bodyInjectionMiddleware);
phieuGiaoViecRouter.get('*', authorizationMiddleware([GiaoViecPermission.READ]));
phieuGiaoViecRouter.put('*', authorizationMiddleware([GiaoViecPermission.UPDATE]));
phieuGiaoViecRouter.delete('*', authorizationMiddleware([GiaoViecPermission.DELETE]));
phieuGiaoViecRouter
  .route('/')
  .get(Controller.getAll)
  .post(Controller.create);

phieuGiaoViecRouter
  .route('/me')
  .get(Controller.getAllMe);

phieuGiaoViecRouter
  .route('/quanlycongviec')
  .get(Controller.getAllQuanLyCongViec);

phieuGiaoViecRouter
  .route('/medrone')
  .get(Controller.getAllMeForDrone);

phieuGiaoViecRouter
  .route('/mecreated')
  .get(Controller.getAllMeCreated);

phieuGiaoViecRouter
  .route('/:id/giaophieu')
  .put(Controller.giaoPhieu);

phieuGiaoViecRouter
  .route('/:id/tiepnhanphieu')
  .put(Controller.tiepNhanPhieu);

phieuGiaoViecRouter
  .route('/:id/tuchoiphieu')
  .put(Controller.tuChoiPhieu);

phieuGiaoViecRouter
  .route('/:id/huygiaophieu')
  .put(Controller.huyGiaoPhieu);

phieuGiaoViecRouter
  .route('/:id/nguoitaohuygiaophieu')
  .put(Controller.nguoiTaoHuyGiaoPhieu);

phieuGiaoViecRouter
  .route('/:id/huykhoaphieu')
  .put(Controller.huyKhoaPhieu);

phieuGiaoViecRouter
  .route('/:id/tuchoikhoaphieu')
  .put(Controller.tuChoiKhoaPhieu);

phieuGiaoViecRouter
  .route('/:id/xacnhanketqua')
  .put(Controller.xacNhanKetQua);

phieuGiaoViecRouter
  .route('/:id/xacnhankhoaphieu')
  .put(Controller.xacNhanKhoaPhieu);

phieuGiaoViecRouter
  .route('/:id/kygiaophieu')
  .put(Controller.kyGiaoPhieu);

phieuGiaoViecRouter
  .route('/:id/huykygiaophieu')
  .put(Controller.huyKyGiaoPhieu);

phieuGiaoViecRouter
  .route('/:id/kytiepnhan')
  .put(Controller.kyTiepNhan);

phieuGiaoViecRouter
  .route('/:id/huykytiepnhan')
  .put(Controller.huyKyTiepNhan);

phieuGiaoViecRouter
  .route('/:id/kykhoaphieu')
  .put(Controller.kyKhoaPhieu);

phieuGiaoViecRouter
  .route('/:id/huykykhoaphieu')
  .put(Controller.huyKyKhoaPhieu);

phieuGiaoViecRouter
  .route('/:id/huyphieu')
  .put(Controller.huyPhieu);

phieuGiaoViecRouter
  .route('/:id/lydohuyphieu')
  .put(Controller.lyDoHuyPhieu);

phieuGiaoViecRouter
  .route('/:id/lydotuchoikhoa')
  .put(Controller.lyDoTuChoiKhoa);

phieuGiaoViecRouter
  .route('/:id/ketquathuchien')
  .put(Controller.updateKetQuaThucHien);

phieuGiaoViecRouter
  .route('/:id/thoigianbay')
  .put(Controller.updateThoiGianBay);

phieuGiaoViecRouter
  .route('/:id/copyphieu')
  .post(Controller.copyPhieu);

phieuGiaoViecRouter
  .route('/:id/ket-qua-do-khong-dat')
  .get(Controller.getKetQuaDoKhongDat);

phieuGiaoViecRouter.route('/:id/phieucongtac').post(Controller.createPhieuCongTacByPhieuGiaoViec);

phieuGiaoViecRouter
  .route('/:id/update-drone')
  .put(Controller.updateDrone)

phieuGiaoViecRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
