import * as responseHelper from '../../../helpers/responseHelper';
import CommonError from '../../../error/CommonError';
import Error from '../../RBAC/Error';

import * as Service from './phieuGiaoViec.service';
import * as KetQuaKiemTraService from '../KetQuaKiemTra/ketQuaKiemTra.service';
import * as CongViecPhatSinhService from '../CongViecPhatSinh/congViecPhatSinh.service';
import * as KetQuaXuLyService from '../KetQuaSuaChuaKhongKeHoach/ketQuaSuaChuaKhongKeHoach.service';
import * as BienPhapAnToanCongViecService from '../BienPhapAnToanCongViec/bienPhapAnToanCongViec.service';
import * as DieuKienAnToanCongViecService from '../DieuKienAnToanCongViec/dieuKienAnToanCongViec.service';
import * as NguoiCongTacService from '../NguoiCongTac/nguoiCongTac.service';
import * as NoiDungCongViecService from '../NoiDungCongViec/noiDungCongViec.service';
import * as ViTriCongViecService from '../ViTriCongViec/viTriCongViec.service';
import * as CongTrinhCongViecService from '../CongTrinhCongViec/congTrinhCongViec.service';
import * as KetQuaSuaChuaCoKeHoachService from '../KetQuaSuaChuaCoKeHoach/ketQuaSuaChuaCoKeHoach.service';
import * as CongViecPhuTroService from '../CongViecPhuTro/congViecPhuTro.service';
import * as PhieuCongTacService from '../PhieuCongTac/phieuCongTac.service';
import * as NotificationService from '../../Notification/notification.service';
import * as AnhViTriService from '../../AnhViTri/anhViTri.service';
import * as DroneService from '../../QuanLyDrone/Drone/drone.service';
import * as HanhLangTuyenService from '../../TongKe/HanhLangTuyen/hanhLangTuyen.service';

import { createChild, createOrUpdateChild } from '../../../helpers/controllerHelper';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import {
  checkRequiredPowerLine,
  isNeedCorridor,
  isNeedCorridorOrLocation,
  isNeedLocation,
  KIEM_TRA,
  LOAI_CONG_VIEC,
  NHOM_CONG_VIEC,
} from '../../DanhMuc/LoaiCongViec';
import * as PhieuGiaoViecError from './phieuGiaoViec.error';
import * as BaoCaoPhaDatService from '../../Report/ReportDoPhaDat/doPhaDat.service';
import * as BaoCaoNhietDoService from '../../Report/ReportDoNhietDo/doNhietDo.service';
import * as BaoCaoDienTroService from '../../Report/ReportDoDienTro/doDienTro.service';
import * as NoiDungKiemTraService from '../../DanhMuc/NoiDungKiemTra/noiDung.service';
import * as TieuChiService from '../../DanhMuc/NoiDungKiemTra/TieuChi/tieuChi.service';
import * as GenerateController from '../../Report/GeneratePhieu/generatePhieu.controller';
import * as TapTinService from '../TapTin/tapTin.service';
import * as HieuChinhTonTaiService from './HieuChinhTonTai/hieuChinhTonTai.service';
import * as ViTriService from '../../TongKe/ViTri/viTri.service';
import * as PhieuCongTacPhuTroService from './PhieuCongTacPhuTro/phieuCongTacPhuTro.service';

import { TRANG_THAI_XU_LY } from '../../DanhMuc/TrangThaiXuLy';
import { TRANG_THAI_HOAN_THANH } from '../../DanhMuc/TrangThaiHoanThanh';

import { removeDuplicateObject } from '../../../common/functionCommons';
import { convertObject, extractIds, extractKeys, groupBy } from '../../../utils/dataconverter';
import { saveHistory } from '../../../utils/documentHistory';
import { createBadRequestError, createError } from '../../../helpers/errorHelper';
import { getViTriByDuongDayList } from '../../TongKe/ViTri/viTri.controller';

import { EXTENSION_FILE, KET_LUAN, PHUONG_PHAP_THUC_HIEN } from '../../../constant/constant';
import { PHIEU_GIAO_VIEC, USER } from '../../../constant/dbCollections';
import { NOTIFICATION_ACTION, NOTIFICATION_TYPE } from '../../Notification/notification.constants';

import Model from './phieuGiaoViec.model';
import KET_QUA_KIEM_TRA from '../KetQuaKiemTra/ketQuaKiemTra.model';
import DO_DIEN_TRO from '../DoDienTro/doDienTro.model';
import CAI_DAT_VAN_HANH from '../../CaiDatVanHanh/caiDatVanHanh.model';
import KET_QUA_DO_KHOANG_CACH_PHA_DAT from '../KetQuaDoKhoangCachPhaDat/ketQuaDoKhoangCachPhaDat.model';
import KET_QUA_DO_NHIET_DO from '../../KetQuaDoNhietDo/ketQuaDoNhietDo.model';
import CHI_TIET_DO_NHIET_DO from '../../KetQuaDoNhietDo/ChiTietDoNhietDo/chiTietDoNhietDo.model';
import PHIEU_CONG_TAC from '../PhieuCongTac/phieuCongTac.model';
import VAN_HANH from '../../TongKe/VanHanh/vanHanh.model';
import KHOANG_COT from '../../TongKe/KhoangCot/khoangCot.model';

import * as KetQuaDoKhongDat from './ketQuaDoKhongDat';


const populateOpts = [
  { path: 'vi_tri_id', select: 'ten_vi_tri' },
  { path: 'duong_day_id', select: 'ten_duong_day' },
  { path: 'khoang_cot_id', populate: 'vi_tri_bat_dau_id vi_tri_ket_thuc_id' },
  { path: 'tieu_chi_id', populate: 'tieu_chi_cha_id' },
  { path: 'cong_viec_id', populate: 'tieu_chi_cha_id' },
  { path: 'anh_vi_tris' },
  { path: 'cach_dien_id' },
  { path: 'day_dan_id' },
  { path: 'day_chong_set_id' },
  { path: 'day_cap_quang_id' },
  { path: 'cot_dien_id' },
  { path: 'giao_cheo_id' },
  { path: 'tiep_dat_id' },
];

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Service.findOneById(id);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.log('err', err);
    return responseHelper.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseHelper.error(res, null, 404, '');
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

async function notificationToUsers(t, beforeUpdatedData, afterUpdatedData) {
  if (afterUpdatedData.trang_thai_cong_viec !== beforeUpdatedData.trang_thai_cong_viec) {
    const nguoiGiaoPhieuId = afterUpdatedData.nguoi_cap_phieu_id._id;
    const chi_huy_truc_tiep_id = afterUpdatedData.chi_huy_truc_tiep_id._id;
    const nguoi_cong_tac = afterUpdatedData.nguoi_cong_tac.map(user => user.user_id._id);
    const notificationData = await Model.findById(afterUpdatedData._id).lean();
    switch (afterUpdatedData.trang_thai_cong_viec) {
      case TRANG_THAI_PHIEU.GIAO_PHIEU.code:
        await NotificationService.notification(NOTIFICATION_TYPE.USER_TO_USER, USER, afterUpdatedData.nguoi_cap_phieu_id, [chi_huy_truc_tiep_id, ...nguoi_cong_tac], NOTIFICATION_ACTION.USER_GIAO_PHIEU, notificationData, PHIEU_GIAO_VIEC, t);
        break;
      case TRANG_THAI_PHIEU.TIEP_NHAN.code:
        await NotificationService.notification(NOTIFICATION_TYPE.USER_TO_USER, USER, afterUpdatedData.chi_huy_truc_tiep_id, [nguoiGiaoPhieuId, ...nguoi_cong_tac], NOTIFICATION_ACTION.USER_NHAN_PHIEU, notificationData, PHIEU_GIAO_VIEC, t);
        break;
      case TRANG_THAI_PHIEU.TU_CHOI_NHAN.code:
        await NotificationService.notification(NOTIFICATION_TYPE.USER_TO_USER, USER, afterUpdatedData.chi_huy_truc_tiep_id, [nguoiGiaoPhieuId, ...nguoi_cong_tac], NOTIFICATION_ACTION.USER_TU_CHOI_NHAN_PHIEU, notificationData, PHIEU_GIAO_VIEC, t);
        break;
      case TRANG_THAI_PHIEU.KHOA_PHIEU.code:
        await NotificationService.notification(NOTIFICATION_TYPE.USER_TO_USER, USER, afterUpdatedData.chi_huy_truc_tiep_id, [nguoiGiaoPhieuId, ...nguoi_cong_tac], NOTIFICATION_ACTION.USER_KHOA_PHIEU, notificationData, PHIEU_GIAO_VIEC, t);
        break;
      case TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code:
        await NotificationService.notification(NOTIFICATION_TYPE.USER_TO_USER, USER, afterUpdatedData.nguoi_cap_phieu_id, [chi_huy_truc_tiep_id, ...nguoi_cong_tac], NOTIFICATION_ACTION.USER_XAC_NHAN_KHOA_PHIEU, notificationData, PHIEU_GIAO_VIEC, t);
        break;
      case TRANG_THAI_PHIEU.TU_CHOI_KHOA.code:
        await NotificationService.notification(NOTIFICATION_TYPE.USER_TO_USER, USER, afterUpdatedData.nguoi_cap_phieu_id, [chi_huy_truc_tiep_id, ...nguoi_cong_tac], NOTIFICATION_ACTION.USER_TU_CHOI_KHOA_PHIEU, notificationData, PHIEU_GIAO_VIEC, t);
        break;
    }
  }
}

async function updateSoMach(req) {

  if (!req.body.su_dung_drone) return;

  const viTriData = await getViTriByDuongDayList(req, req.body.duong_day_ids);
  const khoangCotData = viTriData.map(viTri => viTri.khoang_cot_id)
    .flat()
    .map(khoangCot => ({ _id: khoangCot._id, so_mach: khoangCot.so_mach }));

  const khoangCotObj = convertObject(khoangCotData, '_id');
  req.body.khoang_cot_cong_viec.forEach(khoangCot => {
    khoangCot.so_mach = khoangCotObj[khoangCot.khoang_cot_id]?.so_mach;
  });
}

export async function update(req, res) {

  try {
    const { t } = req;
    const { id } = req.params;
    let { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    let currentData = await Model.findById(id);
    if (currentData.trang_thai_cong_viec !== TRANG_THAI_PHIEU.DANG_TAO_PHIEU.code
      && currentData.trang_thai_cong_viec !== TRANG_THAI_PHIEU.TU_CHOI_NHAN.code
      && currentData.trang_thai_cong_viec !== TRANG_THAI_PHIEU.GIAO_PHIEU.code
    ) {
      return responseHelper.error(res, Error.INSUFFICIENT_PERMISSION);
    }

    const beforeUpdatedData = await Service.findOneById(id);
    if (!beforeUpdatedData) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    if (req.body.loai_cong_viec !== LOAI_CONG_VIEC.CO_KE_HOACH.code) {
      const viTriDeleted = req.body.vi_tri_cong_viec?.filter(item => !!item.is_deleted);
      const viTriNotDeletedYet = req.body.vi_tri_cong_viec?.filter(item => !item.is_deleted);
      const viTriRemoveDuplicate = removeDuplicateObject(viTriNotDeletedYet, 'vi_tri_id');
      const khoangCotDeleted = req.body.khoang_cot_cong_viec?.filter(item => !!item.is_deleted);
      const khoangCotNotDeletedYet = req.body.khoang_cot_cong_viec?.filter(item => !item.is_deleted);
      const khoangCotRemoveDuplicate = removeDuplicateObject(khoangCotNotDeletedYet, 'khoang_cot_id');
      req.body.vi_tri_cong_viec = [viTriDeleted, viTriRemoveDuplicate].flat();
      req.body.khoang_cot_cong_viec = [khoangCotDeleted, khoangCotRemoveDuplicate].flat();
    }
    value = getQuarterYear(value);

    // update số mạch trong khoảng cột công việc
    await updateSoMach(req);

    await Promise.all([
      updateAndSaveHistory(t, beforeUpdatedData, value, req.user._id),
      KetQuaSuaChuaCoKeHoachService.updateTonTai(id, extractIds(req.body.danh_sach_ton_tai)),
      createOrUpdateChild(req.body.bien_phap_an_toan_cong_viec, BienPhapAnToanCongViecService, 'phieu_giao_viec_id', beforeUpdatedData._id),
      createOrUpdateChild(req.body.dieu_kien_an_toan_cong_viec, DieuKienAnToanCongViecService, 'phieu_giao_viec_id', beforeUpdatedData._id),
      createOrUpdateChild(req.body.nguoi_cong_tac, NguoiCongTacService, 'phieu_giao_viec_id', beforeUpdatedData._id),
      createOrUpdateChild(req.body.noi_dung_cong_viec, NoiDungCongViecService, 'phieu_giao_viec_id', beforeUpdatedData._id),
      createOrUpdateChild(req.body.vi_tri_cong_viec, ViTriCongViecService, 'phieu_giao_viec_id', beforeUpdatedData._id),
      createOrUpdateChild(req.body.khoang_cot_cong_viec, ViTriCongViecService, 'phieu_giao_viec_id', beforeUpdatedData._id),
      createOrUpdateChild(req.body.cong_trinh_cong_viec, CongTrinhCongViecService, 'phieu_giao_viec_id', beforeUpdatedData._id),
      createOrUpdateChild(req.body.danh_sach_ton_tai, KetQuaSuaChuaCoKeHoachService, 'phieu_giao_viec_id', beforeUpdatedData._id),
      createOrUpdateChild(req.body.cong_viec_phu_tro, CongViecPhuTroService, 'phieu_giao_viec_id', beforeUpdatedData._id),
    ]);


    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }

    if (afterUpdatedData.phieu_cong_tac) {
      // await autoCreatePhieuCongTac(id, req.body.don_vi_giao_phieu_id);
    }

    await notificationToUsers(t, beforeUpdatedData, afterUpdatedData);
    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function createPhieuCongTacByPhieuGiaoViec(req, res) {
  try {
    const { id } = req.params;
    const phieuGiaoViec = await Model.findById(id).lean();
    const phieuCongTac = await PHIEU_CONG_TAC.findOne({ phieu_giao_viec_id: phieuGiaoViec._id, is_deleted: false });
    if (phieuCongTac) {
      return phieuCongTac;
    }

    const data = await PHIEU_CONG_TAC.create({
      phieu_giao_viec_id: phieuGiaoViec._id,
      so_phieu: await Service.generateSoPhieuCongTac(PHIEU_CONG_TAC, phieuGiaoViec.don_vi_giao_phieu_id),
      don_vi_giao_phieu_id: phieuGiaoViec.don_vi_giao_phieu_id,
    });
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

async function autoCreatePhieuCongTac(phieuGiaoViecId, donViGiaoPhieuId) {
  const checkPhieuCongTac = await PHIEU_CONG_TAC.findOne({
    phieu_giao_viec_id: phieuGiaoViecId,
    is_deleted: false,
  }, { _id: 1 });
  if (!checkPhieuCongTac) {
    await PHIEU_CONG_TAC.create({
      phieu_giao_viec_id: phieuGiaoViecId,
      so_phieu: await Service.generateSoPhieuCongTac(PHIEU_CONG_TAC, donViGiaoPhieuId),
      don_vi_giao_phieu_id: donViGiaoPhieuId,
    });
  }
}

export async function updateKhoiLuongCongViec(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const userId = req.user._id;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    if (!PHUONG_PHAP_THUC_HIEN[value?.phuong_phap_thuc_hien]) {
      return responseHelper.error(res, { message: t('PHUONG_PHAP_THUC_HIEN_KHONG_DUOC_DE_TRONG') }, 400);
    }

    let currentData = await Model.findById(id);

    const nguoiCongTac = await NguoiCongTacService.getAll({ phieu_giao_viec_id: id, is_deleted: false }).lean();
    const nguoiCongTacId = nguoiCongTac.map(user => user.user_id);
    if (currentData.trang_thai_cong_viec !== TRANG_THAI_PHIEU.TIEP_NHAN.code
      || (nguoiCongTacId.length && nguoiCongTacId.includes(userId))
      || currentData.chi_huy_truc_tiep_id.toString() !== userId.toString()) {
      return responseHelper.error(res, Error.INSUFFICIENT_PERMISSION);
    }
    let beforeUpdatedData = await Service.findOneById(id);

    const khoangCotCongViec = beforeUpdatedData.khoang_cot_cong_viec;
    value.pham_vi_bay_da_thuc_hien = value.khoang_cot_cong_viec
      .filter(khoangCot => khoangCot.trang_thai_hoan_thanh === TRANG_THAI_HOAN_THANH.DA_HOAN_THANH)
      .reduce(function(prevValue, currentValue) {
        const congViecCurrent = khoangCotCongViec.find(congViec => congViec._id?.toString() === currentValue._id?.toString());
        return prevValue + ((congViecCurrent?.khoang_cot_id?.chieu_dai || 0) * (congViecCurrent.so_mach || 1));
      }, 0);

    await updateAndSaveHistory(t, beforeUpdatedData, value, req.user._id);
    const congViec = [...(value.vi_tri_cong_viec || []), ...(value.khoang_cot_cong_viec || [])];
    await createOrUpdateChild(congViec, ViTriCongViecService, 'phieu_giao_viec_id', id);

    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    await notificationToUsers(t, currentData, afterUpdatedData);
    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function huyPhieu(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const userId = req.user._id;
    const { error } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    let currentData = await Model.findById(id);
    if (currentData.nguoi_cap_phieu_id.toString() !== userId.toString() ||
      ![TRANG_THAI_PHIEU.DANG_TAO_PHIEU.code, TRANG_THAI_PHIEU.TU_CHOI_NHAN.code].includes(currentData.trang_thai_cong_viec)
    ) {
      return responseHelper.error(res, Error.INSUFFICIENT_PERMISSION);
    }

    const data = await Model.findOneAndUpdate({ _id: id },
      {
        trang_thai_cong_viec: TRANG_THAI_PHIEU.HUY_PHIEU.code,
        thoi_gian_huy_phieu: new Date(),
        ly_do_huy_phieu: req.body.ly_do_huy_phieu,
      });
    if (!data) {
      return responseHelper.error(res, null, 404, '');
    }

    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    await notificationToUsers(t, currentData, afterUpdatedData);

    await notificationToUsers(t, currentData, afterUpdatedData);
    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function lyDoHuyPhieu(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const userId = req.user._id;
    const { error } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    let currentData = await Model.findById(id);
    if (currentData.nguoi_cap_phieu_id.toString() !== userId.toString() ||
      currentData.trang_thai_cong_viec !== TRANG_THAI_PHIEU.HUY_PHIEU.code) {
      return responseHelper.error(res, Error.INSUFFICIENT_PERMISSION);
    }

    const data = await Model.findOneAndUpdate({ _id: id }, { ly_do_huy_phieu: req.body.ly_do_huy_phieu });
    if (!data) {
      return responseHelper.error(res, null, 404, '');
    }
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    await notificationToUsers(t, currentData, afterUpdatedData);

    await notificationToUsers(t, currentData, afterUpdatedData);
    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

async function validatePhieuGiaoViecInfo(t, phieuGiaoViecCurrent, updateData) {
  const phieuGiaoViec = { ...phieuGiaoViecCurrent, ...updateData };
  // console.log('phieuGiaoViecCurrent', phieuGiaoViecCurrent);
  // console.log('updateData', updateData);
  // console.log('phieuGiaoViec', phieuGiaoViec);
  if (!phieuGiaoViec.loai_cong_viec) throw createBadRequestError(t('missing_type_of_work'));
  if (!phieuGiaoViec.chi_huy_truc_tiep_id) throw createError(400, t('missing_leader'));

  if (checkRequiredPowerLine(phieuGiaoViec.loai_cong_viec)) {
    if (!phieuGiaoViec.duong_day_ids || phieuGiaoViec.duong_day_ids.length === 0) throw createError(400, t('line_hasnt_chosen'));
  }

  if (isNeedCorridorOrLocation(phieuGiaoViec.loai_cong_viec)) {
    if ((!phieuGiaoViec.vi_tri_cong_viec || phieuGiaoViec.vi_tri_cong_viec.length === 0) && (!phieuGiaoViec.khoang_cot_cong_viec || phieuGiaoViec.khoang_cot_cong_viec.length === 0)) throw createError(400, t('location_or_tower_hasnt_chosen'));
  }

  if (isNeedCorridor(phieuGiaoViec.loai_cong_viec)) {
    if ((!phieuGiaoViec.khoang_cot_cong_viec || phieuGiaoViec.khoang_cot_cong_viec.length === 0)) throw createError(400, t('tower_hasnt_chosen'));
  }

  if (isNeedLocation(phieuGiaoViec.loai_cong_viec)) {
    if ((!phieuGiaoViec.vi_tri_cong_viec || phieuGiaoViec.vi_tri_cong_viec.length === 0)) throw createError(400, t('location_hasnt_chosen'));
  }

  if (!phieuGiaoViec.thoi_gian_cong_tac_bat_dau) throw createError(400, t('missing_start_time'));
  if (!phieuGiaoViec.thoi_gian_cong_tac_ket_thuc) throw createError(400, t('missing_end_time'));
  if (phieuGiaoViec.thoi_gian_cong_tac_ket_thuc < phieuGiaoViec.thoi_gian_cong_tac_bat_dau) throw createError(400, t('start_time_must_before_end_time'));

  if ((phieuGiaoViec.loai_cong_viec === LOAI_CONG_VIEC.CO_KE_HOACH.code) || (phieuGiaoViec.loai_cong_viec === LOAI_CONG_VIEC.DUY_TU_KET_HOP_KHAC_PHUC.code)) {
    if (!phieuGiaoViec.danh_sach_ton_tai || phieuGiaoViec.danh_sach_ton_tai.length === 0) throw createError(400, t('exist_process_hasnt_chosen'));
  }

  // if (isNeedSafeSolution(phieuGiaoViec.loai_cong_viec)) { // Không thể check được do liên quan phiếu công tác
  //   if ((!phieuGiaoViec.bien_phap_an_toan_cong_viec || phieuGiaoViec.bien_phap_an_toan_cong_viec.length === 0)) throw createError(400, 'Thiếu biện pháp an toàn');
  // }
  //
  // if (isNeedSafeCondition(phieuGiaoViec.loai_cong_viec)) {
  //   if ((!phieuGiaoViec.dieu_kien_an_toan_cong_viec || phieuGiaoViec.dieu_kien_an_toan_cong_viec.length === 0)) throw createError(400, 'Thiếu điều kiện an toàn');
  // }
}

const HANH_DONG = {
  GIAO_PHIEU: 'GIAO_PHIEU',
  TIEP_NHAN: 'TIEP_NHAN',
  HUY_TIEP_NHAN: 'HUY_TIEP_NHAN',
  HUY_GIAO_PHIEU: 'HUY_GIAO_PHIEU',
  XAC_NHAN_KET_QUA: 'XAC_NHAN_KET_QUA',
  KHOA_PHIEU: 'KHOA_PHIEU',
  HUY_KHOA_PHIEU: 'HUY_KHOA_PHIEU',
  TU_CHOI_KHOA_PHIEU: 'TU_CHOI_KHOA_PHIEU',
  XAC_NHAN_KHOA_PHIEU: 'XAC_NHAN_KHOA_PHIEU',
};

const validatePhieuState = (t, userId, phieuCongViec, hanhDong) => {
  if (hanhDong === HANH_DONG.GIAO_PHIEU) {
    if (isNaN(Date.parse(phieuCongViec?.thoi_gian_cong_tac_bat_dau)) || phieuCongViec.thoi_gian_cong_tac_bat_dau <= new Date()) {
      throw createBadRequestError(t('overdue_task_cant_delivery'));
    }
    if (phieuCongViec.trang_thai_cong_viec !== TRANG_THAI_PHIEU.DANG_TAO_PHIEU.code) {
      throw createBadRequestError(t('task_cant_delivery'));
    }
  }

  if (hanhDong === HANH_DONG.TIEP_NHAN) {
    if (isNaN(Date.parse(phieuCongViec?.thoi_gian_cong_tac_ket_thuc)) || phieuCongViec.thoi_gian_cong_tac_ket_thuc <= new Date()) {
      throw createBadRequestError(t('overdue_task_cant_reception'));
    }
    if (phieuCongViec.trang_thai_cong_viec !== TRANG_THAI_PHIEU.GIAO_PHIEU.code) {
      throw createBadRequestError(t('task_need_delivery'));
    }
    if (phieuCongViec.chi_huy_truc_tiep_id == userId) {
      throw createBadRequestError(t('leader_can_reception_work'));
    }
  }

  if (hanhDong === HANH_DONG.HUY_TIEP_NHAN) {
    if (phieuCongViec.trang_thai_cong_viec !== TRANG_THAI_PHIEU.GIAO_PHIEU.code) {
      throw createBadRequestError(t('task_need_delivery'));
    }
    if (phieuCongViec.chi_huy_truc_tiep_id == userId) {
      throw createBadRequestError(t('leader_can_refuse_work'));
    }
    if (isNaN(Date.parse(phieuCongViec?.thoi_gian_cong_tac_ket_thuc)) || phieuCongViec.thoi_gian_cong_tac_ket_thuc <= new Date()) {
      throw createBadRequestError(t('overdue_task_cant_refuse'));
    }
  }

  if (hanhDong === HANH_DONG.XAC_NHAN_KET_QUA) {
    if (phieuCongViec.trang_thai_cong_viec !== TRANG_THAI_PHIEU.TIEP_NHAN.code) {
      throw createBadRequestError(t('task_need_reception'));
    }
    if (phieuCongViec.chi_huy_truc_tiep_id == userId) {
      throw createBadRequestError(t('leader_can_confirm_work'));
    }
  }

  if (hanhDong === HANH_DONG.HUY_GIAO_PHIEU) {
    if (phieuCongViec.trang_thai_cong_viec === TRANG_THAI_PHIEU.TIEP_NHAN.code) {
      throw createBadRequestError(t('leader_reception_task'));
    }
    if (phieuCongViec.trang_thai_cong_viec !== TRANG_THAI_PHIEU.GIAO_PHIEU.code && phieuCongViec.trang_thai_cong_viec !== TRANG_THAI_PHIEU.TU_CHOI_NHAN.code) {
      throw createBadRequestError(t('task_need_delivery'));
    }
  }

  if (hanhDong === HANH_DONG.HUY_KHOA_PHIEU) {
    if (phieuCongViec.trang_thai_cong_viec !== TRANG_THAI_PHIEU.KHOA_PHIEU.code && phieuCongViec.trang_thai_cong_viec !== TRANG_THAI_PHIEU.TU_CHOI_KHOA.code) {
      throw createBadRequestError(t('task_need_lock'));
    }
    if (phieuCongViec.chi_huy_truc_tiep_id == userId) {
      throw createBadRequestError(t('leader_can_cancel_lock'));
    }
  }

  if (hanhDong === HANH_DONG.TU_CHOI_KHOA_PHIEU) {
    if (phieuCongViec.trang_thai_cong_viec !== TRANG_THAI_PHIEU.KHOA_PHIEU.code) {
      throw createBadRequestError(t('task_need_lock'));
    }
  }

  if (hanhDong === HANH_DONG.XAC_NHAN_KHOA_PHIEU) {

    if (phieuCongViec.trang_thai_cong_viec === TRANG_THAI_PHIEU.TIEP_NHAN.code) {
      throw createBadRequestError(t('leader_cancel_lock_task'));
    }

    if (![TRANG_THAI_PHIEU.KHOA_PHIEU.code, TRANG_THAI_PHIEU.TU_CHOI_KHOA.code].includes(phieuCongViec.trang_thai_cong_viec)) {
      throw createBadRequestError(t('task_need_returned_can_confirm'));
    }

    if (!phieuCongViec.chi_huy_xac_nhan_ket_qua) {
      throw createBadRequestError(t('cant_confirm_lock_not_result'));
    }
    if (![TRANG_THAI_PHIEU.KHOA_PHIEU.code, TRANG_THAI_PHIEU.TU_CHOI_KHOA.code].includes(phieuCongViec.trang_thai_cong_viec)) {
      throw PhieuGiaoViecError.LOI_XAC_NHAN_KHOA_KHI_CHUA_DU_DIEU_KIEN();
    }
  }

};

export async function giaoPhieu(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    let currentData = await Model.findById(id);
    validatePhieuState(t, req.user._id, currentData, HANH_DONG.GIAO_PHIEU);
    const dataUpdate = {
      trang_thai_cong_viec: TRANG_THAI_PHIEU.GIAO_PHIEU.code,
      thoi_gian_giao_phieu: new Date(),
      nguoi_giao_phieu_id: req.user._id,
      ky_tao_phieu: true,
      thoi_gian_ky_tao_phieu: new Date(),
      nguoi_tao_huy_giao_phieu: false,
      ly_do_huy_giao_phieu: null,
    };
    let beforeUpdatedData = await Service.findOneById(id);
    await updateAndSaveHistory(t, beforeUpdatedData, dataUpdate, req.user._id);
    saveHistory(beforeUpdatedData, Model, 'update', req.user._id);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }
    await Service.updateTrangThaiKetQuaKiemTra(afterUpdatedData, TRANG_THAI_XU_LY.DANG_XU_LY.code);
    await notificationToUsers(t, beforeUpdatedData, afterUpdatedData);
    GenerateController.generatePhieu(res, afterUpdatedData, TRANG_THAI_PHIEU.GIAO_PHIEU.code, EXTENSION_FILE.PDF);

    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function tiepNhanPhieu(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const dataUpdate = {
      trang_thai_cong_viec: TRANG_THAI_PHIEU.TIEP_NHAN.code,
      thoi_gian_tiep_nhan: new Date(),
      nguoi_tiep_nhan_id: req.user._id,
      ky_tiep_nhan: true,
      thoi_gian_ky_tiep_nhan: new Date(),
    };

    let currentData = await Service.findOneById(id);
    validatePhieuState(t, req.user._id, currentData, HANH_DONG.TIEP_NHAN);
    await updateAndSaveHistory(t, currentData, dataUpdate, req.user._id);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }
    await notificationToUsers(t, currentData, afterUpdatedData);

    GenerateController.generatePhieu(res, afterUpdatedData, TRANG_THAI_PHIEU.TIEP_NHAN.code, EXTENSION_FILE.PDF);

    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function tuChoiPhieu(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const dataUpdate = {
      trang_thai_cong_viec: TRANG_THAI_PHIEU.TU_CHOI_NHAN.code,
      thoi_gian_tu_choi: new Date(),
      ly_do_tu_choi: req.body.ly_do_tu_choi,
      ky_tiep_nhan: false,
      thoi_gian_ky_tiep_nhan: null,
    };
    let beforeUpdatedData = await Service.findOneById(id);
    validatePhieuState(t, req.user._id, beforeUpdatedData, HANH_DONG.HUY_TIEP_NHAN);
    await updateAndSaveHistory(t, beforeUpdatedData, dataUpdate, req.user._id);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }
    await notificationToUsers(t, beforeUpdatedData, afterUpdatedData);
    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function chiHuyXacNhanKetQua(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error || !value.hasOwnProperty('chi_huy_xac_nhan_ket_qua')) return responseHelper.error(res, error, 400);
    const phieuGiaoViecData = await Model.findById(id)
      .populate({ path: 'chuyen_de_id', select: 'noi_dung_kiem_tra_id' });
    if (!phieuGiaoViecData) return responseHelper.error(res, CommonError.NOT_FOUND);
    validatePhieuState(t, req.user._id, phieuGiaoViecData, HANH_DONG.XAC_NHAN_KET_QUA);
    if ([LOAI_CONG_VIEC.KIEM_TRA_DINH_KY_NGAY.code,
      LOAI_CONG_VIEC.KIEM_TRA_DINH_KY_DEM.code,
      LOAI_CONG_VIEC.KIEM_TRA_SU_CO.code,
      LOAI_CONG_VIEC.KIEM_TRA_DOT_XUAT.code,
      LOAI_CONG_VIEC.KIEM_TRA_KY_THUAT.code,
      LOAI_CONG_VIEC.KIEM_TRA_CHUYEN_DE.code].includes(phieuGiaoViecData.loai_cong_viec) && value.chi_huy_xac_nhan_ket_qua) {

      const queryTieuChi = { is_deleted: false };
      if (phieuGiaoViecData.loai_cong_viec === LOAI_CONG_VIEC.KIEM_TRA_CHUYEN_DE.code) {
        queryTieuChi.noi_dung_kiem_tra_id = phieuGiaoViecData.chuyen_de_id.noi_dung_kiem_tra_id;
      } else {
        const allNoiDungKiemTra = await NoiDungKiemTraService.getAll({ is_deleted: false }, { _id: 1 });
        queryTieuChi.noi_dung_kiem_tra_id = extractIds(allNoiDungKiemTra);
      }

      const allTieuChi = await TieuChiService.getAll(queryTieuChi, { _id: 1 });

      const ketQuaKiemTra = await KetQuaKiemTraService.getAll(
        { phieu_giao_viec_id: id, tieu_chi_id: extractIds(allTieuChi), is_deleted: false },
        { duong_day_id: 1, tieu_chi_id: 1, cach_dien_id: 1 },
      ).populate({ path: 'tieu_chi_id', select: 'thiet_bi' });

      const checkNotExistDuongDay = ketQuaKiemTra.find(ketQua => !ketQua?.duong_day_id?.length);
      if (checkNotExistDuongDay) {
        return responseHelper.error(res, { message: t('test_result_cant_empty_line') });
      }

      const checkCachDien = ketQuaKiemTra.find(ketQua => ketQua?.tieu_chi_id.thiet_bi === 'CACH_DIEN' && !ketQua?.cach_dien_id);
      if (checkCachDien) {
        return responseHelper.error(res, { message: t('test_result_cant_empty_electrical_insulation') });
      }
    }

    const dataUpdate = {
      chi_huy_xac_nhan_ket_qua: value.chi_huy_xac_nhan_ket_qua,
      thoi_gian_chi_huy_xac_nhan: new Date(),
    };
    let beforeUpdatedData = await Service.findOneById(id);
    await updateAndSaveHistory(t, beforeUpdatedData, dataUpdate, req.user._id);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }

    await notificationToUsers(t, beforeUpdatedData, afterUpdatedData);
    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function khoaPhieu(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const check = await Model.findOne(
      { _id: id, trang_thai_cong_viec: TRANG_THAI_PHIEU.TIEP_NHAN.code },
      {
        _id: 1, chi_huy_xac_nhan_ket_qua: 1, phuong_phap_thuc_hien: 1,
        loai_cong_viec: 1, ten_drone: 1, thoi_gian_bay: 1, su_dung_drone: 1, ly_do_cong_viec_chua_hoan_thanh: 1,
      });
    if (!check) return responseHelper.error(res, CommonError.NOT_FOUND);
    if (!check.chi_huy_xac_nhan_ket_qua) {
      return responseHelper.error(res, { message: t('unconfirm_task_cant_return') });
    }
    //Check khoi luong cong viec cho phieu kiem tra
    if (([LOAI_CONG_VIEC.KIEM_TRA_DINH_KY_NGAY.code,
      LOAI_CONG_VIEC.KIEM_TRA_DINH_KY_DEM.code,
      LOAI_CONG_VIEC.KIEM_TRA_SU_CO.code,
      LOAI_CONG_VIEC.KIEM_TRA_DOT_XUAT.code,
      LOAI_CONG_VIEC.KIEM_TRA_KY_THUAT.code,
      LOAI_CONG_VIEC.KIEM_TRA_CHUYEN_DE.code].includes(check.loai_cong_viec)) && check.su_dung_drone) {
      const khoiLuongCongViec = await ViTriCongViecService
        .getAll({
          phieu_giao_viec_id: id,
          is_deleted: false,
        });

      const countHoanThanh = khoiLuongCongViec.filter(khoiLuong => khoiLuong.trang_thai_hoan_thanh === TRANG_THAI_HOAN_THANH.DA_HOAN_THANH).length;

      if (countHoanThanh < khoiLuongCongViec.length && !check.ly_do_cong_viec_chua_hoan_thanh) {
        return responseHelper.error(res, { message: t('bo_sung_li_do_chua_hoan_thanh') });
      }
      if (!!countHoanThanh && check.phuong_phap_thuc_hien !== PHUONG_PHAP_THUC_HIEN.KIEM_TRA_TRUYEN_THONG && (!check.ten_drone || !check.thoi_gian_bay)) {
        return responseHelper.error(res, { message: t('bo_sung_thong_tin_bay') });
      }
    }

    const dataUpdate = {
      trang_thai_cong_viec: TRANG_THAI_PHIEU.KHOA_PHIEU.code,
      thoi_gian_khoa_phieu: new Date(),
      ky_khoa_phieu: true,
      nguoi_khoa_phieu_id: req.user._id,
      thoi_gian_ky_khoa_phieu: new Date(),
    };
    let beforeUpdatedData = await Service.findOneById(id);
    await updateAndSaveHistory(t, beforeUpdatedData, dataUpdate, req.user._id);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }
    await notificationToUsers(t, beforeUpdatedData, afterUpdatedData);

    GenerateController.generatePhieu(res, afterUpdatedData, TRANG_THAI_PHIEU.KHOA_PHIEU.code, EXTENSION_FILE.PDF);

    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

async function updateAndSaveHistory(t, phieuGiaoViecCurrent, dataUpdate, userId) {
  await validatePhieuGiaoViecInfo(t, phieuGiaoViecCurrent, dataUpdate);
  let beforeUpdatedData = await Model.findOneAndUpdate({ _id: phieuGiaoViecCurrent._id }, dataUpdate);
  saveHistory(beforeUpdatedData, Model, 'update', userId);
  return beforeUpdatedData;
}

export async function huyKhoaPhieu(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const dataUpdate = {
      trang_thai_cong_viec: TRANG_THAI_PHIEU.TIEP_NHAN.code,
      ky_khoa_phieu: false,
      thoi_gian_ky_khoa_phieu: null,
      thoi_gian_khoa_phieu: null,
    };
    let beforeUpdatedData = await Service.findOneById(id);
    validatePhieuState(t, req.user._id, beforeUpdatedData, HANH_DONG.HUY_KHOA_PHIEU);
    await updateAndSaveHistory(t, beforeUpdatedData, dataUpdate, req.user._id);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }
    await notificationToUsers(t, beforeUpdatedData, afterUpdatedData);
    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function huyGiaoPhieu(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const dataUpdate = {
      trang_thai_cong_viec: TRANG_THAI_PHIEU.DANG_TAO_PHIEU.code,
      thoi_gian_giao_phieu: null,
      ky_tao_phieu: false,
      thoi_gian_ky_tao_phieu: null,
      ky_tiep_nhan: false,
      thoi_gian_ky_tiep_nhan: null,
    };
    let beforeUpdatedData = await Service.findOneById(id);
    validatePhieuState(t, req.user._id, beforeUpdatedData, HANH_DONG.HUY_GIAO_PHIEU);
    await updateAndSaveHistory(t, beforeUpdatedData, dataUpdate, req.user._id);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }
    await Service.updateTrangThaiKetQuaKiemTra(afterUpdatedData, TRANG_THAI_XU_LY.CHUA_XU_LY.code);
    await notificationToUsers(t, beforeUpdatedData, afterUpdatedData);
    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function nguoiTaoHuyGiaoPhieu(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const dataUpdate = {
      trang_thai_cong_viec: TRANG_THAI_PHIEU.DANG_TAO_PHIEU.code,
      thoi_gian_giao_phieu: null,
      ky_tao_phieu: false,
      thoi_gian_ky_tao_phieu: null,
      ky_tiep_nhan: false,
      thoi_gian_ky_tiep_nhan: null,
      nguoi_tao_huy_giao_phieu: true,
      ly_do_huy_giao_phieu: req.body.ly_do_huy_giao_phieu,
    };
    const beforeUpdatedData = await Service.findOneById(id);
    validatePhieuState(t, req.user._id, beforeUpdatedData, HANH_DONG.HUY_GIAO_PHIEU);
    await updateAndSaveHistory(t, beforeUpdatedData, dataUpdate, req.user._id);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }
    await Service.updateTrangThaiKetQuaKiemTra(afterUpdatedData, TRANG_THAI_XU_LY.CHUA_XU_LY.code);
    await notificationToUsers(t, beforeUpdatedData, afterUpdatedData);
    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function tuChoiKhoaPhieu(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const dataUpdate = {
      trang_thai_cong_viec: TRANG_THAI_PHIEU.TU_CHOI_KHOA.code,
      thoi_gian_tu_choi_khoa_phieu: new Date(),
      ly_do_tu_choi_khoa_phieu: req.body.ly_do_tu_choi_khoa_phieu,
    };
    const beforeUpdatedData = await Service.findOneById(id);
    validatePhieuState(t, req.user._id, beforeUpdatedData, HANH_DONG.HUY_KHOA_PHIEU);
    await updateAndSaveHistory(t, beforeUpdatedData, dataUpdate, req.user._id);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }
    await notificationToUsers(t, beforeUpdatedData, afterUpdatedData);
    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function lyDoTuChoiKhoa(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const dataUpdate = { ly_do_tu_choi_khoa_phieu: req.body.ly_do_tu_choi_khoa_phieu };
    const beforeUpdatedData = await Service.findOneById(id);
    await updateAndSaveHistory(t, beforeUpdatedData, dataUpdate, req.user._id);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }
    await notificationToUsers(t, beforeUpdatedData, afterUpdatedData);
    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

async function kiemTraKetQuaDoDienTro(afterUpdatedData) {
  const caiDatVanHanh = await CAI_DAT_VAN_HANH.findOne({ is_deleted: false }).lean();
  if (!caiDatVanHanh.tieu_chi_do_tiep_dia) {
    return;
  }
  const ketquaKhongDat = await DO_DIEN_TRO.find({
    ket_luan: KET_LUAN.KHONG_DAT.value,
    phieu_giao_viec_id: afterUpdatedData._id,
  }).lean();

  async function createTonTai(ketQua) {
    let duongDayId = [];
    const duongdayByViTri = await VAN_HANH.find({ vi_tri_id: ketQua.vi_tri_id, is_deleted: false },
      { _id: 0, duong_day_id: 1 }).lean();

    if (duongdayByViTri) {
      duongdayByViTri.map(duongday => {
        const checkExist = afterUpdatedData.duong_day_ids.some(dd => dd._id.toString() === duongday.duong_day_id.toString());
        if (checkExist) {
          if (!duongDayId.some(id => id.toString() === duongday.duong_day_id.toString())) duongDayId.push(duongday.duong_day_id);
        }
      });
    }

    const tonTai = {
      tieu_chi_id: caiDatVanHanh.tieu_chi_do_tiep_dia,
      phieu_giao_viec_id: afterUpdatedData._id,
      vi_tri_id: ketQua.vi_tri_id,
      nguoi_tao: ketQua.nguoi_tao_id,
      nguoi_chinh_sua_id: ketQua.nguoi_chinh_sua,

      don_vi_id: afterUpdatedData.don_vi_giao_phieu_id,
      duong_day_id: duongDayId,
      tinh_trang_xu_ly: TRANG_THAI_XU_LY.CHUA_XU_LY.code,
      noi_dung_chi_tiet: 'Kết quả đo không đạt',
      is_deleted: false,
      nhom_cong_viec: NHOM_CONG_VIEC.DO_THONG_SO.code,
    };
    await KET_QUA_KIEM_TRA.findOneAndUpdate({
      tieu_chi_id: caiDatVanHanh.tieu_chi_do_tiep_dia,
      phieu_giao_viec_id: afterUpdatedData._id,
      vi_tri_id: ketQua.vi_tri_id,
    }, tonTai, { new: true, upsert: true });
  }

  for (let ketQua of ketquaKhongDat) {
    await createTonTai(ketQua);
  }
}

async function kiemTraKetQuaKhoangCachPha(afterUpdatedData) {
  const caiDatVanHanh = await CAI_DAT_VAN_HANH.findOne({ is_deleted: false }).lean();
  if (!caiDatVanHanh.tieu_chi_do_khoang_cach_pha) {
    return;
  }
  const ketquaKhongDat = await KET_QUA_DO_KHOANG_CACH_PHA_DAT.find({
    ket_luan: KET_LUAN.KHONG_DAT.value,
    phieu_giao_viec_id: afterUpdatedData._id,
  }).lean();

  async function createTonTai(ketQua) {
    let duongDayId = [];
    const duongdayByKhoangCot = await KHOANG_COT.find({ _id: ketQua.khoang_cot_id, is_deleted: false },
      { _id: 0, duong_day_id: 1 }).lean();

    const duongDayIdsByKhoangCot = duongdayByKhoangCot.length ? duongdayByKhoangCot[0].duong_day_id.map((id) => id.toString()) : [];

    if (duongDayIdsByKhoangCot.length && afterUpdatedData.duong_day_ids.length) {
      afterUpdatedData.duong_day_ids.forEach((line) => {
        const stringId = line._id.toString();
        if (duongDayIdsByKhoangCot.includes(stringId)) duongDayId.push(line._id);
      });
    }

    const tonTai = {
      tieu_chi_id: caiDatVanHanh.tieu_chi_do_khoang_cach_pha,
      phieu_giao_viec_id: afterUpdatedData._id,
      khoang_cot_id: ketQua.khoang_cot_id,
      nguoi_tao: ketQua.nguoi_tao,
      nguoi_chinh_sua_id: ketQua.nguoi_chinh_sua,

      don_vi_id: afterUpdatedData.don_vi_giao_phieu_id,
      duong_day_id: duongDayId,
      tinh_trang_xu_ly: TRANG_THAI_XU_LY.CHUA_XU_LY.code,
      noi_dung_chi_tiet: 'Kết quả đo không đạt',
      is_deleted: false,
      nhom_cong_viec: NHOM_CONG_VIEC.DO_THONG_SO.code,
    };
    await KET_QUA_KIEM_TRA.findOneAndUpdate({
      tieu_chi_id: caiDatVanHanh.tieu_chi_do_khoang_cach_pha,
      phieu_giao_viec_id: afterUpdatedData._id,
      khoang_cot_id: ketQua.khoang_cot_id,
    }, tonTai, { new: true, upsert: true });
  }

  for (let ketQua of ketquaKhongDat) {
    await createTonTai(ketQua);
  }
}

async function kiemTraKetQuaNhietDo(afterUpdatedData) {
  const caiDatVanHanh = await CAI_DAT_VAN_HANH.findOne({ is_deleted: false }).lean();
  if (!caiDatVanHanh.tieu_chi_do_nhiet_do) {
    return;
  }
  const ketQuaDoNhietDo = await KET_QUA_DO_NHIET_DO.find({ phieu_giao_viec_id: afterUpdatedData._id }).lean();
  const ketQuaDoNhietDoIds = ketQuaDoNhietDo.map(ketQua => ketQua._id);
  const ketquaKhongDat = await CHI_TIET_DO_NHIET_DO.find({
    danh_gia: KET_LUAN.KHONG_DAT.value,
    ket_qua_do_nhiet_do_id: { $in: ketQuaDoNhietDoIds },
  }).populate('ket_qua_do_nhiet_do_id').lean();

  let viTriKhongDat = [], khoangCotKhongDat = [];
  ketquaKhongDat.forEach(ketQua => {
    !!ketQua.ket_qua_do_nhiet_do_id?.vi_tri_id && viTriKhongDat.push(ketQua.ket_qua_do_nhiet_do_id?.vi_tri_id);
    !!ketQua.ket_qua_do_nhiet_do_id?.khoang_cot_id && khoangCotKhongDat.push(ketQua.ket_qua_do_nhiet_do_id?.khoang_cot_id);
  });
  const mapKhoangCot = {};

  const allKhoangCot = await KHOANG_COT.find({ _id: khoangCotKhongDat, is_deleted: false },
    { _id: 1, duong_day_id: 1 }).lean();

  allKhoangCot.forEach(khoangCot => {
    mapKhoangCot[khoangCot._id] = khoangCot.duong_day_id;
  });

  const mapViTriByDuongDay = await ViTriService.getHashMapViTriByDuongDay(viTriKhongDat);

  async function createTonTai(ketQua) {
    let duongDayId = [];
    afterUpdatedData.duong_day_ids.forEach(duongDay => {
      if (mapViTriByDuongDay[duongDay._id].includes(ketQua.ket_qua_do_nhiet_do_id?.vi_tri_id?.toString())) {
        duongDayId.push(duongDay._id);
      }
      mapKhoangCot[ketQua.ket_qua_do_nhiet_do_id?.khoang_cot_id]?.forEach(item => {
        if (item.toString() === duongDay._id.toString()) {
          duongDayId.push(duongDay._id);
        }
      });
    });

    let noiDungChiTiet = 'Kết quả đo không đạt';
    if (ketQua.huong_do === 'PHIA_LON') noiDungChiTiet += `\nHướng đo: Phía lớn`;
    if (ketQua.huong_do === 'PHIA_NHO') noiDungChiTiet += `\nHướng đo: Phía nhỏ`;
    noiDungChiTiet += `\nDây: ${ketQua.day_dan_phan_pha}`;

    const tonTai = {
      tieu_chi_id: caiDatVanHanh.tieu_chi_do_nhiet_do,
      phieu_giao_viec_id: afterUpdatedData._id,
      khoang_cot_id: ketQua.ket_qua_do_nhiet_do_id?.khoang_cot_id,
      vi_tri_id: ketQua.ket_qua_do_nhiet_do_id?.vi_tri_id,
      nguoi_tao: afterUpdatedData.chi_huy_truc_tiep_id._id,
      don_vi_id: afterUpdatedData.don_vi_giao_phieu_id,
      duong_day_id: duongDayId,
      tinh_trang_xu_ly: TRANG_THAI_XU_LY.CHUA_XU_LY.code,
      noi_dung_chi_tiet: noiDungChiTiet,
      is_deleted: false,
      nhom_cong_viec: NHOM_CONG_VIEC.DO_THONG_SO.code,
    };
    await KET_QUA_KIEM_TRA.create(tonTai);
  }

  for (let ketQua of ketquaKhongDat) {
    await createTonTai(ketQua);
  }
}

async function kiemTraKetQuaDo(afterUpdatedData) {
  await kiemTraKetQuaDoDienTro(afterUpdatedData);
  await kiemTraKetQuaNhietDo(afterUpdatedData);
  await kiemTraKetQuaKhoangCachPha(afterUpdatedData);
}

async function createQuanLyCayCao(req, afterUpdatedData) {
  const loaiCongViec = Object.values(LOAI_CONG_VIEC).filter(congViec => congViec.type === KIEM_TRA);
  const cvKiemTraCode = extractKeys(loaiCongViec, 'code');

  if (!cvKiemTraCode.includes(afterUpdatedData.loai_cong_viec)) return;

  const kqKiemTra = await KetQuaKiemTraService
    .getAll(
      {
        phieu_giao_viec_id: afterUpdatedData._id,
        khoang_cot_id: { $exists: true },
        is_deleted: false,
      },
      { khoang_cot_id: 1, duong_day_id: 1, khoi_luong: 1, tieu_chi_id: 1, khoang_cach_nga_do_gan_nhat: 1 },
    )
    .populate({ path: 'tieu_chi_id', select: 'tieu_chi_cay_cao' });

  const cayCaoData = [];
  kqKiemTra.forEach(ketQua => {
    ketQua.duong_day_id.forEach(dzId => {
      cayCaoData.push({
        duong_day_id: dzId,
        khoang_cot_id: ketQua.khoang_cot_id,
        dien_tich_khoi_luong: ketQua.khoi_luong,
        khoang_cach_gan_nhat: ketQua.khoang_cach_nga_do_gan_nhat,
      });
    });
  });

  const dataCreated = await HanhLangTuyenService.createMulti(cayCaoData);
}

async function createBaoCaoDoThongSo(req, afterUpdatedData) {
  switch (afterUpdatedData.loai_cong_viec) {
    case LOAI_CONG_VIEC.DO_KHOANG_CACH_PHA_DAT.code:
      await BaoCaoPhaDatService.resultToReport(req, afterUpdatedData);
      break;
    case LOAI_CONG_VIEC.DO_DIEN_TRO_TIEP_DIA.code:
      await BaoCaoDienTroService.resultToReport(req, afterUpdatedData);
      break;
    case LOAI_CONG_VIEC.DO_NHIET_DO_TIEP_XUC.code:
      await BaoCaoNhietDoService.resultToReport(req, afterUpdatedData);
      break;
    case LOAI_CONG_VIEC.DO_COROCAM_CACH_DIEN.code:
      // todo:
      break;
    case LOAI_CONG_VIEC.DO_NHIET_DO_CACH_DIEN_COMPOSITE.code:
      // todo:
      break;
    default:
  }
}

export async function xacNhanKhoaPhieu(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const dataUpdate = {
      trang_thai_cong_viec: TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code,
      thoi_gian_xac_nhan_khoa_phieu: new Date(),
      nguoi_xac_nhan_khoa_id: req.user._id,
    };
    const beforeUpdatedData = await Service.findOneById(id);
    if (!beforeUpdatedData) return responseHelper.error(res, CommonError.NOT_FOUND);
    validatePhieuState(t, req.user._id, beforeUpdatedData, HANH_DONG.XAC_NHAN_KHOA_PHIEU);
    saveHistory(beforeUpdatedData, Model, 'update', req.user._id);
    await Model.findOneAndUpdate({ _id: id }, dataUpdate);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }
    await Service.updateTrangThaiKetQuaKiemTra(afterUpdatedData);
    await updateTrangThaiTonTai(afterUpdatedData);

    await kiemTraKetQuaDo(afterUpdatedData);

    await Promise.all([
      notificationToUsers(t, beforeUpdatedData, afterUpdatedData),
      createBaoCaoDoThongSo(req, afterUpdatedData),
      createQuanLyCayCao(req, afterUpdatedData),
    ]);

    GenerateController.generatePhieu(res, afterUpdatedData, TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code, EXTENSION_FILE.PDF);

    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

async function updateTrangThaiTonTai(phieuGiaoViecData) {
  if (phieuGiaoViecData.trang_thai_cong_viec !== TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code) return;

  const danhSachTonTai = await KET_QUA_KIEM_TRA.find({ phieu_giao_viec_id: phieuGiaoViecData._id }).lean();
  for (let i = 0; i < danhSachTonTai.length; i++) {
    await KET_QUA_KIEM_TRA.findOneAndUpdate({ _id: danhSachTonTai[i]._id }, { tinh_trang_xu_ly: TRANG_THAI_XU_LY.CHUA_XU_LY.code });
  }
}

export async function xacNhanKetQua(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const dataUpdate = {
      chi_huy_xac_nhan_ket_qua: true,
      thoi_gian_chi_huy_xac_nhan: new Date(),
    };
    const beforeUpdatedData = await Service.findOneById(id);
    validatePhieuState(t, req.user._id, beforeUpdatedData, HANH_DONG.XAC_NHAN_KET_QUA);
    await updateAndSaveHistory(t, beforeUpdatedData, dataUpdate, req.user._id);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }
    await notificationToUsers(t, beforeUpdatedData, afterUpdatedData);
    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function kyGiaoPhieu(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const dataUpdate = {
      ky_tao_phieu: true,
      thoi_gian_ky_tao_phieu: new Date(),
    };
    const beforeUpdatedData = await Service.findOneById(id);
    validatePhieuState(t, req.user._id, beforeUpdatedData, HANH_DONG.GIAO_PHIEU);
    await Model.findOneAndUpdate({ _id: id }, dataUpdate);
    saveHistory(beforeUpdatedData, Model, 'update', req.user._id);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }
    await notificationToUsers(t, beforeUpdatedData, afterUpdatedData);
    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function huyKyGiaoPhieu(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const dataUpdate = {
      ky_tao_phieu: false,
      thoi_gian_ky_tao_phieu: null,
    };
    let beforeUpdatedData = await Model.findOneAndUpdate({ _id: id }, dataUpdate);
    validatePhieuState(t, req.user._id, beforeUpdatedData, HANH_DONG.HUY_GIAO_PHIEU);
    saveHistory(beforeUpdatedData, Model, 'update', req.user._id);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }
    await notificationToUsers(t, beforeUpdatedData, afterUpdatedData);
    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function kyTiepNhan(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const dataUpdate = {
      ky_tiep_nhan: true,
      thoi_gian_ky_tiep_nhan: new Date(),
    };
    const beforeUpdatedData = await Service.findOneById(id);
    validatePhieuState(t, req.user._id, beforeUpdatedData, HANH_DONG.TIEP_NHAN);
    await Model.findOneAndUpdate({ _id: id }, dataUpdate);
    saveHistory(beforeUpdatedData, Model, 'update', req.user._id);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }
    await notificationToUsers(t, beforeUpdatedData, afterUpdatedData);

    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function huyKyTiepNhan(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const dataUpdate = {
      ky_tiep_nhan: false,
      thoi_gian_ky_tiep_nhan: null,
    };
    let beforeUpdatedData = await Model.findOneAndUpdate({ _id: id }, dataUpdate);
    validatePhieuState(t, req.user._id, beforeUpdatedData, HANH_DONG.HUY_TIEP_NHAN);
    saveHistory(beforeUpdatedData, Model, 'update', req.user._id);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }
    await notificationToUsers(t, beforeUpdatedData, afterUpdatedData);
    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function kyKhoaPhieu(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const { error } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    const dataUpdate = {
      ky_khoa_phieu: true,
      thoi_gian_ky_khoa_phieu: new Date(),
    };
    const beforeUpdatedData = await Service.findOneById(id);
    validatePhieuState(t, req.user._id, beforeUpdatedData, HANH_DONG.HUY_KHOA_PHIEU);
    await Model.findOneAndUpdate({ _id: id }, dataUpdate);
    saveHistory(beforeUpdatedData, Model, 'update', req.user._id);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }
    await notificationToUsers(t, beforeUpdatedData, afterUpdatedData);
    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function huyKyKhoaPhieu(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const { error } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    const dataUpdate = {
      ky_khoa_phieu: false,
      thoi_gian_ky_khoa_phieu: null,
    };
    const beforeUpdatedData = await Service.findOneById(id);
    validatePhieuState(t, req.user._id, beforeUpdatedData, HANH_DONG.HUY_KHOA_PHIEU);
    await Model.findOneAndUpdate({ _id: id }, dataUpdate);
    saveHistory(beforeUpdatedData, Model, 'update', req.user._id);
    const afterUpdatedData = await Service.findOneById(id);
    if (!afterUpdatedData) {
      return responseHelper.error(res, null, 404, '');
    }
    await notificationToUsers(t, beforeUpdatedData, afterUpdatedData);
    return responseHelper.success(res, afterUpdatedData);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function copyPhieu(req, res) {
  try {
    const { id } = req.params;
    const value = req.body;
    const dataPhieu = await Service.findOneById(id);
    const checkCopyPhieu = value.check_copy_phieu;
    let dataRequest = {};
    dataRequest.so_phieu = await Service.generateSoPhieu(Model, dataPhieu?.don_vi_giao_phieu_id);
    dataRequest.nguoi_cap_phieu_id = req.user._id;
    dataRequest.chi_huy_xac_nhan_ket_qua = false;
    dataRequest.qua_thoi_gian_tiep_nhan = false;
    dataRequest.trang_thai_cong_viec = TRANG_THAI_PHIEU.DANG_TAO_PHIEU.code;
    dataRequest.ky_tao_phieu = false;
    dataRequest.ky_tiep_nhan = false;
    dataRequest.ky_khoa_phieu = false;
    dataRequest.nguoi_tao_huy_giao_phieu = false;
    dataRequest.loai_cong_viec = dataPhieu?.loai_cong_viec;
    dataRequest.noi_dung_cong_tac = dataPhieu?.noi_dung_cong_tac;
    dataRequest.tinh_trang_van_hanh_duong_day = dataPhieu?.tinh_trang_van_hanh_duong_day;
    dataRequest.don_vi_giao_phieu_id = dataPhieu?.don_vi_giao_phieu_id;
    dataRequest.don_vi_cong_tac_id = dataPhieu?.don_vi_cong_tac_id;
    dataRequest.pham_vi_bay_du_kien = dataPhieu?.pham_vi_bay_du_kien;
    dataRequest.created_by = dataPhieu?.created_by;
    dataRequest.updated_by = dataPhieu?.updated_by;
    dataRequest.don_vi_chi_huy_truc_tiep_id = dataPhieu?.don_vi_chi_huy_truc_tiep_id;
    dataRequest.created_at = new Date();
    dataRequest.updated_at = new Date();
    dataRequest.file_phuong_an_thuc_hien = dataPhieu?.file_phuong_an_thuc_hien;
    dataRequest.noi_dung_cong_viec = dataPhieu?.noi_dung_cong_viec;
    dataRequest.may_do_id = dataPhieu?.may_do_id;
    dataRequest.chuyen_de_id = dataPhieu?.chuyen_de_id;
    dataRequest.cong_viec_phu_tro = dataPhieu?.cong_viec_phu_tro;
    dataRequest.luu_y_cong_tac = dataPhieu?.luu_y_cong_tac;

    if (dataPhieu?.danh_sach_ton_tai) {
      if (dataPhieu?.trang_thai_cong_viec == TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code) {
        const phieuChuaXuLy = dataPhieu?.danh_sach_ton_tai.filter(data => data.tinh_trang_xu_ly == TRANG_THAI_XU_LY.CHUA_XU_LY.code);
        dataRequest.danh_sach_ton_tai = phieuChuaXuLy;
      } else if (dataPhieu?.trang_thai_cong_viec == TRANG_THAI_PHIEU.DANG_TAO_PHIEU.code) {
        dataRequest.danh_sach_ton_tai = dataPhieu?.danh_sach_ton_tai;
      }
    }

    if (checkCopyPhieu.check_use_drone) {
      dataRequest.su_dung_drone = dataPhieu?.su_dung_drone;
    }
    if (checkCopyPhieu.check_to_cong_tac) {
      dataRequest.chi_huy_truc_tiep_id = dataPhieu?.chi_huy_truc_tiep_id;
      dataRequest.nguoi_cong_tac = dataPhieu?.nguoi_cong_tac;
    }
    if (checkCopyPhieu.check_duong_day) {
      dataRequest.duong_day_ids = dataPhieu?.duong_day_ids;
      dataRequest.vi_tri_cong_viec = dataPhieu?.vi_tri_cong_viec;
      dataRequest.khoang_cot_cong_viec = dataPhieu?.khoang_cot_cong_viec;
    }
    if (checkCopyPhieu.check_thoi_gian) {
      dataRequest.thoi_gian_cong_tac_bat_dau = dataPhieu?.thoi_gian_cong_tac_bat_dau;
      dataRequest.thoi_gian_cong_tac_ket_thuc = dataPhieu?.thoi_gian_cong_tac_ket_thuc;
    }
    if (checkCopyPhieu.check_an_toan) {
      dataRequest.bien_phap_an_toan_cong_viec = dataPhieu?.bien_phap_an_toan_cong_viec;
      dataRequest.dieu_kien_an_toan_cong_viec = dataPhieu?.dieu_kien_an_toan_cong_viec;
    }

    const result = await Service.create(dataRequest);
    if (!result) return responseHelper.error(res, 400, '');

    const dataRtn = await result.populate(populateOpts).execPopulate();

    const {
      bien_phap_an_toan_cong_viec, dieu_kien_an_toan_cong_viec, nguoi_cong_tac, khoang_cot_cong_viec, vi_tri_cong_viec,
      file_phuong_an_thuc_hien, noi_dung_cong_viec, danh_sach_ton_tai, cong_viec_phu_tro,
    } = dataRequest;

    if (bien_phap_an_toan_cong_viec) {
      bien_phap_an_toan_cong_viec.map(item => {
        delete item._id;
        item.phieu_giao_viec_id = result._id;
      });
      await BienPhapAnToanCongViecService.create(bien_phap_an_toan_cong_viec);
    }

    if (dieu_kien_an_toan_cong_viec) {
      dieu_kien_an_toan_cong_viec.map(item => {
        delete item._id;
        item.phieu_giao_viec_id = result._id;
      });
      await DieuKienAnToanCongViecService.create(dieu_kien_an_toan_cong_viec);
    }

    if (nguoi_cong_tac) {
      nguoi_cong_tac.map(item => {
        delete item._id;
        item.phieu_giao_viec_id = result._id;
      });
      await NguoiCongTacService.create(nguoi_cong_tac);
    }

    if (khoang_cot_cong_viec) {
      khoang_cot_cong_viec.map(item => {
        delete item._id;
        item.phieu_giao_viec_id = result._id;
      });
      await ViTriCongViecService.create(khoang_cot_cong_viec);
    }

    if (vi_tri_cong_viec) {
      vi_tri_cong_viec.map(item => {
        delete item._id;
        item.phieu_giao_viec_id = result._id;
      });
      await ViTriCongViecService.create(vi_tri_cong_viec);
    }

    if (file_phuong_an_thuc_hien) {
      file_phuong_an_thuc_hien.map(item => {
        delete item._id;
        item.phieu_giao_viec_id = result._id;
      });
      await TapTinService.create(file_phuong_an_thuc_hien);
    }

    if (noi_dung_cong_viec) {
      noi_dung_cong_viec.map(item => {
        delete item._id;
        item.phieu_giao_viec_id = result._id;
      });
      await NoiDungCongViecService.create(noi_dung_cong_viec);
    }

    if (danh_sach_ton_tai) {
      danh_sach_ton_tai.map(item => {
        delete item._id;
        item.phieu_giao_viec_id = result._id;
      });
      await KetQuaSuaChuaCoKeHoachService.create(danh_sach_ton_tai);
    }

    if (cong_viec_phu_tro) {
      cong_viec_phu_tro.map(item => {
        delete item._id;
        item.phieu_giao_viec_id = result._id;
      });
      await CongViecPhuTroService.create(cong_viec_phu_tro);
    }

    return responseHelper.success(res, dataRtn);

  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function updateKetQuaKiemTra(req, res) {

  try {
    const { id } = req.params;
    const { error, value } = KetQuaKiemTraService.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    let phieuGiaoViecData = await Model.findOne({ _id: id }, { duong_day_id: 1, don_vi_cong_tac_id: 1 }).lean();
    const dataUpdate = value.map(item => {
      item.don_vi_id = phieuGiaoViecData.don_vi_cong_tac_id;
      return item;
    });

    await createOrUpdateChild(dataUpdate, KetQuaKiemTraService, 'phieu_giao_viec_id', id);
    const result = await KetQuaKiemTraService.getAll({ phieu_giao_viec_id: id, is_deleted: false })
      .populate(populateOpts);
    return responseHelper.success(res, result);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function updateKetQuaKiemTraSingle(req, res) {

  try {
    const { id } = req.params;
    const { error, value } = KetQuaKiemTraService.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    let phieuGiaoViecData = await Model.findOne(
      { _id: id, is_deleted: false },
      { duong_day_id: 1, don_vi_cong_tac_id: 1 }).lean();
    if (!phieuGiaoViecData) return responseHelper.error(res, 404, '');

    value.phieu_giao_viec_id = id;
    value.don_vi_id = phieuGiaoViecData.don_vi_cong_tac_id;
    let result;
    if (value._id) {
      value.nguoi_chinh_sua = req.user._id;
      result = await KetQuaKiemTraService.findByIdAndUpdate(value);
    } else {
      value.nguoi_tao = req.user._id;
      result = await KetQuaKiemTraService.create(value);
    }
    if (!result) return responseHelper.error(res, 404, '');

    const dataRtn = await result.populate(populateOpts).execPopulate();

    return responseHelper.success(res, dataRtn);

  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function createKetQuaKiemTraMulti(req, res) {

  try {
    const { id } = req.params;
    let dataRequest = req.body;

    const allAnhViTri = await AnhViTriService.getAll(
      { _id: dataRequest.anh_vi_tris },
      { vi_tri_id: 1, khoang_cot_id: 1 },
    );

    const imageGroupByViTri = groupBy(allAnhViTri, 'vi_tri_id');
    const imageGroupByKhoangCot = groupBy(allAnhViTri, 'khoang_cot_id');


    let phieuGiaoViecData = await Model.findOne(
      { _id: id, is_deleted: false },
      { duong_day_id: 1, don_vi_cong_tac_id: 1 }).lean();
    if (!phieuGiaoViecData) return responseHelper.error(res, 404, '');

    dataRequest.phieu_giao_viec_id = id;
    dataRequest.don_vi_id = phieuGiaoViecData.don_vi_cong_tac_id;
    let resultAll = [];

    const dsViTriId = Array.isArray(dataRequest?.vi_tri_id)
      ? dataRequest.vi_tri_id
      : dataRequest?.vi_tri_id
        ? [dataRequest.vi_tri_id]
        : [];

    const dsKhoangCotId = Array.isArray(dataRequest?.khoang_cot_id)
      ? dataRequest.khoang_cot_id
      : dataRequest?.khoang_cot_id
        ? [dataRequest.khoang_cot_id]
        : [];

    if (dsViTriId.length > 0) {
      for (const item of dsViTriId) {
        dataRequest.vi_tri_id = item;
        dataRequest.khoang_cot_id = null;
        dataRequest.anh_vi_tris = imageGroupByViTri[item];

        const result = await KetQuaKiemTraService.create(dataRequest);
        if (!result) return responseHelper.error(res, 404, '');
        resultAll.push(result);
      }
    }

    if (dsKhoangCotId.length > 0) {
      for (const item of dsKhoangCotId) {
        dataRequest.khoang_cot_id = item;
        dataRequest.vi_tri_id = null;
        dataRequest.anh_vi_tris = imageGroupByKhoangCot[item];

        const result = await KetQuaKiemTraService.create(dataRequest);
        if (!result) return responseHelper.error(res, 404, '');
        resultAll.push(result);
      }
    }

    const dataRtn = await Promise.all(resultAll.map(rs => rs.populate(populateOpts).execPopulate()));

    return responseHelper.success(res, dataRtn);

  } catch (err) {
    console.log(err);
    responseHelper.error(res, err);
  }
}

export async function deleteKetQuaKiemTra(req, res) {
  try {
    const { id } = req.params;

    const check = KetQuaKiemTraService.getOne({ _id: id }, { _id: 1 });
    if (!check) {
      return responseHelper.error(res, 404, '');
    }
    const data = await KetQuaKiemTraService.update({ _id: id }, { is_deleted: true });
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function updateCongViecPhatSinh(req, res) {

  try {
    const { id } = req.params;
    const { error, value } = CongViecPhatSinhService.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    await createOrUpdateChild(value, CongViecPhatSinhService, 'phieu_giao_viec_id', id);
    const result = await CongViecPhatSinhService.getAll({ phieu_giao_viec_id: id, is_deleted: false })
      .populate({ path: 'duong_day_id', select: 'ten_duong_day' })
      .populate({ path: 'vi_tri_id' })
      .populate({ path: 'khoang_cot_id', populate: 'vi_tri_bat_dau_id vi_tri_ket_thuc_id' })
      .populate({ path: 'cong_viec_id' });
    return responseHelper.success(res, result);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function updateCongViecPhatSinhSingle(req, res) {

  try {
    const { id } = req.params;
    const { error, value } = CongViecPhatSinhService.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    let phieuGiaoViecData = await Model.findOne({ _id: id, is_deleted: false }, { _id: 1 }).lean();
    if (!phieuGiaoViecData) {
      return responseHelper.error(res, 404, '');
    }

    value.phieu_giao_viec_id = id;
    let result;
    if (value._id) {
      result = await CongViecPhatSinhService.findByIdAndUpdate(value);
    } else {
      result = await CongViecPhatSinhService.create(value);
    }
    if (!result) return responseHelper.error(res, 404, '');

    const populateCongViecPhatSinh = [
      { path: 'duong_day_id', select: 'ten_duong_day' },
      { path: 'vi_tri_id' },
      { path: 'khoang_cot_id', populate: 'vi_tri_bat_dau_id vi_tri_ket_thuc_id' },
      { path: 'cong_viec_id' },
    ];

    const dataRtn = await result.populate(populateCongViecPhatSinh).execPopulate();
    return responseHelper.success(res, dataRtn);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function updateKetQuaXuLy(req, res) {

  try {
    const { id } = req.params;
    const { error, value } = KetQuaXuLyService.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    const dataUpdate = {
      chi_huy_xac_nhan_ket_qua: false,
      thoi_gian_chi_huy_xac_nhan: null,
    };
    let data = await Model.findOneAndUpdate({ _id: id }, dataUpdate);
    if (!data) {
      return responseHelper.error(res, null, 404);
    } else {
      await createOrUpdateChild(value, KetQuaXuLyService, 'phieu_giao_viec_id', data._id);
      await NguoiCongTacService.xoaXacNhanKetQua(id);
    }
    const result = await KetQuaXuLyService.getAll({ phieu_giao_viec_id: data._id, is_deleted: false })
      .populate(populateOpts);
    return responseHelper.success(res, result);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function updateKetQuaXuLySingle(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = KetQuaXuLyService.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    let phieuGiaoViecData = await Model.findOne({ _id: id, is_deleted: false }, { _id: 1 });
    if (!phieuGiaoViecData) return responseHelper.error(res, 404, '');

    value.phieu_giao_viec_id = id;
    let result;
    if (value._id) {
      result = await KetQuaXuLyService.findByIdAndUpdate(value);
    } else {
      result = await KetQuaXuLyService.create(value);
    }
    if (!result) return responseHelper.error(res, 404, '');
    const dataRtn = await result.populate(populateOpts).execPopulate();

    return responseHelper.success(res, dataRtn);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function updateKetQuaSuaChuaCoKeHoach(req, res) {

  try {
    const { id } = req.params;
    const { error, value } = KetQuaXuLyService.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    await createOrUpdateChild(value, KetQuaSuaChuaCoKeHoachService, 'phieu_giao_viec_id', id);

    const result = await KetQuaSuaChuaCoKeHoachService.getAll({ phieu_giao_viec_id: id, is_deleted: false })
      .populate({
        path: 'ton_tai_id', select: '',
        populate: {
          path: 'tieu_chi_id vi_tri_id khoang_cot_id',
          select: 'ten_tieu_chi noi_dung_kiem_tra_id ten_vi_tri thu_tu ten_khoang_cot',
          populate: { path: 'noi_dung_kiem_tra_id', select: 'ten_noi_dung' },
        },
      });
    for (let i = 0; i < result.length; i++) {
      const tonTaiId = await HieuChinhTonTaiService.getDetailHieuChinh([result[i].ton_tai_id]);
      result[i].ton_tai_id = tonTaiId[0];
    }
    return responseHelper.success(res, result);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function create(req, res) {

  try {
    req.body.so_phieu = await Service.generateSoPhieu(Model, req.body.don_vi_giao_phieu_id);
    let { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error);
    value = getQuarterYear(value);
    let data = await Model.create(value);
    data = data.toObject();
    if (req.body.loai_cong_viec !== LOAI_CONG_VIEC.CO_KE_HOACH.code) {
      req.body.vi_tri_cong_viec = removeDuplicateObject(req.body.vi_tri_cong_viec, 'vi_tri_id');
      req.body.khoang_cot_cong_viec = removeDuplicateObject(req.body.khoang_cot_cong_viec, 'khoang_cot_id');
    }

    if (data) {
      function removeId(arr) {
        if (!arr?.length) return [];
        return arr.map(item => {
          delete item._id;
          return item;
        });
      }

      // update số mạch trong khoảng cột công việc
      await updateSoMach(req);

      data.bien_phap_an_toan_cong_viec = await createChild(removeId(req.body.bien_phap_an_toan_cong_viec), BienPhapAnToanCongViecService, 'phieu_giao_viec_id', data._id);
      data.dieu_kien_an_toan_cong_viec = await createChild(removeId(req.body.dieu_kien_an_toan_cong_viec), DieuKienAnToanCongViecService, 'phieu_giao_viec_id', data._id);
      data.nguoi_cong_tac = await createChild(removeId(req.body.nguoi_cong_tac), NguoiCongTacService, 'phieu_giao_viec_id', data._id);
      data.noi_dung_cong_viec = await createChild(removeId(req.body.noi_dung_cong_viec), NoiDungCongViecService, 'phieu_giao_viec_id', data._id);
      data.vi_tri_cong_viec = await createChild(removeId(req.body.vi_tri_cong_viec), ViTriCongViecService, 'phieu_giao_viec_id', data._id);
      data.khoang_cot_cong_viec = await createChild(removeId(req.body.khoang_cot_cong_viec), ViTriCongViecService, 'phieu_giao_viec_id', data._id);
      data.cong_trinh_cong_viec = await createChild(removeId(req.body.cong_trinh_cong_viec), CongTrinhCongViecService, 'phieu_giao_viec_id', data._id);
      data.danh_sach_ton_tai = await createChild(removeId(req.body.danh_sach_ton_tai), KetQuaSuaChuaCoKeHoachService, 'phieu_giao_viec_id', data._id);

      const congViecPhuTro = req.body.cong_viec_phu_tro?.map(phuTro => {
        delete phuTro._id;
        delete phuTro.tinh_trang_thuc_hien;
        return phuTro;
      });

      data.cong_viec_phu_tro = await createChild(congViecPhuTro, CongViecPhuTroService, 'phieu_giao_viec_id', data._id);
    }
    if (req.body.required_phieu_cong_tac) {
      await autoCreatePhieuCongTac(data._id, req.body.don_vi_giao_phieu_id);
    }

    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseHelper.error(res, err, 500);
  }
}


export async function getAllMe(req, res) {
  try {
    const userId = req.user._id;
    const { criteria, options } = await Service.buildQuery(req);
    delete criteria.don_vi_giao_phieu_id;
    if (!options.hasOwnProperty('sort')) options.sort = { created_at: -1 };
    options.populate = [
      { path: 'don_vi_giao_phieu_id' },
      { path: 'nguoi_cap_phieu_id', select: 'full_name username phone bac_an_toan' },
      { path: 'chi_huy_truc_tiep_id', select: 'full_name username phone bac_an_toan' },
      { path: 'duong_day_ids', select: 'ten_duong_day' },
    ];
    const allNguoiCongTac = await NguoiCongTacService.getAll({ user_id: userId, is_deleted: false });
    const phieuCongTac = await PhieuCongTacService.getAll({
      $or: [
        { giam_sat_an_toan_id: userId },
        { lanh_dao_cong_viec_id: userId },
        { nguoi_cho_phep_id: userId },
      ],
    }, { phieu_giao_viec_id: 1 });

    const phieuGiaoViecId = [
      ...allNguoiCongTac?.map(nguoiCongTac => nguoiCongTac.phieu_giao_viec_id) || [],
      ...phieuCongTac?.map(x => x.phieu_giao_viec_id) || [],
    ];

    criteria.$or = [{ chi_huy_truc_tiep_id: userId }, { _id: { $in: Array.from(new Set(phieuGiaoViecId)) } }];
    const data = await Model.paginate(criteria, options);
    for (const item of data?.docs) {
      const nguoiCongTac = await NguoiCongTacService.getAll({
        phieu_giao_viec_id: item?._id,
        is_deleted: false,
      }).populate({ path: 'user_id', select: 'full_name' }).lean();
      item['nguoi_cong_tac'] = nguoiCongTac;
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllQuanLyCongViec(req, res) {
  try {
    const { criteria, options } = await Service.buildQuery(req);
    if (!options.hasOwnProperty('sort')) options.sort = { created_at: -1 };


    console.log('options', options);
    const data = await Model.find(criteria)
      .populate({ path: 'don_vi_giao_phieu_id', select: 'ten_don_vi' })
      .select('so_phieu loai_cong_viec don_vi_giao_phieu_id created_at trang_thai_cong_viec')
      .sort(options.sort)
      .lean();

    await PhieuCongTacPhuTroService.addCongViec(data);


    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function getAllMeForDrone(req, res) {
  try {
    const userId = req.user._id;
    const { criteria, options } = await Service.buildQuery(req);
    delete criteria.don_vi_giao_phieu_id;
    if (!options.hasOwnProperty('sort')) options.sort = { created_at: -1 };
    options.populate = [
      { path: 'don_vi_giao_phieu_id' },
      { path: 'nguoi_cap_phieu_id', select: 'full_name username phone bac_an_toan' },
      { path: 'chi_huy_truc_tiep_id', select: 'full_name username phone bac_an_toan' },
      { path: 'duong_day_ids', select: 'ten_duong_day' },
    ];
    const allNguoiCongTac = await NguoiCongTacService.getAll({ user_id: userId, is_deleted: false });
    const phieuCongTac = await PhieuCongTacService.getAll({
      $or: [
        { giam_sat_an_toan_id: userId },
        { lanh_dao_cong_viec_id: userId },
        { nguoi_cho_phep_id: userId },
      ],
    }, { phieu_giao_viec_id: 1 });

    const phieuGiaoViecId = [
      ...allNguoiCongTac?.map(nguoiCongTac => nguoiCongTac.phieu_giao_viec_id) || [],
      ...phieuCongTac?.map(x => x.phieu_giao_viec_id) || [],
    ];

    criteria.$or = [{ chi_huy_truc_tiep_id: userId }, { _id: { $in: Array.from(new Set(phieuGiaoViecId)) } }];
    const data = await Model.paginate(criteria, options);
    if (data.docs && data.docs.length > 0) {
      for (let i = 0; i < data.docs.length; i++) {
        const viTriCongViec = await ViTriCongViecService
          .getAll({ phieu_giao_viec_id: data.docs[i]._id, is_deleted: false })
          .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' })
          .populate({ path: 'khoang_cot_id', select: 'ten_khoang_cot' });
        let viTri, khoangCot;
        viTriCongViec.forEach(item => {
          viTri = [viTri, item.vi_tri_id?.ten_vi_tri].join(' ');
          khoangCot = [khoangCot, item.khoang_cot_id?.ten_khoang_cot].join(' ');
        });
        data.docs[i].vi_tri = viTri?.trim();
        data.docs[i].khoang_cot = khoangCot?.trim();
      }
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAll(req, res) {
  try {
    const { criteria, options } = await Service.buildQuery(req);
    if (!options.hasOwnProperty('sort')) options.sort = { created_at: -1 };
    options.populate = [
      { path: 'don_vi_giao_phieu_id' },
    ];
    const data = await Model.paginate(criteria, options);
    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function getAllMeCreated(req, res) {
  try {
    const { criteria, options } = await Service.buildQuery(req);
    criteria.nguoi_cap_phieu_id = req.user._id;
    if (!options.hasOwnProperty('sort')) options.sort = { created_at: -1 };
    options.populate = [
      { path: 'don_vi_giao_phieu_id' },
      { path: 'nguoi_cap_phieu_id', select: 'full_name username phone bac_an_toan' },
      { path: 'chi_huy_truc_tiep_id', select: 'full_name username phone bac_an_toan' },
    ];
    const data = await Model.paginate(criteria, options);
    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function updateKetQuaThucHien(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);

    let currentData = await Model.findById(id, { trang_thai_cong_viec: 1 });
    if (!currentData) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    if (![TRANG_THAI_PHIEU.TIEP_NHAN.code].includes(currentData.trang_thai_cong_viec)) {
      return responseHelper.error(res, Error.INSUFFICIENT_PERMISSION);
    }

    const data = await Model.findOneAndUpdate({ _id: id }, {
        ket_qua_thuc_hien: value.ket_qua_thuc_hien,
        kien_nghi: value.kien_nghi,
      },
      {
        new: true,
        projection: { ket_qua_thuc_hien: 1, kien_nghi: 1 },
      },
    );
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function updateThoiGianBay(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);

    let currentData = await Model.findById(id);
    if (!currentData) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    if (![TRANG_THAI_PHIEU.TIEP_NHAN.code].includes(currentData.trang_thai_cong_viec)) {
      return responseHelper.error(res, Error.INSUFFICIENT_PERMISSION);
    }

    const data = await Model.findOneAndUpdate({ _id: id }, {
        ten_drone: value.ten_drone,
        thoi_gian_bay: value.thoi_gian_bay,
        serial_drone: value.serial_drone,
        sync_time: Date(),
        sync_status: 'synced',
      },
      {
        new: true,
        projection: { ten_drone: 1, thoi_gian_bay: 1, sync_status: 1, sync_time: 1, serial_drone: 1 },
      },
    );
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export function getQuarterYear(data) {

  if (data.thang_kiem_tra) {
    data.nam_kiem_tra = new Date().getFullYear();
    switch (data.thang_kiem_tra) {
      case 'THANG_1':
        if (new Date(data.thoi_gian_cong_tac_bat_dau).getMonth() + 1 === 12) {
          data.nam_kiem_tra = new Date().getFullYear() + 1;
        }
        data.quy_kiem_tra = 'QUY_1';
        break;
      case 'THANG_2':
      case 'THANG_3':
        data.quy_kiem_tra = 'QUY_1';
        break;
      case 'THANG_4':
      case 'THANG_5':
      case 'THANG_6':
        data.quy_kiem_tra = 'QUY_2';
        break;
      case 'THANG_7':
      case 'THANG_8':
      case 'THANG_9':
        data.quy_kiem_tra = 'QUY_3';
        break;
      case 'THANG_10':
      case 'THANG_11':
      case 'THANG_12':
        data.quy_kiem_tra = 'QUY_4';
        break;
      default:
        data.nam_kiem_tra = new Date().getFullYear();
    }
  }
  return data;
}


export async function huyPhieuDonViTest(req, res) {
  try {
    if (!req.user.is_system_admin) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    const donViId = '6225be128422d3001bfbe7eb';

    const allPhieuGiaoViec = await Service.getAll({
      don_vi_cong_tac_id: donViId,
      trang_thai_cong_viec: {
        $in: [
          TRANG_THAI_PHIEU.DANG_TAO_PHIEU.code,
          TRANG_THAI_PHIEU.GIAO_PHIEU.code,
          TRANG_THAI_PHIEU.TIEP_NHAN.code,
          TRANG_THAI_PHIEU.TU_CHOI_NHAN.code,
          TRANG_THAI_PHIEU.KHOA_PHIEU.code,
          TRANG_THAI_PHIEU.TU_CHOI_KHOA.code,
        ],
      },
    });

    const allPhieuId = extractIds(allPhieuGiaoViec);
    const dataUpdate = allPhieuId.map(phieuId => ({
      _id: phieuId,
      trang_thai_cong_viec: TRANG_THAI_PHIEU.HUY_PHIEU.code,
    }));
    const dataCongViecUpdated = await Service.updateAll(dataUpdate);


    const allPhieuCongTac = await PhieuCongTacService.getAll({
      don_vi_giao_phieu_id: donViId,
      huy_phieu: false,
      hoan_thanh_cong_tac: false,
      is_deleted: false,
    });

    const allPhieuCongTacId = extractIds(allPhieuCongTac);
    const dataCongTacUpdate = allPhieuCongTacId.map(phieuId => ({
      _id: phieuId,
      xac_nhan_cap_phieu: false,
      cho_phep_cong_tac: false,
      lanh_dao_xac_nhan_tiep_nhan: false,
      chi_huy_xac_nhan_tiep_nhan: false,
      giam_sat_xac_nhan_tiep_nhan: false,
      tiep_nhan_lam_viec: false,
      ket_thuc_cong_tac: false,
      lanh_dao_xac_nhan_ket_thuc: false,
      nguoi_cho_phep_xac_nhan_ket_thuc: false,
      hoan_thanh_cong_tac: false,
      xac_nhan_khoa_phieu: false,
      huy_phieu: true,
      nguoi_huy_phieu_id: req.user._id,
      ly_do_huy_phieu: 'Hủy phiếu test',
      thoi_gian_huy_phieu: new Date(),
    }));

    const dataCongTacUpdated = await PhieuCongTacService.updateAll(dataCongTacUpdate);

    return responseHelper.success(res, {
      dataCongViecUpdated: dataCongViecUpdated?.length,
      phieuGiaoViec: dataCongViecUpdated?.[0],
      dataCongTacUpdated: dataCongTacUpdated?.length,
      phieuCongTac: dataCongTacUpdated?.[0],
    });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function getKetQuaDoKhongDat(req, res) {

  try {
    const { id } = req.params;

    const phieuData = await Service.getOne({ _id: id });

    let kq;
    switch (phieuData.loai_cong_viec) {
      case LOAI_CONG_VIEC.DO_DIEN_TRO_TIEP_DIA.code:
        kq = await KetQuaDoKhongDat.getKetQuaDoDienTroKhongDat(id);
        break;
      case LOAI_CONG_VIEC.DO_NHIET_DO_TIEP_XUC.code:
        kq = await KetQuaDoKhongDat.getKetQuaDoNhietDoTiepXucKhongDat(id);
        break;
      case LOAI_CONG_VIEC.DO_KHOANG_CACH_PHA_DAT.code:
        kq = await KetQuaDoKhongDat.getKetQuaDoKhoangCachPhaDatKhongDat(id);
        break;
      case LOAI_CONG_VIEC.DO_COROCAM_CACH_DIEN.code:
        kq = await KetQuaDoKhongDat.getKetQuaDoCorocamKhongDat(id);
        break;
      case LOAI_CONG_VIEC.DO_NHIET_DO_CACH_DIEN_COMPOSITE.code:
        kq = await KetQuaDoKhongDat.getKetQuaDoCachDienCompositeKhongDat(id);
        break;
      default:
        break;
    }

    // DO_DIEN_TRO_TIEP_DIA

    return responseHelper.success(res, kq);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function updateDrone(req, res) {
  try {
    const { id } = req.params;
    const user = req.user;
    const { serial_drone } = req.body;

    if (!serial_drone) return responseHelper.error(res, CommonError.DRONE_NOT_FOUND);

    const currentData = await Model.findById(id);
    if (currentData.trang_thai_cong_viec !== TRANG_THAI_PHIEU.TIEP_NHAN.code
      || currentData.chi_huy_truc_tiep_id.toString() !== user._id.toString()
      || !currentData.su_dung_drone
    ) {
      return responseHelper.error(res, Error.INSUFFICIENT_PERMISSION);
    }

    const drone = await DroneService.getOne({ serial: serial_drone, don_vi_id: req.user.don_vi_id, is_deleted: false })
      .populate({ path: 'danh_muc_drone_id', select: 'name' });

    const ten_drone = drone?.danh_muc_drone_id?.name;

    const queryUpdate = { serial_drone, ten_drone };
    const updateData = await Model.findOneAndUpdate({ _id: id }, queryUpdate, { new: true });

    return responseHelper.success(res, updateData);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}
