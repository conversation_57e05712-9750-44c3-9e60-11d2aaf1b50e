import * as ValidatorHelper from '../../../../helpers/validatorHelper';
import TON_TAI_CAP_TREN from './tonTaiCapTren.model';

const Joi = require('joi');

const objSchema = Joi.object({
  name: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên tài liệu')),
  hoso_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên hồ sơ')),
  doi_tuong_id: Joi.string().messages(ValidatorHelper.messageDefine('<PERSON><PERSON><PERSON> tượng')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function createMany(dataList = []) {
  if (!Array.isArray(dataList)) {
    throw new Error('Input must be an array');
  }

  const validRecords = [];

  for (const data of dataList) {
    const { error, value } = validate(data);
    if (!error) {
      validRecords.push(value);
    }
  }

  return TON_TAI_CAP_TREN.insertMany(validRecords);
}

export function getAll(query, projection = {}) {
  return TON_TAI_CAP_TREN.find(query, projection).lean();
}

export async function updateAll(dataUpdate, options = {}) {
  options.new = true;
  const results = [];
  for (const row of dataUpdate) {
    try {
      const { error, value } = validate(row);
      if (!error) {
        const updated = await TON_TAI_CAP_TREN.findByIdAndUpdate(value._id, value, options);
        if (updated) results.push(updated);
      }
    } catch {
    }
  }
  return results;
}
