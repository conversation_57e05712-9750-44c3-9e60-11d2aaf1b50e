import express from 'express';
import passport from 'passport';
import * as Controller from './tonTaiCapTren.controller';
import { checkTempFolder, multipartMiddleware } from '../../../../utils/fileUtils';
import { authorizationMiddleware } from '../../../RBAC/middleware';
import TonTaiCapTrenPermission from '../../../RBAC/permissions/TonTaiCapTrenPermission';
import { loggerMiddleware } from '../../../../logs/middleware';

export const tonTaiCapTrenRouter = express.Router();

tonTaiCapTrenRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
tonTaiCapTrenRouter.post('*', authorizationMiddleware([TonTaiCapTrenPermission.CREATE]));
tonTaiCapTrenRouter.put('*', authorizationMiddleware([TonTaiCapTrenPermission.UPDATE]));
tonTaiCapTrenRouter.delete('*', authorizationMiddleware([TonTaiCapTrenPermission.DELETE]));
tonTaiCapTrenRouter.get('*', authorizationMiddleware([TonTaiCapTrenPermission.READ]));

tonTaiCapTrenRouter
  .route('/:id/ketquakiemtra')
  .put(Controller.xacNhanTonTai);

tonTaiCapTrenRouter
  .route('/:id/ketquakiemtra/single')
  .post(Controller.updateKetQuaKiemTraSingle)
  .put(Controller.updateKetQuaKiemTraSingle);

tonTaiCapTrenRouter
  .route('/')
  .get(Controller.getAll)
  .post(checkTempFolder, multipartMiddleware, Controller.createTonTaiCapTren)
  .put(checkTempFolder, multipartMiddleware, Controller.updateTonTaiCapTren);

tonTaiCapTrenRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.removeTonTaiCapTren);


