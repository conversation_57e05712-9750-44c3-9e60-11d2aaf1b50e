import * as responseAction from '../../../../helpers/responseHelper';
import queryHelper from '../../../../helpers/queryHelper';
import * as Service from './tonTaiCapTren.service';
import Model from './tonTaiCapTren.model';
import * as fileUtils from '../../../../utils/fileUtils';
import { deleteFile, getFilePath } from '../../../../utils/fileUtils';
import * as KetQuaKiemTraService from '../../KetQuaKiemTra/ketQuaKiemTra.service';
import { TRANG_THAI_XU_LY } from '../../../DanhMuc/TrangThaiXuLy';
import KET_QUA_KIEM_TRA from '../../KetQuaKiemTra/ketQuaKiemTra.model';
import { loggerMiddleware } from '../../../../logs/middleware';
import { formatTimeCriteria } from '../../../../utils/dataconverter';

const populateOpts = [
  { path: 'vi_tri_id', select: 'ten_vi_tri' },
  { path: 'duong_day_id vi_tri_id anh_vi_tris' },
  { path: 'khoang_cot_id', populate: 'vi_tri_bat_dau_id vi_tri_ket_thuc_id' },
  { path: 'tieu_chi_id', populate: 'tieu_chi_cha_id' },
  { path: 'cach_dien_id day_dan_id day_chong_set_id day_cap_quang_id cot_dien_id giao_cheo_id tiep_dat_id' },
  { path: 'ton_tai_cap_tren_id', populate: 'don_vi_id duong_day_id nguoi_tao_id' },
];

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id)
      .populate('don_vi_id duong_day_id nguoi_tao_id').lean();
    data.ket_qua_kiem_tra = await KetQuaKiemTraService.getAll({ ton_tai_cap_tren_id: id, is_deleted: false })
      .populate('khoang_cot_id vi_tri_id ')
      .populate({ path: 'tieu_chi_id', populate: 'tieu_chi_cha_id' });
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function removeTonTaiCapTren(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseAction.error(res, '', 400);
    }
    if (data.file_id) {
      deleteFile(getFilePath(data.file_id));
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function updateTonTaiCapTren(req, res) {
  try {
    let { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const dataAfterUpdate = await Model.findOne({ _id: value._id });
    const filePath = req.files?.file?.path;
    if (filePath) {
      const file_id = fileUtils.createUniqueFileName(filePath);
      await fileUtils.createByName(filePath, file_id);
      value.file_id = file_id;
      value.file_name = req.files?.file?.name;

      dataAfterUpdate.file_id && deleteFile(getFilePath(dataAfterUpdate.file_id));
    }
    const data = await Model.findOneAndUpdate({ _id: value._id }, value, { new: true })
      .populate('don_vi_id duong_day_id nguoi_tao_id');
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function createTonTaiCapTren(req, res) {

  try {
    let value = req.body;
    const filePath = req.files?.file?.path;
    if(filePath){
      const file_id = fileUtils.createUniqueFileName(filePath);
      await fileUtils.createByName(filePath, file_id);
      value.file_id = file_id;
      value.file_name = req.files?.file?.name;
    }
    const data = await Model.create(value);
    return responseAction.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    options.populate = [
      { path: 'don_vi_id' },
      { path: 'nguoi_tao_id' },
      { path: 'duong_day_id' },
    ];
    options.sort = { createdAt: 1 };
    if (criteria.thoi_gian) {
      criteria.created_at = criteria.thoi_gian;
      delete criteria.thoi_gian;
    }
    formatTimeCriteria(criteria, 'created_at', 'tu_ngay', 'den_ngay');
    const data = await Model.paginate(criteria, options);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function xacNhanTonTai(req, res) {

  try {
    const { id } = req.params;
    const ketQuaKiemTra = await KetQuaKiemTraService.getAll({ ton_tai_cap_tren_id: id, is_deleted: false });
    const data = await Model.findOneAndUpdate({ _id: id }, { xac_nhan_ton_tai: req.body.xac_nhan_ton_tai }, { new: true });
    if (!data) return responseAction.error(res, '', 404);
    ketQuaKiemTra.forEach(tonTai => {
      tonTai.tinh_trang_xu_ly = !!req.body.xac_nhan_ton_tai ? TRANG_THAI_XU_LY.CHUA_XU_LY.code : null;
    });
    await KET_QUA_KIEM_TRA.bulkWrite(
      ketQuaKiemTra.map((row) =>
        ({
          updateOne: {
            filter: { _id: row._id },
            update: { $set: row },
            upsert: false,
          },
        }),
      ),
    );
    const dataRtn = await KetQuaKiemTraService.getAll({ ton_tai_cap_tren_id: id, is_deleted: false })
      .populate(populateOpts);
    return responseAction.success(res, dataRtn);
  } catch (err) {
    console.log(err);
    responseAction.error(res, err);
  }
}

export async function updateKetQuaKiemTraSingle(req, res) {

  try {
    const { id } = req.params;
    const { error, value } = KetQuaKiemTraService.validate(req.body);
    if (error) return responseAction.error(res, error, 400);

    let tonTaiCapTrenData = await Model.findOne(
      { _id: id, is_deleted: false }).lean();
    if (!tonTaiCapTrenData) return responseAction.error(res, 404, '');

    value.ton_tai_cap_tren_id = id;
    value.don_vi_id = tonTaiCapTrenData.don_vi_id;
    let result;
    if (value._id) {
      result = await KetQuaKiemTraService.findByIdAndUpdate(value);
    } else {
      result = await KetQuaKiemTraService.create(value);
    }
    if (!result) return responseAction.error(res, 404, '');

    const dataRtn = await result.populate(populateOpts).execPopulate();

    return responseAction.success(res, dataRtn);

  } catch (err) {
    console.log(err);
    responseAction.error(res, err);
  }
}
