import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DON_VI, DUONG_DAY, TON_TAI_CAP_TREN, USER } from '../../../../constant/dbCollections';

const schema = new Schema({
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  duong_day_id: { type: Schema.Types.ObjectId, ref: DUONG_DAY },
  thoi_gian_tao: { type: Date },
  nguoi_tao_id: { type: Schema.Types.ObjectId, ref: USER },
  xac_nhan_ton_tai: { type: Boolean, default: false },
  file_id: { type: String },
  file_name: { type: String },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized compound indexes for TON_TAI_CAP_TREN queries
// Using background: true for better performance during index creation

// Primary index for don_vi_id filtering with soft delete
schema.index({ 
  don_vi_id: 1, 
  is_deleted: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  name: 'idx_don_vi_deleted_time'
});

// Index for duong_day_id queries with soft delete
schema.index({ 
  duong_day_id: 1, 
  is_deleted: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  name: 'idx_duong_day_deleted_time'
});

// Index for nguoi_tao_id queries with time sorting
schema.index({ 
  nguoi_tao_id: 1, 
  is_deleted: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  name: 'idx_nguoi_tao_deleted_time'
});

// Index for xac_nhan_ton_tai filtering
schema.index({ 
  xac_nhan_ton_tai: 1, 
  is_deleted: 1,
  thoi_gian_tao: -1
}, { 
  background: true,
  name: 'idx_xac_nhan_deleted_time'
});

// Compound index for don_vi and duong_day filtering
schema.index({ 
  don_vi_id: 1, 
  duong_day_id: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_don_vi_duong_day_deleted'
});

schema.plugin(mongoosePaginate);
export default mongoose.model(TON_TAI_CAP_TREN, schema, TON_TAI_CAP_TREN);
