import PHIEU_GIAO_VIEC from './../phieuGiaoViec.model';
import { KIEM_TRA, LOAI_CONG_VIEC } from '../../../DanhMuc/LoaiCongViec';


export function getAll(query) {
  const loaiCongViec = Object.values(LOAI_CONG_VIEC).filter(congViec => congViec.type === KIEM_TRA);
  const loaiCongViecCodes = loaiCongViec.map(loai => loai.code);
  query.loai_cong_viec = { $in: loaiCongViecCodes };
  return PHIEU_GIAO_VIEC.find(query)
    .lean();
}

export function count(query) {
  return PHIEU_GIAO_VIEC.count(query);
}

export function getOne(query, projection = {}) {
  return PHIEU_GIAO_VIEC.findOne(query, projection).lean();
}

export function getById(id, projection = {}) {
  return PHIEU_GIAO_VIEC.findById(id, projection).lean();
}
