import * as responseHelper from '../../../../helpers/responseHelper';
import Model from '../phieuGiaoViec.model';
import { KIEM_TRA, LOAI_CONG_VIEC } from '../../../DanhMuc/LoaiCongViec';
import * as PhieuGiaoViecService from '../phieuGiaoViec.service';
import { buildQuery, buildQueryNhanVien } from '../phieuGiaoViec.service';
import NGUOI_CONG_TAC from '../../NguoiCongTac/nguoiCongTac.model';
import { extractKeyObjectIds, extractObjectIds } from '../../../../utils/dataconverter';
import * as functionCommons from '../../../../common/functionCommons';
import { TRANG_THAI_PHIEU } from '../../../DanhMuc/TrangThaiCongViec';
import queryHelper from '../../../../helpers/queryHelper';
import * as UserService from '../../../User/user.service';
import * as NguoiCongTacService from '../../NguoiCongTac/nguoiCongTac.service';

export async function getAll(req, res) {
  try {
    const { criteria, options } = await buildQuery(req);
    if (!criteria.loai_cong_viec) {
      const loaiCongViec = Object.values(LOAI_CONG_VIEC).filter(congViec => congViec.type === KIEM_TRA);
      const loaiCongViecCodes = loaiCongViec.map(loai => loai.code);
      criteria.loai_cong_viec = { $in: loaiCongViecCodes };
    }

    if (criteria.su_dung_drone === 'TAT_CA') {
      delete criteria.su_dung_drone;
    }

    if (!options.hasOwnProperty('sort')) options.sort = { created_at: -1 };
    options.populate = [
      { path: 'don_vi_giao_phieu_id' },
      { path: 'nguoi_cap_phieu_id', select: 'full_name username phone bac_an_toan' },
      { path: 'chi_huy_truc_tiep_id', select: 'full_name' },
      { path: 'duong_day_ids', select: 'ten_duong_day' },
    ];
    const criteriaNhanVien = await buildQueryNhanVien(req, criteria);
    const data = await Model.paginate(criteriaNhanVien, options);

    if (!data?.docs || data.docs.length === 0) {
      return responseHelper.success(res, data || { docs: [] });
    }

    // Lấy danh sách phiếu giao việc IDs
    const phieuGiaoViecIds = extractObjectIds(data.docs);

    // Thực hiện truy vấn người công tác một lần duy nhất
    const allNguoiCongTac = await NGUOI_CONG_TAC.find({
      phieu_giao_viec_id: { $in: phieuGiaoViecIds },
      is_deleted: false,
    })
      .populate({ path: 'user_id', select: 'full_name' })
      .lean();

    // Nhóm người công tác theo phiếu giao việc
    const nguoiCongTacByPhieuId = functionCommons.groupBy(allNguoiCongTac, 'phieu_giao_viec_id');

    // Gán người công tác vào từng phiếu
    for (const item of data.docs) {
      item.nguoi_cong_tac = nguoiCongTacByPhieuId[item._id] || [];
    }

    // Thực hiện lấy dữ liệu phiếu công tác
    data.docs = await PhieuGiaoViecService.getDataPhieuCongTac(data.docs);

    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllWithDrone(req, res) {
  try {
    const { criteria, options } = queryHelper.extractQueryParam(req);
    const donViId = req.user.don_vi_id;

    criteria.uuid = { $exists: true };
    criteria.don_vi_giao_phieu_id = donViId;
    criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.TIEP_NHAN.code;
    criteria.su_dung_drone = true;
    criteria.chi_huy_truc_tiep_id = req.user._id;

    const tenNhanVien = criteria.ten_nhan_vien;
    delete criteria.ten_nhan_vien;
    if (tenNhanVien) {
      const allUser = await UserService.getAll(
        { full_name: { '$regex': tenNhanVien, '$options': 'i' }, don_vi_id: donViId, is_deleted: false },
        { _id: 1, full_name: 1 });
      const allUserId = extractObjectIds(allUser);
      const allNguoiCongTac = await NguoiCongTacService.getAll(
        { user_id: allUserId, is_deleted: false },
        { phieu_giao_viec_id: 1 });
      criteria['$or'] = [
        { _id: { $in: extractKeyObjectIds(allNguoiCongTac, 'phieu_giao_viec_id') } },
        { chi_huy_truc_tiep_id: { $in: allUserId } },
      ];
    }

    if (!options.hasOwnProperty('sort')) options.sort = { created_at: -1 };
    options.populate = [
      { path: 'don_vi_giao_phieu_id' },
      { path: 'nguoi_cap_phieu_id', select: 'full_name username phone bac_an_toan' },
      { path: 'chi_huy_truc_tiep_id', select: 'full_name' },
      { path: 'duong_day_ids', select: 'ten_duong_day' },
    ];

    const data = await Model.paginate(criteria, options);

    if (!data?.docs || data.docs.length === 0) {
      return responseHelper.success(res, data || { docs: [] });
    }

    // Lấy danh sách phiếu giao việc IDs
    const phieuGiaoViecIds = extractObjectIds(data.docs);

    // Thực hiện truy vấn người công tác một lần duy nhất
    const allNguoiCongTac = await NGUOI_CONG_TAC.find({
      phieu_giao_viec_id: { $in: phieuGiaoViecIds },
      is_deleted: false,
    })
      .populate({ path: 'user_id', select: 'full_name' })
      .lean();

    // Nhóm người công tác theo phiếu giao việc
    const nguoiCongTacByPhieuId = functionCommons.groupBy(allNguoiCongTac, 'phieu_giao_viec_id');

    // Gán người công tác vào từng phiếu
    for (const item of data.docs) {
      item.nguoi_cong_tac = nguoiCongTacByPhieuId[item._id] || [];
    }

    // Thực hiện lấy dữ liệu phiếu công tác
    data.docs = await PhieuGiaoViecService.getDataPhieuCongTac(data.docs);

    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllMeCreated(req, res) {
  try {
    const { criteria, options } = await buildQuery(req);
    criteria.nguoi_cap_phieu_id = req.user._id;
    if (!criteria.loai_cong_viec) {
      const loaiCongViec = Object.values(LOAI_CONG_VIEC).filter(congViec => congViec.type === KIEM_TRA);
      const loaiCongViecCodes = loaiCongViec.map(loai => loai.code);
      criteria.loai_cong_viec = { $in: loaiCongViecCodes };
    }
    if (!options.hasOwnProperty('sort')) options.sort = { created_at: -1 };
    options.populate = [
      { path: 'don_vi_giao_phieu_id' },
      { path: 'nguoi_cap_phieu_id', select: 'full_name username phone bac_an_toan' },
      { path: 'chi_huy_truc_tiep_id' },
      { path: 'duong_day_ids', select: 'ten_duong_day' },
    ];
    const data = await Model.paginate(criteria, options);

    if (!data?.docs || data.docs.length === 0) {
      return responseHelper.success(res, data || { docs: [] });
    }

    // Lấy danh sách phiếu giao việc IDs
    const phieuGiaoViecIds = extractObjectIds(data.docs);

    // Thực hiện truy vấn người công tác một lần duy nhất
    const allNguoiCongTac = await NGUOI_CONG_TAC.find({
      phieu_giao_viec_id: { $in: phieuGiaoViecIds },
      is_deleted: false,
    })
      .populate({ path: 'user_id', select: 'full_name' })
      .lean();

    // Nhóm người công tác theo phiếu giao việc
    const nguoiCongTacByPhieuId = functionCommons.groupBy(allNguoiCongTac, 'phieu_giao_viec_id');

    // Gán người công tác vào từng phiếu
    for (const item of data.docs) {
      item.nguoi_cong_tac = nguoiCongTacByPhieuId[item._id] || [];
    }

    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}
