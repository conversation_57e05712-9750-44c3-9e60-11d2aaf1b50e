import express from 'express';
import passport from 'passport';
import * as donviController from './phieuKiemTra.controller';
import { authorizationMiddleware } from '../../../RBAC/middleware';
import GiaoViecPermission from '../../../RBAC/permissions/GiaoViecPermission';
import { loggerMiddleware } from '../../../../logs/middleware';

export const phieuKiemTraRouter = express.Router();
phieuKiemTraRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
phieuKiemTraRouter.post('*', authorizationMiddleware([GiaoViecPermission.CREATE]));
phieuKiemTraRouter.get('*', authorizationMiddleware([GiaoViecPermission.READ]));
phieuKiemTraRouter.put('*', authorizationMiddleware([GiaoViecPermission.UPDATE]));
phieuKiemTraRouter.delete('*', authorizationMiddleware([GiaoViecPermission.DELETE]));
phieuKiemTraRouter
  .route('/')
  .get(donviController.getAll);
phieuKiemTraRouter
  .route('/mecreated')
  .get(donviController.getAllMeCreated);

phieuKiemTraRouter
  .route('/drone')
  .get(donviController.getAllWithDrone);
