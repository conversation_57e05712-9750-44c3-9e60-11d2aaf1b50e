import * as DonViService from '../../DonVi/donVi.service';
import { TRANG_THAI_XU_LY } from '../../DanhMuc/TrangThaiXuLy';
import momentTimezone from 'moment-timezone';
import PhieuGiaoViecModel from '../PhieuGiaoViec/phieuGiaoViec.model';
import { PHUONG_PHAP_THUC_HIEN } from '../../../constant/constant';

export async function convertCriteria(req, criteria, keepIdObject) {
  if (criteria.tu_ngay || criteria.den_ngay) {
    if (criteria.tu_ngay) {
      criteria.tu_ngay = new Date(criteria.tu_ngay);
    }

    if (criteria.den_ngay) {
      criteria.den_ngay = new Date(criteria.den_ngay);
    }

    criteria.thoi_gian_tao = {};
    if (criteria.tu_ngay) {
      if (criteria.den_ngay) {
        criteria.thoi_gian_tao.$gte = momentTimezone(criteria.tu_ngay).startOf('day');
        criteria.thoi_gian_tao.$lte = momentTimezone(criteria.den_ngay).endOf('day');
      } else {
        criteria.thoi_gian_tao.$gte = momentTimezone(criteria.tu_ngay).startOf('day');
      }
    } else {
      if (criteria.den_ngay) {
        criteria.thoi_gian_tao.$lte = momentTimezone(criteria.den_ngay).endOf('day');
      }
    }
    if (!Object.keys(criteria.thoi_gian_tao).length) {
      delete criteria.thoi_gian_tao;
    }
  }
  delete criteria.tu_ngay;
  delete criteria.den_ngay;

  if (criteria.phuong_phap_thuc_hien) {
    const phieuQuery = { is_deleted: false };
    if (criteria.phuong_phap_thuc_hien === 'KIEM_TRA_BANG_UAV') {
      phieuQuery.phuong_phap_thuc_hien = { $in: [PHUONG_PHAP_THUC_HIEN.BAY_THU_CONG, PHUONG_PHAP_THUC_HIEN.BAY_TU_DONG] };
    } else if (criteria.phuong_phap_thuc_hien === 'KIEM_TRA_TRUYEN_THONG') {
      phieuQuery.phuong_phap_thuc_hien = { $nin: [PHUONG_PHAP_THUC_HIEN.BAY_THU_CONG, PHUONG_PHAP_THUC_HIEN.BAY_TU_DONG] };
    }

    const phieuGiaoViecIds = await PhieuGiaoViecModel.distinct('_id', phieuQuery);
    criteria.phieu_giao_viec_id = { $in: phieuGiaoViecIds };
    delete criteria.phuong_phap_thuc_hien;
  }

  criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id, keepIdObject);
  if (!criteria.tinh_trang_xu_ly) {
    criteria.tinh_trang_xu_ly = { $in: Object.keys(TRANG_THAI_XU_LY) };
  }
  return criteria;
}

