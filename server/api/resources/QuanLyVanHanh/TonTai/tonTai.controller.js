import Model from '../KetQuaKiemTra/ketQuaKiemTra.model';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from '../KetQuaKiemTra/ketQuaKiemTra.service';
import * as controllerHelper from '../../../helpers/controllerHelper';
import { findOneById } from '../../../helpers/controllerHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import * as responseAction from '../../../helpers/responseHelper';
import * as ketQuaSuaChuaCoKeHoachService
  from '../../QuanLyVanHanh/KetQuaSuaChuaCoKeHoach/ketQuaSuaChuaCoKeHoach.service';
import { TRANG_THAI_XU_LY } from '../../DanhMuc/TrangThaiXuLy';
import CommonError from '../../../error/CommonError';
import { generateDocument } from '../../Report/GenerateFile/generate.controller';
import * as HieuChinhTonTaiService from '../PhieuGiaoViec/HieuChinhTonTai/hieuChinhTonTai.service';
import { getFilePath } from '../../../utils/fileUtils';
import { TEMPLATES_DIRS } from '../../../constant/constant';
import * as TieuChiService from '../../DanhMuc/NoiDungKiemTra/TieuChi/tieuChi.service.js';
import { extractObjectIds } from '../../../utils/dataconverter';
import { convertCriteria } from './tonTai.service';
import { Types } from 'mongoose';
import { cloneObj } from '../../../common/functionCommons';


const populateOpts = [
  {
    path: 'duong_day_id',
    select: 'ten_duong_day duong_day_cu_id',
    populate: { path: 'duong_day_cu_id', select: 'ten_duong_day' },
  },
  { path: 'don_vi_id', select: 'ten_don_vi don_vi_cha_id', populate: { path: 'don_vi_cha_id', select: 'ten_don_vi' } },
  { path: 'phieu_giao_viec_id vi_tri_id khoang_cot_id' },
  { path: 'ton_tai_cap_tren_id', populate: 'don_vi_id duong_day_id nguoi_tao_id' },
  { path: 'tieu_chi_id', populate: { path: 'noi_dung_kiem_tra_id tieu_chi_cha_id' } },
  { path: 'cach_dien_id day_dan_id cot_dien_id day_chong_set_id day_cap_quang_id' },
  { path: 'nguoi_tao', select: 'full_name' },
];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service);
export const create = controllerHelper.createCreateFunction(Model, Service);

export async function test(req, res) {
  try {
    const data = await Model.find()
      .populate({ path: 'phieu_giao_viec_id', select: 'duong_day_id' })
      .lean();

    for (let i = 0; i < data.length; i++) {
      data[i] = await Model.update({ _id: data[i]._id }, { duong_day_id: data[i].phieu_giao_viec_id?.duong_day_id }).lean();
    }

    return responseAction.success(res, { a: data.length });
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function doiTruyenTaiDeXuat(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    const beforeUpdate = await findOneById(Model, id);

    value.thoi_gian_de_xuat_cap_doi = new Date();

    await Model.update({ _id: id }, value);
    const updatedData = await findOneById(Model, id, populateOpts, true);
    if (!updatedData) {
      return responseAction.error(res, null, 404);
    }
    return responseAction.success(res, updatedData);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function truyenTaiDienDeXuat(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    value.thoi_gian_phe_duyet = new Date();

    await Model.update({ _id: id }, value);
    const updatedData = await findOneById(Model, id, populateOpts, true);
    if (!updatedData) {
      return responseAction.error(res, null, 404);
    }
    return responseAction.success(res, updatedData);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    if (!options.sort) {
      options.sort = { thoi_gian_tao: -1, don_vi_id: 1 };
    }
    options.populate = populateOpts;
    await convertCriteria(req, criteria);
    await handleHangMucQuery(criteria);

    let dataReturn;
    if (req.query.all) {
      dataReturn = await Model.find(criteria)
        .populate(options.populate);
    } else {
      dataReturn = await Model.paginate(criteria, options);
    }
    if (!dataReturn) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    for (let i = 0; i < dataReturn.docs.length; i++) {
      const doc = dataReturn.docs[i];
      const phieuGiaoViec = await ketQuaSuaChuaCoKeHoachService.getOne({ ton_tai_id: doc._id })
        .sort({ thoi_gian_tao: -1 })
        .populate('phieu_giao_viec_id');
      dataReturn.docs[i].ket_qua_sua_chua = phieuGiaoViec?.phieu_giao_viec_id;
    }

    responseHelper.success(res, dataReturn);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function getTonTaiChuaXuLy(req, res) {
  try {
    const { id } = req.params;
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;

    criteria.don_vi_id = id;
    criteria.tinh_trang_xu_ly = TRANG_THAI_XU_LY.CHUA_XU_LY.code;
    criteria['$or'] = [
      { 'de_xuat_cap_doi': 'XU_LY_TRONG_VAN_HANH' },
      { 'de_xuat_cap_doi': 'BAO_CAO_TTD', thoi_gian_phe_duyet: { $exists: true } },
    ];
    const ketQuaData = await Model.find(criteria)
      .populate(populateOpts)
      .sort({ don_vi_id: 1, thoi_gian_tao: -1 })
      .lean();

    if (!ketQuaData) {
      return responseHelper.error(res, null, 404);
    }

    const tonTai = ketQuaData.filter(ketQua => !!ketQua.phieu_giao_viec_id || !!ketQua.ton_tai_cap_tren_id);

    const dataReturn = {
      docs: tonTai.slice((options.page - 1) * options.limit, options.page * options.limit),
      limit: options.limit,
      page: parseInt(options.page),
      totalDocs: tonTai.length,
      totalPages: Math.floor(tonTai.length / options.limit) + 1,
    };

    dataReturn.docs = await HieuChinhTonTaiService.getDetailHieuChinh(dataReturn.docs);
    return responseHelper.success(res, dataReturn);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function getTonTaiKhiemKhuyetThietBi(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    let { criteria, options } = query;
    options.sort = { don_vi_id: 1, thoi_gian_tao: -1 };
    options.populate = populateOpts;

    criteria = {
      ...criteria, ...{
        $or: [
          { cach_dien_id: { $ne: null } },
          { day_dan_id: { $ne: null } },
          { day_cap_quang_id: { $ne: null } },
          { day_chong_set_id: { $ne: null } },
          { cot_dien_id: { $ne: null } },
          { giao_cheo_id: { $ne: null } },
          { tiep_dat_id: { $ne: null } }],
      },
    };

    const data = await Model.paginate(criteria, options);
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function handleHangMucQuery(criteria) {
  criteria.tieu_chi_id && (criteria.tieu_chi_id = Types.ObjectId(criteria.tieu_chi_id));
  const { hang_muc_ton_tai, tieu_chi_cha_id } = criteria;
  if (hang_muc_ton_tai) {
    const tieu_chi_id = await TieuChiService.getAll({ noi_dung_kiem_tra_id: hang_muc_ton_tai, is_deleted: false });
    criteria.tieu_chi_id = { $in: extractObjectIds(tieu_chi_id) };
    delete criteria.hang_muc_ton_tai;
  } else if (tieu_chi_cha_id) {
    const tieu_chi_id = await TieuChiService.getAll({ tieu_chi_cha_id: tieu_chi_cha_id, is_deleted: false });
    criteria.tieu_chi_id = { $in: extractObjectIds(tieu_chi_id) };
    delete criteria.tieu_chi_cha_id;
  }
  return criteria;
}

export async function bieuMauThongKeTonTai(req, res) {
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  await convertCriteria(req, criteria);
  await handleHangMucQuery(criteria);

  const thongKeTonTai = await Service.bangThongKeTheoDoiTonTai(criteria);
  let data = {};
  data.ton_tai = thongKeTonTai;
  const templateFilePath = getFilePath('bieu_mau_thong_ke_ton_tai.xlsx', TEMPLATES_DIRS.BIEU_MAU);
  const outputFileName = 'Bảng thống kê tồn tại.xlsx';
  generateDocument(res, data, templateFilePath, outputFileName);
}
