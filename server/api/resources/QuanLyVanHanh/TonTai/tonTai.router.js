import express from 'express';
import passport from 'passport';

import { authorizationMiddleware } from '../../RBAC/middleware';
import KeHoachXLTTPermission from '../../RBAC/permissions/KeHoachXLTTPermission';
import * as tonTaiController from './tonTai.controller';
import { loggerMiddleware } from '../../../logs/middleware';

export const tonTaiRouter = express.Router();

tonTaiRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
tonTaiRouter.put('*', authorizationMiddleware([KeHoachXLTTPermission.UPDATE]));

tonTaiRouter.route('/')
  .get(tonTaiController.getAll);

tonTaiRouter.route('/download')
  .get(tonTaiController.bieuMauThongKeTonTai);

tonTaiRouter.route('/donvi/:id/chuaxuly')
  .get(tonTaiController.getTonTaiChuaXuLy);

tonTaiRouter.route('/khiemkhuyetthietbi')
  .get(tonTaiController.getTonTaiKhiemKhuyetThietBi);

tonTaiRouter.route('/:id/doitruyentaidexuat')
  .put(tonTaiController.doiTruyenTaiDeXuat);

tonTaiRouter.route('/:id/truyentaidiendexuat')
  .put(tonTaiController.truyenTaiDienDeXuat);

tonTaiRouter
  .route('/:id')
  .get(tonTaiController.findOne)
  .put(tonTaiController.update);
