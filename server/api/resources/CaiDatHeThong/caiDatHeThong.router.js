import express from 'express';
import passport from 'passport';
import * as caiDatHeThongController from './caiDatHeThong.controller';
import { authorizationMiddleware } from '../RBAC/middleware';
import SettingPermission from '../RBAC/permissions/SettingPermission';
import { androidAppMiddleware, multipartMiddleware } from '../../utils/fileUtils';
import { loggerMiddleware } from '../../logs/middleware';
import { updateConfigMobileApp, updateConfigOwllee, updateConfigPmis } from './caiDatHeThong.controller';

export const caiDatHeThongRouter = express.Router();

caiDatHeThongRouter
  .route('/linkandroidapp')
  .put(multipartMiddleware, caiDatHeThongController.updateLinkAndroid);

caiDatHeThongRouter
  .route('/androidapp')
  .put(androidAppMiddleware, caiDatHeThongController.updateAppAndroid);

caiDatHeThongRouter
  .route('/iosapp')
  .put(multipartMiddleware, caiDatHeThongController.updateAppIos);

caiDatHeThongRouter
  .route('/flightcontrolapp')
  .put(multipartMiddleware, caiDatHeThongController.flightControlApp);

caiDatHeThongRouter
  .route('/serverlog')
  .put(multipartMiddleware, caiDatHeThongController.serverLog);

caiDatHeThongRouter
  .route('/map')
  .post(multipartMiddleware, caiDatHeThongController.checkMapKey)
  .put(caiDatHeThongController.updateConfigMap);

caiDatHeThongRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
caiDatHeThongRouter.post('*', authorizationMiddleware([SettingPermission.CREATE]));
caiDatHeThongRouter.put('*', authorizationMiddleware([SettingPermission.UPDATE]));
caiDatHeThongRouter.delete('*', authorizationMiddleware([SettingPermission.DELETE]));
caiDatHeThongRouter
  .route('/')
  .get(caiDatHeThongController.findOne)
  .put(caiDatHeThongController.update)
  .post(caiDatHeThongController.notificationCapNhatVersion);

caiDatHeThongRouter
  .route('/mobileapp')
  .put(caiDatHeThongController.updateConfigMobileApp);

caiDatHeThongRouter
  .route('/owllee')
  .put(caiDatHeThongController.updateConfigOwllee);

caiDatHeThongRouter
  .route('/pmis')
  .put(caiDatHeThongController.updateConfigPmis);

caiDatHeThongRouter
  .route('/:id')
  .get(caiDatHeThongController.findOne);
