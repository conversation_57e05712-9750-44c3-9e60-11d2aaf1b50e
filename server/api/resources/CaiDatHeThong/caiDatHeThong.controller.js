import * as Service from './caiDatHeThong.service';
import Model, { APP_ANDROID_TYPE } from './caiDatHeThong.model';
import * as controllerHelper from '../../helpers/controllerHelper';
import * as responseHelper from '../../helpers/responseHelper';
import * as fileUtils from '../../utils/fileUtils';
import { STORE_DIRS } from '../../constant/constant';
import CommonError from '../../error/CommonError';
import { deleteFile } from '../../utils/fileUtils';
import { defaultPermissionExtractor } from '../RBAC/middleware';
import SettingPermission from '../RBAC/permissions/SettingPermission';
import { authorizePermission } from '../RBAC/authorizationHelper';
import UserService from '../User/user.service';
import * as NotificationService from '../Notification/notification.service';
import * as dbCollections from '../../constant/dbCollections';
import { NOTIFICATION_ACTION, NOTIFICATION_TYPE } from '../Notification/notification.constants';
import { extractIds } from '../../utils/dataconverter';
import { getMenuConfig } from '../../../config/configChung';
import axios from 'axios';

export async function findOne(req, res) {
  try {
    const menuConfig = getMenuConfig;
    const data = await Model.findOne({}, { _id: 0 }).lean();
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    if (!authorizePermission(defaultPermissionExtractor(req), [SettingPermission.UPDATE])) {
      data.ldap_username = data.ldap_username?.replace(new RegExp('.', 'g'), '*');
      data.ldap_password = data.ldap_password?.replace(new RegExp('.', 'g'), '*');
      data.sign_appcode = data.sign_appcode?.replace(new RegExp('.', 'g'), '*');
      data.sign_password = data.sign_password?.replace(new RegExp('.', 'g'), '*');
    }

    data.menubar = menuConfig.menubar;
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, CommonError.NOT_FOUND);
    const setting = await Model.findOne().lean();
    if (!value.phien_auto_xoa_anh) value.phien_auto_xoa_anh = undefined;
    const data = await Model.findOneAndUpdate({ _id: setting._id }, value, { new: true }).lean();
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export const getAll = controllerHelper.createGetAllFunction(Model);

export async function updateAppAndroid(req, res) {
  try {
    const fileId = fileUtils.createUniqueFileName(req.files.file.originalFilename);
    await fileUtils.createByName(req.files.file.path, fileId, STORE_DIRS.MOBILE_APP);

    const data = await Model.findOne().lean();
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    const dataReturn = await Model.findOneAndUpdate(
      { _id: data._id },
      { android_app: fileId, android_app_type: APP_ANDROID_TYPE.FILE, link_android_app: '' },
      { new: true });
    if (data.android_app) {
      deleteFile(`${STORE_DIRS.MOBILE_APP}/${data.android_app}`);
    }
    return responseHelper.success(res, dataReturn);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function updateLinkAndroid(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, CommonError.NOT_FOUND);
    const beforeUpdate = await Model.findOne().lean();
    if (!beforeUpdate) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    const dataReturn = await Model.findOneAndUpdate(
      { _id: beforeUpdate._id },
      {
        link_android_app: value.link_android_app,
        android_app_type: APP_ANDROID_TYPE.LINK,
        android_app: '',
      },
      { new: true });
    if (beforeUpdate.android_app) {
      deleteFile(`${STORE_DIRS.MOBILE_APP}/${beforeUpdate.android_app}`);
    }
    return responseHelper.success(res, dataReturn);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function updateAppIos(req, res) {
  try {
    const fileId = fileUtils.createUniqueFileName(req.files.file.originalFilename);
    await fileUtils.createByName(req.files.file.path, fileId, STORE_DIRS.MOBILE_APP);

    const data = await Model.findOne().lean();
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    const dataReturn = await Model.findOneAndUpdate({ _id: data._id }, { ios_app: fileId }, { new: true });
    if (data.ios_app) {
      deleteFile(`${STORE_DIRS.MOBILE_APP}/${data.ios_app}`);
    }
    return responseHelper.success(res, dataReturn);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function flightControlApp(req, res) {
  try {
    const fileId = fileUtils.createUniqueFileName(req.files.file.originalFilename);
    await fileUtils.createByName(req.files.file.path, fileId, STORE_DIRS.MOBILE_APP);

    const currentData = await Model.findOne().lean();
    if (!currentData) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    const dataReturn = await Model.findOneAndUpdate({ _id: currentData._id }, { flight_control_app: fileId }, { new: true });
    if (currentData.flight_control_app) {
      deleteFile(`${STORE_DIRS.MOBILE_APP}/${currentData.flight_control_app}`);
    }
    return responseHelper.success(res, dataReturn);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function notificationCapNhatVersion(req, res) {
  try {
    const { t } = req;
    let userData = [];
    let notifyData = { ...req.body };
    delete notifyData.typeNotify;

    const userResponse = await UserService.getAll();
    userData = [...userData, ...userResponse];

    const userNotiId = extractIds(userData).filter(userId => userId?.toString() !== req.user._id?.toString());

    const dataResponse = await NotificationService.notification(NOTIFICATION_TYPE.SYSTEM_TO_USER, dbCollections.CAI_DAT_HE_THONG,
      null, userNotiId, NOTIFICATION_ACTION.CAP_NHAT_VERSION_MOI, notifyData, req.body.typeNotify, t);

    return responseHelper.success(res, dataResponse);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function serverLog(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, CommonError.NOT_FOUND);
    const setting = await Model.findOne().lean();
    const data = await Model.findOneAndUpdate({ _id: setting._id }, value, { new: true });
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function updateConfigMobileApp(req, res) {
  const dataKeys = [
    'version_dieu_khien_bay',
    'mo_ta_dieu_khien_bay',
    'version_quan_ly_duong_day',
    'mo_ta_quan_ly_duong_day',
  ];
  return updateConfigBase(req, res, dataKeys);
}


export async function updateConfigOwllee(req, res) {
  const dataKeys = ['hien_thi_owllee', 'owllee_id'];
  return updateConfigBase(req, res, dataKeys);
}


export async function updateConfigPmis(req, res) {
  const dataKeys = ['pmis_pdkey', 'pmis_service'];
  return updateConfigBase(req, res, dataKeys);
}

export async function updateConfigMap(req, res) {
  const dataKeys = ['map_key', 'loai_ban_do', 'layer_disp'];
  return updateConfigBase(req, res, dataKeys);
}


async function updateConfigBase(req, res, dataKeys = []) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, CommonError.NOT_FOUND);

    const setting = await Model.findOne().lean();
    if (!setting?._id) return responseHelper.error(res, CommonError.NOT_FOUND);

    const dataUpdate = {}, projection = {};

    dataKeys.map(key => {
      dataUpdate[key] = value[key];
      projection[key] = 1;
    });

    const data = await Model.findOneAndUpdate(
      { _id: setting._id },
      dataUpdate,
      {
        projection,
        returnDocument: 'after',
      },
    ).lean();

    if (!data) return responseHelper.error(res, CommonError.NOT_FOUND);

    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function checkMapKey(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    const mapKey = value?.mapKey;
    const apiUrl = "https://geocode-api.arcgis.com/arcgis/rest/services/World/GeocodeServer/findAddressCandidates";
    const address = "Viet Nam";
    const url = `${apiUrl}?f=pjson&singleLine=${address}&token=${mapKey}`;

    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: url,
    };

    let data = {};

    await axios.request(config)
      .then((response) => {
        data = response?.data;
      })
      .catch((error) => {
        data = null;
      });

    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    else if (data?.error) {
      return responseHelper.success(res, { isValid: false });
    }

    return responseHelper.success(res, { isValid: true });

  } catch (err) {
    responseHelper.error(res, err);
  }
}
