import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CAI_DAT_HE_THONG } from '../../constant/dbCollections';
import { flightControlApp } from './caiDatHeThong.controller';

export const APP_ANDROID_TYPE = { FILE: 'FILE', LINK: 'LINK' };
export const FORCE_ALL_2FA = { ALL_USER: 'ALL_USER', DESIGNATED: 'DESIGNATED' };

const schema = new Schema({
  don_vi_dang_nhap: { type: String, validate: /\S+/ },
  don_vi_reset: { type: String, validate: /\S+/ },
  don_vi_doi_mat_khau: { type: String, validate: /\S+/ },
  don_vi_xoa_anh: { type: String, validate: /\S+/ },
  phien_dang_nhap: { type: Number, validate: /\S+/ },
  phien_reset: { type: Number, validate: /\S+/ },
  phien_doi_mat_khau: { type: Number, validate: /\S+/ },
  phien_auto_xoa_anh: { type: Number, validate: /\S+/ },
  kich_hoat_auto_xoa_anh: { type: Boolean, default: false },
  android_app: { type: String },
  flight_control_app: { type: String },
  link_android_app: { type: String },
  android_app_type: {
    type: String,
    enum: Object.values(APP_ANDROID_TYPE),
    default: APP_ANDROID_TYPE.FILE,
  },

  api_ky_dien_tu: { type: String },
  sign_appcode: String,
  sign_password: String,
  loai_ban_do: String,
  layer_disp: [{ type: String }],
  api_kinkei: { type: String },
  api_pmis: { type: String },
  ldap_url: { type: String },
  ldap_base_dn: { type: String },
  ldap_username: { type: String },
  ldap_password: { type: String },

  open_server_log: { type: Boolean, default: true },

  login_tfa: { type: Boolean, default: false },
  force_all_login_tfa: {
    type: String,
    enum: Object.values(FORCE_ALL_2FA),
    default: FORCE_ALL_2FA.DESIGNATED,
  },
  show_import: { type: Boolean, default: false },
  tach_duong_day: { type: Boolean, default: false },

  is_deleted: { type: Boolean, default: false, select: false },

  times_wrong_pass: Number,
  activate_times_wrong_pass: { type: Boolean, default: false },
  wrong_pass_to_capcha: Number,
  activate_wrong_pass_to_capcha: { type: Boolean, default: false },

  //BEGIN version,mota
  version_dieu_khien_bay: { type: Number },
  mo_ta_dieu_khien_bay: { type: String },
  version_quan_ly_duong_day: { type: Number },
  mo_ta_quan_ly_duong_day: { type: String },

  hien_thi_owllee: { type: Boolean },
  owllee_id: { type: String },
  //END version,mota
  pmis_service: { type: String },
  pmis_pdkey: { type: String },
  map_key: { type: String },

  // marquee
  show_marquee: { type: Boolean, default: false },
  text_marquee: { type: String },
  web_version: { type: String },

}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(CAI_DAT_HE_THONG, schema, CAI_DAT_HE_THONG);
