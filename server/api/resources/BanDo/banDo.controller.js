import * as responseAction from '../../helpers/responseHelper';
import queryHelper from '../../helpers/queryHelper';
import * as ViTriService from '../TongKe/ViTri/viTri.service';

export async function getAllViTri(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ma_giao_cheo', 'ten_giao_cheo']);
    const { criteria } = query;
    const data = await ViTriService.getAll(criteria);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}
