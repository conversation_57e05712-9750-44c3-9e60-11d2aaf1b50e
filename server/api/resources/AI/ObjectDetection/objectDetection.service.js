import fetch from 'node-fetch';
import path from 'path';
import FormData from 'form-data';

import { MIME_TYPES } from '../../../constant/constant';
import { ANH_VI_TRI } from '../../../constant/dbCollections';

import * as fileUtils from '../../../utils/fileUtils';
import * as imageUtils from '../../../utils/ImageUtilsGM';
import { extractIds, extractObjectIds } from '../../../utils/dataconverter';
import * as validatorHelper from '../../../helpers/validatorHelper';
import * as requestHelper from '../../../helpers/requestHelper';

import * as ThietBiService from '../../DanhMuc/DmThietBi/dmThietBi.service';
import * as TinhTrangKBTService from '../../DanhMuc/DmThietBi/TinhTrangKBT/tinhTrangKBT.service';
import * as ThietBiPhatHienService from '../../QuanLyVanHanh/ThietBiPhatHien/thietBiPhatHien.service';
import * as BatThuongPhatHienService from '../../QuanLyVanHanh/BatThuongPhatHien/batThuongPhatHien.service';
import * as AnhViTriService from '../../AnhViTri/anhViTri.service';
import * as CaiDatAiService from '../../CaiDatAi/caiDatAi.service';

import { AI_STATUS } from '../../QuanLyVanHanh/BatThuongPhatHien/batThuongPhatHien.model';
import { PROCESS_STATUS } from '../../AnhViTri/anhViTri.constant';

// Concurrency control for AI processing
const MAX_CONCURRENT_AI_REQUESTS = 1;

// Cache for reference data
let cachedThietBi = null;
let cachedKBT = null;
let cacheExpiry = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

class Semaphore {
  constructor(maxConcurrency) {
    this.maxConcurrency = maxConcurrency;
    this.currentConcurrency = 0;
    this.queue = [];
  }

  async acquire() {
    return new Promise((resolve) => {
      if (this.currentConcurrency < this.maxConcurrency) {
        this.currentConcurrency++;
        resolve();
      } else {
        this.queue.push(resolve);
      }
    });
  }

  release() {
    this.currentConcurrency--;
    if (this.queue.length > 0) {
      const resolve = this.queue.shift();
      this.currentConcurrency++;
      resolve();
    }
  }
}

const aiSemaphore = new Semaphore(MAX_CONCURRENT_AI_REQUESTS);

async function getCachedReferenceData() {
  const now = Date.now();
  if (cachedThietBi && cachedKBT && cacheExpiry && now < cacheExpiry) {
    return { cachedThietBi, cachedKBT };
  }

  // Refresh cache
  const [allThietBi, allKBT] = await Promise.all([
    ThietBiService.getAll({ is_deleted: false }),
    TinhTrangKBTService.getAll({ is_deleted: false })
  ]);

  const allThietBiIdMap = {};
  allThietBi.forEach(thietBi => {
    allThietBiIdMap[labelNormalize(thietBi.ma_thiet_bi)] = thietBi;
  });

  const allKBTIdMap = {};
  allKBT.forEach(kbt => {
    allKBTIdMap[labelNormalize(kbt.ma_tinh_trang)] = kbt;
  });

  cachedThietBi = allThietBiIdMap;
  cachedKBT = allKBTIdMap;
  cacheExpiry = now + CACHE_DURATION;

  return { cachedThietBi, cachedKBT };
}

async function getObjectDetection(imagePath, retries = 3) {
  const aiSetting = await CaiDatAiService.getOne();
  if (!aiSetting?.api_phan_tich_hinh_anh) return null;

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const response = await requestHelper.postFile(aiSetting.api_phan_tich_hinh_anh, imagePath);

      if (!response || (!response.success || response.success === 'False')) {
        if (attempt === retries) {
          throw new Error('Không thể thực hiện được sau ' + retries + ' lần thử');
        }
        continue;
      }
      
      if (!response.classified || response.classified.length === 0) return [];

      return response.classified;
    } catch (error) {
      if (attempt === retries) {
        throw error;
      }
      // Wait before retry (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
}

async function promiseObjectDetection(imagePath) {
  await aiSemaphore.acquire();
  try {
    const result = await getObjectDetection(imagePath);
    return result;
  } finally {
    aiSemaphore.release();
  }
}

function labelNormalize(label) {
  return label?.toLowerCase()?.trim();
}

export async function processImage(image) {
  if (image.process_status === PROCESS_STATUS.NOT_PROCESSED) return image;

  async function deleteCurrentAiData() {
    if (!image._id) return;

    const [thietBiAiCurrent, batThuongAiCurrent] = await Promise.all([
      ThietBiPhatHienService.getAll(
        { anh_vi_tri_id: image._id, is_deleted: false, from_ai: true },
        { _id: 1, ai_status: 1 },
      ),
      BatThuongPhatHienService.getAll(
        {
          anh_vi_tri_id: image._id,
          $or: [
            { from_ai: true },
            { thiet_bi_phat_hien_id: { $exists: true } },
          ],
          is_deleted: false,
        },
        { _id: 1, ai_status: 1, thiet_bi_phat_hien_id: 1 },
      )
    ]);

    const thietBiDelete = thietBiAiCurrent.filter(thietBi => thietBi.ai_status === AI_STATUS.CHUA_XAC_NHAN);
    const thietBiDeleteIds = extractIds(thietBiDelete);
    const batThuongDelete = batThuongAiCurrent.filter(batThuong => {
      return batThuong.ai_status === AI_STATUS.CHUA_XAC_NHAN || 
             (batThuong.thiet_bi_phat_hien_id && thietBiDeleteIds.includes(batThuong.thiet_bi_phat_hien_id.toString()));
    });
    
    // Parallel delete operations
    await Promise.all([
      ThietBiPhatHienService.deleteAll(thietBiDelete),
      BatThuongPhatHienService.deleteAll(batThuongDelete)
    ]);
  }

  try {
    // Parallel operations for initial setup
    const [, , { cachedThietBi: allThietBiIdMap, cachedKBT: allKBTIdMap }] = await Promise.all([
      AnhViTriService.processing(image),
      deleteCurrentAiData(),
      getCachedReferenceData()
    ]);

    const imagePath = fileUtils.getFilePath(image.image_id, image?.folder_path);
    const [metadata, classifieds] = await Promise.all([
      imageUtils.getImageMetadata(imagePath),
      promiseObjectDetection(imagePath)
    ]);
    
    if (classifieds.length === 0) {
      await AnhViTriService.processSucess(image);
      return;
    }

    const objectsDetected = classifieds.filter(item => {
      return allThietBiIdMap[labelNormalize(item.label)];
    });
    const defectsDetected = classifieds.filter(item => {
      return allKBTIdMap[labelNormalize(item.label)];
    });

    const thietBiConverted = objectsDetected.map(object => convertObjectToThietBi(image._id, metadata, object, allThietBiIdMap));
    // Batch check existing ThietBi
    const thietBiCheckPromises = thietBiConverted.map(thietBi => 
      ThietBiPhatHienService.getOne({
        dm_thiet_bi_id: thietBi.dm_thiet_bi_id,
        anh_vi_tri_id: thietBi.anh_vi_tri_id,
        height: thietBi.height,
        width: thietBi.width,
        x: thietBi.x,
        y: thietBi.y,
        from_ai: true,
        is_deleted: false,
      })
    );

    const existingThietBi = await Promise.all(thietBiCheckPromises);
    const thietBiPhatHienCreate = [];
    const thietBiPhatHienExist = [];

    thietBiConverted.forEach((thietBi, index) => {
      if (existingThietBi[index]) {
        thietBiPhatHienExist.push(existingThietBi[index]);
      } else {
        thietBiPhatHienCreate.push(thietBi);
      }
    });

    if ([...thietBiPhatHienCreate, ...thietBiPhatHienExist].length > 0) {
      const thietBiPhatHien = await ThietBiPhatHienService.create(thietBiPhatHienCreate);
      const thietBiList = [...thietBiPhatHien, ...thietBiPhatHienExist];
      const batThuongPhatHienConverted = defectsDetected.map(defect => convertDefectToBatThuong(thietBiList, metadata, defect, allKBTIdMap));
      
      if (batThuongPhatHienConverted.length > 0) {
        // Batch check existing BatThuong
        const batThuongCheckPromises = batThuongPhatHienConverted.map(batThuong => 
          BatThuongPhatHienService.getOne({
            dm_thiet_bi_id: batThuong.dm_thiet_bi_id,
            anh_vi_tri_id: batThuong.anh_vi_tri_id,
            height: batThuong.height,
            width: batThuong.width,
            x: batThuong.x,
            y: batThuong.y,
            from_ai: true,
            is_deleted: false,
          })
        );

        const existingBatThuong = await Promise.all(batThuongCheckPromises);
        const batThuongPhatHienCreate = batThuongPhatHienConverted.filter((_, index) => !existingBatThuong[index]);
        
        if (batThuongPhatHienCreate.length > 0) {
          await BatThuongPhatHienService.create(batThuongPhatHienCreate);
        }
      }
    }
    await AnhViTriService.processSucess(image);
  } catch (e) {
    console.log(e);
    await AnhViTriService.processFail(image);
  }
}

function convertDefectToBatThuong(thietBiPhatHien, metadata, defect, allKBTIdMap) {
  const x = defect.xmin / metadata.width;
  const y = defect.ymin / metadata.height;
  const width = (defect.xmax - defect.xmin) / metadata.width;
  const height = (defect.ymax - defect.ymin) / metadata.height;

  let thiet_bi_phat_hien_id = null;
  let dm_thiet_bi_id = null;
  let anh_vi_tri_id = null;

  const thietBi = thietBiPhatHien.find((thietBi) => {
    if (thietBi.x > x) return false;
    if (thietBi.y > y) return false;
    if (thietBi.width + thietBi.x < x + width) return false;
    if (thietBi.height + thietBi.y < y + height) return false;
    return true;
  });

  if (thietBi) {
    thiet_bi_phat_hien_id = thietBi._id;
    dm_thiet_bi_id = thietBi.dm_thiet_bi_id;
    anh_vi_tri_id = thietBi.anh_vi_tri_id;
  }

  return {
    thiet_bi_phat_hien_id: thiet_bi_phat_hien_id,
    tinh_trang_kbt_id: allKBTIdMap[labelNormalize(defect.label)]?._id,
    dm_thiet_bi_id: dm_thiet_bi_id,
    anh_vi_tri_id: anh_vi_tri_id,
    height: height,
    width: width,
    x: x,
    y: y,
    from_ai: true,
  };
}

function convertObjectToThietBi(imageId, metadata, object, allThietBiIdMap) {

  const x = object.xmin;
  const y = object.ymin;
  const width = object.xmax - object.xmin;
  const height = object.ymax - object.ymin;

  return {
    dm_thiet_bi_id: allThietBiIdMap[labelNormalize(object.label)]?._id,
    anh_vi_tri_id: imageId,
    ghi_chu: null,
    height: height / metadata.height,
    width: width / metadata.width,
    x: x / metadata.width,
    y: y / metadata.height,
    from_ai: true,
  };
}

const Joi = require('joi');

const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return ANH_VI_TRI.create(value);
}

export function getAll(query) {
  return ANH_VI_TRI.find(query).lean();
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await ANH_VI_TRI.findByIdAndUpdate(value._id, value);
  }
}

export const validate = (data, method) => {
  return validatorHelper.validate(objSchema, data, method);
};
