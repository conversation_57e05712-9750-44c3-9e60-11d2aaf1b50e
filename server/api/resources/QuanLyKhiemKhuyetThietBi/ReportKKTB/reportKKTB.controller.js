import * as responseAction from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from './reportKKTB.service';
import * as DonViService from '../../DonVi/donVi.service';
import * as CotDienService from '../../TongKe/CotDien/cotDien.service';
import * as DayChongSetService from '../../TongKe/DayChongSet/dayChongSet.service';
import * as DayCapQuangService from '../../TongKe/DayCapQuang/dayCapQuang.service';
import * as CachDienService from '../../TongKe/CachDien/cachDien.service';
import * as DayDanService from '../../TongKe/DayDan/dayDan.service';
import * as TiepDatService from '../../TongKe/TiepDat/tiepDat.service';
import * as GiaoCheoService from '../../TongKe/GiaoCheo/giaoCheo.service';

export async function formatQuery(req) {
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  if (!criteria.don_vi?.hasOwnProperty('$exists')) {
    criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi);
  } else {
    criteria.don_vi_id = await DonViService.getDonViQuery(req, req.user.don_vi_id);
  }
  return criteria;
}

// Cột điện
export async function getAllSoLuongCotDienTheoHang(req, res) {
  try {
    const data = await Service.soLuongTheoHang(await formatQuery(req), CotDienService, 'cot_dien_id');
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getAllSoLuongCotDienTheoDonVi(req, res) {
  try {
    const data = await Service.soLuongTheoDonVi(await formatQuery(req), CotDienService, 'cot_dien_id');
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

// Cách điện
export async function getAllSoLuongCachDienTheoHang(req, res) {
  try {
    const data = await Service.soLuongTheoHang(await formatQuery(req), CachDienService, 'cach_dien_id');
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getAllSoLuongCachDienTheoDonVi(req, res) {
  try {
    const data = await Service.soLuongTheoDonVi(await formatQuery(req), CachDienService, 'cach_dien_id');
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

// Dây dẫn
export async function getAllSoLuongDayDanTheoHang(req, res) {
  try {
    const data = await Service.soLuongTheoHang(await formatQuery(req), DayDanService, 'day_dan_id');
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getAllSoLuongDayDanTheoDonVi(req, res) {
  try {
    const data = await Service.soLuongTheoDonVi(await formatQuery(req), DayDanService, 'day_dan_id');
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

// Cáp quang
export async function getAllSoLuongCapQuangTheoHang(req, res) {
  try {
    const data = await Service.soLuongTheoHang(await formatQuery(req), DayCapQuangService, 'day_cap_quang_id');
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getAllSoLuongCapQuangTheoDonVi(req, res) {
  try {
    const data = await Service.soLuongTheoDonVi(await formatQuery(req), DayCapQuangService, 'day_cap_quang_id');
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

// Chống sét
export async function getAllSoLuongDayChongSetTheoHang(req, res) {
  try {
    const data = await Service.soLuongTheoHang(await formatQuery(req), DayChongSetService, 'day_chong_set_id');
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getAllSoLuongDayChongSetTheoDonVi(req, res) {
  try {
    const data = await Service.soLuongTheoDonVi(await formatQuery(req), DayChongSetService, 'day_chong_set_id');
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

// Tiếp đất
export async function getAllSoLuongDayTiepDatTheoHang(req, res) {
  try {
    const data = await Service.soLuongTheoHang(await formatQuery(req), TiepDatService, 'tiep_dat_id');
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getAllSoLuongDayTiepDatTheoDonVi(req, res) {
  try {
    const data = await Service.soLuongTheoDonVi(await formatQuery(req), TiepDatService, 'tiep_dat_id');
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

// Giao chéo
export async function getAllSoLuongDayGiaoCheoTheoHang(req, res) {
  try {
    const data = await Service.soLuongTheoHang(await formatQuery(req), GiaoCheoService, 'giao_cheo_id');
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getAllSoLuongDayGiaoCheoTheoDonVi(req, res) {
  try {
    const data = await Service.soLuongTheoDonVi(await formatQuery(req), GiaoCheoService, 'giao_cheo_id');
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

// ALL
export async function getAllSoLuongTheoHang(req, res) {
  try {
    const data = await Service.tongHopSoLuongTheoHangSanXuat(await formatQuery(req));
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getAllSoLuongTheoDonVi(req, res) {
  try {
    const data = await Service.tongHopSoLuongTheoDonVi(await formatQuery(req));
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}
