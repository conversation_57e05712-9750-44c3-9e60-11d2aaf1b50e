import express from 'express';
import passport from 'passport';
import * as Controller from './reportKKTB.controller';
import { loggerMiddleware } from '../../../logs/middleware';

export const reportKKTBRouter = express.Router();
reportKKTBRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

// Cột điện
reportKKTBRouter
  .route('/cotdientheohang')
  .get(Controller.getAllSoLuongCotDienTheoHang);

reportKKTBRouter
  .route('/cotdientheodonvi')
  .get(Controller.getAllSoLuongCotDienTheoDonVi);

// Cách điện
reportKKTBRouter
  .route('/cachdientheohang')
  .get(Controller.getAllSoLuongCachDienTheoHang);

reportKKTBRouter
  .route('/cachdientheodonvi')
  .get(Controller.getAllSoLuongCachDienTheoDonVi);

// Dây dẫn
reportKKTBRouter
  .route('/daydantheohang')
  .get(Controller.getAllSoLuongDayDanTheoHang);

reportKKTBRouter
  .route('/daydantheodonvi')
  .get(Controller.getAllSoLuongDayDanTheoDonVi);

// Dây cáp quang
reportKKTBRouter
  .route('/capquangtheohang')
  .get(Controller.getAllSoLuongCapQuangTheoHang);

reportKKTBRouter
  .route('/capquangtheodonvi')
  .get(Controller.getAllSoLuongCapQuangTheoDonVi);

// Dây chống sét
reportKKTBRouter
  .route('/chongsettheohang')
  .get(Controller.getAllSoLuongDayChongSetTheoHang);

reportKKTBRouter
  .route('/chongsettheodonvi')
  .get(Controller.getAllSoLuongDayChongSetTheoDonVi);

// Tiếp đất
reportKKTBRouter
  .route('/tiepdattheohang')
  .get(Controller.getAllSoLuongDayTiepDatTheoHang);

reportKKTBRouter
  .route('/tiepdattheodonvi')
  .get(Controller.getAllSoLuongDayTiepDatTheoDonVi);

// Giao chéo
reportKKTBRouter
  .route('/giaocheotheohang')
  .get(Controller.getAllSoLuongDayGiaoCheoTheoHang);

reportKKTBRouter
  .route('/giaocheotheodonvi')
  .get(Controller.getAllSoLuongDayGiaoCheoTheoDonVi);

// All
reportKKTBRouter
  .route('/soluongtheohang')
  .get(Controller.getAllSoLuongTheoHang);

reportKKTBRouter
  .route('/soluongtheodonvi')
  .get(Controller.getAllSoLuongTheoDonVi);
