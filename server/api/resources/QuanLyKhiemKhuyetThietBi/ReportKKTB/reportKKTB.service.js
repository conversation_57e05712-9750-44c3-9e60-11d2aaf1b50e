import * as CotDienService from '../../TongKe/CotDien/cotDien.service';
import * as DayChongSetService from '../../TongKe/DayChongSet/dayChongSet.service';
import * as DayCapQuangService from '../../TongKe/DayCapQuang/dayCapQuang.service';
import * as CachDienService from '../../TongKe/CachDien/cachDien.service';
import * as DayDanService from '../../TongKe/DayDan/dayDan.service';
import * as TiepDatService from '../../TongKe/TiepDat/tiepDat.service';
import * as GiaoCheoService from '../../TongKe/GiaoCheo/giaoCheo.service';
import * as KetQuaKiemTraService from '../../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.service';
import * as DonViService from '../../DonVi/donVi.service';
import { groupBy, removeDuplicateObject } from '../../../common/functionCommons';
import { CAP_DON_VI } from '../../../constant/constant';

const listHSXFail = ['không xác định', 'null', '', 'undefined', 'n/a', 'chưa xác định', 'khác'];

function sortCompany(a, b) {
  return ('' + a.type).localeCompare(b.type);
}

function sortHangSanXuat(a, b) {
  return a.so_luong - b.so_luong;
}

export function convertObjToArr(objData) {
  const arrayResult = [];
  for (const [key, listData] of Object.entries(objData)) {
    arrayResult.push({
      type: key, so_luong: listData.length,
    });
  }
  return arrayResult;
}

export function formatSoLuongByHangSanXuat(objData) {
  for (const [key, value] of Object.entries(objData)) {
    if (!objData['Khác hoặc chưa xác định']) {
      objData['Khác hoặc chưa xác định'] = [];
    }
    if (listHSXFail.includes(key.toLowerCase().trim())) {
      objData['Khác hoặc chưa xác định'] = [...objData['Khác hoặc chưa xác định'], ...value];
    }
  }
  Object.keys(objData).forEach(key => {
    if (listHSXFail.includes(key.toLowerCase().trim())) {
      delete objData[key];
    }
  });
  return objData;
}

// export function groupDataByTTD(listData) {
//   listData.forEach(item => {
//     if (item.don_vi_id.cap_don_vi === CAP_DON_VI.DOI_TRUYEN_TAI_DIEN) {
//       item.truyen_tai_dien = item.don_vi_id.don_vi_cha_id.ten_don_vi;
//     } else if (item.don_vi_id.cap_don_vi === CAP_DON_VI.TRUYEN_TAI_DIEN) {
//       item.truyen_tai_dien = item.don_vi_id.ten_don_vi;
//     }
//
//   });
//   return groupBy(listData, 'truyen_tai_dien');
// }

export function groupDataByDonVi(listData) {
  listData.forEach(element => {
    switch (element.don_vi_id?.cap_don_vi) {
      case CAP_DON_VI.DOI_TRUYEN_TAI_DIEN:
        element.cong_ty = element.don_vi_id?.don_vi_cha_id?.don_vi_cha_id?.ten_don_vi;
        break;
      case CAP_DON_VI.TRUYEN_TAI_DIEN:
        element.cong_ty = element.don_vi_id?.don_vi_cha_id?.ten_don_vi;
        break;
      case CAP_DON_VI.CONG_TY:
        element.cong_ty = element.don_vi_id?.ten_don_vi;
        break;
      default:
    }
  });
  return groupBy(listData, 'cong_ty');
}

export async function groupDataByCongTyTTD(criteria, Service) {
  const selectedDonVi = await DonViService.getById(criteria.don_vi);
  delete criteria.don_vi;

  let capDonVi;
  switch (selectedDonVi.cap_don_vi) {
    case CAP_DON_VI.TONG_CONG_TY:
      capDonVi = CAP_DON_VI.CONG_TY;
      break;
    case CAP_DON_VI.CONG_TY:
      capDonVi = CAP_DON_VI.TRUYEN_TAI_DIEN;
      break;
    default:
      capDonVi = CAP_DON_VI.DOI_TRUYEN_TAI_DIEN;
  }
  const allCongTy = await DonViService.getAll({
    cap_don_vi: capDonVi, is_deleted: false, _id: criteria.don_vi_id, don_vi_cha_id: { $exists: true },
  }).select('ten_don_vi');

  for (let i = 0; i < allCongTy.length; ++i) {
    const congTy = allCongTy[i];
    const donViInScopeIds = await DonViService.getDonViInScope(congTy._id);
    congTy.type = congTy.ten_don_vi;
    congTy.so_luong = await Service.count({ don_vi_id: { $in: donViInScopeIds }, is_deleted: false });
  }
  return allCongTy.sort(sortCompany);
}

export async function soLuongTheoHang(criteria, Service, thietBiId) {
  delete criteria.don_vi;
  const allThietBi = await Service.getAll(criteria).select('hang_san_xuat');

  const dataGroupByNSX = groupBy(allThietBi, 'hang_san_xuat');

  let thietBiTheoHang = convertObjToArr(formatSoLuongByHangSanXuat(dataGroupByNSX)).sort(sortHangSanXuat).reverse();

  const ketQuaKiemTra = await KetQuaKiemTraService.getAll({
    $and: [{ [thietBiId]: { $exists: true } }, { [thietBiId]: { $ne: null } }],
    is_deleted: false,
    don_vi_id: criteria.don_vi_id,
  }).populate([{ path: thietBiId }, { path: 'don_vi_id', populate: 'don_vi_cha_id' }]);

  const allThietBiHuHong = ketQuaKiemTra.map(item => item[thietBiId]);
  const huHongGroupByNSX = groupBy(removeDuplicateObject(allThietBiHuHong, '_id'), 'hang_san_xuat');
  const huHongBeforeFormat = formatSoLuongByHangSanXuat(huHongGroupByNSX);

  thietBiTheoHang.forEach(thietBi => {
    thietBi.so_luong_hu_hong = huHongBeforeFormat[thietBi.type]?.length || 0;
  });
  return thietBiTheoHang;
}

export async function soLuongTheoDonVi(criteria, Service, thietBiId) {
  let thietBiTheoDonVi = await groupDataByCongTyTTD(criteria, Service);
  const ketQuaKiemTra = await KetQuaKiemTraService.getAll({
    $and: [{ [thietBiId]: { $exists: true } }, { [thietBiId]: { $ne: null } }], is_deleted: false,
  }).populate([{
    path: thietBiId, populate: { path: 'don_vi_id', populate: { path: 'don_vi_cha_id', populate: 'don_vi_cha_id' } },
  }]);

  const allThietBiHuHong = ketQuaKiemTra.map(item => item[thietBiId]);
  const thietBiHuHongs = removeDuplicateObject(allThietBiHuHong, '_id');

  const dataThietBi = groupDataByDonVi(thietBiHuHongs);

  thietBiTheoDonVi.forEach(thietBi => {
    thietBi.so_luong_hu_hong = dataThietBi[thietBi.type]?.length || 0;
  });
  return thietBiTheoDonVi;
}

export async function getAllThietBi(criteria, functionThongKeSoLuong) {
  let thietBiObj = {};

  async function getSoLuongCotDien() {
    thietBiObj.cot_dien = await functionThongKeSoLuong(criteria, CotDienService, 'cot_dien_id');
  }

  async function getSoLuongCachDien() {
    thietBiObj.cach_dien = await functionThongKeSoLuong(criteria, CachDienService, 'cach_dien_id');
  }

  async function getSoLuongDayDan() {
    thietBiObj.day_dan = await functionThongKeSoLuong(criteria, DayDanService, 'day_dan_id');
  }

  async function getSoLuongDayCapQuang() {
    thietBiObj.cap_quang = await functionThongKeSoLuong(criteria, DayCapQuangService, 'day_cap_quang_id');
  }

  async function getSoLuongDayChongSet() {
    thietBiObj.chong_set = await functionThongKeSoLuong(criteria, DayChongSetService, 'day_chong_set_id');
  }

  async function getSoLuongTiepDat() {
    thietBiObj.tiep_dat = await functionThongKeSoLuong(criteria, TiepDatService, 'tiep_dat_id');
  }

  async function getSoLuongGiaoCheo() {
    thietBiObj.giao_cheo = await functionThongKeSoLuong(criteria, GiaoCheoService, 'giao_cheo_id');
  }

  const allPromise = [
    getSoLuongCotDien(),
    getSoLuongCachDien(),
    getSoLuongDayDan(),
    getSoLuongDayCapQuang(),
    getSoLuongDayChongSet(),
    getSoLuongTiepDat(),
    getSoLuongGiaoCheo(),
  ];
  await Promise.all(allPromise);

  let allThietBi = [];
  Object.values(thietBiObj).forEach(thietBi => {
    allThietBi = [...allThietBi, ...thietBi];
  });

  const mapThietBi = {};
  allThietBi.forEach(thietBi => {
    if (!mapThietBi[thietBi.type]) {
      mapThietBi[thietBi.type] = {
        type: thietBi.type, so_luong: thietBi.so_luong, so_luong_hu_hong: thietBi.so_luong_hu_hong,
      };
    } else {
      mapThietBi[thietBi.type].so_luong += thietBi.so_luong;
      mapThietBi[thietBi.type].so_luong_hu_hong += thietBi.so_luong_hu_hong;
    }
  });

  let dataThietBi = [];
  Object.values(mapThietBi).forEach(thietBi => {
    dataThietBi.push(thietBi);
  });
  return dataThietBi;
}

export async function tongHopSoLuongTheoHangSanXuat(criteria) {
  const dataThietBi = await getAllThietBi(criteria, soLuongTheoHang);
  return dataThietBi.sort(sortHangSanXuat).reverse();
}

export async function tongHopSoLuongTheoDonVi(criteria) {
  return (await getAllThietBi(criteria, soLuongTheoDonVi));
}
