import express from 'express';
import passport from 'passport';
import * as khiemKhuyetThietBiController from './khiemKhuyetThietBi.controller';
import { tonTaiRouter } from '../../QuanLyVanHanh/TonTai/tonTai.router';
import { loggerMiddleware } from '../../../logs/middleware';

export const khiemKhuyetThietBiRouter = express.Router();

khiemKhuyetThietBiRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
// khiemKhuyetThietBiRouter.post('*', authorizationMiddleware([CongTrinhXayDungPermission.CREATE]));
// khiemKhuyetThietBiRouter.put('*', authorizationMiddleware([CongTrinhXayDungPermission.UPDATE]));
// khiemKhuyetThietBiRouter.get('*', authorizationMiddleware([CongTrinhXayDungPermission.READ]));
// khiemKhuyetThietBiRouter.delete('*', authorizationMiddleware([CongTrinhXayDungPermission.DELETE]));
khiemKhuyetThietBiRouter
  .route('/')
  .get(khiemKhuyetThietBiController.getAll)
  .post(khiemKhuyetThietBiController.create);

khiemKhuyetThietBiRouter
  .route('/:id')
  .get(khiemKhuyetThietBiController.findOne)
  .put(khiemKhuyetThietBiController.update)
  .delete(khiemKhuyetThietBiController.remove);

khiemKhuyetThietBiRouter
  .route('/tontai/:id')
  .get(khiemKhuyetThietBiController.getOneByTonTaiId);
