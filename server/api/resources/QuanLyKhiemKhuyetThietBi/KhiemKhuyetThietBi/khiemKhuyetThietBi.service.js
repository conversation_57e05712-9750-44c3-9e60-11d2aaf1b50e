import * as ValidatorHelper from '../../../helpers/validatorHelper';
import Model from './khiemKhuyetThietBi.model';

export function getAll(query) {
  return Model.find(query).lean();
}

const Joi = require('joi');

const objSchema = Joi.object({});


export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function removeAll(query) {
  return Model.updateMany(query, { is_deleted: true });
}
