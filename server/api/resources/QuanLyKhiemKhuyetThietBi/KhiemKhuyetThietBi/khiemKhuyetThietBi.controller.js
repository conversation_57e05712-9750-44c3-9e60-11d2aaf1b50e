import Model from './khiemKhuyetThietBi.model';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from './khiemKhuyetThietBi.service';
import * as responseHelper from '../../../helpers/responseHelper';
import * as controllerHelper from '../../../helpers/controllerHelper';
import * as responseAction from '../../../helpers/responseHelper';

const populateOpts = [
  { path: 'don_vi_id', select: 'ten_don_vi don_vi_cha_id', populate: { path: 'don_vi_cha_id', select: 'ten_don_vi' } },
  {
    path: 'ton_tai_id', populate: {
      path: 'tieu_chi_id duong_day_id', populate: [
        { path: 'noi_dung_kiem_tra_id' },
        { path: 'tieu_chi_cha_id' },
      ],
    },
  },
];
const uniqueOpts = [];

export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    let data = await Model.findById(id).lean();
    if (!data) {
      return responseHelper.error(res, 404);
    }
    return res.json(data);
  } catch (err) {
    return res.status(500).send(err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getAll(req, res) {
  try {
    let query = queryHelper.extractQueryParam(req);
    let { criteria, options } = query;
    options.populate = populateOpts;
    const data = await Model.paginate(criteria, options);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getOneByTonTaiId(req, res) {
  try {
    const { id } = req.params;
    let data = await Model.find({ ton_tai_id: id }).lean();
    if (!data) {
      return responseAction.error(res, 400, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}


