import mongoose from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import { DON_VI, KET_QUA_KIEM_TRA, KHIEM_KHUYET_THIET_BI } from '../../../constant/dbCollections';

const { Schema } = mongoose;


const schema = new Schema({
  ton_tai_id: { type: Schema.Types.ObjectId, ref: KET_QUA_KIEM_TRA },
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  yeu_cau_tach_khoi_van_hanh: { type: String },
  gian_doan_cung_cap_dien: { type: String },
  cong_suat_truoc_khi_tach_thiet_bi: { type: String },
  dien_nang_khong_cung_cap_duoc: { type: String },
  kien_nghi_doi_ttd: { type: String },
  ket_qua_xu_ly_khiem_khuyet: { type: String },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export default mongoose.model(KHIEM_KHUYET_THIET_BI, schema, KHIEM_KHUYET_THIET_BI);
