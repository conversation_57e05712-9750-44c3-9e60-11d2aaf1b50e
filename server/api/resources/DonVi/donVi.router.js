import express from 'express';
import passport from 'passport';
import * as DonviController from './donVi.controller';
import { authorizationMiddleware } from '../RBAC/middleware';
import DonViPermission from '../RBAC/permissions/DonViPermission';
import { loggerMiddleware } from '../../logs/middleware';

export const donViRouter = express.Router();
donViRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
donViRouter.post('*', authorizationMiddleware([DonViPermission.CREATE]));
donViRouter.put('*', authorizationMiddleware([DonViPermission.UPDATE]));
donViRouter.delete('*', authorizationMiddleware([DonViPermission.DELETE]));
donViRouter
  .route('/')
  .get(DonviController.getAll)
  .post(authorizationMiddleware([DonViPermission.CREATE]), DonviController.create);

donViRouter
  .route('/donvingoai')
  .get(DonviController.getAllDonViNgoai);

donViRouter
  .route('/capcongty')
  .get(DonviController.getAllDonViCapCongTy);

donViRouter
  .route('/danh-sach-don-vi')
  .get(DonviController.getDanhSachDonVi);

donViRouter
  .route('/:id/all')
  .delete(DonviController.deleteAll)

donViRouter
  .route('/:id/khoi-phuc')
  .put(DonviController.khoiPhucDonViDaXoa);

donViRouter
  .route('/:id')
  .get(DonviController.findOne)
  .delete(DonviController.remove)
  .put(DonviController.update);
