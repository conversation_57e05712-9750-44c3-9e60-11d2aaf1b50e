import * as responseHelper from '../../helpers/responseHelper';
import queryHelper from '../../helpers/queryHelper';

import * as DonViService from './donVi.service';
import * as PhieuGiaoViecService from '../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';

import Model from './donVi.model';
import DON_VI from './donVi.model';
import USER from '../User/user.model';
import CONG_TRINH from '../TongKe/CongTrinh/congTrinh.model';
import DUONG_DAY from '../TongKe/DuongDay/duongDay.model';
import VI_TRI from '../TongKe/ViTri/viTri.model';
import PHIEU_GIAO_VIEC from '../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.model';

import { CAP_DON_VI, TYPE_DON_VI } from '../../constant/constant';
import CommonError from '../../error/CommonError';

import { TINH_TRANG_VAN_HANH } from '../DanhMuc/TinhTrangVanHanh';

const populateOpts = [];


export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id).populate('don_vi_cha_id');
    if (!data) {
      return responseHelper.error(res, 404, '');
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;

    const parentUnit = await Model.findOne({ don_vi_cha_id: id, is_deleted: false });
    if (parentUnit) {
      return responseHelper.error(res, { message: t('delete_unit_child_before_delete_unit_parent') }, 400);
    }
    const phieuGiaoViecNumber = await PhieuGiaoViecService.count({
      don_vi_giao_phieu_id: id,
      is_deleted: false,
    });
    if (phieuGiaoViecNumber > 0) {
      return responseHelper.error(res, { message: t('cant_delete_unit_assign') }, 400);
    }

    // const nguoiDungNumber = await UserService.count({
    //   don_vi_id: id,
    //   is_deleted: false,
    // })
    // if (nguoiDungNumber > 0) {
    //   return responseHelper.error(res, { message: t('cant_delete_unit_user') }, 400);
    // }

    const data = await Model.findOneAndUpdate({ _id: id, is_root: false }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    return responseHelper.success(res, data);

  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function deleteAll(req, res) {
  try {
    const { id } = req.params;
    const allAvailableDonVi = await Model.find({
      is_deleted: false,
      _id: await DonViService.getDonViQuery(req, id),
    }).lean();
    const ids = allAvailableDonVi.map(element => element._id);
    const result = {};
    // Xóa tất cả người dùng
    result.user = await USER.bulkWrite(
      ids.map((row) =>
        ({
          updateOne: {
            filter: { don_vi_id: row._id },
            update: { $set: { is_deleted: true } },
            upsert: false,
          },
        }),
      ),
    );

    // Xóa tất cả công trình
    result.cong_trinh = await CONG_TRINH.bulkWrite(
      ids.map((row) =>
        ({
          updateOne: {
            filter: { don_vi_id: row._id },
            update: { $set: { is_deleted: true } },
            upsert: false,
          },
        }),
      ),
    );

    // Xóa tất cả đường dây
    result.duong_day = await DUONG_DAY.bulkWrite(
      ids.map((row) =>
        ({
          updateOne: {
            filter: { don_vi_id: row._id },
            update: { $set: { is_deleted: true } },
            upsert: false,
          },
        }),
      ),
    );

    // Xóa tất cả vi trí
    result.vi_tri = await VI_TRI.bulkWrite(
      ids.map((row) =>
        ({
          updateOne: {
            filter: { don_vi_id: row._id },
            update: { $set: { is_deleted: true } },
            upsert: false,
          },
        }),
      ),
    );

    // Xóa tất cả phiếu giao việc
    result.phieu_giao_viec = await PHIEU_GIAO_VIEC.bulkWrite(
      ids.map((row) =>
        ({
          updateOne: {
            filter: { don_vi_cong_tac_id: row._id },
            update: { $set: { is_deleted: true } },
            upsert: false,
          },
        }),
      ),
    );

    // Xóa tất cả đơn vị
    result.don_vi = await DON_VI.bulkWrite(
      ids.map((row) =>
        ({
          updateOne: {
            filter: { _id: row._id },
            update: { $set: { is_deleted: true } },
            upsert: false,
          },
        }),
      ),
    );

    return responseHelper.success(res, result);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const value = req.body;
    // const { error, value } = DonViService.validate(req.body);
    // if (error) return responseHelper.error(res, error, 400);

    // check unique
    const isUniqueCode = await Model.findOne({
      ma_don_vi: value.ma_don_vi,
      _id: { $ne: value._id },
    }, { _id: 1 });
    if (isUniqueCode) {
      return responseHelper.error(res, { message: t('unit_code_already_exists') }, 400);
    }
    if (value.email) {
      const isUniqueEmail = await Model.findOne({
        email: value.email,
        _id: { $ne: value._id },
      }, { _id: 1 });
      if (isUniqueEmail) {
        return responseHelper.error(res, { message: t('email_already_exists') }, 400);
      }
    }
    // !check unique
    if (!req.user.is_system_admin) {
      delete value.loai_don_vi;
    }
    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true })
      .populate({ path: 'don_vi_cha_id', select: 'ten_don_vi' });
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    await DonViService.trimTenDonVi();
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function create(req, res) {

  try {
    const { t } = req;
    const { error, value } = DonViService.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    // check unique
    const isUnique = await Model.findOne({ ma_don_vi: value.ma_don_vi }, { _id: 1 });
    if (isUnique) {
      return responseHelper.error(res, { message: t('unit_code_already_exists') }, 400);
    }
    if (value.email) {
      const isUniqueEmail = await Model.findOne({ email: value.email }, { _id: 1 });
      if (isUniqueEmail) {
        return responseHelper.error(res, { message: t('email_already_exists') }, 400);
      }
    }

    value.loai_don_vi = TYPE_DON_VI.TRIAL;
    const data = await Model.create(value);
    let dataRtn = await data
      .populate({ path: 'don_vi_cha_id', select: 'ten_don_vi' }).execPopulate();
    return responseHelper.success(res, dataRtn);
  } catch (err) {
    console.error(err);
    return responseHelper.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    if (req.query.tree) {
      let allAvailableDonVi;
      if (req.query.show_all) {
        allAvailableDonVi = await Model.find({
          is_deleted: false,
          $or: [
            {
              $and: [
                { don_vi_cha_id: { $exists: true } },
                { cap_don_vi: { $ne: CAP_DON_VI.TONG_CONG_TY } },
              ],
            },
            {
              $and: [
                { don_vi_cha_id: { $exists: false } },
                { cap_don_vi: { $eq: CAP_DON_VI.TONG_CONG_TY } },
              ],
            },
          ],
          $and: [
            { tinh_trang_van_hanh: { $exists: true } },
            { tinh_trang_van_hanh: TINH_TRANG_VAN_HANH.VAN_HANH },
          ],
        }).lean();
      } else {
        allAvailableDonVi = await Model.find({
          is_deleted: false,
          $and: [
            { tinh_trang_van_hanh: { $exists: true } },
            { tinh_trang_van_hanh: TINH_TRANG_VAN_HANH.VAN_HANH },
          ],
          _id: await DonViService.getDonViQuery(req, null),
        }).lean();
      }

      allAvailableDonVi.forEach(donVi => donVi.don_vi_cha = donVi.don_vi_cha_id);
      await Model.populate(allAvailableDonVi, {
        path: 'don_vi_cha',
        select: 'ten_don_vi',
      });
      let congTyTreeForAdmin = DonViService.buildTree(allAvailableDonVi);
      return responseHelper.success(res, congTyTreeForAdmin);
    } else {
      const query = queryHelper.extractQueryParam(req, ['ma_don_vi', 'ten_don_vi']);
      const { criteria, options } = query;
      criteria._id = await DonViService.getDonViQuery(req, null);
      options.populate = [
        { path: 'don_vi_cha_id', select: 'ten_don_vi' },
      ];
      options.sort = { thu_tu: 1, ten_don_vi: 1 };
      const data = await Model.paginate(criteria, options);
      return responseHelper.success(res, data);
    }
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function getAllDonViNgoai(req, res) {
  try {
    const { don_vi_id } = req.user;
    const data = await Model.find({
      _id: {
        $ne: don_vi_id,
      },
      $and: [
        { tinh_trang_van_hanh: { $exists: true } },
        { tinh_trang_van_hanh: TINH_TRANG_VAN_HANH.VAN_HANH },
      ],
    }).lean();
    const treeData = DonViService.buildTree(data);
    return responseHelper.success(res, treeData);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function getAllDonViCapCongTy(req, res) {
  try {
    const data = await Model.find({
      cap_don_vi: CAP_DON_VI.CONG_TY,
      $and: [
        { tinh_trang_van_hanh: { $exists: true } },
        { tinh_trang_van_hanh: TINH_TRANG_VAN_HANH.VAN_HANH },
      ],
    }).lean();
    const treeData = DonViService.buildTree(data);
    return responseHelper.success(res, treeData);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function getDanhSachDonVi(req, res) {
  try {
    if (!req.user.is_system_admin) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    const query = queryHelper.extractQueryParam(req, ['ten_don_vi', 'ma_don_vi']);
    const { criteria, options } = query;
    if (req.query.hasOwnProperty('is_deleted')) {
      criteria.is_deleted = req.query.is_deleted;
    } else {
      delete criteria.is_deleted;
    }

    options.populate = populateOpts;
    options.select = '+is_deleted';

    const data = await DON_VI.paginate(criteria, options);

    if (!data) return responseHelper.error(res, CommonError.NOT_FOUND);

    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function khoiPhucDonViDaXoa(req, res) {
  try {
    if (!req.user.is_system_admin) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    const { id } = req.params;
    const currentData = await DON_VI.findById(id);
    if (!currentData) return responseHelper.error(res, CommonError.NOT_FOUND);


    const donViUpdated = await DON_VI.findByIdAndUpdate(id, { is_deleted: false }, { new: true });
    return responseHelper.success(res, donViUpdated);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}
