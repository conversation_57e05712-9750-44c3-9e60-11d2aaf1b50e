import mongoose, { Schema } from 'mongoose';
import { DON_VI } from '../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';
import { CAP_DON_VI, TYPE_DON_VI } from '../../constant/constant';
import { TINH_TRANG_VAN_HANH } from '../DanhMuc/TinhTrangVanHanh';

const schema = new Schema({
  ten_don_vi: { type: String, required: true, validate: /\S+/ },
  ma_don_vi: { type: String, required: true, validate: /\S+/, unique: true },
  ma_in: { type: String },
  duong_day_nong: { type: String, required: false },
  email: { type: String, required: false },
  fax: { type: String, required: false },
  dia_chi: { type: String, required: false },
  cap_don_vi: {
    type: String,
    enum: Object.values(CAP_DON_VI),
    required: true,
  },
  don_vi_cha_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: DON_VI,
  },
  thu_tu: { type: Number },
  loai_don_vi: {
    type: String,
    enum: Object.values(TYPE_DON_VI),
  },
  tinh_trang_van_hanh: {
    type: String,
    enum: Object.values(TINH_TRANG_VAN_HANH),
    default: TINH_TRANG_VAN_HANH.VAN_HANH,
  },
  is_root: { type: Boolean, default: false, select: false },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
// Optimized compound indexes for DON_VI queries
// Primary index for organizational hierarchy with soft delete
schema.index({ 
  cap_don_vi: 1, 
  is_deleted: 1, 
  ten_don_vi: 1 
}, { 
  background: true,
  name: 'idx_cap_deleted_ten'
});

// Index for hierarchical queries (parent-child relationships)
schema.index({ 
  don_vi_cha_id: 1, 
  cap_don_vi: 1, 
  is_deleted: 1 
}, { 
  background: true,
  name: 'idx_cha_cap_deleted'
});

// Index for name-based searches and populate operations
schema.index({ 
  ten_don_vi: 1, 
  is_deleted: 1, 
  created_at: -1 
}, { 
  background: true,
  name: 'idx_ten_deleted_time'
});

// Index for code-based searches
schema.index({ 
  ma_don_vi: 1, 
  is_deleted: 1 
}, { 
  background: true,
  name: 'idx_ma_deleted'
});

// Index for type-based filtering
schema.index({ 
  loai_don_vi: 1, 
  cap_don_vi: 1, 
  is_deleted: 1 
}, { 
  background: true,
  sparse: true,
  name: 'idx_loai_cap_deleted'
});

// Index for root organization queries
schema.index({ 
  is_root: 1, 
  is_deleted: 1 
}, { 
  background: true,
  sparse: true,
  name: 'idx_root_deleted'
});

// Index for ordering and display
schema.index({ 
  thu_tu: 1, 
  cap_don_vi: 1, 
  is_deleted: 1 
}, { 
  background: true,
  sparse: true,
  name: 'idx_thu_tu_cap_deleted'
});
schema.plugin(mongoosePaginate);
export default mongoose.model(DON_VI, schema, DON_VI);

