import * as ValidatorHelper from '../../helpers/validatorHelper';
import DON_VI from './donVi.model';
import { Types } from 'mongoose';
import { createDataTree } from '../../common/DataStructureHelper';
import { trim } from '../../common/functionCommons';
import { extractIds, extractKeys } from '../../utils/dataconverter';
import { TYPE_DON_VI } from '../../constant/constant';
import { createBadRequestError } from '../../helpers/errorHelper';
import { TINH_TRANG_VAN_HANH } from '../DanhMuc/TinhTrangVanHanh';

export function getAll(query, projection = {}) {
  return DON_VI.find({
    ...query,
    $and: [
      { tinh_trang_van_hanh: { $exists: true } },
      { tinh_trang_van_hanh: TINH_TRANG_VAN_HANH.VAN_HANH },
    ],
  }, projection).lean();
}

export function getOne(query, projection = {}) {
  return DON_VI.findOne(query, projection).lean();
}

export function getById(id, projection = {}) {
  return DON_VI.findById(id, projection).lean();
}

export async function getDonViQuery(req, querryDonViId = null, keepIdObject) {
  const availableDonViIds = await getDonViInScope(req.user?.don_vi_id);
  let donViQuery = [];
  if (querryDonViId) {
    if (availableDonViIds.includes(querryDonViId)) {
      donViQuery = await getDonViInScope(querryDonViId);
    } else {
      donViQuery = [Types.ObjectId()];
    }
  } else {
    donViQuery = availableDonViIds;
  }
  if (keepIdObject) {
    donViQuery = donViQuery.map(id => Types.ObjectId(id));
  }
  return { $in: donViQuery };
}

export async function getArrayDonViQuery(req, querryDonViId = null, keepIdObject) {
  const availableDonViIds = await getDonViInScope(req.user?.don_vi_id);
  let donViQuery = [];
  if (querryDonViId) {
    if (availableDonViIds.includes(querryDonViId)) {
      donViQuery = await getDonViInScope(querryDonViId);
    } else {
      donViQuery = [Types.ObjectId()];
    }
  } else {
    donViQuery = availableDonViIds;
  }
  if (keepIdObject) {
    donViQuery = donViQuery.map(id => Types.ObjectId(id));
  }
  return donViQuery;
}

export async function getAllDonViByParent(req, querryDonViId = null, keepIdObject) {
  const availableDonViIds = await getDonViInScope(req.user?.don_vi_id);
  let donViQuery = [];
  if (querryDonViId) {
    donViQuery = await getDonViInScope(querryDonViId);
  } else {
    donViQuery = availableDonViIds;
  }
  if (keepIdObject) {
    donViQuery = donViQuery.map(id => Types.ObjectId(id));
  }
  return { $in: donViQuery };
}

export async function getDonViQueryIncludeDeleted(req, querryDonViId = null, keepIdObject) {
  const availableDonViIds = await getDonViInScope(req.user?.don_vi_id, true);
  let donViQuery = [];
  if (querryDonViId) {
    if (availableDonViIds.includes(querryDonViId)) {
      donViQuery = await getDonViInScope(querryDonViId, true);
    } else {
      donViQuery = [Types.ObjectId()];
    }
  } else {
    donViQuery = availableDonViIds;
  }
  if (keepIdObject) {
    donViQuery = donViQuery.map(id => Types.ObjectId(id));
  }
  return { $in: donViQuery };
}

export function buildTree(allDonVi) {
  let congTyTreeForAdmin = [];
  congTyTreeForAdmin = createDataTree(allDonVi, '_id', 'don_vi_cha_id');
  return congTyTreeForAdmin;
}

export async function getDonViInScope(currentDonViId, includeDeletedUnit = false) {
  const currentDonVi = await DON_VI.findById(currentDonViId);
  if (!currentDonVi) return [];
  let parentIs = [currentDonVi._id.toString()];
  let donViSet = new Set([...parentIs]);
  while (parentIs.length) {
    if (includeDeletedUnit) {
      const children = await DON_VI.find({
        don_vi_cha_id: { $in: parentIs },
        $and: [
          { tinh_trang_van_hanh: { $exists: true } },
          { tinh_trang_van_hanh: TINH_TRANG_VAN_HANH.VAN_HANH },
        ],
      }).lean();
      parentIs = children.map(child => child._id.toString());
    } else {
      const children = await DON_VI.find({
        is_deleted: false, don_vi_cha_id: { $in: parentIs },
        $and: [
          { tinh_trang_van_hanh: { $exists: true } },
          { tinh_trang_van_hanh: TINH_TRANG_VAN_HANH.VAN_HANH },
        ],
      }).lean();
      parentIs = children.map(child => child._id.toString());
    }
    donViSet = new Set([...donViSet, ...parentIs]);
  }
  return [...donViSet.values()];
}

export async function getAllParentChildDonVi(currentDonViId) {
  const currentDonVi = await getById(currentDonViId, { _id: 1, don_vi_cha_id: 1 });
  if (!currentDonVi) return [];
  let parentId = [currentDonVi._id?.toString()];
  let childId = [currentDonVi.don_vi_cha_id?.toString()];
  let donViSet = new Set([...parentId, ...childId]);
  while (parentId.length) {
    const children = await getAll({ is_deleted: false, don_vi_cha_id: parentId }, { _id: 1 });
    parentId = extractIds(children);
    donViSet = new Set([...donViSet, ...parentId]);
  }
  while (childId.length) {
    const parent = await getAll(
      { _id: childId, is_deleted: false, don_vi_cha_id: { $exists: true } },
      { don_vi_cha_id: 1 },
    );
    childId = extractKeys(parent, 'don_vi_cha_id');
    donViSet = new Set([...donViSet, ...childId]);
  }
  return [...donViSet.values()].filter(donVi => !!donVi);
}

export async function trimTenDonVi() {
  const allDonVi = await DON_VI.find({ is_deleted: false });

  await DON_VI.bulkWrite(
    allDonVi.map((row) =>
      ({
        updateOne: {
          filter: { _id: row._id },
          update: { $set: { ten_don_vi: trim(row.ten_don_vi) } },
          upsert: false,
        },
      }),
    ),
  );
}

const Joi = require('joi');

const objSchema = Joi.object({
  ten_don_vi: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên đơn vị')),
  ma_don_vi: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã đơn vị')),
  don_vi_cha_id: Joi.string().messages(ValidatorHelper.messageDefine('Đơn vị cha')),
  duong_day_nong: Joi.string(),
  email: Joi.string(),
  fax: Joi.string(),
  dia_chi: Joi.string(),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function checkActivateDonVi(donViId) {
  const donVi = await DON_VI.findById(donViId);
  if (donVi) {
    if (!isActivated(donVi)) {
      throw createBadRequestError('Đơn vị chưa được kích hoạt để sử dụng chức năng tạo phiếu này. Vui lòng liên hệ quản trị viên.');
    }
  } else {
    throw createBadRequestError('Thiếu đơn vị giao phiếu');
  }
}

export function isActivated(donvi) {
  return donvi.loai_don_vi !== TYPE_DON_VI.TRIAL;
}
