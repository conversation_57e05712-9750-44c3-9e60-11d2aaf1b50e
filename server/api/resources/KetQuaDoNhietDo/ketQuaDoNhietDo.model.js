import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { KET_QUA_DO_NHIET_DO, KHOANG_COT, PHIEU_GIAO_VIEC, USER, VI_TRI } from '../../constant/dbCollections';

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  khoang_cot_id: { type: Schema.Types.ObjectId, ref: KHOANG_COT },
  nhiet_do_moi_truong: { type: Number },
  nguoi_do: [{ type: Schema.Types.ObjectId, ref: USER }],
  thoi_gian_do: { type: Date },
  ghi_chu: { type: String },
  version: { type: Number },
  is_deleted: { type: <PERSON>olean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized indexes for KET_QUA_DO_NHIET_DO queries
// Using background: true for better performance during index creation

// Index for khoang_cot_id queries with soft delete
schema.index({ 
  khoang_cot_id: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_khoang_cot_deleted'
});

// Index for vi_tri_id queries with soft delete
schema.index({ 
  vi_tri_id: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_vi_tri_deleted'
});

// Index for phieu_giao_viec_id queries
schema.index({ 
  phieu_giao_viec_id: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_phieu_giao_viec_deleted'
});

// Index for nguoi_do queries
schema.index({ 
  nguoi_do: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_nguoi_do_deleted'
});

// Index for thoi_gian_do queries
schema.index({ 
  thoi_gian_do: -1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_thoi_gian_do_deleted'
});

// Index for time-based queries
schema.index({ 
  created_at: -1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_created_deleted'
});

schema.plugin(mongoosePaginate);

export default mongoose.model(KET_QUA_DO_NHIET_DO, schema, KET_QUA_DO_NHIET_DO);

