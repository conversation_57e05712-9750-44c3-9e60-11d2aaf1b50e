import * as responseAction from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';
import * as DoNhietDoService from './chiTietDoNhietDo.service';
import * as Service from './chiTietDoNhietDo.service';
import Model from './chiTietDoNhietDo.model';
import * as controllerHelper from '../../../helpers/controllerHelper';


export const remove = controllerHelper.createRemoveFunction(Model);


export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id)

      .populate({ path: 'ket_qua_do_nhiet_do_id' })
      .lean();
    data.kqd = await DoNhietDoService.findOne({ ket_qua_do_nhiet_do_id: data._id, is_deleted: false });

    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true })
      .populate({ path: 'ket_qua_do_nhiet_do_id' });
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const data = await Model.create(value);
    let dataRtn = await data
      .populate({ path: 'ket_qua_do_nhiet_do_id' }).execPopulate();
    return responseAction.success(res, dataRtn);
    return responseAction.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ket_qua_do_nhiet_do_id']);
    const { criteria, options } = query;
    options.sort = { thu_tu: 1 };
    options.populate = [
      { path: 'ket_qua_do_nhiet_do_id' },
    ];
    const data = await Model.paginate(criteria, options);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}
