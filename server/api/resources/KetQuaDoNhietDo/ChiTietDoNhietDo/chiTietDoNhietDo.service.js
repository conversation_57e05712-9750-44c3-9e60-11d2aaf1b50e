import * as ValidatorHelper from '../../../helpers/validatorHelper';
import Model from './chiTietDoNhietDo.model';

export function getAll(query, projection = {}) {
  return Model.find(query, projection).lean();
}


const Joi = require('joi');

const objSchema = Joi.object({
  dong_tai: Joi.number().messages(ValidatorHelper.messageDefine('Dòng tải tại thời điểm đo')),
  nhiet_do_moi_truong: Joi.number().messages(ValidatorHelper.messageDefine('Nhiệt độ môi trường')),
  pha_a: Joi.string().messages(ValidatorHelper.messageDefine('Pha a')),
  pha_b: Joi.string().messages(ValidatorHelper.messageDefine('Pha b')),
  pha_c: Joi.string().messages(ValidatorHelper.messageDefine('Pha c')),

  mach: Joi.number().messages(ValidatorHelper.messageDefine('Đ<PERSON>i tượng đo')),
  day: Joi.number().messages(ValidatorHelper.messageDefine('<PERSON><PERSON>h giá')),
  doi_tuong_do: Joi.string().messages(ValidatorHelper.messageDefine('Đối tượng đo')),
  danh_gia: Joi.string().required().messages(ValidatorHelper.messageDefine('Đánh giá')),
  ket_qua_do_nhiet_do_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Kết quả đo nhiệt độ')),
  ghi_chu: Joi.string(),
  nguoi_do: Joi.string(),
  vi_tri_id: Joi.string().messages(ValidatorHelper.messageDefine('Vị trí')),
  ngay_gio_do: Joi.string().messages(ValidatorHelper.messageDefine('Ngày giờ đo')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
