import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CHI_TIET_DO_NHIET_DO, KET_QUA_DO_NHIET_DO, KHOANG_COT, USER, VI_TRI } from '../../../constant/dbCollections';

const schema = new Schema({
  ket_qua_do_nhiet_do_id: { type: Schema.Types.ObjectId, required: true, ref: KET_QUA_DO_NHIET_DO },
  mach: { type: Number },
  huong_do: { type: String },
  day_dan_phan_pha: { type: String },
  pha_a: { type: String },
  pha_b: { type: String },
  pha_c: { type: String },
  dong_tai: { type: Number },
  danh_gia: { type: String },
  day_pha: { type: String },
  xuat_tuyen: { type: String },
  nhiet_do_moi_truong: { type: Number },
  nhiet_do_day_dan: { type: String },
  order: { type: Number },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(CHI_TIET_DO_NHIET_DO, schema, CHI_TIET_DO_NHIET_DO);

