import express from 'express';
import passport from 'passport';
import * as doNhietDoController from './chiTietDoNhietDo.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import WorkPermission from '../../RBAC/permissions/WorkPermission';

export const chiTietDoNhietDoRouter = express.Router();
chiTietDoNhietDoRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
chiTietDoNhietDoRouter.post('*', authorizationMiddleware([WorkPermission.CREATE]));
chiTietDoNhietDoRouter.put('*', authorizationMiddleware([WorkPermission.UPDATE]));
chiTietDoNhietDoRouter.delete('*', authorizationMiddleware([WorkPermission.DELETE]));
chiTietDoNhietDoRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), doNhietDoController.getAll)
  .post(passport.authenticate('jwt', { session: false }), doNhietDoController.create);

chiTietDoNhietDoRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), doNhietDoController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), doNhietDoController.remove)
  .put(passport.authenticate('jwt', { session: false }), doNhietDoController.update);
