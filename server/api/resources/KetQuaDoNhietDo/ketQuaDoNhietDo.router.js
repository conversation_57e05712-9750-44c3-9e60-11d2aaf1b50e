import express from 'express';
import passport from 'passport';
import * as ketQuaDoNhietDoController from './ketQuaDoNhietDo.controller';
import { authorizationMiddleware } from '../RBAC/middleware';
import WorkPermission from '../RBAC/permissions/WorkPermission';
import { loggerMiddleware } from '../../logs/middleware';

export const ketQuaDoNhietDoRouter = express.Router();

ketQuaDoNhietDoRouter
  .route('/download')
  .get(ketQuaDoNhietDoController.downloadDoNhietDo);

ketQuaDoNhietDoRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
ketQuaDoNhietDoRouter.post('*', authorizationMiddleware([WorkPermission.CREATE]));
ketQuaDoNhietDoRouter.put('*', authorizationMiddleware([WorkPermission.UPDATE]));
ketQuaDoNhietDoRouter.delete('*', authorizationMiddleware([WorkPermission.DELETE]));
ketQuaDoNhietDoRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), ketQuaDoNhietDoController.getAll)
  .post(passport.authenticate('jwt', { session: false }), ketQuaDoNhietDoController.create);

ketQuaDoNhietDoRouter
  .route('/phieugiaoviec/:id')
  .get(ketQuaDoNhietDoController.findByPhieuGiaoViecId);

ketQuaDoNhietDoRouter
  .route('/duongday')
  .get(ketQuaDoNhietDoController.getAllKetQuaDoByDuongDay);

ketQuaDoNhietDoRouter
  .route('/:id')
  .get(ketQuaDoNhietDoController.findOne)
  .delete(ketQuaDoNhietDoController.remove)
  .put(ketQuaDoNhietDoController.update);
