import * as responseHelper from '../../helpers/responseHelper';
import CommonError from '../../error/CommonError';
import queryHelper from '../../helpers/queryHelper';
import * as Service from './ketQuaDoNhietDo.service';
import * as ChiTietDoNhietDoService from './ChiTietDoNhietDo/chiTietDoNhietDo.service';
import Model from './ketQuaDoNhietDo.model';
import ChiTietDoNhietDoModel from './ChiTietDoNhietDo/chiTietDoNhietDo.model';
import { generateDocument } from '../Report/GenerateFile/generate.controller';
import * as LichSuDoNhietDoService from './LichSuDoNhietDo/lichSuDoNhietDo.service';
import { getFilePath } from '../../utils/fileUtils';
import { TEMPLATES_DIRS } from '../../constant/constant';

const populateOpts = [
  { path: 'vi_tri_id' },
  { path: 'khoang_cot_id' },
];

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id)
      .populate({ path: 'duong_day_id', select: 'ten_duong_day' })
      .populate({ path: 'may_do_id', select: 'ten_may_do' })
      .lean();
    if (!data) {
      return responseHelper.error(res, 404, '');
    }
    data.chi_tiet = await ChiTietDoNhietDoService.getAll({ ket_qua_do_nhiet_do_id: id, is_deleted: false });
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseHelper.error(res, 404, '');
    }
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true })
      .populate(populateOpts)
      .lean();

    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    if (!Array.isArray(value.chi_tiet)) {
      return responseHelper.error(res, { message: t('have_unexpected_error') });
    }

    const checkNull = value.chi_tiet.filter(chiTiet => !chiTiet || !chiTiet?._id);
    if (checkNull.length) {
      return responseHelper.error(res, { message: t('have_unexpected_error') });
    }

    const chiTietResponse = [...value.chi_tiet];
    if (Array.isArray(value.chi_tiet)) {
      for (let i = 0; i < value.chi_tiet.length; i++) {
        const chiTietItem = value.chi_tiet[i];
        chiTietResponse[i] = await ChiTietDoNhietDoModel.findOneAndUpdate({ _id: chiTietItem._id }, chiTietItem, { new: true }).lean();
      }
    }

    data.chi_tiet = chiTietResponse;
    saveHistory(req, data);
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const { t } = req;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    if (!Array.isArray(value.chi_tiet)) {
      return responseHelper.error(res, { message: t('have_unexpected_error') });
    }
    const checkNull = value.chi_tiet.filter(chiTiet => !chiTiet);
    if (checkNull.length) {
      return responseHelper.error(res, { message: t('have_unexpected_error') });
    }
    value.version = 2;
    let data = await Model.create(value);
    if (data) {
      data = await Model.populate(data, 'vi_tri_id');
      data = await Model.populate(data, 'khoang_cot_id');
    }
    data = JSON.parse(JSON.stringify(data));

    if (Array.isArray(value.chi_tiet)) {
      let doNhietDoResponse = [];
      for (let i = 0; i < value.chi_tiet.length; i++) {
        const chiTietItem = value.chi_tiet[i];
        if (chiTietItem) {
          chiTietItem.ket_qua_do_nhiet_do_id = data._id;
          let doNhietDoCreated = await ChiTietDoNhietDoModel.create(chiTietItem);
          if (doNhietDoCreated) {
            doNhietDoResponse = [...doNhietDoResponse, JSON.parse(JSON.stringify(doNhietDoCreated))];
          }
        }
      }
      data.chi_tiet = doNhietDoResponse;
    }

    saveHistory(req, data);
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseHelper.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    options.sort = { thu_tu: 1 };
    options.populate = populateOpts;
    const data = await Model.paginate(criteria, options);
    responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}


export async function findByPhieuGiaoViecId(req, res) {
  try {
    const { id } = req.params;
    const ketQuaDo = await Model.find({ phieu_giao_viec_id: id, is_deleted: false })
      .populate(populateOpts)
      .lean();

    const version = ketQuaDo?.[0]?.version || 1;
    let sortOpts = {};
    switch (version) {
      case 1:
        sortOpts = { mach: 1, huong_do: -1 };
        break;
      case 2:
        sortOpts = { order: 1 };
        break;
      default:
        break;
    }

    const ketQuaDoId = ketQuaDo.map(ketQua => ketQua._id);
    const chiTietData = await ChiTietDoNhietDoService
      .getAll({ ket_qua_do_nhiet_do_id: ketQuaDoId, is_deleted: false })
      .sort(sortOpts);

    for (let i = 0; i < ketQuaDo.length; i++) {
      ketQuaDo[i].chi_tiet = chiTietData.filter(chiTiet => chiTiet.ket_qua_do_nhiet_do_id.toString() === ketQuaDo[i]._id.toString());
    }

    responseHelper.success(res, ketQuaDo);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function getAllKetQuaDoByDuongDay(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const data = await Service.bangTongHopKetQuaDoNhietDo(req, criteria);
    responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function downloadDoNhietDo(req, res) {
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;

  const dataDoNhietDo = await Service.dataDownloadPhieuDoNhietDo(req, criteria);
  const templateFilePath = getFilePath('phieu_do_nhiet_do_tiep_xuc.xlsx', TEMPLATES_DIRS.BIEU_MAU);
  const outputFileName = 'Phiếu đo nhiệt độ tiếp xúc.xlsx';
  generateDocument(res, dataDoNhietDo, templateFilePath, outputFileName);
}

function saveHistory(req, data) {
  LichSuDoNhietDoService.create({ ket_qua_do_nhiet_do_id: data._id, payload: data, user_id: req.user._id });
}
