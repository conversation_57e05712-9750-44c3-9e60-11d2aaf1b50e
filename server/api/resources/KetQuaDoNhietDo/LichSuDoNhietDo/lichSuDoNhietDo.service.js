import * as ValidatorHelper from '../../../helpers/validatorHelper';
import LICH_SU_DO_NHIET_DO from './lichSuDoNhietDo.model';

const Joi = require('joi');

const objSchema = Joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function getAll(query, projection = {}) {
  return LICH_SU_DO_NHIET_DO.find(query, projection).lean();
}

export async function updateAll(dataUpdate) {
  for (const row of dataUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await LICH_SU_DO_NHIET_DO.findByIdAndUpdate(value._id, value);
  }
}

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return LICH_SU_DO_NHIET_DO.create(value);
}
