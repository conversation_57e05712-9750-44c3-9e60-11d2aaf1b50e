import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { KET_QUA_DO_NHIET_DO, LICH_SU_DO_NHIET_DO, USER } from '../../../constant/dbCollections';

const schema = new Schema({
  ket_qua_do_nhiet_do_id: { type: Schema.Types.ObjectId, ref: KET_QUA_DO_NHIET_DO },
  user_id: { type: Schema.Types.ObjectId, ref: USER },
  payload: Object,
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(LICH_SU_DO_NHIET_DO, schema, LICH_SU_DO_NHIET_DO);

