import Model from './lichSuDoNhietDo.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import * as Service from '../../DanhMuc/BienPhapAnToan/bienPhapAnToan.service';
import queryHelper from '../../../helpers/queryHelper';
import * as responseHelper from '../../../helpers/responseHelper';

export const findOne = controllerHelper.createFindOneFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service);
export const create = controllerHelper.createCreateFunction(Model, Service);

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const data = await Model.find(criteria)
      .populate({ path: 'user_id', select: 'full_name' });
    responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}
