import * as ValidatorHelper from '../../helpers/validatorHelper';

import KET_QUA_DO_NHIET_DO from './ketQuaDoNhietDo.model';
import { LOAI_CONG_VIEC } from '../DanhMuc/LoaiCongViec';
import { TRANG_THAI_PHIEU } from '../DanhMuc/TrangThaiCongViec';

import * as PhieuGiaoViecService from '../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';
import * as ChiTietDoNhietDoService from './ChiTietDoNhietDo/chiTietDoNhietDo.service';
import * as ReportService from '../Report/report.service';
import * as VanHanhService from '../TongKe/VanHanh/vanHanh.service';
import * as DuongDayService from '../TongKe/DuongDay/duongDay.service';
import * as KhoangCotService from '../TongKe/KhoangCot/khoangCot.service';

import { KET_LUAN } from '../../constant/constant';
import { addIndexToListData, removeDuplicateObject, groupBy } from '../../common/functionCommons';
import { extractIds } from '../../utils/dataconverter';
import { formatTimeDate } from '../../common/formatUTCDateToLocalDate';

export function getAll(query, projection = {}) {
  return KET_QUA_DO_NHIET_DO.find(query, projection).lean();
}

export function getOne(query) {
  return KET_QUA_DO_NHIET_DO.findOne(query).lean();
}

export async function updateAll(dataUpdate) {
  for (const row of dataUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await KET_QUA_DO_NHIET_DO.findByIdAndUpdate(value._id, value);
  }
}

const Joi = require('joi');

const objSchema = Joi.object({
  so_day: Joi.number().required().messages(ValidatorHelper.messageDefine('Số dây  ')),
  loai_mach: Joi.number().required().messages(ValidatorHelper.messageDefine('Loại mạch')),
  ghi_chu: Joi.string(),
  duong_day_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Vị trí')),
  may_do_id: Joi.string(),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function templateDataDoNhietDo(vitriDoNhietDo, khoangCotDoNhietDo) {
  let result = [];
  result.push({
    stt: 'I',
    title: 'Kết quả đo nhiệt độ khóa néo, đầu cốt lèo',
    vi_tri: vitriDoNhietDo,
  }, {
    stt: 'II',
    title: 'Kết quả đo nhiệt độ ống nối',
    khoang_cot: khoangCotDoNhietDo,
  });
  return result;
}

export async function getAllIdsPhieuGiaoViec(criteria) {
  criteria.loai_cong_viec = LOAI_CONG_VIEC.DO_NHIET_DO_TIEP_XUC.code;
  criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code;
  criteria.duong_day_ids = criteria.duong_day_id;
  delete criteria.duong_day_id;

  const allPhieuGiaoViec = await PhieuGiaoViecService.getAllPopulate(criteria); //Get all phiếu giao việc
  return extractIds(allPhieuGiaoViec);
}

export async function getKetQuaDoNhietDo(allDoNhietDo) {

  const allDoNhietDoIds = extractIds(allDoNhietDo);

  const allChiTietDoNhietDo = await ChiTietDoNhietDoService.getAll({
    ket_qua_do_nhiet_do_id: { $in: allDoNhietDoIds },
    is_deleted: false,
  });

  allChiTietDoNhietDo.forEach(item => {
    item.danh_gia = KET_LUAN[item.danh_gia]?.label;
  });

  const groupKetQuaDoByPhieuId = groupBy(allChiTietDoNhietDo, 'ket_qua_do_nhiet_do_id');
  allDoNhietDo.forEach(doNhietDo => {
    doNhietDo.chi_tiet = groupKetQuaDoByPhieuId[doNhietDo._id];
    doNhietDo.vi_tri = doNhietDo?.vi_tri_id?._id;
    doNhietDo.khoang_cot = doNhietDo?.khoang_cot_id?._id;
  });
  // Vị trí công việc, Khoảng cột công việc
  let viTriCongViec = [], khoangCotCongViec = [], viTriIds = [];

  allDoNhietDo.forEach(item => {
    if (item.vi_tri_id) {
      viTriCongViec = [...viTriCongViec, item.vi_tri_id];
      viTriIds = [...viTriIds, item.vi_tri_id._id];
    } else {
      khoangCotCongViec = [...khoangCotCongViec, item.khoang_cot_id];
      viTriIds = [...viTriIds, item.khoang_cot_id?.vi_tri_id];
    }
  });

  const allVanHanh = await VanHanhService.getAll({
    vi_tri_id: { $in: viTriIds },
    is_deleted: false,
  }).populate('duong_day_id');
  const vanHanhGroupByViTri = groupBy(allVanHanh, 'vi_tri_id');

  viTriCongViec.forEach(viTri => {
    viTri.van_hanh = vanHanhGroupByViTri[viTri._id];
  });

  khoangCotCongViec.forEach(khoangCot => {
    khoangCot.van_hanh = vanHanhGroupByViTri[khoangCot?.vi_tri_id];
  });
  let result = {};
  result.vi_tri_cong_viec = viTriCongViec;
  result.khoang_cot_cong_viec = khoangCotCongViec;
  result.do_nhiet_do = allDoNhietDo;

  return result;
}

export async function bangTongHopKetQuaDoNhietDo(req, criteria) {

  const allPhieuGiaoViecIds = await getAllIdsPhieuGiaoViec(criteria);
  const allViTriByDuongDayIds = (await DuongDayService.getViTriByDuongDay(req, criteria.duong_day_ids)).vi_tri_ids;
  const allKhoangCot = await KhoangCotService.getAll({ vi_tri_id: { $in: allViTriByDuongDayIds } });
  const allKhoangCotIds = extractIds(allKhoangCot);

  const allDoNhietDo = await getAll({
    phieu_giao_viec_id: { $in: allPhieuGiaoViecIds },
    $or: [
      { vi_tri_id: { $in: allViTriByDuongDayIds } },
      { khoang_cot_id: { $in: allKhoangCotIds } },
    ],
    is_deleted: false,
  }).populate([{ path: 'vi_tri_id' }, { path: 'khoang_cot_id', populate: 'vi_tri_ket_thuc_id vi_tri_bat_dau_id' }]);
  return getKetQuaDoNhietDo(allDoNhietDo);
}

export async function dataDownloadPhieuDoNhietDo(req, criteria) {
  function sortViTri(a, b) {
    return a.vi_tri_id?.thu_tu - b.vi_tri_id?.thu_tu;
  }

  function sortKhoangCot(a, b) {
    return a.khoang_cot_id?.vi_tri_ket_thuc_id?.thu_tu - b.khoang_cot_id?.vi_tri_ket_thuc_id?.thu_tu;
  }

  const allPhieuGiaoViecIds = await getAllIdsPhieuGiaoViec(criteria);

  let dataPhieuGiaoViec = await ReportService.getInfoPhieuGiaoViec({ id: allPhieuGiaoViecIds[0] });

  function convertDataPhieuGiaoViec(phieuGiaoViec) {
    return {
      duong_day: phieuGiaoViec.duong_day_ids,
      don_vi_giao_phieu_id: phieuGiaoViec.don_vi_giao_phieu_id,
    };
  }

  addIndexToListData(dataPhieuGiaoViec.duong_day_ids);
  let result = convertDataPhieuGiaoViec(dataPhieuGiaoViec);

  const allViTriByDuongDayIds = (await DuongDayService.getViTriByDuongDay(req, criteria.duong_day_ids)).vi_tri_ids;
  const allKhoangCot = await KhoangCotService.getAll({ vi_tri_id: { $in: allViTriByDuongDayIds } });
  const allKhoangCotIds = extractIds(allKhoangCot);

  const allDoNhietDo = await getAll({
    phieu_giao_viec_id: { $in: allPhieuGiaoViecIds },
    $or: [
      { vi_tri_id: { $in: allViTriByDuongDayIds } },
      { khoang_cot_id: { $in: allKhoangCotIds } },
    ],
    is_deleted: false,
  }).populate([{ path: 'vi_tri_id' }, { path: 'khoang_cot_id', populate: 'vi_tri_ket_thuc_id vi_tri_bat_dau_id' }])
    .populate({ path: 'nguoi_do', select: 'full_name' });

  const data = await getKetQuaDoNhietDo(allDoNhietDo);

  let dataDoNhietDo = data.do_nhiet_do;

  let viTriDoNhietDo = [], khoangCotDoNhietDo = [];
  dataDoNhietDo.forEach(doNhietDo => {
    if (doNhietDo.vi_tri) {
      viTriDoNhietDo = [...viTriDoNhietDo, doNhietDo];
    } else {
      khoangCotDoNhietDo = [...khoangCotDoNhietDo, doNhietDo];
    }
  });
  viTriDoNhietDo.sort(sortViTri);
  khoangCotDoNhietDo.sort(sortKhoangCot);

  let viTriDoNhietDoCheckDup = removeDuplicateObject(viTriDoNhietDo, 'vi_tri');
  let khoangCotDoNhietDoCheckDup = removeDuplicateObject(khoangCotDoNhietDo, 'khoang_cot');


  function convertDataToRows(doNhietDo) {
    let nguoiDo = undefined;
    doNhietDo?.nguoi_do.forEach(user => {
      if (!nguoiDo) {
        nguoiDo = `- ${user.full_name}`;
      } else {
        nguoiDo = [nguoiDo, user.full_name].join('\n- ');
      }
    });
    return {
      nguoi_do: nguoiDo,
      chi_tiet: doNhietDo.chi_tiet,
      ghi_chu: doNhietDo.ghi_chu,
      nhiet_do_moi_truong: doNhietDo.nhiet_do_moi_truong,
      thoi_gian_do: doNhietDo.thoi_gian_do ? formatTimeDate(doNhietDo.thoi_gian_do) : '',
      vi_tri_id: doNhietDo?.vi_tri_id,
      khoang_cot_id: doNhietDo?.khoang_cot_id,
    };
  }


  result.ket_qua = templateDataDoNhietDo(viTriDoNhietDoCheckDup.map(convertDataToRows), khoangCotDoNhietDoCheckDup.map(convertDataToRows));
  return result;
}
