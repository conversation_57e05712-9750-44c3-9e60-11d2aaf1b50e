import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DANHMUC_DRONE, DON_VI, DRONE } from '../../../constant/dbCollections';

const schema = new Schema({
  danh_muc_drone_id: { type: Schema.Types.ObjectId, ref: DANHMUC_DRONE },
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  ma_drone: { type: String, required: true },
  serial: { type: String, required: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized indexes for DRONE queries
// Using background: true for better performance during index creation

// Index for ma_drone queries with soft delete
schema.index({ 
  ma_drone: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_ma_drone_deleted'
});

// Index for serial queries with soft delete
schema.index({ 
  serial: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_serial_deleted'
});

// Index for danh_muc_drone_id queries
schema.index({ 
  danh_muc_drone_id: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_danh_muc_drone_deleted'
});

// Index for don_vi_id queries
schema.index({ 
  don_vi_id: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_don_vi_deleted'
});

// Index for time-based queries
schema.index({ 
  created_at: -1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_created_deleted'
});

schema.plugin(mongoosePaginate);

export default mongoose.model(DRONE, schema, DRONE);
