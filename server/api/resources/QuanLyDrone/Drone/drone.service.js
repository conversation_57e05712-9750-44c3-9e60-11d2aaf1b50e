import * as ValidatorHelper from '../../../helpers/validatorHelper';
import createBaseService from '../../../base/baseService';

import DroneModel from './drone.model';

const Joi = require('joi');

const objSchema = Joi.object({
  danh_muc_drone_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Danh mục drone')),
  don_vi_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Đơn vị')),
  ma_drone: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã drone')),
  serial: Joi.string().required().messages(ValidatorHelper.messageDefine('Serial')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}


const baseService = createBaseService(DroneModel);

export const createMulti = baseService.createMulti;
export const getAll = baseService.getAll;
export const getOne = baseService.getOne;
export const remove = baseService.remove;
export const distinctId = baseService.distinctId;
export const updateByQuery = baseService.updateByQuery;
export const updateByRows = baseService.updateByRows;
export const deleteAll = baseService.deleteAll;
export const updateAll = baseService.updateAll;
