import * as ValidatorHelper from '../../../../helpers/validatorHelper';
import PHU_KIEN_DRONE from './phuKien.model';

const Joi = require('joi');

const objSchema = Joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return PHU_KIEN_DRONE.create(value);
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await PHU_KIEN_DRONE.findByIdAndUpdate(value._id, value);
  }
}

export function getAll(query) {
  return PHU_KIEN_DRONE.find(query).lean();
}

export async function removeAll(query) {
  return PHU_KIEN_DRONE.updateMany(query, { is_deleted: true });
}
