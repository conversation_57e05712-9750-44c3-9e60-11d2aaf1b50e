import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DRONE, PHU_KIEN_DRONE } from '../../../../constant/dbCollections';

const schema = new Schema({
  drone_id: { type: Schema.Types.ObjectId, ref: DRONE, required: true },
  ten_phu_kien: { type: String, required: true },
  so_luong: { type: Number, validate: /\S+/ },
  mo_ta: { type: String },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized indexes for PHU_KIEN_DRONE queries
// Using background: true for better performance during index creation

// Index for drone_id queries with soft delete
schema.index({ 
  drone_id: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_drone_deleted'
});

// Index for ten_phu_kien queries with soft delete
schema.index({ 
  ten_phu_kien: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_ten_phu_kien_deleted'
});

// Index for time-based queries
schema.index({ 
  created_at: -1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_created_deleted'
});

schema.plugin(mongoosePaginate);

export default mongoose.model(PHU_KIEN_DRONE, schema, PHU_KIEN_DRONE);

