import express from 'express';
import passport from 'passport';
import * as Controller from './phuKien.controller';

export const phu<PERSON>ienRouter = express.Router();
phuKienRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), Controller.getAll)
  .post(passport.authenticate('jwt', { session: false }), Controller.create);

phuKienRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), Controller.findOne)
  .delete(passport.authenticate('jwt', { session: false }), Controller.remove)
  .put(passport.authenticate('jwt', { session: false }), Controller.update);
