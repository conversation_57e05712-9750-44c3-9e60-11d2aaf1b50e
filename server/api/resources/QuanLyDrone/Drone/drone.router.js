import express from 'express';
import passport from 'passport';
import * as droneController from './drone.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import Permission from '../../RBAC/permissions/QuanLyThietBiPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const droneRouter = express.Router();
droneRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
droneRouter.post('*', authorizationMiddleware([Permission.CREATE]));
droneRouter.put('*', authorizationMiddleware([Permission.UPDATE]));
droneRouter.delete('*', authorizationMiddleware([Permission.DELETE]));

droneRouter
  .route('/')
  .get(droneController.getAll)
  .post(droneController.create);

droneRouter
  .route('/:id')
  .get(droneController.findOne)
  .delete(droneController.remove)
  .put(droneController.update);


