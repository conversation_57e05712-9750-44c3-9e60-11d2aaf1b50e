import * as responseAction from '../../../helpers/responseHelper';
import * as Service from './drone.service';
import * as PhuKienService from './PhuKien/phuKien.service';
import Model from './drone.model';
import PhuKienModel from './PhuKien/phuKien.model';
import * as controllerHelper from '../../../helpers/controllerHelper';

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id).lean();
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    data.phu_kien = await PhuKienService.getAll({ drone_id: id, is_deleted: false });
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true }).lean();
    if (!data) {
      return responseAction.error(res, 404, '');
    } else {
      await PhuKienService.removeAll({ drone_id: data._id });
      data.phu_kien = await PhuKienService.getAll({ drone_id: data._id });
    }
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const isUniqueCode = await Model.findOne({
      ma_drone: value.ma_drone,
      is_deleted: false,
      _id: { $ne: value._id },
    }, { _id: 1 });
    if (isUniqueCode) {
      return responseAction.error(res, { message: t('drone_code_already_exists') }, 400);
    }
    const isUniqueSerial = await Model.findOne({
      serial: value.serial,
      is_deleted: false,
      _id: { $ne: value._id },
    }, { _id: 1 });
    if (isUniqueSerial) {
      return responseAction.error(res, { message: t('serial_already_exists') }, 400);
    }
    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true }).lean();
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    let phukienResponse = [];
    if (Array.isArray(value.phu_kien)) {
      for (let i = 0; i < value.phu_kien.length; i++) {
        const phu_kien = value.phu_kien[i];
        if (phu_kien._id) {
          if (phu_kien.delete) {
            await PhuKienModel.findOneAndUpdate({ _id: phu_kien._id }, { is_deleted: true });
          } else {
            const dataUpdated = await PhuKienModel.findOneAndUpdate({ _id: phu_kien._id }, phu_kien, { new: true }).lean();
            if (dataUpdated) {
              phukienResponse = [...phukienResponse, JSON.parse(JSON.stringify(dataUpdated))];
            }
          }
        } else {
          // tao moi khi khong co _id
          phu_kien.drone_id = id;
          const dataCreated = await PhuKienModel.create(phu_kien);
          if (dataCreated) {
            phukienResponse = [...phukienResponse, JSON.parse(JSON.stringify(dataCreated))];
          }
        }
      }
    }
    data.phu_kien = phukienResponse;
    return responseAction.success(res, data);
  } catch (err) {
    console.log('err', err);
    responseAction.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const { t } = req;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const isUniqueCode = await Model.findOne({ ma_drone: value.ma_drone, is_deleted: false }, { _id: 1 });
    if (isUniqueCode) {
      return responseAction.error(res, { message: t('drone_code_already_exists') }, 400);
    }
    const isUniqueSerial = await Model.findOne({ serial: value.serial, is_deleted: false }, { _id: 1 });
    if (isUniqueSerial) {
      return responseAction.error(res, { message: t('serial_already_exists') }, 400);
    }
    const data = await Model.create(value);

    if (Array.isArray(value.phu_kien)) {
      let phukienResponse = [];
      for (let i = 0; i < value.phu_kien.length; i++) {
        const phu_kien = value.phu_kien[i];
        phu_kien.drone_id = data._id;
        const phukienCreated = await PhuKienModel.create(phu_kien);
        if (phukienCreated) {
          phukienResponse = [...phukienResponse, JSON.parse(JSON.stringify(phukienCreated))];
        }
      }
      data.phu_kien = phukienResponse;
    }

    return responseAction.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

const searchLike = ['ma_drone', 'serial'];
const populateOpts = [{ path: 'don_vi_id' }, { path: 'danh_muc_drone_id' }];

export const getAll = controllerHelper.createGetAllFunction(Model, searchLike, populateOpts, [], 'don_vi_id');
