import * as ValidatorHelper from '../../../helpers/validatorHelper';
import DANHMUC_DRONE from './danhMucDrone.model';

export function getAll(query) {
  return DANHMUC_DRONE.find(query).lean();
}

const Joi = require('joi');

const objSchema = Joi.object({
  name: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên drone')),
  hang_san_xuat: Joi.string().messages(ValidatorHelper.messageDefine('Hãng sản xuất')),
  thoi_gian_bay_toi_da: Joi.string().messages(ValidatorHelper.messageDefine('Thời gian bay tối đa')),
  ho_tro_rtk: Joi.bool().messages(ValidatorHelper.messageDefine('Hỗ trợ RTK')),
  do_phan_giai_camera: Joi.string().messages(ValidatorHelper.messageDefine('<PERSON><PERSON> phân giải camera')),
  ho_tro_bay_tu_dong: Joi.bool().messages(ValidatorHelper.messageDefine('Hỗ trợ bay tự đôngj')),
  chong_nhieu: Joi.string().messages(ValidatorHelper.messageDefine('Chống nhiễu')),
  khoang_cach_an_toan: Joi.string().messages(ValidatorHelper.messageDefine('Khoảng cách an toàn')),
  ho_tro_chong_va_cham: Joi.bool().messages(ValidatorHelper.messageDefine('Hỗ trợ chống va chạm')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
