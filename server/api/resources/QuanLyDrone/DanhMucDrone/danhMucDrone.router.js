import express from 'express';
import passport from 'passport';
import * as danhMucDroneController from './danhMucDrone.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import Permission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const danhMucDroneRouter = express.Router();
danhMucDroneRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
danhMucDroneRouter.post('*', authorizationMiddleware([Permission.CREATE]));
danhMucDroneRouter.put('*', authorizationMiddleware([Permission.UPDATE]));
danhMucDroneRouter.delete('*', authorizationMiddleware([Permission.DELETE]));
danhMucDroneRouter
  .route('/')
  .get(danhMucDroneController.getAll)
  .post(danhMucDroneController.create);

danhMucDroneRouter
  .route('/:id')
  .get(danhMucDroneController.findOne)
  .delete(danhMucDroneController.remove)
  .put(danhMucDroneController.update);
