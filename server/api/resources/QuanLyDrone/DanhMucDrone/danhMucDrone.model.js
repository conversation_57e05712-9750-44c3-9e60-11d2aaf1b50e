import mongoose, { Schema } from 'mongoose';
import { DANHMUC_DRONE } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  name: { type: String, required: true, validate: /\S+/ },
  hang_san_xuat: { type: String },
  thoi_gian_bay_toi_da: { type: String },
  ho_tro_rtk: { type: Boolean, default: false },
  do_phan_giai_camera: { type: String },
  ho_tro_bay_tu_dong: { type: Boolean, default: false },
  chong_nhieu: { type: Boolean, default: false },
  khoang_cach_an_toan: { type: String },
  ho_tro_chong_va_cham: { type: Boolean, default: false },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized indexes for DANHMUC_DRONE queries
// Using background: true for better performance during index creation

// Index for name queries with soft delete
schema.index({ 
  name: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_name_deleted'
});

// Index for hang_san_xuat queries
schema.index({ 
  hang_san_xuat: 1, 
  is_deleted: 1
}, { 
  background: true,
  sparse: true,
  name: 'idx_hang_san_xuat_deleted'
});

// Index for ho_tro_rtk filtering
schema.index({ 
  ho_tro_rtk: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_ho_tro_rtk_deleted'
});

// Index for time-based queries
schema.index({ 
  created_at: -1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_created_deleted'
});

schema.plugin(mongoosePaginate);

export default mongoose.model(DANHMUC_DRONE, schema, DANHMUC_DRONE);
