import express from 'express';
import passport from 'passport';
import * as Controller from './chuyenDe.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const chuyenDeRouter = express.Router();
chuyenDeRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
chuyenDeRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
chuyenDeRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
chuyenDeRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
chuyenDeRouter.route('/')
  .get(Controller.getAll)
  .post(Controller.create);

chuyenDeRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
