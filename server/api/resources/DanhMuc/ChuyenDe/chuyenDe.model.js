import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CHUYEN_DE, NOI_DUNG_KIEM_TRA } from '../../../constant/dbCollections';

const schema = new Schema({
  ten_chuyen_de: { type: String, required: true, validate: /\S+/ },
  noi_dung_kiem_tra_id: [{ type: Schema.Types.ObjectId, ref: NOI_DUNG_KIEM_TRA }],
  ghi_chu: String,
  thu_tu: { type: Number },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized indexes for CHUYEN_DE queries
// Using background: true for better performance during index creation

// Index for ten_chuyen_de queries with soft delete
schema.index({ 
  ten_chuyen_de: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_ten_chuyen_de_deleted'
});

// Index for noi_dung_kiem_tra_id array queries
schema.index({ 
  noi_dung_kiem_tra_id: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_noi_dung_kiem_tra_deleted'
});

// Index for time-based queries
schema.index({ 
  created_at: -1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_created_deleted'
});

schema.plugin(mongoosePaginate);

export default mongoose.model(CHUYEN_DE, schema, CHUYEN_DE);
