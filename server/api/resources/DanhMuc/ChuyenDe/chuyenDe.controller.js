import * as Service from './chuyenDe.service';
import Model from './chuyenDe.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import CommonError from '../../../error/CommonError';
import * as PhieuKiemTraService from '../../QuanLyVanHanh/PhieuGiaoViec/PhieuKiemTra/phieuKiemTra.service';

const searchLike = ['ten_chuyen_de'];
const populateOpts = [{ path: 'noi_dung_kiem_tra_id' }];
const uniqueOpts = [{ field: 'ten_chuyen_de', message: 'Tên chuyên đề' }];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, searchLike, populateOpts);

export async function remove(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const currentData = await Service.getById(id, { _id: 1, ten_chuyen_de: 1 });
    if (!currentData) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    const checkExistPhieuKiemTra = await PhieuKiemTraService.getOne(
      { chuyen_de_id: id, is_deleted: false },
      { _id: 1 });
    if (checkExistPhieuKiemTra) {
      return responseHelper.error(res, { message: t('cant_delete_assigned_thematic') });
    }
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
};
