import mongoose, { Schema } from 'mongoose';
import { BIEN_PHAP_AN_TOAN } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  ten_bien_phap_an_toan: { type: String },
  mo_ta: { type: String },
  is_deleted: { type: Boolean, default: false, select: false },
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(BIEN_PHAP_AN_TOAN, schema, BIEN_PHAP_AN_TOAN);
