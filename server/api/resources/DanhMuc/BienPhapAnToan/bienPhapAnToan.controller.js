import * as Service from './bienPhapAnToan.service';
import Model from './bienPhapAnToan.model';
import * as controllerHelper from '../../../helpers/controllerHelper';

export const findOne = controllerHelper.createFindOneFunction(Model);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service);
export const create = controllerHelper.createCreateFunction(Model, Service);
export const getAll = controllerHelper.createGetAllFunction(Model, ['ten_bien_phap_an_toan']);
