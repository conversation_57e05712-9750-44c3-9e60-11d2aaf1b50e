import express from 'express';
import passport from 'passport';
import * as bienPhapAnToanController from './bienPhapAnToan.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const bienPhapAnToanRouter = express.Router();
bienPhapAnToanRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
bienPhapAnToanRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
bienPhapAnToanRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
bienPhapAnToanRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
bienPhapAnToanRouter.route('/')
  .get(bienPhapAnToanController.getAll)
  .post(bienPhapAnToanController.create);

bienPhapAnToanRouter
  .route('/:id')
  .get(bienPhapAnToanController.findOne)
  .delete(bienPhapAnToanController.remove)
  .put(bienPhapAnToanController.update);
