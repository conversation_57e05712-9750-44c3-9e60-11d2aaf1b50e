import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CHONG_RUNG } from '../../../constant/dbCollections';

const schema = new Schema({
  ten_chong_rung: { type: String, required: true, validate: /\S+/ },
  loai_day: { type: String },
  ghi_chu: String,
  thu_tu: { type: Number },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

export default mongoose.model(CHONG_RUNG, schema, CHONG_RUNG);
