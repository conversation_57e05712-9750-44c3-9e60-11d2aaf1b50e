import express from 'express';
import passport from 'passport';
import * as chongRungController from './chongRung.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const chongRungRouter = express.Router();
chongRungRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
chongRungRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
chongRungRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
chongRungRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
chongRungRouter.route('/')
  .get(chongRungController.getAll)
  .post(chongRungController.create);

chongRungRouter
  .route('/:id')
  .get(chongRungController.findOne)
  .delete(chongRungController.remove)
  .put(chongRungController.update);
