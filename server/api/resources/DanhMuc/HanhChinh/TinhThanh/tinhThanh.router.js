import express from 'express';
import passport from 'passport';
import * as tinhThanhController from './tinhThanh.controller';

export const tinhThanhRouter = express.Router();
tinhThanhRouter.route('/')
  .get(passport.authenticate('jwt', { session: false }), tinhThanhController.getAll)
  .post(passport.authenticate('jwt', { session: false }), tinhThanhController.create);

tinhThanhRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), tinhThanhController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), tinhThanhController.remove)
  .put(passport.authenticate('jwt', { session: false }), tinhThanhController.update);
