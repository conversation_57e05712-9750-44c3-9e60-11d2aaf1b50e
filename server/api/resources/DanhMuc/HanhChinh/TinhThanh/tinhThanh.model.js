import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { TINH_THANH } from '../../../../constant/dbCollections';

const schema = new Schema({
  ten_tinh_thanh: { type: String, required: true, validate: /\S+/ },
  thu_tu: { type: Number },
  ghi_chu: String,
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

export default mongoose.model(TINH_THANH, schema, TINH_THANH);
