import express from 'express';
import passport from 'passport';
import * as tinhThanhController from './phuongXa.controller';

export const phuongXaRouter = express.Router();
phuongXaRouter.route('/')
  .get(passport.authenticate('jwt', { session: false }), tinhThanhController.getAll)
  .post(passport.authenticate('jwt', { session: false }), tinhThanhController.create);

phuongXaRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), tinhThanhController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), tinhThanhController.remove)
  .put(passport.authenticate('jwt', { session: false }), tinhThanhController.update);
