import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { PHUONG_XA, QUAN_HUYEN } from '../../../../constant/dbCollections';

const schema = new Schema({
  ten_phuong_xa: { type: String, required: true, validate: /\S+/ },
  quan_huyen_id: { type: Schema.Types.ObjectId, ref: QUAN_HUYEN },
  thu_tu: { type: Number },
  ghi_chu: String,
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

export default mongoose.model(PHUONG_XA, schema, PHUONG_XA);
