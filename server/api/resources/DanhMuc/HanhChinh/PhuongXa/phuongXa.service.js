import * as ValidatorHelper from '../../../../helpers/validatorHelper';

const Joi = require('joi');

const objSchema = Joi.object({
  ten_tinh_thanh: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên Tỉnh/Thành phố')),
  ghi_chu: Joi.string().allow(null).allow(''),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
