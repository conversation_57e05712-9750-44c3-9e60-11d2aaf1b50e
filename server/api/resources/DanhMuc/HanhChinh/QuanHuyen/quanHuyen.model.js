import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { QUAN_HUYEN, TINH_THANH } from '../../../../constant/dbCollections';

const schema = new Schema({
  ten_quan_huyen: { type: String, required: true, validate: /\S+/ },
  tinh_thanh_id: { type: Schema.Types.ObjectId, ref: TINH_THANH },
  thu_tu: { type: Number },
  ghi_chu: String,
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

export default mongoose.model(QUAN_HUYEN, schema, QUAN_HUYEN);
