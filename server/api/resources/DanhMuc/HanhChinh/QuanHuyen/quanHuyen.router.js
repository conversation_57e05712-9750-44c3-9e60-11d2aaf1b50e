import express from 'express';
import passport from 'passport';
import * as quan<PERSON><PERSON>en<PERSON>ontroller from './quanHuyen.controller';

export const quanHuyenRouter = express.Router();
quanHuyenRouter.route('/')
  .get(passport.authenticate('jwt', { session: false }), quanHuyenController.getAll)
  .post(passport.authenticate('jwt', { session: false }), quanHuyenController.create);

quanHuyenRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), quanHuyenController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), quanHuyenController.remove)
  .put(passport.authenticate('jwt', { session: false }), quanHuyenController.update);
