import express from 'express';
import passport from 'passport';
import * as hanhChinhController from './hanhChinh.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const hanhChinhRouter = express.Router();
hanhChinhRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
hanhChinhRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
hanhChinhRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
hanhChinhRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
hanhChinhRouter
  .route('/')
  .get(hanhChinhController.getAll);
