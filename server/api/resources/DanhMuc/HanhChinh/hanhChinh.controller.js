import * as responseAction from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';

import TinhThanhModel from './TinhThanh/tinhThanh.model';
import QuanHuyenModel from './QuanHuyen/quanHuyen.model';
import PhuongXaModel from './PhuongXa/phuongXa.model';


export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const tinhThanh = await TinhThanhModel.find(query.criteria).sort({ thu_tu: 1, ten_tinh_thanh: 1 }).lean();
    const quanHuyen = await QuanHuyenModel.find({ tinh_thanh_id: { $in: tinhThanh.map(item => item._id) } })
      .sort({ thu_tu: 1, ten_quan_huyen: 1 }).lean();
    const phuongXa = await PhuongXaModel.find({ quan_huyen_id: { $in: quanHuyen.map(item => item._id) } })
      .sort({ thu_tu: 1, ten_phuong_xa: 1 }).lean();

    for (let province of tinhThanh) {
      for (let district of quanHuyen) {
        district.phuong_xa = phuongXa.filter(item => item.quan_huyen_id.toString() === district._id.toString());
      }
      province.quan_huyen = quanHuyen.filter(item => item.tinh_thanh_id.toString() === province._id.toString());
    }

    responseAction.success(res, tinhThanh);
  } catch (err) {
    responseAction.error(res, err);
  }
}
