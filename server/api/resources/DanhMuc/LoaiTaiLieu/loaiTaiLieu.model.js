import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { LOAI_TAI_LIEU } from '../../../constant/dbCollections';
import { LOAI_HO_SO } from './loaiTaiLieu.constants';

const schema = new Schema({
  name: { type: String },
  type: {
    type: String,
    enum: Object.values(LOAI_HO_SO),
    default: LOAI_HO_SO.TAI_LIEU_KY_THUAT,
  },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(LOAI_TAI_LIEU, schema, LOAI_TAI_LIEU);
