import express from 'express';
import passport from 'passport';
import * as donviController from './loaiTaiLieu.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';

export const loaiTaiLieuRouter = express.Router();
loaiTaiLieuRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
loaiTaiLieuRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
loaiTaiLieuRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
loaiTaiLieuRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
loaiTaiLieuRouter
  .route('/')
  .get(donviController.getAll)
  .post(donviController.create);

loaiTaiLieuRouter
  .route('/:id')
  .get(donviController.findOne)
  .delete(donviController.remove)
  .put(donviController.update);
