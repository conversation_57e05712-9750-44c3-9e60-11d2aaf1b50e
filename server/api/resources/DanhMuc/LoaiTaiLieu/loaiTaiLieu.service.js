import * as ValidatorHelper from '../../../helpers/validatorHelper';
import LOAI_TAI_LIEU from './loaiTaiLieu.model';

const Joi = require('joi');

const objSchema = Joi.object({
  ten_duong_day: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên đường dây')),
  ma_duong_day: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã đường dây')),
  loai_duong_day_id: Joi.string().messages(ValidatorHelper.messageDefine('Loại đường dây')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function getAll(query, projection = {}) {
  return LOAI_TAI_LIEU.find(query, projection).lean();
}
