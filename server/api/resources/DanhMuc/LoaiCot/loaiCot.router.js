import express from 'express';
import passport from 'passport';
import * as loaiCotController from './loaiCot.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loaiCongViecRouter } from '../LoaiCongViec/loaiCongViec.router';
import { loggerMiddleware } from '../../../logs/middleware';

export const loaiCotRouter = express.Router();
loaiCongViecRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
loaiCongViecRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
loaiCongViecRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
loaiCongViecRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
loaiCongViecRouter.route('/')
  .get(loaiCotController.getAll)
  .post(loaiCotController.create);

loaiCongViecRouter
  .route('/:id')
  .get(loaiCotController.findOne)
  .delete(loaiCotController.remove)
  .put(loaiCotController.update);
