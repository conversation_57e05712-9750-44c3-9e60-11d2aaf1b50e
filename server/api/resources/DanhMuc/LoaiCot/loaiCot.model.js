import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { LOAI_COT } from '../../../constant/dbCollections';

const schema = new Schema({
  ten_loai: { type: String, required: true, validate: /\S+/ },
  ghi_chu: String,
  thu_tu: { type: Number },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

export default mongoose.model(LOAI_COT, schema, LOAI_COT);
