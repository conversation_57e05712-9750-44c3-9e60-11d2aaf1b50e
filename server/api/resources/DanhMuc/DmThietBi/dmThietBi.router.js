import express from 'express';
import passport from 'passport';
import * as dmthietbiController from './dmThietBi.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const dmThietBiRouter = express.Router();
dmThietBiRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
dmThietBiRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
dmThietBiRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
dmThietBiRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
dmThietBiRouter
  .route('/')
  .get(dmthietbiController.getAll)
  .post(dmthietbiController.create);

dmThietBiRouter
  .route('/:id')
  .get(dmthietbiController.findOne)
  .delete(dmthietbiController.remove)
  .put(dmthietbiController.update);
