import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import { DM_THIET_BI } from '../../../constant/dbCollections';

const schema = new Schema({
  ma_thiet_bi: { type: String, required: true, unique: true, validate: /\S+/ },
  ten_thiet_bi: { type: String, required: true, validate: /\S+/ },
  ghi_chu: { type: String },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(DM_THIET_BI, schema, DM_THIET_BI);
