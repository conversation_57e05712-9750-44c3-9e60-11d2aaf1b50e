import * as ValidatorHelper from '../../../helpers/validatorHelper';
import DM_THIET_BI from './dmThietBi.model';

export function getAll(query) {
  return DM_THIET_BI.find(query).lean();
}


const Joi = require('joi');

const objSchema = Joi.object({
  ma_thiet_bi: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã thiết bị')),
  ten_thiet_bi: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên thiết bị')),
  ghi_chu: Joi.string(),
});


export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
