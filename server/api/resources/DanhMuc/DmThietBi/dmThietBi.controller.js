import * as responseAction from '../../../helpers/responseHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from './dmThietBi.service';
import * as TinhTrangKBTService from './TinhTrangKBT/tinhTrangKBT.service';
import Model from './dmThietBi.model';
import TinhTrangKBTModel from './TinhTrangKBT/tinhTrangKBT.model';

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id).lean();
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    data.tinh_trang_kbt = await TinhTrangKBTService.getAll({
      dm_thiet_bi_id: id,
      is_deleted: false,
    }).populate('tieu_chi_id');
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);

    const checkUnique = await Model.count({ ma_thiet_bi: value.ma_thiet_bi, _id: { $ne: id } });
    if (checkUnique) {
      return responseHelper.error(res, { message: t('device_already_exists') }, 404);
    }

    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true }).lean();
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    let tinhTrangKBTResponse = [];
    if (Array.isArray(value.tinh_trang_kbt)) {
      for (let i = 0; i < value.tinh_trang_kbt.length; i++) {
        const tinhTrangKBT = value.tinh_trang_kbt[i];

        if (tinhTrangKBT._id) {
          if (tinhTrangKBT.delete) {
            await TinhTrangKBTModel.findOneAndUpdate({ _id: tinhTrangKBT._id }, { is_deleted: true });
          } else {
            const dataUpdated = await TinhTrangKBTModel.findOneAndUpdate({ _id: tinhTrangKBT._id }, tinhTrangKBT, { new: true }).lean();
            if (dataUpdated) {
              tinhTrangKBTResponse = [...tinhTrangKBTResponse, JSON.parse(JSON.stringify(dataUpdated))];
            }
          }
        } else {
          // tao moi khi khong co _id
          tinhTrangKBT.dm_thiet_bi_id = id;
          const dataCreated = await TinhTrangKBTModel.create(tinhTrangKBT);
          if (dataCreated) {
            tinhTrangKBTResponse = [...tinhTrangKBTResponse, JSON.parse(JSON.stringify(dataCreated))];
          }
        }
      }
    }
    data.tinh_trang_kbt = tinhTrangKBTResponse;
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}


export async function create(req, res) {
  try {
    const { t } = req;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);

    const checkUnique = await Model.findOne({ ma_thiet_bi: value.ma_thiet_bi });
    if (checkUnique) {
      return responseHelper.error(res, { message: t('device_already_exists') }, 404);

    }

    const data = await Model.create(value);
    if (Array.isArray(value.tinh_trang_kbt)) {

      let tinhTrangKBTResponse = [];
      for (let i = 0; i < value.tinh_trang_kbt.length; i++) {
        const tinhTrangKBT = value.tinh_trang_kbt[i];
        tinhTrangKBT.dm_thiet_bi_id = data._id;

        const queryId = tinhTrangKBT._id ? { _id: { $ne: tinhTrangKBT._id } } : {};

        const checkUniqueTenTinhTrang = await TinhTrangKBTModel.count({
          ten_tinh_trang: tinhTrangKBT.ten_tinh_trang,
          is_deleted: false, ...queryId,
        });
        if (checkUniqueTenTinhTrang) {
          return responseHelper.error(res, { message: `${t('status_name')} "${tinhTrangKBT.ten_tinh_trang}" ${t('already_exists')}` }, 404);
        }
        const checkUniqueTinhTrang = await TinhTrangKBTModel.count({
          ma_tinh_trang: tinhTrangKBT.ma_tinh_trang,
          is_deleted: false, ...queryId,
        });
        if (checkUniqueTinhTrang) {
          return responseHelper.error(res, { message: `${t('status_code')} "${tinhTrangKBT.ma_tinh_trang}" ${t('already_exists')}` }, 404);
        }

        const tinhTrangKBTCreated = await TinhTrangKBTModel.create(tinhTrangKBT);
        if (tinhTrangKBTCreated) {
          tinhTrangKBTResponse = [...tinhTrangKBTResponse, JSON.parse(JSON.stringify(tinhTrangKBTCreated))];
        }
      }
      data.tinh_trang_kbt = tinhTrangKBTResponse;

    }
    return responseAction.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}


export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ma_thiet_bi', 'ten_thiet_bi']);
    const { criteria, options } = query;
    let tinhTrangKBT = null;
    if (criteria.get_tinh_trang) {
      tinhTrangKBT = await TinhTrangKBTService.getAll({ is_deleted: false }).populate('tieu_chi_id');
      delete criteria.get_tinh_trang;
    }

    const data = await Model.paginate(criteria, options);

    if (Array.isArray(tinhTrangKBT)) {
      data.docs = data.docs.map(doc => {
        doc.tinh_trang_kbt = tinhTrangKBT.filter(tinhTrang => tinhTrang.dm_thiet_bi_id.toString() === doc._id.toString());
        return doc;
      });
    }

    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}
