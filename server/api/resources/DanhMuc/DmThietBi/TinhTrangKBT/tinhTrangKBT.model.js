import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DM_THIET_BI, TIEU_CHI, TINH_TRANG_KBT } from '../../../../constant/dbCollections';

const schema = new Schema({
  ten_tinh_trang: { type: String, required: true, validate: /\S+/ },
  ma_tinh_trang: { type: String, validate: /\S+/ },
  is_deleted: { type: Boolean, default: false, select: false },
  dm_thiet_bi_id: {
    type: Schema.Types.ObjectId, ref: DM_THIET_BI, required: true,
  },
  tieu_chi_id: {
    type: Schema.Types.ObjectId, ref: TIEU_CHI,
  },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(TINH_TRANG_KBT, schema, TINH_TRANG_KBT);

