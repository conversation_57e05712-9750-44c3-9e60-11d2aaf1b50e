import express from 'express';
import passport from 'passport';
import * as tinhtrangkbtController from './tinhTrangKBT.controller';
import { authorizationMiddleware } from '../../../RBAC/middleware';
import DanhMucPermission from '../../../RBAC/permissions/DanhMucPermission';

export const tinhTrangKBTRouter = express.Router();
tinhTrangKBTRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
tinhTrangKBTRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
tinhTrangKBTRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
tinhTrangKBTRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
tinhTrangKBTRouter
  .route('/')
  .get(tinhtrangkbtController.getAll)
  .post(tinhtrangkbtController.create);

tinhTrangKBTRouter
  .route('/:id')
  .get(tinhtrangkbtController.findOne)
  .delete(tinhtrangkbtController.remove)
  .put(tinhtrangkbtController.update);
