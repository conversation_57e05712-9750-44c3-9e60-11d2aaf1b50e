import * as ValidatorHelper from '../../../../helpers/validatorHelper';
import TINH_TRANG_KBT from './tinhTrangKBT.model';

export function getAll(query) {
  return TINH_TRANG_KBT.find(query).lean();
}


const Joi = require('joi');

const objSchema = Joi.object({
  ten_tinh_trang: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên tình trạng')),
  ma_tinh_trang: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã tình trạng')),
  dm_thiet_bi_id: Joi.string().required().messages(ValidatorHelper.messageDefine('tên thiết bị')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
