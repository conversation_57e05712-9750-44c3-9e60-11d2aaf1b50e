import * as responseAction from '../../../../helpers/responseHelper';
import * as responseHelper from '../../../../helpers/responseHelper';
import queryHelper from '../../../../helpers/queryHelper';
import * as Service from './tinhTrangKBT.service';
import Model from './tinhTrangKBT.model';

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id)
      .populate({ path: 'dm_thiet_bi_id', select: 'ten_thiet_bi' })
      .populate({ path: 'tieu_chi_id', select: 'ten_tieu_chi' });
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { id } = req.params;
    console.log(id);
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);

    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true })
      .populate({ path: 'dm_thiet_bi_id', select: 'ten_thiet_bi' })
      .populate({ path: 'tieu_chi_id', select: 'ten_tieu_chi' });
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    return responseAction.success(res, data);
  } catch (err) {
    console.log(err);
    responseAction.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const data = await Model.create(value);
    let dataRtn = await data
      .populate({ path: 'tieu_chi_id', select: 'ten_tieu_chi' })
      .populate({ path: 'dm_thiet_bi_id', select: 'ten_thiet_bi' }).execPopulate();
    return responseAction.success(res, dataRtn);

  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_tinh_trang']);
    const { criteria, options } = query;
    options.populate = [
      { path: 'dm_thiet_bi_id', select: 'ten_thiet_bi' },
      { path: 'tieu_chi_id', select: 'ten_tieu_chi' },
    ];
    options.sort = { thu_tu: 1 };
    const data = await Model.paginate(criteria, options);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}
