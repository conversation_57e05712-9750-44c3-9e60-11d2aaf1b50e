import express from 'express';
import passport from 'passport';
import * as loaiDuongDayController from './loaiViTri.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const loaiViTriRouter = express.Router();
loaiViTriRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
loaiViTriRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
loaiViTriRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
loaiViTriRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
loaiViTriRouter.route('/')
  .get(loaiDuongDayController.getAll)
  .post(loaiDuongDayController.create);

loaiViTriRouter
  .route('/:id')
  .get(loaiDuongDayController.findOne)
  .delete(loaiDuongDayController.remove)
  .put(loaiDuongDayController.update);
