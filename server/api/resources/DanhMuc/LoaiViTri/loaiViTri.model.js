import mongoose, { Schem<PERSON> } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { HANG_MUC, LOAI_VI_TRI } from '../../../constant/dbCollections';

const schema = new Schema({
  ten_loai: { type: String, required: true, validate: /\S+/ },
  hang_muc_id: [{ type: Schema.Types.ObjectId, ref: HANG_MUC }],
  thu_tu: { type: Number },
  ghi_chu: String,
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

export default mongoose.model(LOAI_VI_TRI, schema, LOAI_VI_TRI);
