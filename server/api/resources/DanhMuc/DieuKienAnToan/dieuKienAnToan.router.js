import express from 'express';
import passport from 'passport';
import * as dieuKienAnToanController from './dieuKienAnToan.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const dieuKienAnToanRouter = express.Router();
dieuKienAnToanRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
dieuKienAnToanRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
dieuKienAnToanRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
dieuKienAnToanRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
dieuKienAnToanRouter.route('/')
  .get(dieuKienAnToanController.getAll)
  .post(dieuKienAnToanController.create);

dieuKienAnToanRouter
  .route('/:id')
  .get(dieuKienAnToanController.findOne)
  .delete(dieuKienAnToanController.remove)
  .put(dieuKienAnToanController.update);
