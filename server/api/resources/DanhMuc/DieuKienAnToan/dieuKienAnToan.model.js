import mongoose, { Schema } from 'mongoose';
import { DIEU_KIEN_AN_TOAN } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';
import { LOAI_DIEU_KIEN_AN_TOAN } from '../../../constant/constant';

const schema = new Schema({
  ten_dieu_kien_an_toan: { type: String },
  loai_dieu_kien: {
    type: String,
    enum: Object.keys(LOAI_DIEU_KIEN_AN_TOAN),
    default: LOAI_DIEU_KIEN_AN_TOAN.CO_LAP_DUONG_DAY.code,
  },
  mo_ta: { type: String },
  is_deleted: { type: Boolean, default: false, select: false },
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(DIEU_KIEN_AN_TOAN, schema, DIEU_KIEN_AN_TOAN);
