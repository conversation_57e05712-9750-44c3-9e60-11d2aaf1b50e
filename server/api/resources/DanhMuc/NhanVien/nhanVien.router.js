import express from 'express';
import passport from 'passport';
import * as nhanvienController from './nhanVien.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const nhanVienRouter = express.Router();
nhanVienRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
nhanVienRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
nhanVienRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
nhanVienRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
nhanVienRouter.route('/')
  .get(nhanvienController.getAll)
  .post(nhanvienController.create);

nhanVienRouter
  .route('/:id')
  .get(nhanvienController.findOne)
  .delete(nhanvienController.remove)
  .put(nhanvienController.update);
