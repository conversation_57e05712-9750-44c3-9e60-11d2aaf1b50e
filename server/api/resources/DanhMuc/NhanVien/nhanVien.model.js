import mongoose, { Schema } from 'mongoose';
import { DON_VI, NHAN_VIEN } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  ten_nhan_vien: { type: String, required: true, validate: /\S+/ },
  ngay_cap: { type: Date, required: true, validate: /\S+/ },
  noi_cap: { type: String, required: true, validate: /\S+/ },
  don_vi_id: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: DON_VI,
  },
  is_active: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});


schema.plugin(mongoosePaginate);
export default mongoose.model(NHAN_VIEN, schema, NHAN_VIEN);
