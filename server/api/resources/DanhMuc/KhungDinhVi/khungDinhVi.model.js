import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { KHUNG_DINH_VI } from '../../../constant/dbCollections';

const schema = new Schema({
  ten_khung: { type: String, required: true, validate: /\S+/ },
  ghi_chu: String,
  thu_tu: { type: Number },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

export default mongoose.model(KHUNG_DINH_VI, schema, KHUNG_DINH_VI);
