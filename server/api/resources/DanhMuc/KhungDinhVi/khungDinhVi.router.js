import express from 'express';
import passport from 'passport';
import * as khungDinhViController from './khungDinhVi.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const khungDinhViRouter = express.Router();
khungDinhViRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
khungDinhViRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
khungDinhViRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
khungDinhViRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
khungDinhViRouter.route('/')
  .get(khungDinhViController.getAll)
  .post(khungDinhViController.create);

khungDinhViRouter
  .route('/:id')
  .get(khungDinhViController.findOne)
  .delete(khungDinhViController.remove)
  .put(khungDinhViController.update);
