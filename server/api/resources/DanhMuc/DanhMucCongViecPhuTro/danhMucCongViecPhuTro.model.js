import mongoose, { Schema } from 'mongoose';
import { DANH_MUC_CONG_VIEC, DANH_MUC_CONG_VIEC_PHU_TRO } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  ten_cong_viec: { type: String },
  don_vi_tinh: { type: String },
  mo_ta: { type: String },
  is_deleted: { type: Boolean, default: false, select: false },
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(DANH_MUC_CONG_VIEC_PHU_TRO, schema, DANH_MUC_CONG_VIEC_PHU_TRO);
