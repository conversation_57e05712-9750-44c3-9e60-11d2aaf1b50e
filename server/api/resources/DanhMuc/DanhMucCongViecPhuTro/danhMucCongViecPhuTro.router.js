import express from 'express';
import passport from 'passport';
import * as danhMucCongViecController from './danhMucCongViecPhuTro.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const danhMucCongViecPhuTroRouter = express.Router();
danhMucCongViecPhuTroRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
danhMucCongViecPhuTroRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
danhMucCongViecPhuTroRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
danhMucCongViecPhuTroRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
danhMucCongViecPhuTroRouter.route('/')
  .get(danhMucCongViecController.getAll)
  .post(danhMucCongViecController.create);

danhMucCongViecPhuTroRouter
  .route('/:id')
  .get(danhMucCongViecController.findOne)
  .delete(danhMucCongViecController.remove)
  .put(danhMucCongViecController.update);
