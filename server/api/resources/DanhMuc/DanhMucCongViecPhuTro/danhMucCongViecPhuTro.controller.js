import * as Service from './danhMucCongViecPhuTro.service';
import Model from './danhMucCongViecPhuTro.model';
import * as controllerHelper from '../../../helpers/controllerHelper';

export const findOne = controllerHelper.createFindOneFunction(Model);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service);
export const create = controllerHelper.createCreateFunction(Model, Service);
export const getAll = controllerHelper.createGetAllFunction(Model, ['ten_cong_viec']);
