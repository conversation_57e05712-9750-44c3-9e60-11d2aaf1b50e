import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { LOAI_CACH_DIEN } from '../../../constant/dbCollections';

const schema = new Schema({
  ky_hieu_chuoi: { type: String, required: true, validate: /\S+/ },
  so_luong_chuoi: { type: String },
  loai_day: { type: String },
  loai_cach_dien: { type: String },
  so_luong_bat_cach_dien: { type: String },
  ghi_chu: String,
  thu_tu: { type: Number },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

export default mongoose.model(LOAI_CACH_DIEN, schema, LOAI_CACH_DIEN);
