import express from 'express';
import passport from 'passport';
import * as cachDienController from './cachDien.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const loaiCachDienRouter = express.Router();
loaiCachDienRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
loaiCachDienRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
loaiCachDienRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
loaiCachDienRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
loaiCachDienRouter.route('/')
  .get(cachDienController.getAll)
  .post(cachDienController.create);

loaiCachDienRouter
  .route('/:id')
  .get(cachDienController.findOne)
  .delete(cachDienController.remove)
  .put(cachDienController.update);
