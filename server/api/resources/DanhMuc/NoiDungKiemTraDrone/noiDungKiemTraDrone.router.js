import express from 'express';
import passport from 'passport';
import * as Controller from './noiDungKiemTraDrone.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const ndktDroneRouter = express.Router();
ndktDroneRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
ndktDroneRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
ndktDroneRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
ndktDroneRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
ndktDroneRouter.route('/')
  .get(Controller.getAll)
  .post(Controller.create);

ndktDroneRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
