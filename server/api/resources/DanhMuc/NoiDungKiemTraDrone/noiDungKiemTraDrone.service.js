import * as ValidatorHelper from '../../../helpers/validatorHelper';
import NOI_DUNG_KIEM_TRA_DRONE from './noiDungKiemTraDrone.model';

const Joi = require('joi');

const objSchema = Joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function getAll(query, projection = {}) {
  return NOI_DUNG_KIEM_TRA_DRONE.find(query, projection).lean();
}

export function getById(id, projection = {}) {
  return NOI_DUNG_KIEM_TRA_DRONE.findById(id, projection).lean();
}
