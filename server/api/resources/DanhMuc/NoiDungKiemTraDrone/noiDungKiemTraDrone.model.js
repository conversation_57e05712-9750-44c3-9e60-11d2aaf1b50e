import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { NDKT_DRONE, NOI_DUNG_KIEM_TRA } from '../../../constant/dbCollections';

const schema = new Schema({
  ten_ndkt_drone: { type: String, required: true, validate: /\S+/ },
  noi_dung_kiem_tra_id: [{ type: Schema.Types.ObjectId, ref: NOI_DUNG_KIEM_TRA }],
  ghi_chu: String,
  active: { type: Boolean },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized indexes for NDKT_DRONE queries
// Using background: true for better performance during index creation

// Index for ten_ndkt_drone queries with soft delete
schema.index({ 
  ten_ndkt_drone: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_ten_ndkt_deleted'
});

// Index for active status filtering
schema.index({ 
  active: 1, 
  is_deleted: 1
}, { 
  background: true,
  sparse: true,
  name: 'idx_active_deleted'
});

// Index for noi_dung_kiem_tra_id array queries
schema.index({ 
  noi_dung_kiem_tra_id: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_noi_dung_kiem_tra_deleted'
});

// Index for time-based queries
schema.index({ 
  created_at: -1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_created_deleted'
});

schema.plugin(mongoosePaginate);

export default mongoose.model(NDKT_DRONE, schema, NDKT_DRONE);
