import * as ValidatorHelper from '../../../helpers/validatorHelper';
import LOAI_DUONG_DAY from './loaiDuongDay.model';

const Joi = require('joi');

export function getAll(query, projection = {}) {
  return LOAI_DUONG_DAY.find(query, projection).lean();
}

const objSchema = Joi.object({
  name: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên loại đường dây')),
  description: Joi.string().allow(null).allow(''),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
