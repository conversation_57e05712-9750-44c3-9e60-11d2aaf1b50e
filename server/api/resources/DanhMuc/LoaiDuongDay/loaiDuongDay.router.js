import express from 'express';
import passport from 'passport';
import * as loaiDuongDayController from './loaiDuongDay.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const loaiDuongDayRouter = express.Router();
loaiDuongDayRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
loaiDuongDayRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
loaiDuongDayRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
loaiDuongDayRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
loaiDuongDayRouter.route('/')
  .get(loaiDuongDayController.getAll)
  .post(loaiDuongDayController.create);

loaiDuongDayRouter
  .route('/:id')
  .get(loaiDuongDayController.findOne)
  .delete(loaiDuongDayController.remove)
  .put(loaiDuongDayController.update);
