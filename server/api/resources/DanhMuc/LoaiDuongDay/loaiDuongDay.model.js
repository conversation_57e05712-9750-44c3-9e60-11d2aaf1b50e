import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { LOAI_DUONG_DAY } from '../../../constant/dbCollections';

const schema = new Schema({
  ten_loai: { type: String, required: true, validate: /\S+/ },
  ghi_chu: String,
  color: { type: String },
  thu_tu: { type: Number },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

export default mongoose.model(LOAI_DUONG_DAY, schema, LOAI_DUONG_DAY);
