import express from 'express';
import passport from 'passport';
import * as loaiDayChongSetController from './loaiDayChongSet.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const loaiDayChongSetRouter = express.Router();
loaiDayChongSetRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
loaiDayChongSetRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
loaiDayChongSetRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
loaiDayChongSetRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
loaiDayChongSetRouter.route('/')
  .get(loaiDayChongSetController.getAll)
  .post(loaiDayChongSetController.create);

loaiDayChongSetRouter
  .route('/:id')
  .get(loaiDayChongSetController.findOne)
  .delete(loaiDayChongSetController.remove)
  .put(loaiDayChongSetController.update);
