import mongoose, { Schem<PERSON> } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { LOAI_DAY_CHONG_SET } from '../../../constant/dbCollections';

const schema = new Schema({

  ma_hieu_day_chong_set: String,
  tiet_dien: String,
  luc_keo_dut: String,
  he_so_dan_dai: String,
  trong_luong_day: String,
  dien_tro_mot_chieu: String,
  so_luong_soi_quang: String,


  ghi_chu: String,
  thu_tu: { type: Number },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

export default mongoose.model(LOAI_DAY_CHONG_SET, schema, LOAI_DAY_CHONG_SET);
