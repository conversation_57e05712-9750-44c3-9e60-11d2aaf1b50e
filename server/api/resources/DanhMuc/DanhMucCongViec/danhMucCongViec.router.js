import express from 'express';
import passport from 'passport';
import * as danhMucCongViecController from './danhMucCongViec.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const danhMucCongViecRouter = express.Router();
danhMucCongViecRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
danhMucCongViecRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
danhMucCongViecRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
danhMucCongViecRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
danhMucCongViecRouter.route('/')
  .get(danhMucCongViecController.getAll)
  .post(danhMucCongViecController.create);

danhMucCongViecRouter
  .route('/:id')
  .get(danhMucCongViecController.findOne)
  .delete(danhMucCongViecController.remove)
  .put(danhMucCongViecController.update);
