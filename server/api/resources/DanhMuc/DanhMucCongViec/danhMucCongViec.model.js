import mongoose, { Schema } from 'mongoose';
import { DANH_MUC_CONG_VIEC } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  ten_cong_viec: { type: String },
  loai_cong_viec: { type: String, required: true },
  don_vi_tinh: { type: String },
  mo_ta: { type: String },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized indexes for DANH_MUC_CONG_VIEC queries
// Primary index for loai_cong_viec filtering (most common query)
schema.index({ 
  loai_cong_viec: 1, 
  is_deleted: 1,
  ten_cong_viec: 1
}, { 
  background: true,
  name: 'idx_loai_cong_viec_deleted_ten'
});

// Index for work name searches with soft delete
schema.index({ 
  ten_cong_viec: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_ten_cong_viec_deleted'
});

// Index for unit-based filtering
schema.index({ 
  don_vi_tinh: 1, 
  loai_cong_viec: 1,
  is_deleted: 1
}, { 
  background: true,
  sparse: true,
  name: 'idx_don_vi_tinh_loai_deleted'
});

// Index for soft delete queries
schema.index({ 
  is_deleted: 1,
  created_at: -1
}, { 
  background: true,
  name: 'idx_deleted_time'
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(DANH_MUC_CONG_VIEC, schema, DANH_MUC_CONG_VIEC);
