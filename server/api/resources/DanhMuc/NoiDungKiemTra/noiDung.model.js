import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { NOI_DUNG_KIEM_TRA } from '../../../constant/dbCollections';

export const LOAI_NOI_DUNG = {
  KIEM_TRA_VI_TRI: 'KIEM_TRA_VI_TRI',
  KIEM_TRA_KHOANG_COT: 'KIEM_TRA_KHOANG_COT',
};

const schema = new Schema({
  ten_noi_dung: { type: String, required: true, validate: /\S+/, unique: true },
  loai_noi_dung: [{
    type: String,
    enum: Object.keys(LOAI_NOI_DUNG),
  }],
  ghi_chu: { type: String },
  is_active: { type: Boolean, default: true },
  is_system: { type: Boolean, default: false },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized compound indexes for NOI_DUNG_KIEM_TRA queries
// Primary index for ten_noi_dung searches with soft delete
schema.index({ 
  ten_noi_dung: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_ten_noi_dung_deleted'
});

// Index for loai_noi_dung filtering
schema.index({ 
  loai_noi_dung: 1, 
  is_deleted: 1,
  created_at: -1
}, { 
  background: true,
  name: 'idx_loai_noi_dung_deleted_time'
});

// Index for is_active filtering
schema.index({ 
  is_active: 1, 
  is_deleted: 1,
  ten_noi_dung: 1
}, { 
  background: true,
  name: 'idx_active_deleted_ten'
});

// Index for is_system filtering
schema.index({ 
  is_system: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_system_deleted'
});

// Index for time-based queries
schema.index({ 
  created_at: -1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_time_deleted'
});

schema.plugin(mongoosePaginate);
export default mongoose.model(NOI_DUNG_KIEM_TRA, schema, NOI_DUNG_KIEM_TRA);
