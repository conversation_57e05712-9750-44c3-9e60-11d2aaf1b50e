import * as ValidatorHelper from '../../../helpers/validatorHelper';
import NOI_DUNG_KIEM_TRA from './noiDung.model';

export function getAll(query) {
  return NOI_DUNG_KIEM_TRA.find(query).collation({ locale: 'vi' }).lean();
}

export function count(query) {
  return NOI_DUNG_KIEM_TRA.count(query);
}

const Joi = require('joi');

const objSchema = Joi.object({
  ten_noi_dung: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên nội dung')),

  ghi_chu: Joi.string(),

});


export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
