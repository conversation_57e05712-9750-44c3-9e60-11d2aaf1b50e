import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { NOI_DUNG_KIEM_TRA, TIEU_CHI } from '../../../../constant/dbCollections';

export const CONFIG_THIET_BI = {
  CACH_DIEN: { code: 'CACH_DIEN', label: '<PERSON><PERSON>ch điện' },
  DAY_DAN: { code: 'DAY_DAN', label: 'Dây dẫn' },
  CHONG_SET: { code: 'CHONG_SET', label: 'Chống sét' },
  CAP_QUANG: { code: 'CAP_QUANG', label: '<PERSON>á<PERSON> quang' },
  COT_DIEN: { code: 'COT_DIEN', label: 'Cột điện' },
  GIAO_CHEO: { code: 'GIAO_CHEO', label: 'Giao chéo' },
  TIEP_DAT: { code: 'TIEP_DAT', label: 'Tiếp đất' },
};


const schema = new Schema({
  ten_tieu_chi: { type: String, required: true, validate: /\S+/ },
  thiet_bi: {
    type: String,
    enum: [...Object.keys(CONFIG_THIET_BI), null],
  },
  ghi_chu: { type: String },
  don_vi: { type: String },
  is_deleted: { type: Boolean, default: false },
  noi_dung_kiem_tra_id: { type: Schema.Types.ObjectId, ref: NOI_DUNG_KIEM_TRA, required: true },
  tieu_chi_cha_id: { type: Schema.Types.ObjectId, ref: TIEU_CHI },
  tieu_chi_cay_cao: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized compound indexes for TIEU_CHI queries
// Primary index for noi_dung_kiem_tra_id filtering with soft delete
schema.index({ 
  noi_dung_kiem_tra_id: 1, 
  is_deleted: 1,
  ten_tieu_chi: 1
}, { 
  background: true,
  name: 'idx_noi_dung_kiem_tra_deleted_ten'
});

// Index for thiet_bi filtering
schema.index({ 
  thiet_bi: 1, 
  is_deleted: 1,
  created_at: -1
}, { 
  background: true,
  sparse: true,
  name: 'idx_thiet_bi_deleted_time'
});

// Index for hierarchical queries (parent-child relationships)
schema.index({ 
  tieu_chi_cha_id: 1, 
  is_deleted: 1,
  ten_tieu_chi: 1
}, { 
  background: true,
  sparse: true,
  name: 'idx_tieu_chi_cha_deleted_ten'
});

// Index for name-based searches
schema.index({ 
  ten_tieu_chi: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_ten_tieu_chi_deleted'
});

// Index for time-based queries
schema.index({ 
  created_at: -1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_time_deleted'
});

schema.plugin(mongoosePaginate);
export default mongoose.model(TIEU_CHI, schema, TIEU_CHI);

