import express from 'express';
import passport from 'passport';
import * as tieuchiController from './tieuChi.controller';
import { authorizationMiddleware } from '../../../RBAC/middleware';
import DanhMucPermission from '../../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../../logs/middleware';

export const tieuChiRouter = express.Router();
tieuChiRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
tieuChiRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
tieuChiRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
tieuChiRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
tieuChiRouter
  .route('/')
  .get(tieuchiController.getAll)
  .post(tieuchiController.create);

tieuChiRouter
  .route('/tree')
  .get(tieuchiController.getAllTree);

tieuChiRouter
  .route('/:id')
  .get(tieuchiController.findOne)
  .delete(tieuchiController.remove)
  .put(tieuchiController.update);
