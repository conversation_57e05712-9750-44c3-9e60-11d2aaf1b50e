import * as responseAction from '../../../../helpers/responseHelper';
import queryHelper from '../../../../helpers/queryHelper';
import * as Service from './tieuChi.service';
import Model from './tieuChi.model';

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id)
      .populate({ path: 'noi_dung_kiem_tra_id', select: 'ten_noi_dung' });
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { id } = req.params;

    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true })
      .populate({ path: 'noi_dung_kiem_tra_id', select: 'ten_noi_dung' });
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const data = await Model.create(value);
    let dataRtn = await data
      .populate({ path: 'noi_dung_kiem_tra_id', select: 'ten_noi_dung' }).execPopulate();
    return responseAction.success(res, dataRtn);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_tieu_chi']);
    const { criteria, options } = query;
    options.populate = [
      {
        path: 'noi_dung_kiem_tra_id tieu_chi_cha_id',
      },
    ];
    options.sort = { thu_tu: 1 };
    const data = await Model.paginate(criteria, options);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getAllTree(req, res) {
  try {
    const tieuChiParent = await Service
      .getAll({ is_deleted: false, tieu_chi_cha_id: { $exists: false } })
      .sort({ ten_tieu_chi: 1 });

    const tieuChiChild = await Service
      .getAll({ is_deleted: false, tieu_chi_cha_id: { $exists: true } })
      .sort({ ten_tieu_chi: 1 });

    tieuChiParent.map(parent => {
      parent.chi_tiet = tieuChiChild.filter(child => child.tieu_chi_cha_id.toString() === parent._id.toString());
      return parent;
    });
    responseAction.success(res, tieuChiParent);
  } catch (err) {
    responseAction.error(res, err);
  }


}
