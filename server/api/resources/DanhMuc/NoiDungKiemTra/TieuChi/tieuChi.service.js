import * as ValidatorHelper from '../../../../helpers/validatorHelper';
import TIEU_CHI from './tieuChi.model';

export function getAll(query, projection = {}) {
  return TIEU_CHI.find(query, projection).lean();
}

const Joi = require('joi');

const objSchema = Joi.object({
  ten_tieu_chi: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên tiêu chí')),

  ghi_chu: Joi.string(),
  noi_dung_kiem_tra_id: Joi.string().required().messages(ValidatorHelper.messageDefine('tên nội dung')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
