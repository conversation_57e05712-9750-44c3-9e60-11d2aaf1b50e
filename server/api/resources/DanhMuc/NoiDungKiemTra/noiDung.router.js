import express from 'express';
import passport from 'passport';
import * as noidungController from './noiDung.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const noiDungRouter = express.Router();
noiDungRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
noiDungRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
noiDungRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
noiDungRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
noiDungRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), noidungController.getAll)
  .post(passport.authenticate('jwt', { session: false }), noidungController.create);

noiDungRouter
  .route('/tree')
  .get(passport.authenticate('jwt', { session: false }), noidungController.getAllTree);

noiDungRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), noidungController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), noidungController.remove)
  .put(passport.authenticate('jwt', { session: false }), noidungController.update);
