import * as responseAction from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from './noiDung.service';
import * as TieuChiService from './TieuChi/tieuChi.service';
import * as KetQuaKiemTraService from '../../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.service';
import * as CaiDatVanHanhService from '../../CaiDatVanHanh/caiDatVanHanh.service';
import Model, { LOAI_NOI_DUNG } from './noiDung.model';
import TieuChiModel from './TieuChi/tieuChi.model';
import * as responseHelper from '../../../helpers/responseHelper';
import { extractIds } from '../../../utils/dataconverter';
import { getOne } from '../../CaiDatVanHanh/caiDatVanHanh.service';

async function getTieuChiChild(noiDungId) {
  const tieuChiParent = await TieuChiService.getAll({
    noi_dung_kiem_tra_id: noiDungId,
    is_deleted: false,
    tieu_chi_cha_id: { $exists: false },
  });
  const tieuChiChild = await TieuChiService.getAll({
    noi_dung_kiem_tra_id: noiDungId,
    is_deleted: false,
    tieu_chi_cha_id: { $exists: true },
  });

  return tieuChiParent.map(parent => {
    parent.chi_tiet = tieuChiChild.filter(child => child.tieu_chi_cha_id.toString() === parent._id.toString());
    return parent;
  });
}

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id).lean();
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    data.tieu_chi = await getTieuChiChild(id);
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;

    const allTieuChi = await TieuChiService.getAll({ noi_dung_kiem_tra_id: id, is_deleted: false }).lean();
    const allTieuChiId = extractIds(allTieuChi);
    const checkKiemTra = await KetQuaKiemTraService.getOne({ tieu_chi_id: allTieuChiId }, { _id: 1 });
    if (checkKiemTra) {
      return responseAction.error(res, { message: t('cant_delete_content_with_test_result') }, 404);
    }
    const checkCaiDat = await CaiDatVanHanhService.getOne(
      {
        $or: [
          { tieu_chi_do_tiep_dia: allTieuChiId },
          { tieu_chi_do_khoang_cach_pha: allTieuChiId },
          { tieu_chi_do_nhiet_do: allTieuChiId },
        ],
      },
      { _id: 1 },
    );
    if (checkCaiDat) {
      return responseAction.error(res, { message: t('cant_delete_content_system_setting') }, 404);
    }

    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {

  try {
    const { t } = req;
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    if (!value.loai_noi_dung?.length) {
      return responseAction.error(res, { message: t('content_type_cant_empty') });
    }
    // check update loai_noi_dung
    const tieuChiBeforeUpdated = await getTieuChiChild(id);
    const ketQuaList = await KetQuaKiemTraService.getAll(
      { tieu_chi_id: extractIds(tieuChiBeforeUpdated), is_deleted: false },
      { khoang_cot_id: 1, vi_tri_id: 1 });

    if (ketQuaList.length) {
      const beforeUpdate = await Model.findOne({ _id: id }).lean();
      if (Array.isArray(beforeUpdate.loai_noi_dung)) {
        let intersection = beforeUpdate.loai_noi_dung?.filter(x => !value.loai_noi_dung.includes(x));
        const check = ketQuaList.find(ketQua => {
          return intersection.includes(LOAI_NOI_DUNG.KIEM_TRA_KHOANG_COT) && !!ketQua.khoang_cot_id ||
            intersection.includes(LOAI_NOI_DUNG.KIEM_TRA_VI_TRI) && !!ketQua.vi_tri_id;
        });
        if (check) {
          return responseAction.error(res, { message: `${check.vi_tri_id ? t('location_results_available') : t('tower_results_available')}` });
        }
      }
    }
    // !check update loai_noi_dung

    // check delete tieu chi danh gia
    let checkKiemTra = false, checkCaiDat = false, tieuChiError = null;
    const tieuChiData = [];


    for (let i = 0; i < value.tieu_chi.length; i++) {
      const tieuChiItem = value.tieu_chi[i];
      if (Array.isArray(tieuChiItem.chi_tiet)) {
        tieuChiData.push(...tieuChiItem.chi_tiet);
      }
      tieuChiData.push(tieuChiItem);
    }

    for (let i = 0; i < tieuChiData.length; i++) {
      if (tieuChiData[i].delete) {
        let tieuChiId = [tieuChiData[i]._id];
        if (Array.isArray(tieuChiData[i].chi_tiet)) {
          tieuChiId = [...tieuChiId, ...extractIds(tieuChiData[i].chi_tiet)];
        }
        checkKiemTra = await KetQuaKiemTraService.getAll({ tieu_chi_id: tieuChiId }).count();
        if (checkKiemTra) {
          tieuChiError = tieuChiData[i];
          break;
        }

        checkCaiDat = await CaiDatVanHanhService.getOne(
          {
            $or: [
              { tieu_chi_do_tiep_dia: tieuChiId },
              { tieu_chi_do_khoang_cach_pha: tieuChiId },
              { tieu_chi_do_nhiet_do: tieuChiId },
            ],
          },
        );
        if (checkCaiDat) {
          tieuChiError = tieuChiData[i];
          break;
        }
      }
    }
    if (checkKiemTra) {
      return responseAction.error(res, { message: `${t('criteria')} "${tieuChiError?.ten_tieu_chi}" ${t('have_test_result')}` }, 404);
    }
    if (checkCaiDat) {
      return responseAction.error(res, { message: `${t('criteria')} "${tieuChiError?.ten_tieu_chi}" ${t('exist_in_system_setting')}` }, 404);
    }
    // !check delete tieu chi danh gia

    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true }).lean();
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    await createOrUpdate(id, value.tieu_chi);

    data.tieu_chi = await getTieuChiChild(id);
    return responseAction.success(res, data);
  } catch (err) {
    console.log('err', err);
    responseAction.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const { t } = req;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);

    const checkUnique = await Service.count({ ten_noi_dung: value.ten_noi_dung });
    if (checkUnique) {
      return responseHelper.error(res, { message: t('content_name_already_exist') }, 404);
    }

    const data = await Model.create(value);
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    await createOrUpdate(data._id, value.tieu_chi);
    data.tieu_chi = await getTieuChiChild(data._id);
    return responseAction.success(res, data);
  } catch (err) {
    // console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_noi_dung']);
    const { criteria, options } = query;

    let getTieuChi = false;
    if (criteria.get_tieu_chi) {
      getTieuChi = true;
      delete criteria.get_tieu_chi;
    }

    options.sort = { thu_tu: 1 };
    const data = await Model.paginate(criteria, options);

    if (getTieuChi && Array.isArray(data.docs)) {
      const noiDungId = data.docs.map(doc => doc._id);
      const tieuChiAll = await TieuChiModel.find({ noi_dung_kiem_tra_id: { $in: noiDungId }, is_deleted: false })
        .populate('noi_dung_kiem_tra_id')
        .populate('tieu_chi_cha_id')
        .lean();

      const tieuChiParent = tieuChiAll.filter(tieuChi => !tieuChi.tieu_chi_cha_id);
      const tieuChiChild = tieuChiAll.filter(tieuChi => !!tieuChi.tieu_chi_cha_id);

      for (let i = 0; i < data.docs.length; i++) {
        const doc = data.docs[i];
        data.docs[i].tieu_chi_id = tieuChiParent
          .filter(tieuChi => tieuChi.noi_dung_kiem_tra_id._id.toString() === doc._id.toString())
          .map(parent => {
            parent.chi_tiet = tieuChiChild.filter(child => child.tieu_chi_cha_id?._id?.toString() === parent._id.toString());
            return parent;
          });
      }
    }

    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

async function createOrUpdate(noiDungId, danhSachTieuChi) {
  if (Array.isArray(danhSachTieuChi)) {
    for (let i = 0; i < danhSachTieuChi.length; i++) {
      const tieuChi = danhSachTieuChi[i];
      if (tieuChi._id) {
        if (tieuChi.delete) {
          await TieuChiModel.findOneAndUpdate({ _id: tieuChi._id }, { is_deleted: true });
        } else {
          await TieuChiModel.findOneAndUpdate({ _id: tieuChi._id }, tieuChi);
          await createOrUpdate(noiDungId, tieuChi.chi_tiet);
        }
      } else {
        // tao moi khi khong co _id
        tieuChi.noi_dung_kiem_tra_id = noiDungId;
        const dataCreated = await TieuChiModel.create(tieuChi);
        if (dataCreated && Array.isArray(tieuChi.chi_tiet)) {
          for (let j = 0; j < tieuChi.chi_tiet.length; j++) {
            tieuChi.chi_tiet[j].tieu_chi_cha_id = dataCreated._id;
            tieuChi.chi_tiet[j].noi_dung_kiem_tra_id = noiDungId;
            await TieuChiModel.create(tieuChi.chi_tiet[j]);
          }
        }
      }
    }
  }
}

export async function getAllTree(req, res) {
  try {

    const data = await Service.getAll().sort({ ten_noi_dung: 1 });

    const allTieuChi = await TieuChiModel.find({ is_deleted: false })
      .populate('noi_dung_kiem_tra_id')
      .populate('tieu_chi_cha_id')
      .lean();

    const tieuChiParent = allTieuChi.filter(tieuChi => !tieuChi.tieu_chi_cha_id);
    const tieuChiChild = allTieuChi.filter(tieuChi => !!tieuChi.tieu_chi_cha_id);
    for (let i = 0; i < data.length; i++) {
      const doc = data[i];
      data[i].tieu_chi_id = tieuChiParent
        .filter(tieuChi => tieuChi.noi_dung_kiem_tra_id?._id.toString() === doc._id.toString())
        .map(parent => {
          parent.chi_tiet = tieuChiChild.filter(child => child.tieu_chi_cha_id?._id?.toString() === parent._id.toString());
          return parent;
        });
    }
    // tieuChiChild.forEach(tieuChi => {
    //   tieuChi.tieuchicha_id = tieuChi.tieu_chi_cha_id?._id;
    // });
    // const groupTieuChiChild = groupBy(tieuChiChild, 'tieuchicha_id');
    // tieuChiParent.forEach(parent => {
    //   parent.chi_tiet = groupTieuChiChild[parent._id];
    //   parent.noidung_id = parent.noi_dung_kiem_tra_id._id;
    // });
    // const groupTieuChiParent = groupBy(tieuChiParent, 'noidung_id');
    // data.forEach(noiDung => {
    //   noiDung.tieu_chi_id = groupTieuChiParent[noiDung._id];
    // });
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}
