import express from 'express';
import passport from 'passport';
import * as loaiDayCapQuangController from './loaiDayCapQuang.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const loaiDayCapQuangRouter = express.Router();
loaiDayCapQuangRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
loaiDayCapQuangRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
loaiDayCapQuangRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
loaiDayCapQuangRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
loaiDayCapQuangRouter.route('/')
  .get(loaiDayCapQuangController.getAll)
  .post(loaiDayCapQuangController.create);

loaiDayCapQuangRouter
  .route('/:id')
  .get(loaiDayCapQuangController.findOne)
  .delete(loaiDayCapQuangController.remove)
  .put(loaiDayCapQuangController.update);
