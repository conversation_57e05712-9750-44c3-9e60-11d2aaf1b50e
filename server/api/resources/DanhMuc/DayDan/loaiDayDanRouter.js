import express from 'express';
import passport from 'passport';
import * as dayDan<PERSON>ontroller from './dayDan.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const loaiDayDanRouter = express.Router();
loaiDayDanRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
loaiDayDanRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
loaiDayDanRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
loaiDayDanRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
loaiDayDanRouter.route('/')
  .get(dayDanController.getAll)
  .post(dayDanController.create);

loaiDayDanRouter
  .route('/:id')
  .get(dayDanController.findOne)
  .delete(dayDanController.remove)
  .put(dayDanController.update);
