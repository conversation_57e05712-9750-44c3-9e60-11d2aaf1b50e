import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { LOAI_DAY_DAN } from '../../../constant/dbCollections';

const schema = new Schema({
  ten_day_dan: { type: String, required: true, validate: /\S+/ },
  ghi_chu: String,
  thu_tu: { type: Number },
  duong_kinh_day: String,
  duong_kinh_loi: String,
  cau_tao: String,
  trong_luong_day: String,
  luc_chiu_keo: String,
  dien_tro_mot_chieu: String,
  i_max: String,
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

export default mongoose.model(LOAI_DAY_DAN, schema, LOAI_DAY_DAN);
