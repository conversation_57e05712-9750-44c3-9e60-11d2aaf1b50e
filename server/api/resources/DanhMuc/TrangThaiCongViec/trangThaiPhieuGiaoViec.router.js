import express from 'express';
import passport from 'passport';
import * as Controller from './trangThaiPhieuGiaoViec.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const trangThaiPhieuGiaoViec = express.Router();
trangThaiPhieuGiaoViec.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
trangThaiPhieuGiaoViec.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
trangThaiPhieuGiaoViec.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
trangThaiPhieuGiaoViec.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
trangThaiPhieuGiaoViec.route('/')
  .get(Controller.getAll);
