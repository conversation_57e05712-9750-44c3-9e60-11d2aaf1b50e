import * as responseAction from '../../../helpers/responseHelper';
import { TRANG_THAI_PHIEU } from './index';

export async function getAll(req, res) {
  try {
    const { t } = req;
    Object.keys(TRANG_THAI_PHIEU).forEach(typeWork => {
      TRANG_THAI_PHIEU[typeWork]['name'] = t(TRANG_THAI_PHIEU[typeWork]['code']);
    });
    responseAction.success(res, TRANG_THAI_PHIEU);
  } catch (err) {
    responseAction.error(res, err);
  }
}
