import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { LOAI_TIEP_DAT } from '../../../constant/dbCollections';

const schema = new Schema({
  ten_tiep_dia: { type: String, required: true, validate: /\S+/ },
  ghi_chu: String,
  so_tia: String,
  chieu_dai_tia: String,
  vat_lieu_tia: String,
  so_coc: String,
  chieu_dai_coc: String,
  khoang_cach_cac_coc: String,
  vat_lieu_coc: String,
  thu_tu: { type: Number },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

export default mongoose.model(LOAI_TIEP_DAT, schema, LOAI_TIEP_DAT);
