import express from 'express';
import passport from 'passport';
import * as loaiTiepDatController from './loaiTiepDat.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const loaiTiepDatRouter = express.Router();
loaiTiepDatRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
loaiTiepDatRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
loaiTiepDatRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
loaiTiepDatRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
loaiTiepDatRouter.route('/')
  .get(loaiTiepDatController.getAll)
  .post(loaiTiepDatController.create);

loaiTiepDatRouter
  .route('/:id')
  .get(loaiTiepDatController.findOne)
  .delete(loaiTiepDatController.remove)
  .put(loaiTiepDatController.update);
