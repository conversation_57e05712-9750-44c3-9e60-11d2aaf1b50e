import express from 'express';
import passport from 'passport';
import * as loaiMongController from './loaiMong.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const loaiMongRouter = express.Router();
loaiMongRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
loaiMongRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
loaiMongRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
loaiMongRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
loaiMongRouter.route('/')
  .get(loaiMongController.getAll)
  .post(loaiMongController.create);

loaiMongRouter
  .route('/:id')
  .get(loaiMongController.findOne)
  .delete(loaiMongController.remove)
  .put(loaiMongController.update);
