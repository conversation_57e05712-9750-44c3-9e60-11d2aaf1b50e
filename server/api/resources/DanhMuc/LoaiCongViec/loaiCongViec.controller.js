import * as responseAction from '../../../helpers/responseHelper';
import { LOAI_CONG_VIEC } from './index';

export async function getAll(req, res) {
  try {
    const { t } = req;
    Object.keys(LOAI_CONG_VIEC).forEach(typeWork => {
      LOAI_CONG_VIEC[typeWork]['name'] = t(LOAI_CONG_VIEC[typeWork]['code']);
    });
    responseAction.success(res, LOAI_CONG_VIEC);
  } catch (err) {
    responseAction.error(res, err);
  }
}
