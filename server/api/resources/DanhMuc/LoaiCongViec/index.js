function createRecord(code, name, type) {
  return { code, name, type };
}

export const KIEM_TRA = 'KIEM_TRA';
export const SUA_CHUA_BAO_DUONG = 'SUA_CHUA_BAO_DUONG';
export const DO_THONG_SO = 'DO_THONG_SO';
export const CONG_TAC_PHU_TRO = 'CONG_TAC_PHU_TRO';

export const LOAI_CONG_VIEC = {
  KIEM_TRA_DINH_KY_NGAY: createRecord('KIEM_TRA_DINH_KY_NGAY', 'Kiểm tra định kỳ ngày', KIEM_TRA),
  KIEM_TRA_DINH_KY_DEM: createRecord('KIEM_TRA_DINH_KY_DEM', 'Kiểm tra định kỳ đêm', KIEM_TRA),
  KIEM_TRA_SU_CO: createRecord('KIEM_TRA_SU_CO', '<PERSON>ể<PERSON> tra sự cố', KIEM_TRA),
  KIEM_TRA_DOT_XUAT: createRecord('KIEM_TRA_DOT_XUAT', '<PERSON>ểm tra đột xuất', KIEM_TRA),
  KIEM_TRA_KY_THUAT: createRecord('KIEM_TRA_KY_THUAT', 'Kiểm tra kỹ thuật', KIEM_TRA),
  KIEM_TRA_CHUYEN_DE: createRecord('KIEM_TRA_CHUYEN_DE', 'Kiểm tra chuyên đề', KIEM_TRA),

  CO_KE_HOACH: createRecord('CO_KE_HOACH', 'Khắc phục tồn tại lưới điện', SUA_CHUA_BAO_DUONG),
  KHONG_CO_KE_HOACH: createRecord('KHONG_CO_KE_HOACH', 'Duy tu, bảo dưỡng tăng cường', SUA_CHUA_BAO_DUONG),
  DUY_TU_KET_HOP_KHAC_PHUC: createRecord('DUY_TU_KET_HOP_KHAC_PHUC', 'Sửa chữa bảo dưỡng kết hợp', SUA_CHUA_BAO_DUONG),

  DO_DIEN_TRO_TIEP_DIA: createRecord('DO_DIEN_TRO_TIEP_DIA', 'Đo điện trở tiếp địa', DO_THONG_SO),
  DO_NHIET_DO_TIEP_XUC: createRecord('DO_NHIET_DO_TIEP_XUC', 'Đo nhiệt độ tiếp xúc', DO_THONG_SO),
  DO_KHOANG_CACH_PHA_DAT: createRecord('DO_KHOANG_CACH_PHA_DAT', 'Đo khoảng cách pha đất', DO_THONG_SO),
  DO_COROCAM_CACH_DIEN: createRecord('DO_COROCAM_CACH_DIEN', 'Đo Corocam cách điện', DO_THONG_SO),
  DO_NHIET_DO_CACH_DIEN_COMPOSITE: createRecord('DO_NHIET_DO_CACH_DIEN_COMPOSITE', 'Đo nhiệt độ cách điện Composite', DO_THONG_SO),
  DO_CUONG_DO_DIEN_TRUONG: createRecord('DO_CUONG_DO_DIEN_TRUONG', 'Đo cường độ điện trường', DO_THONG_SO),
  DO_KHOANG_CACH_GIAO_CHEO: createRecord('DO_KHOANG_CACH_GIAO_CHEO', 'Đo khoảng cách giao chéo', DO_THONG_SO),

  CONG_TAC_PHU_TRO: createRecord('CONG_TAC_PHU_TRO', 'Công tác phụ trợ', CONG_TAC_PHU_TRO),
};

export const NHOM_CONG_VIEC = {
  KIEM_TRA: { code: 'KIEM_TRA', name: 'Kiểm tra' },
  SUA_CHUA_BAO_DUONG: { code: 'SUA_CHUA_BAO_DUONG', name: 'Sửa chữa bảo dưỡng' },
  DO_THONG_SO: { code: 'DO_THONG_SO', name: 'Đo thông số' },
  CONG_TAC_PHU_TRO: { code: 'CONG_TAC_PHU_TRO', name: 'Công tác phụ trợ' },
};


export const LOAI_CONG_VIEC_SUA_CHUA = {
  SUA_CHUA_VI_TRI: { code: 'SUA_CHUA_VI_TRI', label: 'Sửa chữa vị trí' },
  SUA_CHUA_KHOANG_COT: { code: 'SUA_CHUA_KHOANG_COT', label: 'Sửa chữa khoảng cột' },
  CONG_VIEC_KHAC: { code: 'CONG_VIEC_KHAC', label: 'Công việc khác' },
};

export function isNeedSafeSolution(loaiCongViec) {
  switch (loaiCongViec) {
    case LOAI_CONG_VIEC.CO_KE_HOACH.code:
    case LOAI_CONG_VIEC.KHONG_CO_KE_HOACH.code:
    case LOAI_CONG_VIEC.DUY_TU_KET_HOP_KHAC_PHUC.code:
      return true;
    default:
      return false;
  }
}

export function isNeedSafeCondition(loaiCongViec) {
  switch (loaiCongViec) {
    case LOAI_CONG_VIEC.CO_KE_HOACH.code:
    case LOAI_CONG_VIEC.KHONG_CO_KE_HOACH.code:
    case LOAI_CONG_VIEC.DUY_TU_KET_HOP_KHAC_PHUC.code:
      return true;
    default:
      return false;
  }
}

export function isNeedCorridorOrLocation(loaiCongViec) {
  switch (loaiCongViec) {
    case LOAI_CONG_VIEC.KIEM_TRA_DINH_KY_NGAY.code:
    case LOAI_CONG_VIEC.KIEM_TRA_DINH_KY_DEM.code:
    case LOAI_CONG_VIEC.KIEM_TRA_SU_CO.code:
    case LOAI_CONG_VIEC.KIEM_TRA_DOT_XUAT.code:
    case LOAI_CONG_VIEC.KIEM_TRA_KY_THUAT.code:
    case LOAI_CONG_VIEC.KIEM_TRA_CHUYEN_DE.code:
    case LOAI_CONG_VIEC.KHONG_CO_KE_HOACH.code:
    case LOAI_CONG_VIEC.DO_NHIET_DO_TIEP_XUC.code:
      return true;
    default:
      return false;
  }
}

export function isNeedCorridor(loaiCongViec) {
  switch (loaiCongViec) {
    case LOAI_CONG_VIEC.DO_KHOANG_CACH_PHA_DAT.code:
      return true;
    default:
      return false;
  }
}

export function isNeedLocation(loaiCongViec) {
  switch (loaiCongViec) {
    case LOAI_CONG_VIEC.DO_DIEN_TRO_TIEP_DIA.code:
      return true;
    default:
      return false;
  }
}

export function checkRequiredPowerLine(loaiCongViec) {
  switch (loaiCongViec) {
    case LOAI_CONG_VIEC.KIEM_TRA_DINH_KY_NGAY.code:
    case LOAI_CONG_VIEC.KIEM_TRA_DINH_KY_DEM.code:
    case LOAI_CONG_VIEC.KIEM_TRA_SU_CO.code:
    case LOAI_CONG_VIEC.KIEM_TRA_DOT_XUAT.code:
    case LOAI_CONG_VIEC.KIEM_TRA_KY_THUAT.code:
    case LOAI_CONG_VIEC.KIEM_TRA_CHUYEN_DE.code:
    case LOAI_CONG_VIEC.KHONG_CO_KE_HOACH.code:
    case LOAI_CONG_VIEC.CO_KE_HOACH.code:
    case LOAI_CONG_VIEC.DUY_TU_KET_HOP_KHAC_PHUC.code:
    case LOAI_CONG_VIEC.DO_DIEN_TRO_TIEP_DIA.code:
    case LOAI_CONG_VIEC.DO_NHIET_DO_TIEP_XUC.code:
    case LOAI_CONG_VIEC.DO_KHOANG_CACH_PHA_DAT.code:
    case LOAI_CONG_VIEC.DO_COROCAM_CACH_DIEN.code:
    case LOAI_CONG_VIEC.DO_NHIET_DO_CACH_DIEN_COMPOSITE.code:
    case LOAI_CONG_VIEC.DO_CUONG_DO_DIEN_TRUONG.code:
      return true;
    default:
      return false;
  }
}
