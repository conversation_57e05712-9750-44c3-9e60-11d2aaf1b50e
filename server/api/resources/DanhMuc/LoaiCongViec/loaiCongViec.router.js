import express from 'express';
import passport from 'passport';
import * as Controller from './loaiCongViec.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const loaiCongViecRouter = express.Router();
loaiCongViecRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
loaiCongViecRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
loaiCongViecRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
loaiCongViecRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
loaiCongViecRouter.route('/')
  .get(Controller.getAll);
