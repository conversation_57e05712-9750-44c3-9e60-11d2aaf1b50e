import express from 'express';
import passport from 'passport';
import * as hangMucController from './hangMuc.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const hangMucRouter = express.Router();
hangMucRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
hangMucRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
hangMucRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
hangMucRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
hangMucRouter
  .route('/')
  .get(hangMucController.getAll)
  .post(hangMucController.create);

hangMucRouter
  .route('/:id')
  .get(hangMucController.findOne)
  .delete(hangMucController.remove)
  .put(hangMucController.update);
