import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { HANG_MUC } from '../../../constant/dbCollections';

const schema = new Schema({
  ten_hang_muc: { type: String, required: true, validate: /\S+/ },
  ma_hang_muc: { type: String, required: true },
  thu_tu: { type: Number },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(HANG_MUC, schema, HANG_MUC);
