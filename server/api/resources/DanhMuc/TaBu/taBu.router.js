import express from 'express';
import passport from 'passport';
import * as taBuController from './taBu.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const taBuRouter = express.Router();
taBuRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
taBuRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
taBuRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
taBuRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
taBuRouter.route('/')
  .get(taBuController.getAll)
  .post(taBuController.create);

taBuRouter
  .route('/:id')
  .get(taBuController.findOne)
  .delete(taBuController.remove)
  .put(taBuController.update);
