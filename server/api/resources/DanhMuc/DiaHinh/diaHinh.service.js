import * as ValidatorHelper from '../../../helpers/validatorHelper';
import DIA_HINH from './diaHinh.model';

const Joi = require('joi');

const objSchema = Joi.object({});

export function getAll(query, projection = {}) {
  return DIA_HINH.find(query, projection).lean();
}

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
