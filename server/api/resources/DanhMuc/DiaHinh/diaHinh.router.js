import express from 'express';
import passport from 'passport';
import * as diaHinhController from './diaHinh.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const diaHinhRouter = express.Router();
diaHinhRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
diaHinhRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
diaHinhRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
diaHinhRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
diaHinhRouter.route('/')
  .get(diaHinhController.getAll)
  .post(diaHinhController.create);

diaHinhRouter
  .route('/:id')
  .get(diaHinhController.findOne)
  .delete(diaHinhController.remove)
  .put(diaHinhController.update);
