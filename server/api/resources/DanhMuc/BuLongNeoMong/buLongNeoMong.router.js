import express from 'express';
import passport from 'passport';
import * as tiepDiaController from './buLongNeoMong.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import DanhMucPermission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const buLongNeoMongRouter = express.Router();
buLongNeoMongRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
buLongNeoMongRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
buLongNeoMongRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
buLongNeoMongRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
buLongNeoMongRouter.route('/')
  .get(tiepDiaController.getAll)
  .post(tiepDiaController.create);

buLongNeoMongRouter
  .route('/:id')
  .get(tiepDiaController.findOne)
  .delete(tiepDiaController.remove)
  .put(tiepDiaController.update);
