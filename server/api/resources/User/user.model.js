import mongoose from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DON_VI, ROLE, USER } from '../../constant/dbCollections';
import userService from './user.service';
import { LOAI_NGUOI_DUNG } from './LoaiNguoiDung';
import { LOAI_TAI_KHOAN } from './LoaiTaiKhoan';

const { Schema } = mongoose;
const userSchema = new Schema({
  loai_tai_khoan: {
    type: String,
    enum: Object.keys(LOAI_TAI_KHOAN),
    default: LOAI_TAI_KHOAN.TAI_KHOAN_HE_THONG.code,
  },
  loai_nguoi_dung: {
    type: String,
    enum: Object.keys(LOAI_NGUOI_DUNG),
    default: LOAI_NGUOI_DUNG.DON_VI_DANG_KY.code,
  },
  full_name: { type: String, required: true },
  name: { type: String },
  email: { type: String, required: true },
  username: { type: String, unique: true },
  password: { type: String },
  gender: { type: String },
  phone: { type: String },
  avatar: { type: String },
  alias: { type: String },
  chu_ky_id: { type: String, require: true },
  is_deleted: { type: Boolean, default: false, select: false },
  role_id: [{ type: Schema.Types.ObjectId, ref: ROLE }],
  permissions: [{ type: String }],
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  is_system_admin: { type: Boolean, default: false },
  bac_an_toan: { type: String },
  active: { type: Boolean, default: true },
  login_tfa: { type: Boolean, default: false },
  times_wrong_pass: { type: Number, default: 0 },
  never_login: { type: Boolean, default: true },
  last_login: { type: Date },
  last_change_password: { type: Date, default: new Date() },
  device_tokens: [],
  chuc_vu: String,
}, {
  timestamps: {
    createdAt: 'created_at', updatedAt: 'updated_at',
  }, collation: { locale: 'vi' }, versionKey: false,
});

userSchema.pre('save', function(next) {
  let user = this;
  user = formatName(user);
  // only hash the password if it has been modified (or is new)
  if (!user.isModified('password')) return next();
  user.password = userService.encryptPassword(user.password);
  next();
});

userSchema.pre('findOneAndUpdate', function(next) {
  this._update = formatName(this._update);
  next();
});

function formatName(dataInput) {
  const nameArr = dataInput.full_name?.split(' ');
  if (Array.isArray(nameArr) && nameArr.length) {
    dataInput.name = nameArr[nameArr.length - 1];
  }
  return dataInput;
}

// Optimized indexes for User queries
// Using background: true for better performance during index creation

// Primary index for don_vi_id filtering (most common in populate queries)
userSchema.index({ 
  don_vi_id: 1, 
  is_deleted: 1,
  active: 1
}, { 
  background: true,
  name: 'idx_don_vi_deleted_active'
});

// Index for full_name searches (used in populate select)
userSchema.index({ 
  full_name: 1, 
  don_vi_id: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_full_name_don_vi_deleted'
});

// Index for username queries
userSchema.index({ 
  username: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_username_deleted'
});

// Index for role-based queries
userSchema.index({ 
  role_id: 1, 
  don_vi_id: 1,
  active: 1
}, { 
  background: true,
  name: 'idx_role_don_vi_active'
});

// Index for authentication queries
userSchema.index({ 
  email: 1, 
  active: 1
}, { 
  background: true,
  name: 'idx_email_active'
});

// Index for soft delete with time sorting
userSchema.index({ 
  is_deleted: 1, 
  created_at: -1
}, { 
  background: true,
  name: 'idx_deleted_time'
});

// Index for bac_an_toan filtering (used in populate select)
userSchema.index({ 
  bac_an_toan: 1, 
  don_vi_id: 1
}, { 
  background: true,
  name: 'idx_bac_an_toan_don_vi'
});

// Index for JWT authentication in passport-jwt middleware
// Optimizes query: { _id: payload.id, is_deleted: false, active: true }
userSchema.index({ 
  _id: 1, 
  is_deleted: 1,
  active: 1
}, { 
  background: true,
  name: 'idx_id_deleted_active_auth'
});

// Index for password change validation in JWT auth
// Supports checking last_change_password against token iat
userSchema.index({ 
  _id: 1, 
  last_change_password: 1,
  is_deleted: 1,
  active: 1
}, { 
  background: true,
  name: 'idx_id_password_change_auth'
});

// Index for login attempts and account locking
userSchema.index({ 
  username: 1, 
  times_wrong_pass: 1,
  active: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_username_login_attempts'
});

userSchema.plugin(mongoosePaginate);

export default mongoose.model(USER, userSchema, USER);
