import userService from './user.service';
import { extractIds, groupBy } from '../../utils/dataconverter';

// import * as LoaiTaiLieuService from '../DanhMuc/LoaiTaiLieu/loaiTaiLieu.service';
import * as AnhViTriService from '../AnhViTri/anhViTri.service';
import * as CaiDatAppService from '../CaiDatMaApp/caiDatMaApp.service';
import * as LichSuDoNhietDoService from '../KetQuaDoNhietDo/LichSuDoNhietDo/lichSuDoNhietDo.service';
import * as KetQuaDoNhietDoService from '../KetQuaDoNhietDo/ketQuaDoNhietDo.service';
import * as NotificationService from '../Notification/notification.service';
import * as AnhTonTaiService from '../QuanLyTonTaiCongTrinhXayDung/AnhTonTai/anhTonTai.service';
import * as BatThuongPhatHienService from '../QuanLyVanHanh/BatThuongPhatHien/batThuongPhatHien.service';
import * as CongViecPhatSinhService from '../QuanLyVanHanh/CongViecPhatSinh/congViecPhatSinh.service';
import * as DoDienTroService from '../QuanLyVanHanh/DoDienTro/doDienTro.service';
import * as KetQuaDoKhoangCachPhaDatService
  from '../QuanLyVanHanh/KetQuaDoKhoangCachPhaDat/ketQuaDoKhoangCachPhaDat.service';
import * as KetQuaKiemTraService from '../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.service';
import * as KetQuaSuaChuaCoKeHoachService from '../QuanLyVanHanh/KetQuaSuaChuaCoKeHoach/ketQuaSuaChuaCoKeHoach.service';
import * as KetQuaSuaChuaKhongKeHoachService
  from '../QuanLyVanHanh/KetQuaSuaChuaKhongKeHoach/ketQuaSuaChuaKhongKeHoach.service';
import * as NguoiCongTacService from '../QuanLyVanHanh/NguoiCongTac/nguoiCongTac.service';
import * as BienPhapAnToanBoSungService
  from '../QuanLyVanHanh/PhieuCongTac/BienPhapAnToanBoSung/bienPhapAnToanBoSung.service';
import * as PhieuCongTacService from '../QuanLyVanHanh/PhieuCongTac/phieuCongTac.service';
import * as HieuChinhTonTaiService from '../QuanLyVanHanh/PhieuGiaoViec/HieuChinhTonTai/hieuChinhTonTai.service';
import * as TonTaiCapTrenService from '../QuanLyVanHanh/PhieuGiaoViec/TonTaiCapTren/tonTaiCapTren.service';
import * as PhieuGiaoViecService from '../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';
import * as PhieuMauService from '../QuanLyVanHanh/PhieuMau/phieuMau.service';
import * as ThietBiPhatHienService from '../QuanLyVanHanh/ThietBiPhatHien/thietBiPhatHien.service';
import * as RefreshTokenService from '../RefreshToken/refreshToken.service';
import * as RemindService from '../Remind/remind.service';
import * as ReportDoDienTroService from '../Report/ReportDoDienTro/doDienTro.service';
import * as BaoCaoDoNhietDoService from '../Report/ReportDoNhietDo/doNhietDo.service';
import * as ReportDoPhaDatService from '../Report/ReportDoPhaDat/doPhaDat.service';
import * as ServerLogService from '../Log/ServerLog/serverLog.service';
import * as HanhLangTuyenService from '../TongKe/HanhLangTuyen/hanhLangTuyen.service';
import * as EventsService from '../UserTrackings/Events/events.service';
import * as VisitsService from '../UserTrackings/Visits/visits.service';
import UserService from './user.service';

const MODEL_RELATE = [
  { service: AnhViTriService, name: 'AnhViTri', fields: ['nguoi_tao', 'nguoi_chinh_sua'] },
  { service: CaiDatAppService, name: 'CaiDatApp', fields: ['user_cai_dat'] },
  { service: LichSuDoNhietDoService, name: 'LichSuDoNhietDo', fields: ['user_id'] },
  { service: KetQuaDoNhietDoService, name: 'KetQuaDoNhietDo', fields: ['nguoi_do'] },
  { service: NotificationService, name: 'Notification', fields: ['user_id'] },
  { service: AnhTonTaiService, name: 'AnhTonTai', fields: ['nguoi_tao', 'nguoi_chinh_sua'] },
  { service: BatThuongPhatHienService, name: 'BatThuongPhatHien', fields: ['nguoi_tao', 'nguoi_chinh_sua'] },
  { service: CongViecPhatSinhService, name: 'CongViecPhatSinh', fields: ['nguoi_tao', 'nguoi_chinh_sua'] },
  { service: DoDienTroService, name: 'DoDienTro', fields: ['nguoi_tao_id', 'nguoi_chinh_sua_id'] },
  {
    service: KetQuaDoKhoangCachPhaDatService,
    name: 'KetQuaDoKhoangCachPhaDat',
    fields: ['nguoi_tao', 'nguoi_chinh_sua'],
  },
  { service: KetQuaKiemTraService, name: 'KetQuaKiemTra', fields: ['nguoi_tao', 'nguoi_chinh_sua'] },
  { service: KetQuaSuaChuaCoKeHoachService, name: 'KetQuaSuaChuaCoKeHoach', fields: ['nguoi_tao', 'nguoi_chinh_sua'] },
  {
    service: KetQuaSuaChuaKhongKeHoachService,
    name: 'KetQuaSuaChuaKhongKeHoach',
    fields: ['nguoi_tao', 'nguoi_chinh_sua'],
  },
  { service: NguoiCongTacService, name: 'NguoiCongTac', fields: ['user_id', 'nguoi_tao', 'nguoi_chinh_sua'] },
  { service: BienPhapAnToanBoSungService, name: 'BienPhapAnToanBoSung', fields: ['nguoi_tao', 'nguoi_chinh_sua'] },
  {
    service: PhieuCongTacService,
    name: 'PhieuCongTac',
    fields: ['lanh_dao_cong_viec_id', 'giam_sat_an_toan_id', 'nguoi_cho_phep_id', 'nguoi_cap_phieu_id', 'nguoi_huy_phieu_id'],
  },
  { service: HieuChinhTonTaiService, name: 'HieuChinhTonTai', fields: ['nguoi_tao', 'nguoi_chinh_sua'] },
  { service: TonTaiCapTrenService, name: 'TonTaiCapTren', fields: ['nguoi_tao_id'] },
  {
    service: PhieuGiaoViecService,
    name: 'PhieuGiaoViec',
    fields: ['nguoi_cap_phieu_id', 'chi_huy_truc_tiep_id', 'nguoi_giao_phieu_id', 'nguoi_tiep_nhan_id', 'nguoi_khoa_phieu_id', 'nguoi_xac_nhan_khoa_id', 'nguoi_cho_phep_id', 'created_by', 'updated_by'],
  },
  { service: PhieuMauService, name: 'PhieuMau', fields: ['nguoi_tao_id', 'nguoi_chinh_sua'] },
  { service: ThietBiPhatHienService, name: 'ThietBiPhatHien', fields: ['nguoi_tao', 'nguoi_chinh_sua'] },
  { service: RefreshTokenService, name: 'RefreshToken', fields: ['user_id'] },
  { service: RemindService, name: 'Remind', fields: ['user_id'] },
  { service: ReportDoDienTroService, name: 'ReportDoDienTro', fields: ['nguoi_tao_id', 'nguoi_chinh_sua_id'] },
  { service: BaoCaoDoNhietDoService, name: 'BaoCaoDoNhietDo', fields: ['nguoi_do', 'nguoi_chinh_sua_id'] },
  { service: ReportDoPhaDatService, name: 'ReportDoPhaDat', fields: ['nguoi_tao', 'nguoi_chinh_sua_id'] },
  { service: ServerLogService, name: 'ServerLog', fields: ['req_user_id'] },
  { service: HanhLangTuyenService, name: 'HanhLangTuyen', fields: ['nguoi_chinh_sua'] },
  { service: EventsService, name: 'Events', fields: ['user_id'] },
  { service: VisitsService, name: 'Visits', fields: ['user_id'] },
];


export async function handleRemoveDuplicates(removeUser = false) {
  const allUser = await userService.getAll({ is_deleted: false }, { username: 1, full_name: 1 });
  const userGroup = groupBy(allUser, 'username');

  const userDuplicates = [];
  const recordWillUpdate = [];

  Object.entries(userGroup).forEach(([key, value]) => {
    if (value?.length > 1) {
      userDuplicates.push({
        retain: value[0]._id,
        remove: extractIds(value.slice(1)),
      });
    }
  });

  for (let i = 0; i < userDuplicates.length; i++) {
    const duplicate = userDuplicates[i];
    for (let j = 0; j < duplicate.remove.length; j++) {
      const willUpdate = await updateRecord(duplicate.remove[j], duplicate.retain);
      recordWillUpdate.push(willUpdate);
    }
  }

  const dataReturn = { userDuplicates, recordWillUpdate };

  if (removeUser) {
    const userIdRemove = userDuplicates.map(user => user.remove).flat();
    dataReturn.userRemoved = await UserService.remove({ _id: userIdRemove });
  }

  return dataReturn;
}


async function updateRecord(removeId, retainId) {
  const recordReturn = [];
  for (let i = 0; i < MODEL_RELATE.length; i++) {
    const model = MODEL_RELATE[i];
    const projection = {};
    model.fields.forEach(field => {
      projection[field] = 1;
    });

    const currentData = await model.service.getAll(
      {
        '$or': model.fields.map(field => {
          return { [field]: removeId };
        }),
      },
      projection,
    );

    if (currentData.length) {
      currentData.forEach(current => {
        Object.keys(current).forEach(key => {
          if (!model.fields.includes(key) && key !== '_id') delete current[key];
        });

        model.fields.forEach(field => {
          if (current[field]?.hasOwnProperty('_id')) {
            current[field] = current[field]._id;
          }

          if (current[field]?.toString() === removeId?.toString()) {
            current[field] = retainId;
          }
        });

      });

      await model.service.updateAll(currentData);

      recordReturn.push({
        name: model.name,
        data: currentData,
      });
    }
  }

  return recordReturn.flat();
}
