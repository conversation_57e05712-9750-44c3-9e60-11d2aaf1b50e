import express from 'express';
import passport from 'passport';
import * as Controller from './hoSoThietBi.controller';
import { checkTempFolder, multipartMiddleware } from '../../utils/fileUtils';
import { authorizationMiddleware } from '../RBAC/middleware';
import HoSoPermission from '../RBAC/permissions/HoSoPermission';
import { loggerMiddleware } from '../../logs/middleware';

export const hoSoThietBiRouter = express.Router();

hoSoThietBiRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
hoSoThietBiRouter.post('*', authorizationMiddleware([HoSoPermission.CREATE]));
hoSoThietBiRouter.put('*', authorizationMiddleware([HoSoPermission.UPDATE]));
hoSoThietBiRouter.delete('*', authorizationMiddleware([HoSoPermission.DELETE]));
hoSoThietBiRouter.get('*', authorizationMiddleware([HoSoPermission.READ]));

hoSoThietBiRouter
  .route('/')
  .get(Controller.getAll)
  .post(checkTempFolder, multipartMiddleware, Controller.createHoSoThietBi)
  .put(checkTempFolder, multipartMiddleware, Controller.updateHoSoThietBi);

hoSoThietBiRouter
  .route('/tenthietbi')
  .get(Controller.getAllByTenThietBi);

hoSoThietBiRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.removeHoSoThietBi);


