import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { HO_SO_THIET_BI } from '../../constant/dbCollections';

const schema = new Schema({
  file_id: { type: String },
  file_name: { type: String },
  loai_thiet_bi: { type: String },
  ten_thiet_bi: { type: String },
  loai_ho_so: { type: String },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(HO_SO_THIET_BI, schema, HO_SO_THIET_BI);
