import * as responseHelper from '../../helpers/responseHelper';
import * as controllerHelper from '../../helpers/controllerHelper';
import * as fileUtils from '../../utils/fileUtils';
import { extractIds } from '../../utils/dataconverter';
import { getFileSizeInBytes } from '../../common/functionCommons';

import * as Service from './hoSoThietBi.service';
import Model from './hoSoThietBi.model';

import HoSoModel from '../QuanLyVanHanh/HoSo/hoSo.model';
import LoaiTaiLieuModel from '../TaiLieu/taiLieu.model';


const searchLikes = ['loai_thiet_bi', 'ten_thiet_bi', 'loai_ho_so'];
const populateOpts = [];
const sortOpts = { loai_thiet_bi: 1, ten_thiet_bi: 1, loai_ho_so: 1, created_at: 1 };

export const findOne = controllerHelper.createFindOneFunction(Model);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service);
export const create = controllerHelper.createCreateFunction(Model, Service);
export const getAll = controllerHelper.createGetAllFunction(Model, searchLikes, populateOpts, sortOpts);

export async function removeHoSoThietBi(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseHelper.error(res, '', 400);
    }
    if (data.file_id) {
      fileUtils.deleteFile(fileUtils.getFilePath(data.file_id));
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function updateHoSoThietBi(req, res) {
  try {
    let { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    const dataAfterUpdate = await Model.findOne({ _id: value._id });

    const filePath = req.files?.file?.path;
    if (filePath) {
      const file_id = fileUtils.createUniqueFileName(filePath);
      await fileUtils.createByName(filePath, file_id);
      value.file_id = file_id;
      value.file_name = req.files?.file?.name;

      dataAfterUpdate.file_id && fileUtils.deleteFile(fileUtils.getFilePath(dataAfterUpdate.file_id));
    }


    const data = await Model.findOneAndUpdate({ _id: value._id }, value, { new: true });
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function createHoSoThietBi(req, res) {
  try {
    let value = req.body;
    const filePath = req.files?.file?.path;
    const file_id = fileUtils.createUniqueFileName(filePath);
    await fileUtils.createByName(filePath, file_id);
    value.file_id = file_id;
    value.file_name = req.files?.file?.name;
    const data = await Model.create(value);
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseHelper.error(res, err, 500);
  }
}

export async function getAllByTenThietBi(req, res) {
  try {
    let { ten_thiet_bi, vi_tri_id, device_code } = req.query;
    const [hoSoThietBi, hoSo] = await Promise.all([
      Model.find({ ten_thiet_bi, is_deleted: false }),
      HoSoModel.find({ ten_thiet_bi: device_code, is_deleted: false }),
    ]);
    const hoSoId = extractIds(hoSo);
    const allTaiLieu = await LoaiTaiLieuModel.find({ vi_tri_id, ho_so_id: hoSoId, is_deleted: false });
    const listTaiLieu = allTaiLieu.map((item) => {
      let size = 0;
      const path = fileUtils.getAndCheckExistFilePath(item?.file_id);
      if (path) {
        size = getFileSizeInBytes(path) / (1024 * 1000);
      }
      return {
        _id: item._id,
        ho_so_id: item.ho_so_id,
        file_id: item.file_id,
        file_name: item.file_name,
        created_at: item.created_at,
        ten_thiet_bi,
        size,
      };
    });
    return responseHelper.success(res, [...hoSoThietBi, ...listTaiLieu]);
  } catch (err) {
    console.error(err);
    return responseHelper.error(res, err, 500);
  }
}
