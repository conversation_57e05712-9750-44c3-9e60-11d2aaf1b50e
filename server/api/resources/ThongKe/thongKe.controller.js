import mongoose from 'mongoose';
import * as responseHelper from '../../helpers/responseHelper';
import queryHelper from '../../helpers/queryHelper';
import { convertObject, extractIds, extractKeys, groupBy } from '../../utils/dataconverter';
import CommonError from '../../error/CommonError';

import quanLyVanHanhCommons from '../QuanLyVanHanh/quanLyVanHanhCommons';

import * as ViTriService from '../TongKe/ViTri/viTri.service';
import * as DonViService from '../DonVi/donVi.service';
import * as PhieuGiaoViecService from '../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';
import * as CongViecPhatSinhService from '../QuanLyVanHanh/CongViecPhatSinh/congViecPhatSinh.service';
import * as KetQuaSuaChuaCoKeHoachService from '../QuanLyVanHanh/KetQuaSuaChuaCoKeHoach/ketQuaSuaChuaCoKeHoach.service';
import * as KetQuaSuaChuaKhongKeHoachService
  from '../QuanLyVanHanh/KetQuaSuaChuaKhongKeHoach/ketQuaSuaChuaKhongKeHoach.service';
import * as DuongDayService from '../TongKe/DuongDay/duongDay.service';
import * as VanHanhService from '../TongKe/VanHanh/vanHanh.service';
import * as ViTriCongViecService from '../QuanLyVanHanh/ViTriCongViec/viTriCongViec.service';
import * as CongViecPhuTroService from '../QuanLyVanHanh/CongViecPhuTro/congViecPhuTro.service';

import { DANH_MUC_CONG_VIEC, KHOANG_COT, VI_TRI } from '../../constant/dbCollections';
import { LOAI_VI_TRI } from '../TongKe/ViTri/viTri.model';
import { CONG_TAC_PHU_TRO, DO_THONG_SO, KIEM_TRA, LOAI_CONG_VIEC, SUA_CHUA_BAO_DUONG } from '../DanhMuc/LoaiCongViec';
import { TRANG_THAI_PHIEU } from '../DanhMuc/TrangThaiCongViec';
import { TRANG_THAI_THUC_HIEN, TRANG_THAI_XU_LY } from '../DanhMuc/TrangThaiXuLy';
import { formatUnique } from '../../common/functionCommons';
import { TRANG_THAI_HOAN_THANH } from '../DanhMuc/TrangThaiHoanThanh';
import SuaChuaCoKeHoachModel from '../QuanLyVanHanh/KetQuaSuaChuaCoKeHoach/ketQuaSuaChuaCoKeHoach.model';
import ViTriCongViecModel from '../QuanLyVanHanh/ViTriCongViec/viTriCongViec.model';
import CongViecPhuTroModel from '../QuanLyVanHanh/CongViecPhuTro/congViecPhuTro.model';

const { Types } = mongoose;

export async function baoCaoGiamThieuSuCo(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;

    criteria.don_vi_giao_phieu_id = await DonViService.getDonViQuery(req, criteria.don_vi_giao_phieu_id, true);
    criteria = quanLyVanHanhCommons.buildThoiGianCongTacQuery(criteria);

    const allViTri = await ViTriService.getAll({
      don_vi_id: criteria.don_vi_giao_phieu_id,
      loai_vi_tri: { $ne: LOAI_VI_TRI.XA_POOCTIC },
      is_deleted: false,
    });

    const allDuongDay = await quanLyVanHanhCommons.getDuongDayByViTri(allViTri, { ten_duong_day: 1 });
    const duongDayIds = extractIds(allDuongDay);
    allDuongDay.forEach(duongDay => {
      duongDay.khac_phuc_ton_tai = 0;
      duongDay.duy_tu_bao_duong = 0;
      duongDay.cong_viec_phat_sinh = 0;
    });

    const loaiCongViecSuaChua = Object.values(LOAI_CONG_VIEC)
      .filter(item => item.type === SUA_CHUA_BAO_DUONG)
      .map(item => item.code);

    criteria.duong_day_ids = { $in: duongDayIds };
    criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code;

    const allPhieuGiaoViec = await PhieuGiaoViecService.getAll(criteria, { duong_day_ids: 1, loai_cong_viec: 1 });
    const allPhieuId = extractIds(allPhieuGiaoViec);
    const phieuGroupByType = groupBy(allPhieuGiaoViec, 'loai_cong_viec');

    const allPhieuSuaChua = loaiCongViecSuaChua.flatMap(loaiCv => phieuGroupByType[loaiCv]);
    const phieuSuaChuaId = extractIds(allPhieuSuaChua);

    const [kqCoKeHoach, kqKoKeHoach, cvPhatSinh] = await Promise.all([
      KetQuaSuaChuaCoKeHoachService
        .getAll(
          { phieu_giao_viec_id: phieuSuaChuaId, tinh_trang_xu_ly: TRANG_THAI_XU_LY.DA_XU_LY.code, is_deleted: false },
          { ton_tai_id: 1 },
        )
        .populate([{ path: 'ton_tai_id', select: 'duong_day_id' }]),
      KetQuaSuaChuaKhongKeHoachService.getAll(
        { phieu_giao_viec_id: phieuSuaChuaId, is_deleted: false },
        { duong_day_id: 1 },
      ),
      CongViecPhatSinhService.getAll(
        { phieu_giao_viec_id: allPhieuId, is_deleted: false },
        { duong_day_id: 1 },
      ),
    ]);

    const duongDayObj = convertObject(allDuongDay, '_id');

    kqCoKeHoach.forEach(kq => {
      if (Array.isArray(kq.ton_tai_id?.duong_day_id)) {
        kq.ton_tai_id.duong_day_id.forEach(duongDayId => {
          if (duongDayObj[duongDayId]) {
            duongDayObj[duongDayId].khac_phuc_ton_tai += 1;
          }
        });
      }
    });

    kqKoKeHoach.forEach(kq => {
      if (Array.isArray(kq.duong_day_id)) {
        kq.duong_day_id.forEach(duongDayId => {
          if (duongDayObj[duongDayId]) {
            duongDayObj[duongDayId].duy_tu_bao_duong += 1;
          }
        });
      }
    });

    cvPhatSinh.forEach(cv => {
      if (duongDayObj[cv?.duong_day_id]) {
        duongDayObj[cv?.duong_day_id].cong_viec_phat_sinh += 1;
      }
    });

    return responseHelper.success(res, Object.values(duongDayObj));
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function baoCaoGiamThieuSuCoByDuongDay(req, res) {
  try {
    const { id } = req.params;
    const duongDayCurrent = await DuongDayService.getOne({ _id: id, is_deleted: false });

    if (!duongDayCurrent) return responseHelper.error(res, CommonError.NOT_FOUND);

    duongDayCurrent.khac_phuc_ton_tai = 0;
    duongDayCurrent.duy_tu_bao_duong = 0;
    duongDayCurrent.cong_viec_phat_sinh = 0;


    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;

    criteria.don_vi_giao_phieu_id = await DonViService.getDonViQuery(req, criteria.don_vi_giao_phieu_id, true);
    criteria = quanLyVanHanhCommons.buildThoiGianCongTacQuery(criteria);

    const loaiCongViecSuaChua = Object.values(LOAI_CONG_VIEC)
      .filter(item => item.type === SUA_CHUA_BAO_DUONG)
      .map(item => item.code);

    criteria.duong_day_ids = duongDayCurrent._id;
    criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code;

    const allPhieuGiaoViec = await PhieuGiaoViecService.getAll(criteria, { duong_day_ids: 1, loai_cong_viec: 1 });
    const phieuGroupByType = groupBy(allPhieuGiaoViec, 'loai_cong_viec');

    const allPhieuSuaChua = loaiCongViecSuaChua.flatMap(loaiCv => phieuGroupByType[loaiCv]);
    const phieuSuaChuaId = extractIds(allPhieuSuaChua);

    const [kqCoKeHoach, kqKoKeHoach, cvPhatSinh] = await Promise.all([
      KetQuaSuaChuaCoKeHoachService
        .getAll(
          { phieu_giao_viec_id: phieuSuaChuaId, tinh_trang_xu_ly: TRANG_THAI_XU_LY.DA_XU_LY.code, is_deleted: false },
          { ton_tai_id: 1, phieu_giao_viec_id: 1 },
        )
        .populate([
          {
            path: 'ton_tai_id',
            select: 'vi_tri_id khoang_cot_id tieu_chi_id duong_day_id',
            populate: [
              {
                path: 'tieu_chi_id',
                select: 'ten_tieu_chi tieu_chi_cha_id noi_dung_kiem_tra_id',
                populate: [
                  { path: 'noi_dung_kiem_tra_id', select: 'ten_noi_dung' },
                  { path: 'tieu_chi_cha_id', select: 'ten_tieu_chi noi_dung_kiem_tra_id' },
                ],
              },
              { path: 'vi_tri_id', select: 'ten_vi_tri' },
              { path: 'khoang_cot_id', select: 'ten_khoang_cot' },
            ],
          },
          { path: 'phieu_giao_viec_id', select: 'so_phieu' },
        ]),
      KetQuaSuaChuaKhongKeHoachService.aggregate([
        {
          $match: {
            phieu_giao_viec_id: {
              $in: phieuSuaChuaId.map(id => new Types.ObjectId(id)), // cast từng id trong array
            },
            duong_day_id: duongDayCurrent._id,
            is_deleted: false,
          },
        },
        {
          $lookup: {
            from: DANH_MUC_CONG_VIEC,
            localField: 'cong_viec_id',
            foreignField: '_id',
            as: 'cong_viec',
          },
        },
        { $unwind: '$cong_viec' },
        {
          $group: {
            _id: '$cong_viec.ten_cong_viec',
            count: { $sum: 1 },
          },
        },
        {
          $project: {
            ten_cong_viec: '$_id',
            count: 1,
            _id: 0,
          },
        },
      ]),
      CongViecPhatSinhService.aggregate([
        {
          $lookup: {
            from: 'PhieuGiaoViec',
            localField: 'phieu_giao_viec_id',
            foreignField: '_id',
            as: 'phieu',
          },
        },
        { $unwind: '$phieu' },
        {
          $match: {
            duong_day_id: new Types.ObjectId(criteria.duong_day_ids),
            is_deleted: false,
            'phieu.is_deleted': false,
            'phieu.thoi_gian_cong_tac_bat_dau': criteria.thoi_gian_cong_tac_bat_dau,
            'phieu.trang_thai_cong_viec': criteria.trang_thai_cong_viec,
            'phieu.duong_day_ids': { $exists: true },
          },
        },

        //
        {
          $lookup: {
            from: DANH_MUC_CONG_VIEC,
            localField: 'cong_viec_id',
            foreignField: '_id',
            as: 'cong_viec',
          },
        },
        { $unwind: '$cong_viec' },

        // Lookup VI_TRI
        {
          $lookup: {
            from: 'VI_TRI',
            let: { ids: '$vi_tri_id' },
            pipeline: [
              { $match: { $expr: { $in: ['$_id', '$$ids'] } } },
              { $project: { ten_vi_tri: 1 } },
            ],
            as: 'vi_tri',
          },
        },

        // Lookup KHOANG_COT
        {
          $lookup: {
            from: 'KHOANG_COT',
            let: { ids: '$khoang_cot_id' },
            pipeline: [
              { $match: { $expr: { $in: ['$_id', '$$ids'] } } },
              { $project: { ten_khoang_cot: 1 } },
            ],
            as: 'khoang_cot',
          },
        },

        // Group theo tên công việc
        {
          $group: {
            _id: '$cong_viec.ten_cong_viec',
            count: { $sum: 1 },
            vi_tri_list: { $push: '$vi_tri' },
            khoang_cot_list: { $push: '$khoang_cot' },
          },
        },

        // Làm phẳng & loại bỏ trùng lặp trong mảng
        {
          $project: {
            _id: 0,
            ten_cong_viec: '$_id',
            count: 1,
            vi_tri_list: {
              $reduce: {
                input: '$vi_tri_list',
                initialValue: [],
                in: { $setUnion: ['$$value', '$$this'] },
              },
            },
            khoang_cot_list: {
              $reduce: {
                input: '$khoang_cot_list',
                initialValue: [],
                in: { $setUnion: ['$$value', '$$this'] },
              },
            },
          },
        },
      ]),

    ]);

    const khacPhucTonTai = kqCoKeHoach.filter(ketQua => {
      return ketQua.ton_tai_id?.duong_day_id
        ?.map(x => x.toString())
        ?.includes(id);
    });

    return responseHelper.success(res, {
      khac_phuc_ton_tai: khacPhucTonTai,
      duy_tu_bao_duong: kqKoKeHoach,
      cong_viec_phat_sinh: cvPhatSinh,
    });

  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}


export async function thongKeViTriDaKiemTraByDuongDay(req, res) {
  try {
    const { id } = req.params;
    const duongDayCurrent = await DuongDayService.getOne({ _id: id, is_deleted: false });

    if (!duongDayCurrent) return responseHelper.error(res, CommonError.NOT_FOUND);

    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;

    criteria.don_vi_giao_phieu_id = await DonViService.getDonViQuery(req, criteria.don_vi_giao_phieu_id, true);
    criteria = quanLyVanHanhCommons.buildThoiGianCongTacQuery(criteria);


    const allViTri = await VanHanhService.aggregate([
      {
        $lookup: {
          from: VI_TRI,
          localField: 'vi_tri_id',
          foreignField: '_id',
          as: 'viTri',
        },
      },
      { $unwind: '$viTri' },
      {
        $match: {
          duong_day_id: new Types.ObjectId(id),
          is_deleted: false,
          'viTri.loai_vi_tri': LOAI_VI_TRI.COT_DIEN,
        },
      },
      { $replaceRoot: { newRoot: '$viTri' } },
      { $sort: { thu_tu: 1 } },
      { $project: { ten_vi_tri: 1 } },
    ]);

    const loaiCvKiemTra = Object.values(LOAI_CONG_VIEC)
      .filter(item => item.type === KIEM_TRA)
      .map(item => item.code);

    const allPhieuKiemTra = await PhieuGiaoViecService.getAll(
      {
        ...criteria,
        duong_day_ids: id,
        loai_cong_viec: { $in: loaiCvKiemTra },
        trang_thai_cong_viec: TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code,
      },
      { _id: 1, so_phieu: 1 },
    );

    const allCongViecHoanThanh = await ViTriCongViecService.getAll(
      {
        phieu_giao_viec_id: { $in: extractIds(allPhieuKiemTra) },
        vi_tri_id: { $exists: true },
        khoang_cot_id: { $exists: false },
        is_deleted: false,
        trang_thai_hoan_thanh: TRANG_THAI_HOAN_THANH.DA_HOAN_THANH,
      },
      { vi_tri_id: 1 },
    );

    const viTriHoanThanhCongTac = formatUnique(extractKeys(allCongViecHoanThanh, 'vi_tri_id'));

    // const vi_tri_list = allViTri.map(viTri => {
    //   viTri.trang_thai_hoan_thanh = viTriHoanThanhCongTac.includes(viTri._id.toString())
    //     ? TRANG_THAI_HOAN_THANH.DA_HOAN_THANH
    //     : TRANG_THAI_HOAN_THANH.CHUA_HOAN_THANH;
    //   return viTri;
    // });


    const vi_tri_da_hoan_thanh = [];
    const vi_tri_chua_hoan_thanh = [];

    allViTri.forEach((viTri) => {
      const isDone = viTriHoanThanhCongTac.includes(viTri._id.toString());

      if (isDone) {
        vi_tri_da_hoan_thanh.push(viTri);
      } else {
        vi_tri_chua_hoan_thanh.push(viTri);
      }
    });


    return responseHelper.success(res, {
      vi_tri_list: allViTri,
      vi_tri_da_hoan_thanh,
      vi_tri_chua_hoan_thanh,
    });
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

async function phanLoaiTinhTrangPhieuKiemTra(phieuList) {
  const allPhieuId = extractIds(phieuList);
  let kiemTraKhongHoanThanh = await ViTriCongViecService.distinctField(
    'phieu_giao_viec_id',
    {
      phieu_giao_viec_id: { $in: allPhieuId },
      trang_thai_hoan_thanh: TRANG_THAI_HOAN_THANH.CHUA_HOAN_THANH,
    });
  return kiemTraKhongHoanThanh.map(id => id.toString());
}

async function phanLoaiTinhTrangPhieuSuaChua(phieuList) {
  const allPhieuId = extractIds(phieuList);

  const coKeHoachCHT = await SuaChuaCoKeHoachModel.distinct(
    'phieu_giao_viec_id',
    {
      phieu_giao_viec_id: { $in: allPhieuId },
      tinh_trang_xu_ly: TRANG_THAI_XU_LY.CHUA_XU_LY.code,
      is_deleted: false,
    });

  const khongKeHoachCHT = await ViTriCongViecModel.distinct(
    'phieu_giao_viec_id',
    {
      phieu_giao_viec_id: { $in: allPhieuId },
      trang_thai_hoan_thanh: TRANG_THAI_HOAN_THANH.CHUA_HOAN_THANH,
      is_deleted: false,
    });

  return formatUnique([...coKeHoachCHT, ...khongKeHoachCHT]).map(id => id.toString());
}


async function phanLoaiTinhTrangPhieuDoThongSo(phieuList) {
  const allPhieuId = extractIds(phieuList);
  const doThongSoChuaHoanThanh = await ViTriCongViecModel.distinct(
    'phieu_giao_viec_id', {
      phieu_giao_viec_id: { $in: allPhieuId },
      trang_thai_hoan_thanh: TRANG_THAI_HOAN_THANH.CHUA_HOAN_THANH,
      is_deleted: false,
    });

  return doThongSoChuaHoanThanh.map(id => id.toString());
}

async function phanLoaiTinhTrangCongViecPhuTro(phieuList) {
  const allPhieuId = extractIds(phieuList);
  const congViecPhuTroCHT = await CongViecPhuTroModel.distinct(
    'phieu_giao_viec_id',
    {
      phieu_giao_viec_id: { $in: allPhieuId },
      tinh_trang_thuc_hien: TRANG_THAI_THUC_HIEN.CHUA_THUC_HIEN.code,
      is_deleted: false,
    });

  return congViecPhuTroCHT.map(id => id.toString());
}

export async function chiTietTinhTrangCongViecByNhomCongViec(req, res) {
  try {
    const { assignType } = req.params;

    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;

    criteria = quanLyVanHanhCommons.buildThoiGianCongTacQuery(criteria);

    const loaiCongViec = Object.values(LOAI_CONG_VIEC)
      .filter(congViec => congViec.type === assignType)
      .map(loai => loai.code);

    const phieuQuery = {
      thoi_gian_cong_tac_bat_dau: criteria.thoi_gian_cong_tac_bat_dau,
      don_vi_giao_phieu_id: await DonViService.getDonViQuery(req, criteria.don_vi_giao_phieu_id),
      trang_thai_cong_viec: TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code,
      loai_cong_viec: loaiCongViec,
      is_deleted: false,
    };

    const allPhieu = await PhieuGiaoViecService.getAll(
      phieuQuery,
      { so_phieu: 1 },
    ).sort({ created_at: 1 });

    let phieuKhongHoanThanhId = [];
    switch (assignType) {
      case KIEM_TRA:
        phieuKhongHoanThanhId = await phanLoaiTinhTrangPhieuKiemTra(allPhieu);
        break;
      case SUA_CHUA_BAO_DUONG:
        phieuKhongHoanThanhId = await phanLoaiTinhTrangPhieuSuaChua(allPhieu);
        break;
      case DO_THONG_SO:
        phieuKhongHoanThanhId = await phanLoaiTinhTrangPhieuDoThongSo(allPhieu);
        break;
      case CONG_TAC_PHU_TRO:
        phieuKhongHoanThanhId = await phanLoaiTinhTrangCongViecPhuTro(allPhieu);
        break;
      default:
        break;
    }

    const phieuHoanThanh = [], phieuKhongHoanThanh = [];
    allPhieu.forEach((phieu) => {
      if (phieuKhongHoanThanhId.includes(phieu._id.toString())) phieuKhongHoanThanh.push(phieu);
      else phieuHoanThanh.push(phieu);
    });

    return responseHelper.success(res, {
      phieu_da_hoan_thanh: phieuHoanThanh,
      phieu_chua_hoan_thanh: phieuKhongHoanThanh,
    });
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}


export async function baoCaoSuaChua(req, res) {
  try {

    criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code;
    criteria.duong_day_ids = criteria.duong_day_id;

    return responseHelper.success(res, { a: 1 });
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}
