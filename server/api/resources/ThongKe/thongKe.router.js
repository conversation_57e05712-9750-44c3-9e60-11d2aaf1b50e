import { Router } from 'express';
import passport from 'passport';

import * as ServerLogsController from './thongKe.controller';

export const thongKeRouter = Router();
thongKeRouter.use(passport.authenticate('jwt', { session: false }));

thongKeRouter.route('/bao-cao-giam-thieu-su-co')
  .get(ServerLogsController.baoCaoGiamThieuSuCo);

thongKeRouter.route('/bao-cao-giam-thieu-su-co/duong-day/:id')
  .get(ServerLogsController.baoCaoGiamThieuSuCoByDuongDay);

thongKeRouter.route('/thong-ke-vi-tri-da-kiem-tra/duong-day/:id')
  .get(ServerLogsController.thongKeViTriDaKiemTraByDuongDay);

thongKeRouter.route('/tinh-trang-cong-viec/cong-viec/:assignType')
  .get(ServerLogsController.chiTietTinhTrangCongViecByNhomCongViec);

thongKeRouter.route('/bao-cao-sua-chua')
  .get(ServerLogsController.baoCaoSuaChua);

