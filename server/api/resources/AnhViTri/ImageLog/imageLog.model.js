import mongoose, { Schema } from 'mongoose';
import { IMAGE_LOG } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
    type: { type: String },
    payload: Object,
  },
  {
    timestamps: {
      createdAt: 'thoi_gian_tao',
      updatedAt: 'thoi_gian_cap_nhat',
    },
  });
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(IMAGE_LOG, schema, IMAGE_LOG);
