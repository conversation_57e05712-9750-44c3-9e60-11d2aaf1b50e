import mongoose, { Schema } from 'mongoose';
import { ANH_VI_TRI, PHIEU_GIAO_VIEC, USER, VI_TRI, KHOANG_COT, CAU_HINH_BAY } from '../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';
import { PointSchema } from '../GeoObjects/GeoObjects.schema';
import { PROCESS_STATUS } from './anhViTri.constant';

const schema = new Schema({
    local_id: String,
    cau_hinh_bay_id: { type: String, ref: CAU_HINH_BAY },
    vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
    khoang_cot_id: { type: Schema.Types.ObjectId, ref: KHOANG_COT },
    phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },

    image_id: { type: String, require: true },
    thumbnail_id: { type: String, require: true },
    storage_type: String,
    storage_location: String,

    file_name: String,
    device: String,
    thoi_gian_chup: Date,
    location: { type: PointSchema },
    latitude : Number,
    longitude : Number,
    altitude : Number,
    heading : Number,
    gimbal_pitch : Number,

    xac_nhan: Boolean,
    vi_tri_chup: { type: Schema.Types.ObjectId, ref: VI_TRI },

    process_status: {
      type: String,
      enum: Object.values(PROCESS_STATUS),
      default: PROCESS_STATUS.UNPROCESSING,
    },

    is_deleted: { type: Boolean, default: false, select: true },
    nguoi_tao: { type: Schema.Types.ObjectId, ref: USER },
    nguoi_chinh_sua: { type: Schema.Types.ObjectId, ref: USER },
    sync_time: { type: String },
    sync_status: { type: String, default: 'synced' },
    folder_path: { type: String },
  },
  {
    timestamps: {
      createdAt: 'thoi_gian_tao',
      updatedAt: 'thoi_gian_cap_nhat',
    },
  });

// Thêm index cho các trường thường xuyên tìm kiếm
schema.index({ phieu_giao_viec_id: 1 });
schema.index({ is_deleted: 1 });
schema.index({ phieu_giao_viec_id: 1, is_deleted: 1 });
schema.index({ vi_tri_id: 1 });
schema.index({ khoang_cot_id: 1 });

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(ANH_VI_TRI, schema, ANH_VI_TRI);
