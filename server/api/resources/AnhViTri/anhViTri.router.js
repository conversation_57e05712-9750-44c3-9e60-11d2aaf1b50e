import express from 'express';
import passport from 'passport';
import * as Controller from './anhViTri.controller';
import { checkTempFolder, multipartMiddleware } from '../../utils/fileUtils';
import { loggerMiddleware } from '../../logs/middleware';
import { authorizationMiddleware } from '../RBAC/middleware';
import SettingPermission from '../RBAC/permissions/SettingPermission';

export const anhViTriRouter = express.Router();
anhViTriRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
anhViTriRouter.route('/')
  .get(Controller.getAll)
  .post(checkTempFolder, multipartMiddleware, Controller.create)
  .delete(Controller.removeMulti);

anhViTriRouter.route('/phantichlai').put(Controller.reanalyze);

anhViTriRouter.get('/countsyncimageai', Controller.countSyncImageAi);
anhViTriRouter.put('/syncimageai', Controller.syncImageAi);

anhViTriRouter.route('/phanloaianh').put(Controller.phanLoaiAnh);

anhViTriRouter.route('/tudongphanloai')
  .post(checkTempFolder, multipartMiddleware, Controller.tuDongPhanLoai);

anhViTriRouter.route('/app')
  .get(Controller.getAll)
  .post(checkTempFolder, multipartMiddleware, Controller.createOnApp)
  .delete(Controller.removeMulti);

anhViTriRouter.route('/autodelete')
  .get(authorizationMiddleware([SettingPermission.READ]), Controller.getAutoDelete)
  .delete(authorizationMiddleware([SettingPermission.DELETE]), Controller.autoDelete);

anhViTriRouter.route('/trash')
  .get(authorizationMiddleware([SettingPermission.READ]), Controller.getTrash)
  .delete(authorizationMiddleware([SettingPermission.DELETE]), Controller.cleanTrash);

anhViTriRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
