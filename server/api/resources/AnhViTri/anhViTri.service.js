import moment from "moment";
import path from "path";

import ANH_VI_TRI from "./anhViTri.model";
import PHIEU_GIAO_VIEC from "../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.model";
import BAT_THUONG_PHAT_HIEN from "../QuanLyVanHanh/BatThuongPhatHien/batThuongPhatHien.model";

import * as ValidatorHelper from "../../helpers/validatorHelper";
import * as requestHelper from "../../helpers/requestHelper";
import { momentValid } from "../../helpers/checkDataHelper";

import { PROCESS_STATUS } from "./anhViTri.constant";
import { STORE_DIRS } from "../../constant/constant";
import { cloneObj, formatUnique } from "../../common/functionCommons";
import { extractIds, extractKeys, groupBy } from "../../utils/dataconverter";
import { buildChunks, sortThuTu } from "../../common/DataStructureHelper";
import { checkFileExist, deleteFileAsync, getFilePath } from "../../utils/fileUtils";
import { findCorridorOfPhoto, findViTriOfPhoto } from "../../utils/geoUtils";

import * as CaiDatAiService from "../CaiDatAi/caiDatAi.service";
import * as ImageLogService from "./ImageLog/imageLog.service";
import * as BatThuongPhatHienService from "../QuanLyVanHanh/BatThuongPhatHien/batThuongPhatHien.service";
import * as ThietBiPhatHien from "../QuanLyVanHanh/ThietBiPhatHien/thietBiPhatHien.service";
import * as CaiDatHeThongService from "../CaiDatHeThong/caiDatHeThong.service";
import * as mediaFileService from "../DJICloud/MediaFile/mediaFile.service";
import { LOAI_CONG_VIEC, KIEM_TRA } from "../DanhMuc/LoaiCongViec";
import KetQuaKiemTra from "../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.model";
import { AI_STATUS } from "../QuanLyVanHanh/BatThuongPhatHien/batThuongPhatHien.model";

export async function processing(image) {
  return ANH_VI_TRI.findByIdAndUpdate(image._id, { process_status: PROCESS_STATUS.PROCESSING });
}

export async function processFail(image) {
  return ANH_VI_TRI.findByIdAndUpdate(image._id, { process_status: PROCESS_STATUS.ERROR });
}

export async function processSucess(image) {
  return ANH_VI_TRI.findByIdAndUpdate(image._id, { process_status: PROCESS_STATUS.SUCCESS });
}

export function findAnhChupCorridorLocation(latlong, khoangCotList) {
  let listAllLineCorridor = khoangCotList.map((khoangCot) => {
    return {
      ...khoangCot,
      nodes: [
        {
          ...khoangCot.vi_tri_bat_dau_id,
          latitude: khoangCot.vi_tri_bat_dau_id.vi_do,
          longitude: khoangCot.vi_tri_bat_dau_id.kinh_do,
        },
        {
          ...khoangCot.vi_tri_ket_thuc_id,
          latitude: khoangCot.vi_tri_ket_thuc_id.vi_do,
          longitude: khoangCot.vi_tri_ket_thuc_id.kinh_do,
        },
      ],
    };
  });
  return listAllLineCorridor.find((corridor) => findCorridorOfPhoto(corridor, latlong));
}

export function findAnhChupViTriLocation(location, viTriList) {
  const listAllStation = viTriList.map((vitri) => {
    return {
      ...vitri,
      latitude: vitri.vi_do,
      longitude: vitri.kinh_do,
    };
  });
  listAllStation.sort(sortThuTu);
  return listAllStation.find((corridor) => findViTriOfPhoto(corridor, location));
}

const Joi = require("joi");

const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return ANH_VI_TRI.create(value);
}

export function getAll(query, projection = {}) {
  return ANH_VI_TRI.find(query, projection).lean();
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await ANH_VI_TRI.findByIdAndUpdate(value._id, value);
  }
}

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};

export async function checkValidAiToken() {
  const caiDatAi = await CaiDatAiService.getOne();
  if (!caiDatAi) return false;

  const apiGetInfo = `${caiDatAi.domain}/api/get_info`.replace(/\/+api/g, "/api");
  const info = await requestHelper.get(apiGetInfo, caiDatAi.token);
  return !!info?.id_user;
}

function formatQueryTime(time, isStart = false) {
  if (!time || !momentValid(time)) {
    time = moment().subtract(1, "days");
  } else {
    time = moment(time);
  }
  if (isStart) {
    return time.startOf("days");
  }
  return time.endOf("days");
}

export async function countSyncImageAi(startTime, endTime) {
  startTime = formatQueryTime(startTime, true);
  endTime = formatQueryTime(endTime, false);

  const allPhieuGiaoViec = await PHIEU_GIAO_VIEC.find(
    {
      ky_khoa_phieu: true,
      $and: [
        { thoi_gian_xac_nhan_khoa_phieu: { $gte: startTime.toISOString() } },
        { thoi_gian_xac_nhan_khoa_phieu: { $lte: endTime.toISOString() } },
      ],
      is_deleted: false,
    },
    { _id: 1 }
  );

  let allImage = await getAll(
    { phieu_giao_viec_id: extractIds(allPhieuGiaoViec), is_deleted: false },
    { image_id: 1, folder_path: 1, thoi_gian_tao: 1, is_deleted: -1 }
  );
  if (!allImage.length) return 0;

  allImage = allImage.filter((image) => {
    const filepath = path.join(image.folder_path || STORE_DIRS.ANH_VI_TRI, image.image_id);
    return checkFileExist(filepath);
  });
  if (!allImage.length) return 0;

  const allBatThuong = await BatThuongPhatHienService.getAll(
    { anh_vi_tri_id: extractIds(allImage) },
    { x: 1, y: 1, width: 1, height: 1, tinh_trang_kbt_id: 1, anh_vi_tri_id: 1 }
  ).populate({ path: "tinh_trang_kbt_id", select: "ten_tinh_trang" });

  const batThuongGroupByImage = groupBy(allBatThuong, "anh_vi_tri_id");

  // filter ảnh có bất thường
  allImage = allImage.filter((image) => !!batThuongGroupByImage[image._id]);

  return allImage?.length;
}

export async function syncImageAi(startTime, endTime) {
  startTime = formatQueryTime(startTime, true);
  endTime = formatQueryTime(endTime, false);

  const allPhieuGiaoViec = await PHIEU_GIAO_VIEC.find(
    {
      ky_khoa_phieu: true,
      $and: [
        { thoi_gian_xac_nhan_khoa_phieu: { $gte: startTime.toISOString() } },
        { thoi_gian_xac_nhan_khoa_phieu: { $lte: endTime.toISOString() } },
      ],
      is_deleted: false,
    },
    { _id: 1 }
  );

  let allImage = await getAll(
    { phieu_giao_viec_id: extractIds(allPhieuGiaoViec), is_deleted: false },
    { image_id: 1, folder_path: 1, thoi_gian_tao: 1, is_deleted: -1 }
  );

  const payload = {
    startTime,
    endTime,
    image: cloneObj(allImage),
  };

  allImage = allImage.filter((image) => {
    const filepath = path.join(image.folder_path || STORE_DIRS.ANH_VI_TRI, image.image_id);
    return checkFileExist(filepath);
  });
  if (!allImage.length) return;

  const caiDatAi = await CaiDatAiService.getOne();
  if (!caiDatAi) return;

  payload.caiDatAi = caiDatAi;

  const apiGetInfo = `${caiDatAi.domain}/api/get_info`.replace(/\/+api/g, "/api");
  const apiDataset = `${caiDatAi.domain}/api/dataset/${caiDatAi.sync_dataset_id}/label_token`.replace(
    /\/+api/g,
    "/api"
  );
  const apiSync = `${caiDatAi.domain}${caiDatAi.api_sync_image}`
    .replace(/\/+api/g, "/api")
    .replace(/<DATASET_ID>/g, caiDatAi.sync_dataset_id);

  const info = await requestHelper.get(apiGetInfo, caiDatAi.token);
  if (!info?.id_user) return;

  payload.info = info;

  const allBatThuong = await BatThuongPhatHienService.getAll(
    { anh_vi_tri_id: extractIds(allImage) },
    { x: 1, y: 1, width: 1, height: 1, tinh_trang_kbt_id: 1, anh_vi_tri_id: 1 }
  ).populate({ path: "tinh_trang_kbt_id", select: "ten_tinh_trang" });

  const batThuongGroupByImage = groupBy(allBatThuong, "anh_vi_tri_id");

  const allTinhTrang = extractKeys(
    allBatThuong?.map((batThuong) => batThuong?.tinh_trang_kbt_id),
    "ten_tinh_trang"
  );

  // filter ảnh có bất thường
  allImage = allImage.filter((image) => !!batThuongGroupByImage[image._id]);

  const allThietBi = await ThietBiPhatHien.getAll(
    { anh_vi_tri_id: extractIds(allImage) },
    { x: 1, y: 1, width: 1, height: 1, dm_thiet_bi_id: 1, anh_vi_tri_id: 1 }
  ).populate({ path: "dm_thiet_bi_id", select: "ten_thiet_bi" });

  const thietBiGroupByImage = groupBy(allThietBi, "anh_vi_tri_id");

  const allTenThietBi = allThietBi?.map((thietBi) => thietBi?.dm_thiet_bi_id?.ten_thiet_bi).filter((x) => !!x);

  const labelNew = formatUnique([...allTinhTrang, ...allTenThietBi]);
  if (labelNew?.length) {
    await requestHelper.put(apiDataset, caiDatAi.token, {
      token: caiDatAi.token,
      category_label: labelNew,
    });
  }

  while (allImage.length) {
    const firstImage = allImage[0];

    const filepath = path.join(firstImage.folder_path || STORE_DIRS.ANH_VI_TRI, firstImage.image_id);
    let imageResponse = { data: null, time: 0 };
    do {
      imageResponse.data = await requestHelper.syncImageAi(apiSync, filepath, info.id_user, caiDatAi.token);
      imageResponse.time += 1;
    } while (!imageResponse.data && imageResponse.time < 5);

    if (imageResponse.data?.success === "True") {
      payload.image.forEach((img) => {
        if (img.image_id === firstImage.image_id) {
          img.syncImageAi ||= [];
          img.syncImageAi = [...img.syncImageAi, imageResponse.data];
        }
      });

      if (batThuongGroupByImage[firstImage._id] || thietBiGroupByImage[firstImage._id]) {
        const boundingBoxData = [];

        batThuongGroupByImage[firstImage._id]?.map((batThuong) => {
          boundingBoxData.push({
            label: batThuong.tinh_trang_kbt_id.ten_tinh_trang,
            xmin: batThuong.x,
            xmax: batThuong.x + batThuong.width,
            ymin: batThuong.y,
            ymax: batThuong.y + batThuong.height,
          });
        });

        thietBiGroupByImage[firstImage._id]?.map((thietBi) => {
          boundingBoxData.push({
            label: thietBi.dm_thiet_bi_id?.ten_thiet_bi,
            xmin: thietBi.x,
            xmax: thietBi.x + thietBi.width,
            ymin: thietBi.y,
            ymax: thietBi.y + thietBi.height,
          });
        });

        const objectBody = {
          token: caiDatAi.token,
          bounding_box: boundingBoxData,
          segment_polygons: [],
        };

        const apiObjectDetection =
          `${caiDatAi.domain}/api/files/${imageResponse.data.id_images}/objects/token?id_user=${info.id_user}`.replace(
            /\/+api/g,
            "/api"
          );
        let objectDetectionResponse = { data: null, time: 0 };
        do {
          objectDetectionResponse.data = await requestHelper.post(apiObjectDetection, objectBody);
          objectDetectionResponse.time += 1;
        } while (!objectDetectionResponse.data && objectDetectionResponse.time < 5);
      }
    }
    allImage.shift();
  }

  return await ImageLogService.create({ payload, type: "ANH_VI_TRI" });
}

const FROM_DATE = moment("2020-01-01");
const TRASH_DAYS = 30;
const CHUNK_SIZE = 100;

export async function getAutoDeleteTime() {
  const configSystem = await CaiDatHeThongService.getAll();
  let configTime = configSystem[0].phien_auto_xoa_anh;
  if (!configTime || configTime == 0) configTime = 365 * 10;
  return configTime;
}

export async function buildAutoDeleteImageQuery() {
  const configTime = await getAutoDeleteTime();
  return { thoi_gian_tao: { $gte: getFromDate().toISOString(), $lte: getToDate(configTime).toISOString() } };
}

export async function autoDeleteImage() {
  const configSystem = await CaiDatHeThongService.getOne({ is_deleted: false });
  if (!configSystem || !configSystem.phien_auto_xoa_anh || !configSystem.kich_hoat_auto_xoa_anh) return;

  //Get ảnh sẽ tự động xóa
  const anhViTri = await getAll(await buildAutoDeleteImageQuery());
  const chunks = buildChunks(anhViTri, CHUNK_SIZE);

  async function checkBatThuongAndDelete(image) {
    // console.log("checkBatThuongAndDelete", image._id);
    const phieuCongViec = await PHIEU_GIAO_VIEC.findOne({ _id: image.phieu_giao_viec_id }).lean();
    if (LOAI_CONG_VIEC[phieuCongViec?.loai_cong_viec]?.type !== KIEM_TRA) {
       console.log("cong viec khac", image._id, LOAI_CONG_VIEC[phieuCongViec?.loai_cong_viec]?.type);
      return;
    }
    const tonTai = await KetQuaKiemTra.findOne({ anh_vi_tris: image._id, is_deleted: false });
    if (tonTai) {
      console.log("tonTai", image._id);
      return;
    }

    const batThuong = await BAT_THUONG_PHAT_HIEN.find({ anh_vi_tri_id: image._id, is_deleted: false }).lean();

    let batThuongXacNhan = batThuong?.find((x) => x.ai_status === AI_STATUS.AI_XAC_NHAN);
    let batThuongNguoiTao = batThuong?.find((x) => x.from_ai === false);
    let batThuongNguoiTaoNew = batThuong?.find((x) => x.nguoi_tao === true);

    if (!batThuongXacNhan && !batThuongNguoiTaoNew && !batThuongNguoiTao) {
      console.log("deleteImage", image._id);
      return deleteImage(image);
    } else {
      console.log("not deleteImage", image._id);
    }
  }

  for (let i = 0; i < chunks.length; i++) {
    const deleteTasks = chunks[i].map((image) => checkBatThuongAndDelete(image));
    await Promise.allSettled(deleteTasks);
  }
}

export async function deleteById(image) {
  // return ANH_VI_TRI.findByIdAndUpdate(image._id, { is_deleted: true });
  return ANH_VI_TRI.deleteOne({ _id: image._id });
}

export function getFromDate() {
  return FROM_DATE;
}

export function getToDate(daysToKeep = TRASH_DAYS) {
  const now = moment();
  return now.subtract(daysToKeep, "days");
}

export function buildTrashImageQuery() {
  return {
    is_deleted: true,
    thoi_gian_tao: { $gte: getFromDate().toISOString(), $lte: getToDate(TRASH_DAYS).toISOString() },
  };
}

export async function countAutoDeleteImage() {
  return ANH_VI_TRI.count(await buildAutoDeleteImageQuery());
}

export async function countTrashImage() {
  return ANH_VI_TRI.count(buildTrashImageQuery());
}

async function deleteFileInOldStorage(image) {
  const filePath = getFilePath(image.image_id);
  const fileThumbnailPath = getFilePath(image.thumbnail_id);
  await deleteFileAsync(filePath);
  await deleteFileAsync(fileThumbnailPath);
}

async function deleteImage(image) {
  // await mediaFileService.deleteOne({ anh_vi_tri_id: image._id });

  await deleteFileInOldStorage(image);

  const filePath = getFilePath(image.image_id, image.folder_path, image.storage_type);
  const fileThumbnailPath = getFilePath(image.thumbnail_id, image.folder_path, image.storage_type);
  await deleteFileAsync(filePath);
  await deleteFileAsync(fileThumbnailPath);
  if (!checkFileExist(filePath) && !checkFileExist(fileThumbnailPath)) {
    await deleteById(image);
  }
}

export async function cleanTrash() {
  const deletedAnhViTris = await getAll(buildTrashImageQuery());
  const chunks = buildChunks(deletedAnhViTris, CHUNK_SIZE);
  for (let i = 0; i < chunks.length; i++) {
    const deleteTasks = chunks[i].map((image) => deleteImage(image));
    await Promise.allSettled(deleteTasks);
  }
}
