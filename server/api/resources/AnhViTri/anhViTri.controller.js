import path from 'path';

import * as controllerHelper from '../../helpers/controllerHelper';
import * as responseHelper from '../../helpers/responseHelper';
import * as responseAction from '../../helpers/responseHelper';
import * as fileUtils from '../../utils/fileUtils';

import AnhViTriModel from './anhViTri.model';
import BatThuongPhatHienModel from '../../resources/QuanLyVanHanh/BatThuongPhatHien/batThuongPhatHien.model';
import ThietBiPhatHienModel from '../../resources/QuanLyVanHanh/ThietBiPhatHien/thietBiPhatHien.model';

import queryHelper from '../../helpers/queryHelper';
import CommonError from '../../error/CommonError';

import { KIEM_TRA, LOAI_CONG_VIEC } from '../DanhMuc/LoaiCongViec';
import { PROCESS_STATUS } from './anhViTri.constant';
import { STORE_DIRS } from '../../constant/constant';

import * as Service from './anhViTri.service';
import * as PhieuGiaoViecService from '../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';
import * as GeoObjectsService from '../GeoObjects/GeoObjects.service';
import * as ObjectDetectionService from '../AI/ObjectDetection/objectDetection.service';
import * as NguoiCongTacService from '../QuanLyVanHanh/NguoiCongTac/nguoiCongTac.service';
import * as KetQuaKiemTraService from '../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.service';
import * as KetQuaSuaChuaKhongKeHoachService
  from '../QuanLyVanHanh/KetQuaSuaChuaKhongKeHoach/ketQuaSuaChuaKhongKeHoach.service';
import * as ViTriCongViecService from '../QuanLyVanHanh/ViTriCongViec/viTriCongViec.service';

import * as functionCommons from '../../common/functionCommons';
import { cloneObj, isValidObjectId } from '../../common/functionCommons';
import { extractObjectIds } from '../../utils/dataconverter';

const populateOpts = [
  { path: 'vi_tri_id' },
  { path: 'khoang_cot_id' },
  { path: 'nguoi_chinh_sua', select: 'full_name username phone bac_an_toan' },
  { path: 'nguoi_tao', select: 'full_name username phone bac_an_toan' },
  { path: 'phieu_giao_viec_id' },
];

export const remove = controllerHelper.createRemoveFunction(AnhViTriModel);
export const update = controllerHelper.createUpdateByIdFunction(AnhViTriModel, Service, populateOpts);

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await controllerHelper.findOneById(AnhViTriModel, id, populateOpts, true);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    data.nguoi_cong_tac = await NguoiCongTacService.getAll({
      phieu_giao_viec_id: data.phieu_giao_viec_id._id,
      is_deleted: false,
    });
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

async function handlerUploadImage(req, readExif = false, outputDir) {
  const phieuCurrent = await PhieuGiaoViecService.getById(req.body.phieu_giao_viec_id, { loai_cong_viec: 1 });
  const loaiCv = LOAI_CONG_VIEC[phieuCurrent.loai_cong_viec]?.type;

  const { imageId, thumbnailId, lat, lng } = await functionCommons.generateImage(req, readExif, outputDir);
  return {
    ...req.body,
    image_id: imageId,
    thumbnail_id: thumbnailId,
    location: GeoObjectsService.convertToPointObject(req.body.longitude, req.body.latitude),
    latitude: lat,
    longitude: lng,
    process_status: loaiCv === KIEM_TRA ? PROCESS_STATUS.PROCESSING : PROCESS_STATUS.NOT_PROCESSED,
    nguoi_tao: req.user._id,
    nguoi_chinh_sua: req.user._id,
    sync_status: 'synced',
    folder_path: outputDir,
  };
}

export async function create(req, res) {
  try {
    // await checkActivateDonVi(req.body.don_vi_giao_phieu_id);
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    //find folder path by month
    const folderPath = fileUtils.getFileDirByMonth(path.join(STORE_DIRS.IMAGE));

    const body = await handlerUploadImage(req, false, folderPath);
    body.vi_tri_id ||= '';
    body.khoang_cot_id ||= '';

    const viTriId = body.vi_tri_id.split(',').map(id => id.trim()).filter(id => isValidObjectId(id));
    const khoangCotId = body.khoang_cot_id.split(',').map(id => id.trim()).filter(id => isValidObjectId(id));

    const viTriKhoangCotId = [...viTriId, ...khoangCotId];
    if (viTriKhoangCotId.length < 2) {
      if (!isValidObjectId(body.vi_tri_id)) delete body.vi_tri_id;
      if (!isValidObjectId(body.khoang_cot_id)) delete body.khoang_cot_id;

      const data = await AnhViTriModel.create(body);
      const createdData = await controllerHelper.findOneById(AnhViTriModel, data._id, populateOpts);

      ObjectDetectionService.processImage(data);

      return responseHelper.success(res, createdData);
    }

    const bodyMulti = [];
    viTriId.forEach(id => {
      const anhViTriBody = cloneObj(body);
      anhViTriBody.vi_tri_id = id;
      delete anhViTriBody.khoang_cot_id;
      bodyMulti.push(anhViTriBody);
    });

    khoangCotId.forEach(id => {
      const anhViTriBody = cloneObj(body);
      anhViTriBody.khoang_cot_id = id;
      delete anhViTriBody.vi_tri_id;
      bodyMulti.push(anhViTriBody);
    });

    const dataMultiCreated = [];
    for (let i = 0; i < bodyMulti.length; i++) {
      const dataCreate = bodyMulti[i];

      if (i) {
        const { imageId, thumbnailId } = await functionCommons.generateImage(req, false, folderPath);
        dataCreate.image_id = imageId;
        dataCreate.thumbnail_id = thumbnailId;
      }

      const data = await AnhViTriModel.create(dataCreate);
      const createdData = await controllerHelper.findOneById(AnhViTriModel, data._id, populateOpts);
      dataMultiCreated.push(createdData);

      ObjectDetectionService.processImage(data);
    }

    return responseHelper.success(res, dataMultiCreated);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function countSyncImageAi(req, res) {
  try {
    const { t } = req;

    if (!req.user.is_system_admin) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    const isValid = await Service.checkValidAiToken();
    if (!isValid) {
      return responseHelper.error(res, { message: t('INVALID_TOKEN') }, 400);
    }

    const { from_date, to_date } = req.query;

    const number_of_images = await Service.countSyncImageAi(from_date, to_date);
    return responseHelper.success(res, { success: true, number_of_images });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function syncImageAi(req, res) {
  try {
    const { t } = req;

    if (!req.user.is_system_admin) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    const isValid = await Service.checkValidAiToken();
    if (!isValid) {
      return responseHelper.error(res, { message: t('INVALID_TOKEN') }, 400);
    }

    const { from_date, to_date } = req.body;

    Service.syncImageAi(from_date, to_date);
    return responseHelper.success(res, { success: true });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function phanLoaiAnh(req, res) {
  try {
    const { image_id, vi_tri_id, khoang_cot_id } = req.body;
    if (!image_id || (!vi_tri_id && !khoang_cot_id)) {
      return responseAction.error(res, CommonError.NOT_FOUND);
    }

    const anhViTri = await AnhViTriModel.updateMany({ _id: image_id }, { vi_tri_id, khoang_cot_id }).lean();
    if (!anhViTri) {
      return responseAction.error(res, CommonError.NOT_FOUND);
    }

    const dataUpdated = await AnhViTriModel.find({ _id: image_id }).populate(populateOpts);

    return responseHelper.success(res, dataUpdated);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function tuDongPhanLoai(req, res) {
  try {
    // await checkActivateDonVi(req.body.don_vi_giao_phieu_id);
    const { error } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    const sub_folder_path = path.join(STORE_DIRS.IMAGE);
    const folder_path = fileUtils.getFileDirByMonth(sub_folder_path);

    const body = await handlerUploadImage(req, true, folder_path);
    delete body.vi_tri_id;
    delete body.khoang_cot_id;
    try {
      await checkAnhPosition(body);
    } catch (e) {
      console.log(e);
    }
    const data = await AnhViTriModel.create(body);
    const createdData = await controllerHelper.findOneById(AnhViTriModel, data._id, populateOpts);
    ObjectDetectionService.processImage(data);
    return responseHelper.success(res, createdData);
  } catch (err) {
    console.log('err', err);
    return responseHelper.error(res, err, 500);
  }
}

async function checkAnhPosition(body) {
  if (body.vi_tri_id || body.khoang_cot_id || !body.latitude || !body.longitude) return;
  const viTriCongViec = await ViTriCongViecService
    .getAll({
      phieu_giao_viec_id: body.phieu_giao_viec_id,
      vi_tri_id: { $exists: true },
      khoang_cot_id: { $exists: false },
      is_deleted: false,
    })
    .populate({ path: 'vi_tri_id' });
  const viTriList = viTriCongViec.map(vtcv => {
    return vtcv.vi_tri_id;
  });
  const khoang_cot_cong_viec = await ViTriCongViecService
    .getAll({
      phieu_giao_viec_id: body.phieu_giao_viec_id,
      vi_tri_id: { $exists: false },
      khoang_cot_id: { $exists: true },
      is_deleted: false,
    })
    .populate({ path: 'khoang_cot_id', populate: 'vi_tri_bat_dau_id vi_tri_ket_thuc_id vi_tri_id' });
  const location = { latitude: body.latitude, longitude: body.longitude };
  const viTriResult = await Service.findAnhChupViTriLocation(location, viTriList);

  if (viTriResult) {
    body.vi_tri_id = viTriResult._id;
    return;
  }

  const khoangCotList = khoang_cot_cong_viec.map(kccv => kccv.khoang_cot_id);
  const khoangCotResult = await Service.findAnhChupCorridorLocation(location, khoangCotList);
  if (khoangCotResult) {
    body.khoang_cot_id = khoangCotResult._id;
    return;
  }
}

export async function createOnApp(req, res) {
  try {
    // await checkActivateDonVi(req.body.don_vi_giao_phieu_id);
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    const sub_folder_path = path.join(STORE_DIRS.IMAGE);
    const folder_path = fileUtils.getFileDirByMonth(sub_folder_path);

    const body = await handlerUploadImage(req, true, folder_path);
    try {
      await checkAnhPosition(body);
    } catch (e) {
      console.log(e);
    }
    const data = await AnhViTriModel.create(body);
    const createdData = await controllerHelper.findOneById(AnhViTriModel, data._id, null);
    ObjectDetectionService.processImage(data);
    return responseHelper.success(res, createdData);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;

    // Tăng tốc truy vấn bằng lean và chỉ chọn các trường cần thiết
    const data = await AnhViTriModel.find(criteria)
      .populate(populateOpts)
      .lean();

    // Nếu không có kết quả, trả về mảng rỗng
    if (!data || data.length === 0) {
      return responseHelper.success(res, []);
    }

    // Lấy danh sách ID ảnh vị trí để truy vấn
    const anhViTriIds = extractObjectIds(data);

    // Thực hiện truy vấn song song để tăng hiệu suất
    const [allBatThuong, allThietBi] = await Promise.all([
      BatThuongPhatHienModel.find({
        anh_vi_tri_id: { $in: anhViTriIds },
        is_deleted: false,
      })
        .populate({ path: 'nguoi_chinh_sua', select: 'full_name avatar' })
        .lean(),

      ThietBiPhatHienModel.find({
        anh_vi_tri_id: { $in: anhViTriIds },
        is_deleted: false,
      })
        .populate({ path: 'nguoi_chinh_sua', select: 'full_name avatar' })
        .lean(),
    ]);

    const objBatThuong = functionCommons.groupBy(allBatThuong, 'anh_vi_tri_id');
    const objThietBi = functionCommons.groupBy(allThietBi, 'anh_vi_tri_id');

    for (const item of data) {
      item.image_bat_thuong = false;
      item.image_bat_thuong_ai = false;

      if (objBatThuong[item._id]?.length) {
        item.image_bat_thuong = true;
      }

      const batThuongFromAi = objBatThuong[item._id]?.filter(batThuong => batThuong.from_ai)?.length;
      if (batThuongFromAi > 0) {
        item.image_bat_thuong_ai = true;
      }

      item.thiet_bi_phat_hien = objThietBi[item._id];
      item.bat_thuong_phat_hien = objBatThuong[item._id];
    }

    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function removeMulti(req, res) {
  try {
    const imageRemove = req.body;
    if (!Array.isArray(imageRemove)) {
      return responseHelper.error(res, 404, '');
    }
    const data = await AnhViTriModel.updateMany({ _id: { $in: imageRemove } }, { is_deleted: true }).lean();
    if (data.ok && data.n === data.nModified) {
      const dataImage = await AnhViTriModel.find({ _id: imageRemove }).lean();

      for (let i = 0; i < dataImage.length; i++) {
        const item = dataImage[i];
        fileUtils.deleteFile(path.join(item.folder_path, item.image_id));
        fileUtils.deleteFile(path.join(item.folder_path, item.thumbnail_id));

        const ketQuaKT = await KetQuaKiemTraService.getAll({ anh_vi_tris: item._id });
        if (ketQuaKT.length) {
          ketQuaKT.forEach(ketQua => {
            const anhViTris = ketQua.anh_vi_tris.filter(image => image._id.toString() !== item._id.toString());
            if (anhViTris.length !== ketQua.anh_vi_tris.length) {
              ketQua.anh_vi_tris = anhViTris;
              ketQua.isEdited = true;
            }
          });
          await KetQuaKiemTraService.updateAll(ketQuaKT.filter(ketQua => ketQua.isEdited));
        }

        const suaChuaKhongKeHoach = await KetQuaSuaChuaKhongKeHoachService.getAll({ anh_vi_tris: item._id });
        if (suaChuaKhongKeHoach.length) {
          suaChuaKhongKeHoach.forEach(suaChua => {
            const anhViTris = suaChua.anh_vi_tris.filter(image => image._id.toString() !== item._id.toString());
            if (anhViTris.length !== suaChua.anh_vi_tris.length) {
              suaChua.anh_vi_tris = anhViTris;
              suaChua.isEdited = true;
            }
          });
          await KetQuaSuaChuaKhongKeHoachService.updateAll(suaChuaKhongKeHoach.filter(suaChua => suaChua.isEdited));
        }
      }
      return responseHelper.success(res, { success: true });
    } else {
      return responseHelper.error(res, 404, '');
    }
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function reanalyze(req, res) {
  try {
    if (!Array.isArray(req.body.data)) {
      return responseHelper.error(res, 404, '');
    }

    const dataImage = await AnhViTriModel.find({ _id: req.body.data }).lean();
    dataImage.forEach(item => {
      ObjectDetectionService.processImage(item);
    });

    return responseHelper.success(res, { success: 1 });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function getAutoDelete(req, res) {
  try {
    const deletedAnhViTris = await Service.countAutoDeleteImage();
    return responseHelper.success(res, {
      success: 1,
      count: deletedAnhViTris,
      configTime: await Service.getAutoDeleteTime(),
      query: await Service.buildAutoDeleteImageQuery(),
    });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function autoDelete(req, res) {
  try {
    const deletedAnhViTris = await Service.countAutoDeleteImage();
    Service.autoDeleteImage();
    return responseHelper.success(res, {
      success: 1,
      count: deletedAnhViTris,
      configTime: await Service.getAutoDeleteTime(),
      query: await Service.buildAutoDeleteImageQuery(),
    });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function getTrash(req, res) {
  try {
    const deletedAnhViTris = await Service.countTrashImage();
    return responseHelper.success(res, { success: 1, count: deletedAnhViTris, query: Service.buildTrashImageQuery() });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function cleanTrash(req, res) {
  try {
    const deletedAnhViTris = await Service.countTrashImage();
    Service.cleanTrash();
    return responseHelper.success(res, { success: 1, count: deletedAnhViTris, query: Service.buildTrashImageQuery() });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

