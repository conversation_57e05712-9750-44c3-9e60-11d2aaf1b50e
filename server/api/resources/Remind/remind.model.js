import mongoose, { Schema } from 'mongoose';
import { REMIND, USER } from '../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  user_id: { type: Schema.Types.ObjectId, ref: USER },
  source_type: { type: String },
  payload_type: { type: String },
  type: { type: String },
  status: { type: String },
  content: { type: String },
  payload: Object,
  source_id: { type: Schema.Types.ObjectId },
  title: String,
  is_deleted: { type: Boolean, default: false, select: false },
},
  {
    timestamps: {
      createdAt: 'thoi_gian_tao',
      updatedAt: 'thoi_gian_cap_nhat',
    },
    collation: { locale: 'vi' },
    versionKey: false,
  });
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(REMIND, schema, REMIND);
