import * as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../../helpers/validatorHelper';
import { REMIND_PAYLOAD_TYPE, REMIND_SOURCE_TYPE, REMIND_STATUS, REMIND_TYPE } from './remind.constants';
import REMIND from './remind.model';
import i18next from 'i18next';
import * as DuongDayService from '../TongKe/DuongDay/duongDay.service';
import * as ViTriService from '../TongKe/ViTri/viTri.service';
import * as KhoangCotService from '../TongKe/KhoangCot/khoangCot.service';
import * as TieuChiService from '../DanhMuc/NoiDungKiemTra/TieuChi/tieuChi.service';
import * as NoiDungKiemTraService from '../DanhMuc/NoiDungKiemTra/noiDung.service';
import * as NotificationService from '../Notification/notification.service';
import KetQuaKiemTra from '../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.model';
import Role from '../Role/role.model';
import User from '../User/user.model';
import { extractIds } from '../../utils/dataconverter';
import moment from 'moment';
import duongDayModel from '../TongKe/DuongDay/duongDay.model';

const Joi = require('joi');
const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return REMIND.create(value);
}

export function getAll(query, projection = {}) {
  return REMIND.find(query, projection).lean();
}

export function getAllByUserId(userId) {
  return REMIND.find({ user_id: userId, is_deleted: false }).sort('-thoi_gian_tao').lean();
}

export async function updateAll(chiTietUpdate) {
  for (const row of chiTietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await REMIND.findByIdAndUpdate(value._id, value);
  }
}

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};

async function generateContent(type, sourceType, source, action, data, payloadType) {
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1; //getMonth return (0-11)
  let timeRemind = currentMonth < 7 ? 1 : 2; // Định kỳ lần 1 hoặc lần 2

  if (type === REMIND_TYPE.USER_TO_USER) {
    return '';
  } else if (type === REMIND_TYPE.SYSTEM_TO_USER) {
    switch (sourceType) {
      case REMIND_SOURCE_TYPE.DO_THONG_SO:
        switch (payloadType) {
          case REMIND_PAYLOAD_TYPE.DO_DIEN_TRO_TIEP_DIA:
            // return `Đo điện trở tiếp địa định kỳ đường dây...năm...`;
            return `{"key": "DO_DIEN_TRO_TIEP_DIA_DINH_KY","lineName": "${data.ten_duong_day}","year": "${currentYear}"}`;
          case REMIND_PAYLOAD_TYPE.DO_NHIET_DO_TIEP_XUC:
            // return `Đo nhiệt độ tiếp xúc định kỳ đường dây...năm...`;
            return `{"key": "DO_NHIET_DO_TIEP_XUC_DINH_KY","lineName": "${data.ten_duong_day}","year": "${currentYear}", "time": "${timeRemind}"}`;
          default:
            // return `Bạn có thông báo mới`;
            return `{"key": "BAN_CO_THONG_BAO_MOI"}`;
        }
      case REMIND_SOURCE_TYPE.SUA_CHUA_DUONG_DAY:
        switch (payloadType) {
          case REMIND_PAYLOAD_TYPE.XU_LY_NOI_DUNG_TON_TAI:
            // return `Đo điện trở tiếp địa định kỳ đường dây...năm...`;
            const duongDay = await DuongDayService.getById(data?.duong_day_id[0]);
            const viTri = await ViTriService.getOne({ _id: data?.vi_tri_id });
            const khoangCot = await KhoangCotService.getOne({ _id: data?.khoang_cot_id });
            const tieuChi = await TieuChiService.getAll({ _id: data?.tieu_chi_id });
            let noiDungKiemTra;
            let hangMuc;
            if (tieuChi.length) noiDungKiemTra = await NoiDungKiemTraService.getAll({ _id: tieuChi[0].noi_dung_kiem_tra_id });
            if (noiDungKiemTra) hangMuc = noiDungKiemTra[0].ten_noi_dung;

            return `{"key": "XU_LY_NOI_DUNG_TON_TAI","lineName": "${duongDay?.ten_duong_day}","viTri":"${viTri?.ten_vi_tri}","khoangCot":"${khoangCot?.ten_khoang_cot}","hangMuc":"${hangMuc}"}`;
          default:
            // return `Bạn có thông báo mới`;
            return `{"key": "BAN_CO_THONG_BAO_MOI"}`;
        }
      default:
        return '';
    }
  }
  // return `Bạn có thông báo mới`;
  return `{"key": "BAN_CO_THONG_BAO_MOI"}`;
}

function generateTitle(type, sourceType, source, action, data, payloadType) {
  if (type === REMIND_TYPE.USER_TO_USER) {
    return ``;
  } else if (type === REMIND_TYPE.SYSTEM_TO_USER) {
    switch (sourceType) {
      case 'DO_THONG_SO':
        switch (payloadType) {
          case 'DO_DIEN_TRO_TIEP_DIA':
            // return `Đo điện trở tiếp địa định kỳ`;
            return `{"key": "DO_DIEN_TRO_TIEP_DIA_DINH_KY"}`;
          case 'DO_NHIET_DO_TIEP_XUC':
            // return `Đo nhiệt độ tiếp xúc định kỳ`;
            return `{"key": "DO_NHIET_DO_TIEP_XUC_DINH_KY"}`;
          default:
            // return `Bạn có thông báo mới`;
            return `{"key": "BAN_CO_THONG_BAO_MOI"}`;
        }
      case 'SUA_CHUA_DUONG_DAY':
        switch (payloadType) {
          case 'XU_LY_NOI_DUNG_TON_TAI':
            // return ``;
            return `{"key": "XU_LY_NOI_DUNG_TON_TAI"}`;
          default:
            // return `Bạn có thông báo mới`;
            return `{"key": "BAN_CO_THONG_BAO_MOI"}`;
        }
      default:
        return '';
    }
  }
}

export async function pushRemind(type, sourceType, source, users, action, data, payloadType, t) {
  try {

    const contentGenerated = await generateContent(type, sourceType, source, action, data, payloadType);
    const titleGenerated = generateTitle(type, sourceType, source, action, data, payloadType);
    const newNotificationData = users.map(userId => {
      return {
        user_id: userId,
        source_type: sourceType,
        type: type,
        payload_type: payloadType,
        payload_id: data?._id,
        status: REMIND_STATUS.SENT,
        content: contentGenerated,
        payload: data,
        title: titleGenerated,
        source_id: source?._id,
      };
    });
    const newNotifications = await REMIND.create(newNotificationData);
    NotificationService.pushNotification(t, newNotifications, 'Remind');
  } catch (e) {
    console.log('e', e);
  }
}

export function multiLanguageRemind(t, noti) {
  let notifyContent = '';
  let notifyContentObject;
  let notifyTitle = '';
  let notifyTitleObject;

  try {
    notifyContentObject = JSON.parse(`${noti.content}`);
  } catch {
    notifyContentObject = null;
  }

  try {
    notifyTitleObject = JSON.parse(`${noti.title}`);
  } catch {
    notifyTitleObject = null;
  }

  if (notifyContentObject?.key) {
    switch (notifyContentObject.key) {
      case 'DO_DIEN_TRO_TIEP_DIA_DINH_KY':
        notifyContent = (t ? t('periodic_measure_grounding_resistance_line_content') : i18next.t('periodic_measure_grounding_resistance_line_content')).format(notifyContentObject.lineName, notifyContentObject.year);
        break;
      case 'DO_NHIET_DO_TIEP_XUC_DINH_KY':
        notifyContent = (t ? t('periodic_measure_tangent_temperature_line_content') : i18next.t('periodic_measure_tangent_temperature_line_content')).format(notifyContentObject.lineName, notifyContentObject.year, notifyContentObject.time);
        break;
      case 'XU_LY_NOI_DUNG_TON_TAI':
        notifyContent = (t ? t('handling_existing_content') : i18next.t('handling_existing_content')).format(notifyContentObject.lineName, notifyContentObject.viTri === 'undefined' ? '' : notifyContentObject.viTri, notifyContentObject.khoangCot === 'undefined' ? '' : notifyContentObject.khoangCot, notifyContentObject.hangMuc);
        break;
      default:
        notifyContent = (t ? t('notify_default_new_message') : i18next.t('notify_default_new_message'));
        break;
    }
  } else {
    notifyContent = noti.content;
  }

  if (notifyTitleObject?.key) {
    switch (notifyTitleObject.key) {
      case 'DO_DIEN_TRO_TIEP_DIA_DINH_KY':
        notifyTitle = (t ? t('periodic_measure_grounding_resistance_line_title') : i18next.t('periodic_measure_grounding_resistance_line_title'));
        break;
      case 'DO_NHIET_DO_TIEP_XUC_DINH_KY':
        notifyTitle = (t ? t('periodic_measure_tangent_temperature_line_title') : i18next.t('periodic_measure_tangent_temperature_line_title'));
        break;
      case 'XU_LY_NOI_DUNG_TON_TAI':
        notifyTitle = (t ? t('handling_existing_title') : i18next.t('handling_existing_title'));
        break;
      default:
        notifyTitle = '';
        break;
    }
  } else {
    notifyTitle = noti.title;
  }

  noti.content = notifyContent;
  noti.title = notifyTitle;
}

export async function remindToTaiXLVH() {
  try {
    const listTonTaiXLVH = await KetQuaKiemTra.find({
      is_deleted: false,
      de_xuat_cap_doi: 'XU_LY_TRONG_VAN_HANH',
      thoi_han_xu_ly_cap_doi: {
        $gte: new Date(moment().startOf('day').add(1, 'd')),
        $lte: new Date(moment().endOf('day').add(1, 'd'))
      }}).populate({ path: 'duong_day_id' }).lean();
    const roles = await Role.find({ permissions: 'NHAC_NHO_TON_TAI_TRONG_XLVH#READ' }).lean();

    if (listTonTaiXLVH) {
      const type = 'SYSTEM_TO_USER';
      const sourceType = 'SUA_CHUA_DUONG_DAY';
      const payloadType = 'XU_LY_NOI_DUNG_TON_TAI';
      listTonTaiXLVH.map(async tonTai => {
        const donViId = tonTai.don_vi_id;
        const users = await User.find({
          don_vi_id: donViId,
          $or: [{ permissions: 'NHAC_NHO_TON_TAI_TRONG_XLVH#READ' }, { role_id: { $in: extractIds(roles) } }],
        }).lean();

        if (users.length) {
          await pushRemind(type, sourceType, null, users, null, tonTai, payloadType, null);
        }
      });
    }
  } catch (e) {
    console.log(e);
  }
}

export async function remindToTaiBaoCaoTTD() {
  try {
    const listTonTaiBaoCaoTTD = await KetQuaKiemTra.find(
      {
        is_deleted: false,
        de_xuat_cap_doi: 'BAO_CAO_TTD',
        thoi_han_xu_ly_cap_doi: {
          $gte: new Date(moment().startOf('day').add(1, 'd')),
          $lte: new Date(moment().endOf('day').add(1, 'd'))
        }
      }).populate([{ path: 'duong_day_id' }, { path: 'don_vi_id' }]).lean();
    const roles = await Role.find({ permissions: 'NHAC_NHO_TON_TAI_DA_BAO_CAO_TTD#READ' }).lean();
    if (listTonTaiBaoCaoTTD) {
      const type = 'SYSTEM_TO_USER';
      const sourceType = 'SUA_CHUA_DUONG_DAY';
      const payloadType = 'XU_LY_NOI_DUNG_TON_TAI';
      listTonTaiBaoCaoTTD.map(async tonTai => {
        const donViId = tonTai.don_vi_id?.don_vi_cha_id;
        const users = await User.find({
          don_vi_id: donViId,
          $or: [{ permissions: 'NHAC_NHO_TON_TAI_DA_BAO_CAO_TTD#READ' }, { role_id: { $in: extractIds(roles) } }],
        }).lean();
        if (users.length) {
          console.log('users', users)
          await pushRemind(type, sourceType, null, users, null, tonTai, payloadType, null);
        }
      });
    }
  } catch (e) {
    console.log(e);
  }
}

export async function remindDoThongSo(payloadType) {
  try {
    let ngayDo, dinhKy, add, time, ngayDoDelete;
    if (payloadType === 'DO_DIEN_TRO_TIEP_DIA') {
      ngayDo = 'ngay_do_dien_tro_ke_tiep';
      dinhKy = 'so_ngay_dinh_ky_do_dien_tro';
      add = 2;
      time = 'years';
    } else {
      ngayDo = 'ngay_do_nhiet_do_ke_tiep';
      dinhKy = 'so_ngay_dinh_ky_do_nhiet_do';
      add = 6;
      time = 'months';
    }
    const roles = await Role.find({ permissions: 'NHAC_NHO_DO_THONG_SO#READ' }).lean();
    const queryDuongDay = {
      [ngayDo]: {
        $gte: new Date(moment().startOf('day').add(7, 'd')),
        $lte: new Date(moment().endOf('day').add(7, 'd'))
      },
      is_deleted: false
    }
    //Tạo mảng danh sách các element chứa thông tin đường dây cần gửi nhắc nhở và _id 
    //của đơn vị quản lý các vị trí của đường đây:
    const unitAndLineArr = await getDonViAndDuongDay(queryDuongDay);
    if (unitAndLineArr.length) {
      let dataUpdateArr = [];
      const type = 'SYSTEM_TO_USER';
      const sourceType = 'DO_THONG_SO';
      await unitAndLineArr.forEach(async (line, index) => {
        const data = line.data[0];
        //tìm user cần gửi nhắc nhở
        const users = await User.find({
          don_vi_id: line.don_vi_id,
          $or: [{ permissions: 'NHAC_NHO_DO_THONG_SO#READ' }, { role_id: { $in: extractIds(roles) } }],
        }).lean();
        if (users.length) await pushRemind(type, sourceType, null, users, null, data, payloadType, null);

        //Tạo mảng đường dây cần update theo ngày đo mới
        let dataUpdate = { _id: data._id };
        if (!data[dinhKy] || data[dinhKy] === 0) {
          dataUpdate[ngayDo] = moment(data[ngayDo]).add(add, time);
        } else {
          dataUpdate[ngayDo] = moment(data[ngayDo]).add(data[dinhKy], 'days');
        }
        dataUpdate.updated_at = moment();
        dataUpdateArr.push(dataUpdate);
        //Nếu push remind xong thì update các đường dây
        if (index === unitAndLineArr.length - 1) {
          await DuongDayService.updateAll(dataUpdateArr);
        }
      });
    }
  } catch (e) {
    console.log(e);
  }
}

function getDonViAndDuongDay(query) {
  return duongDayModel.aggregate(
    [
      {
        $match: query
      },
      {
        $lookup: {
          from: 'VanHanh',
          localField: '_id',
          foreignField: 'duong_day_id',
          as: 'vanHanhs',
        }
      },
      {
        $unwind: {
          path: '$vanHanhs'
        },
      },
      {
        $lookup: {
          from: "ViTri",
          localField: 'vanHanhs.vi_tri_id',
          foreignField: '_id',
          as: 'viTris'
        }
      },
      {
        $unwind: {
          path: '$viTris'
        },
      },
      {
        $group: {
          _id: {
            duong_day_id: "$_id",
            don_vi_id: "$viTris.don_vi_id"
          }
        }
      },
      {
        $lookup: {
          from: "DuongDay",
          localField: '_id.duong_day_id',
          foreignField: '_id',
          as: 'data'
        }
      },
      {
        $addFields: {
          _id: "$_id.duong_day_id",
          don_vi_id: "$_id.don_vi_id"
        }
      }
    ],
  ).option({
    'allowDiskUse': true,
  });
}

export default {
  getAllByUserId,
  multiLanguageRemind,
};
