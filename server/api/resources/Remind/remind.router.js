import express from 'express';
import passport from 'passport';
import * as Controller from './remind.controller';
import { loggerMiddleware } from '../../logs/middleware';

export const remindRouter = express.Router();
remindRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
remindRouter.route('/')
  .get(Controller.getAll)
  .post(Controller.create);

  remindRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
