import * as responseHelper from '../../common/responseHelper';

import {
  handleRemoveDuplicateConstructions,
  handleRemoveDuplicateLines,
  handleRemoveDuplicateLocation,
} from './removeDuplicates.service';
import { convertToCamelCase } from '../../common/dataConverter';

export async function removeDuplicateLines(req, res) {
  try {
    const removeRecord = req.query.hasOwnProperty('remove-lines');
    const docs = await handleRemoveDuplicateLines(removeRecord);
    return responseHelper.success(res, docs);
  } catch (err) {
    return responseHelper.error(res, err, 400);
  }
}

export async function removeDuplicateLocations(req, res) {
  try {
    const query = convertToCamelCase(req.query);
    console.log('req.query', query);
    const removeRecord = query.hasOwnProperty('removeLocations');
    const { donViId } = query;
    const docs = await handleRemoveDuplicateLocation(removeRecord, donViId);
    return responseHelper.success(res, docs);
  } catch (err) {
    return responseHelper.error(res, err, 400);
  }
}

export async function removeDuplicateConstructions(req, res) {
  try {
    const removeRecord = req.query.hasOwnProperty('remove-constructions');
    const docs = await handleRemoveDuplicateConstructions(removeRecord);
    return responseHelper.success(res, docs);
  } catch (err) {
    return responseHelper.error(res, err, 400);
  }
}
