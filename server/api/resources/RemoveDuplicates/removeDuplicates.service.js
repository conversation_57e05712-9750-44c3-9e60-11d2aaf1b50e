import { convertObject, extractIds, groupBy } from '../../utils/dataconverter';

// import * as LoaiTaiLieuService from '../../DanhMuc/LoaiTaiLieu/loaiTaiLieu.service';
import * as VanHanhService from '../TongKe/VanHanh/vanHanh.service';
import * as CongViecPhatSinhService from '../QuanLyVanHanh/CongViecPhatSinh/congViecPhatSinh.service';
import * as KetQuaKiemTraService from '../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.service';
import * as KetQuaSuaChuaKhongKeHoachService
  from '../QuanLyVanHanh/KetQuaSuaChuaKhongKeHoach/ketQuaSuaChuaKhongKeHoach.service';
import * as DieuKienTienHanhService from '../QuanLyVanHanh/PhieuCongTac/DieuKienTienHanh/dieuKienTienHanh.service';
import * as ThuTucCatDienService from '../QuanLyVanHanh/PhieuCongTac/ThuTucCatDien/thuTucCatDien.service';
import * as TonTaiCapTrenService from '../QuanLyVanHanh/PhieuGiaoViec/TonTaiCapTren/tonTaiCapTren.service';
import * as PhieuGiaoViecService from '../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';
import * as DongBoDuLieuDoService from '../Report/DongBoDuLieuDo/dongBo.service';
import * as DuongDayService from '../TongKe/DuongDay/duongDay.service';
import * as HanhLangTuyenService from '../TongKe/HanhLangTuyen/hanhLangTuyen.service';
import * as KhoangCotService from '../TongKe/KhoangCot/khoangCot.service';
import * as SuCoDuongDayService from '../TongKe/SuCoDuongDay/suCoDuongDay.service';
import * as TongKeMoiNoiService from '../TongKe/TongKeMoiNoi/tongKeMoiNoi.service';
import { formatUnique } from '../../common/functionCommons';
import * as ViTriService from '../TongKe/ViTri/viTri.service';
import * as AnhViTriService from '../AnhViTri/anhViTri.service';
import * as CauHinhBayService from '../CauHinhBay/cau_hinh_bay.service';
import * as KetQuaDoNhietDoService from '../KetQuaDoNhietDo/ketQuaDoNhietDo.service';
import * as GiaoCheoService from '../TongKe/GiaoCheo/giaoCheo.service';
import * as MissionPointService from '../MissionPoint/mission_point.service';
import * as TonTaiLamQuangService from '../QuanLyTonTaiCongTrinhXayDung/TonTaiLamQuang/tonTaiLamQuang.service';
import * as DoDienTroService from '../QuanLyVanHanh/DoDienTro/doDienTro.service';
import * as BienPhapAnToanBoSungService
  from '../QuanLyVanHanh/PhieuCongTac/BienPhapAnToanBoSung/bienPhapAnToanBoSung.service';
import * as CongTacHangNgayService from '../QuanLyVanHanh/PhieuCongTac/CongTacHangNgay/congTacHangNgay.service';
import * as ThuTucPhamViLamViecService
  from '../QuanLyVanHanh/PhieuCongTac/ThuTucPhamViLamViec/thuTucPhamViLamViec.service';
import * as ThuTucRaoChanBienBaoService
  from '../QuanLyVanHanh/PhieuCongTac/ThuTucRaoChanBienBao/thuTucRaoChanBienBao.service';
import * as ThuTucTiepDatService from '../QuanLyVanHanh/PhieuCongTac/ThuTucTiepDat/thuTucTiepDat.service';
import * as ViTriCongViecService from '../QuanLyVanHanh/ViTriCongViec/viTriCongViec.service';
import * as ReportDoDienTroService from '../Report/ReportDoDienTro/doDienTro.service';
import * as ReportDoNhietDoService from '../Report/ReportDoNhietDo/doNhietDo.service';
import * as CotDienService from '../TongKe/CotDien/cotDien.service';
import * as DayCapQuangService from '../TongKe/DayCapQuang/dayCapQuang.service';
import * as DayChongSetService from '../TongKe/DayChongSet/dayChongSet.service';
import * as KhoangNeoService from '../TongKe/KhoangNeo/khoangNeo.service';
import * as TiepDatService from '../TongKe/TiepDat/tiepDat.service';

import * as CongTrinhService from '../TongKe/CongTrinh/congTrinh.service';
import * as BienBanService from '../QuanLyTonTaiCongTrinhXayDung/BienBanDongDien/bienBan.service';
import * as PhuLucTonTaiService from '../QuanLyTonTaiCongTrinhXayDung/PhuLucTonTai/phuLucTonTai.service';
import * as TonTaiThoiDiemBanGiaoService
  from '../QuanLyTonTaiCongTrinhXayDung/TonTaiThoiDiemBanGiao/tonTaiThoiDiemBanGiao.service';
import * as CongTrinhCongViecService from '../QuanLyVanHanh/CongTrinhCongViec/congTrinhCongViec.service';
import * as LinkFileService from '../QuanLyVanHanh/HoSo/LinkFile/linkFile.service';
import * as DonViService from '../DonVi/donVi.service';


const MODEL_REF_DUONG_DAY = [
  { service: KetQuaKiemTraService, name: 'KetQuaKiemTra', fields: ['duong_day_id'] },
  { service: KetQuaSuaChuaKhongKeHoachService, name: 'KetQuaSuaChuaKhongKeHoach', fields: ['duong_day_id'] },
  { service: VanHanhService, name: 'VanHanh', fields: ['duong_day_id', 'duong_day_chinh'] },
  { service: CongViecPhatSinhService, name: 'CongViecPhatSinh', fields: ['duong_day_id'] },
  { service: DieuKienTienHanhService, name: 'DieuKienTienHanh', fields: ['duong_day_id'] },
  { service: ThuTucCatDienService, name: 'ThuTucCatDien', fields: ['duong_day_id'] },
  { service: TonTaiCapTrenService, name: 'TonTaiCapTren', fields: ['duong_day_id', 'duong_day_ids'] },
  { service: PhieuGiaoViecService, name: 'PhieuGiaoViec', fields: ['duong_day_id', 'duong_day_ids'] },
  { service: DongBoDuLieuDoService, name: 'DongBoDuLieuDo', fields: ['duong_day_id'] },
  { service: DuongDayService, name: 'DuongDay', fields: ['duong_day_chinh_id', 'duong_day_cu_id'] },
  { service: HanhLangTuyenService, name: 'HanhLangTuyen', fields: ['duong_day_id'] },
  { service: KhoangCotService, name: 'KhoangCot', fields: ['duong_day_id'] },
  { service: SuCoDuongDayService, name: 'SuCoDuongDay', fields: ['duong_day_id'] },
  { service: TongKeMoiNoiService, name: 'TongKeMoiNoi', fields: ['duong_day_id'] },
];


const MODEL_REF_VI_TRI = [
  { service: VanHanhService, name: 'VanHanh', fields: ['vi_tri_id'] },
  { service: AnhViTriService, name: 'AnhViTri', fields: ['vi_tri_id', 'vi_tri_chup'] },
  { service: CauHinhBayService, name: 'CauHinhBay', fields: ['vi_tri_id', 'vi_tri_range'] },
  { service: KetQuaDoNhietDoService, name: 'KetQuaDoNhietDo', fields: ['vi_tri_id'] },
  { service: GiaoCheoService, name: 'GiaoCheo', fields: ['vi_tri_id'] },
  { service: MissionPointService, name: 'MissionPoint', fields: ['vi_tri_id'] },
  { service: TonTaiLamQuangService, name: 'TonTaiLamQuang', fields: ['vi_tri_id'] },
  { service: CongViecPhatSinhService, name: 'CongViecPhatSinh', fields: ['vi_tri_id'] },
  { service: DoDienTroService, name: 'DoDienTro', fields: ['vi_tri_id'] },
  { service: KetQuaKiemTraService, name: 'KetQuaKiemTra', fields: ['vi_tri_id'] },
  { service: KetQuaSuaChuaKhongKeHoachService, name: 'KetQuaSuaChuaKhongKeHoach', fields: ['vi_tri_id'] },
  { service: BienPhapAnToanBoSungService, name: 'BienPhapAnToanBoSung', fields: ['vi_tri_id'] },
  { service: CongTacHangNgayService, name: 'CongTacHangNgay', fields: ['vi_tri_id'] },
  { service: ThuTucPhamViLamViecService, name: 'ThuTucPhamViLamViec', fields: ['vi_tri_id'] },
  { service: ThuTucRaoChanBienBaoService, name: 'ThuTucRaoChanBienBao', fields: ['vi_tri_id'] },
  { service: ThuTucTiepDatService, name: 'ThuTucTiepDat', fields: ['vi_tri_id'] },
  { service: ViTriCongViecService, name: 'ViTriCongViec', fields: ['vi_tri_id'] },
  { service: ReportDoDienTroService, name: 'ReportDoDienTro', fields: ['vi_tri_id'] },
  { service: ReportDoNhietDoService, name: 'ReportDoNhietDo', fields: ['vi_tri_id'] },
  { service: CotDienService, name: 'CotDien', fields: ['vi_tri_id'] },
  { service: DayCapQuangService, name: 'DayCapQuang', fields: ['vi_tri_id'] },
  { service: DayChongSetService, name: 'DayChongSet', fields: ['vi_tri_id'] },
  { service: GiaoCheoService, name: 'GiaoCheo', fields: ['vi_tri_id'] },
  { service: KhoangCotService, name: 'KhoangCot', fields: ['vi_tri_bat_dau_id', 'vi_tri_ket_thuc_id', 'vi_tri_id'] },
  { service: KhoangNeoService, name: 'KhoangNeo', fields: ['vi_tri_bat_dau_id', 'vi_tri_ket_thuc_id'] },
  { service: SuCoDuongDayService, name: 'SuCoDuongDay', fields: ['vi_tri_id'] },
  { service: TiepDatService, name: 'TiepDat', fields: ['vi_tri_id'] },
];


const MODEL_REF_CONG_TRINH = [
  { service: ViTriService, name: 'ViTri', fields: ['cong_trinh_id'] },
  { service: BienBanService, name: 'BienBan', fields: ['cong_trinh_id'] },
  { service: PhuLucTonTaiService, name: 'PhuLucTonTai', fields: ['cong_trinh_id'] },
  { service: TonTaiLamQuangService, name: 'TonTaiLamQuang', fields: ['cong_trinh_id'] },
  { service: TonTaiThoiDiemBanGiaoService, name: 'TonTaiThoiDiemBanGiao', fields: ['cong_trinh_id'] },
  { service: CongTrinhCongViecService, name: 'CongTrinhCongViec', fields: ['cong_trinh_id'] },
  { service: LinkFileService, name: 'LinkFile', fields: ['doi_tuong_id'] },
  { service: CongTrinhService, name: 'CongTrinh', fields: ['cong_trinh_chinh_id'] },
];

export async function handleRemoveDuplicateLines(removeRecord = false) {
  const allDuongDay = await DuongDayService.getAll({ is_deleted: false }, { ma_duong_day: 1 });

  const duongDayGroup = groupBy(allDuongDay, 'ma_duong_day');
  const dataReturn = await getDataDuplicate(duongDayGroup, MODEL_REF_DUONG_DAY);

  if (removeRecord) {
    const recordIdRemove = dataReturn.recordDuplicates.map(record => record.remove).flat();
    dataReturn.recordRemoved = await DuongDayService.remove({ _id: recordIdRemove });
  }
  return dataReturn;
}

export async function handleRemoveDuplicateLocation(removeRecord = false, donViId = undefined) {
  const allViTri = await ViTriService.getAll({ is_deleted: false }, { ma_vi_tri: 1, don_vi_id: 1 });
  const groupViTriByDonVi = groupBy(allViTri, 'don_vi_id');

  const donViQuery = { is_deleted: false };
  if (donViId) {
    donViQuery._id = donViId;
  }
  const allDonVi = await DonViService.getAll(donViQuery, { ten_don_vi: 1 });

  const dataReturn = [];

  for (let i = 0; i < allDonVi.length; i++) {
    const donVi = allDonVi[i];
    if (groupViTriByDonVi.hasOwnProperty(donVi._id)) {
      const viTriGroup = groupBy(groupViTriByDonVi[donVi._id], 'ma_vi_tri');

      const dataHandled = await getDataDuplicate(viTriGroup, MODEL_REF_VI_TRI);
      dataHandled.donViId = donVi;
      if (removeRecord) {
        const recordIdRemove = dataHandled.recordDuplicates.map(record => record.remove).flat();
        dataHandled.recordRemoved = await ViTriService.remove({ _id: recordIdRemove });
      }
      dataReturn.push(dataHandled);
    }
  }

  return dataReturn;
}

export async function handleRemoveDuplicateConstructions(removeRecord = false) {
  const allCongTrinh = await CongTrinhService.getAll({ is_deleted: false }, { ma_cong_trinh: 1 });

  const congTrinhGroup = groupBy(allCongTrinh, 'ma_cong_trinh');
  const dataReturn = await getDataDuplicate(congTrinhGroup, MODEL_REF_CONG_TRINH);

  if (removeRecord) {
    const recordIdRemove = dataReturn.recordDuplicates.map(record => record.remove).flat();
    dataReturn.recordRemoved = await CongTrinhService.remove({ _id: recordIdRemove });
  }
  return dataReturn;
}

async function getDataDuplicate(recordGroup, modelRelate) {
  const recordDuplicates = [];
  const recordUpdated = [];

  Object.entries(recordGroup).forEach(([key, value]) => {
    if (value?.length > 1) {
      recordDuplicates.push({
        retain: value[0]._id,
        remove: extractIds(value.slice(1)),
      });
    }
  });

  for (let i = 0; i < recordDuplicates.length; i++) {
    const duplicate = recordDuplicates[i];
    for (let j = 0; j < duplicate.remove.length; j++) {
      try {
        const willUpdate = await updateRecord(duplicate.remove[j], duplicate.retain, modelRelate);
        recordUpdated.push(willUpdate);

      } catch (e) {
        console.log('e1', e);
      }
    }
  }
  return { recordDuplicates, recordUpdated };
}

async function updateRecord(removeId, retainId, modelRelate = []) {
  if (!Array.isArray(modelRelate)) return [];

  removeId = removeId.toString();
  retainId = retainId.toString();
  const recordReturn = [];
  for (let i = 0; i < modelRelate.length; i++) {
    const model = modelRelate[i];
    const projection = {};
    model.fields.forEach(field => {
      projection[field] = 1;
    });

    if (!model.service.hasOwnProperty('updateAll')) {
      console.log('model.name', model.name);
    }

    const currentData = await model.service.getAll(
      {
        '$or': model.fields.map(field => {
          return { [field]: removeId };
        }),
      },
      projection,
    );

    if (currentData?.length) {
      currentData.forEach(current => {
        Object.keys(current).forEach(key => {
          if (!model.fields.includes(key) && key !== '_id') delete current[key];
        });

        model.fields.forEach(field => {
          if (current[field]?.hasOwnProperty('_id')) {
            current[field] = current[field]._id;
          }
          if (Array.isArray(current[field])) {
            current[field] = current[field].map(item => item.toString());
            if (current[field].includes(removeId)) {
              current[field] = formatUnique(current[field].map(item => item === removeId ? retainId : item));
            }
          } else if (current[field]?.toString() === removeId) {
            current[field] = retainId;
          }
        });

      });

      try {
        await model.service.updateAll(currentData);
      } catch (e) {

      }


      recordReturn.push({
        name: model.name,
        data: currentData,
      });
    }
  }

  return recordReturn.flat();
}
