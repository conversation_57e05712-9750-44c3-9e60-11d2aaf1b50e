import * as responseHelper from "../../helpers/responseHelper";
import * as systemHelper from "../../helpers/systemHelper";

export async function getInfo(req, res) {
  try {
    const systemInfo = await systemHelper.getInfo();
    return responseHelper.success(res, systemInfo);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getStatus(req, res) {
  try {
    const systemStatus = await systemHelper.getStatus();
    return responseHelper.success(res, systemStatus);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}
