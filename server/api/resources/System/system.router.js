import express from "express";
import * as configChungController from "./system.controller";
import { sysadminAuthorizationMiddleware } from "../RBAC/middleware";
import passport from "passport";
import { loggerMiddleware } from "../../logs/middleware";

export const systemRouter = express.Router();
systemRouter.use(passport.authenticate("jwt", { session: false }), loggerMiddleware);
systemRouter.route("/info").get(sysadminAuthorizationMiddleware(), configChungController.getInfo);
systemRouter.route("/status").get(sysadminAuthorizationMiddleware(), configChungController.getStatus);
