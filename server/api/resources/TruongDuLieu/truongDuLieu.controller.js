import Model from './truongDuLieu.model';
import * as controllerHelper from '../../helpers/controllerHelper';
import * as Service from './truongDuLieu.service';
import { updateSchemaModel } from './truongDuLieu.service';
import queryHelper from '../../helpers/queryHelper';
import * as responseHelper from '../../helpers/responseHelper';
import CommonError from '../../error/CommonError';
import { findOneById } from '../../helpers/controllerHelper';

async function handleChangeData(data) {
  await updateSchemaModel(data.model);
}

export const findOne = controllerHelper.createFindOneFunction(Model);
export const remove = controllerHelper.createRemoveFunction(Model, handleChangeData);

async function handleCheckUnique(t, res, value) {
  const query = { model: value.model, is_deleted: false };
  if (value._id) {
    query._id = { $ne: value._id };
  }

  const checkKey = await Model.findOne({ ...query, field_key: value.field_key });
  if (checkKey) {
    return responseHelper.error(res, { message: t('field_code_existed') }, 404);
  }
  const checkName = await Model.findOne({ ...query, field_name: value.field_name });
  if (checkName) {
    return responseHelper.error(res, { message: t('field_name_existed') }, 404);
  }
}

export async function update(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    const beforeUpdate = await Model.findById(id);
    if (!beforeUpdate) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    const isDuplicate = await handleCheckUnique(t, res, { ...value, model: beforeUpdate.model });
    if (isDuplicate) return;

    value.nguoi_chinh_sua = req.user._id;
    if (!value.thoi_gian_cap_nhat) {
      value.thoi_gian_cap_nhat = Date.now();
    }
    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true });
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    const updatedData = await findOneById(Model, id, [], true);

    handleChangeData(updatedData);

    return responseHelper.success(res, updatedData);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const { t } = req;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    const isDuplicate = await handleCheckUnique(t, res, value);
    if (isDuplicate) return;

    value.nguoi_tao = req.user._id;
    value.nguoi_chinh_sua = req.user._id;
    const data = await Model.create(value);
    const createdData = await findOneById(Model, data._id, [], true);

    handleChangeData(createdData);

    return responseHelper.success(res, createdData);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const data = await Model.find(criteria);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}
