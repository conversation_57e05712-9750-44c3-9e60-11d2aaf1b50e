import express from 'express';
import passport from 'passport';
import * as loaiDuongDayController from './truongDuLieu.controller';
import { authorizationMiddleware } from '../RBAC/middleware';
import Permission from '../RBAC/permissions/ExtraDataPermission';
import { loggerMiddleware } from '../../logs/middleware';

export const truongDuLieuRouter = express.Router();
truongDuLieuRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
truongDuLieuRouter.post('*', authorizationMiddleware([Permission.CREATE]));
truongDuLieuRouter.put('*', authorizationMiddleware([Permission.UPDATE]));
truongDuLieuRouter.delete('*', authorizationMiddleware([Permission.DELETE]));
truongDuLieuRouter.route('/')
  .get(loaiDuongDayController.getAll)
  .post(loaiDuongDayController.create);

truongDuLieuRouter
  .route('/:id')
  .get(loaiDuongDayController.findOne)
  .delete(loaiDuongDayController.remove)
  .put(loaiDuongDayController.update);
