import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { TRUONG_DU_LIEU } from '../../constant/dbCollections';
import { KIEU_DU_LIEU } from './truongDuLieu.constant';

const schema = new Schema({
  model: { type: String, default: null, required: true },
  field_name: { type: String, required: true, validate: /\S+/ },
  field_key: { type: String, required: true, validate: /\S+/ },
  thu_tu: { type: Number },
  field_type: {
    type: String,
    enum: Object.values(KIEU_DU_LIEU),
    default: null,
  },
  field_options: Array,
  is_active: { type: Boolean, default: false },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

export default mongoose.model(TRUONG_DU_LIEU, schema, TRUONG_DU_LIEU);
