import * as Validator<PERSON>elper from '../../helpers/validatorHelper';
import mongoose from 'mongoose';
import Model from './truongDuLieu.model';
import TRUONG_DU_LIEU from './truongDuLieu.model';
import { KIEU_DU_LIEU } from './truongDuLieu.constant';

const Joi = require('joi');

const objSchema = Joi.object({
  name: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên loại đường dây')),
  description: Joi.string().allow(null).allow(''),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}


function getTypeOfTruongDuLieu(kieuDuLieu) {
  switch (kieuDuLieu) {
    case KIEU_DU_LIEU.DANH_SACH:
      return String;
    case KIEU_DU_LIEU.THOI_GIAN:
      return Date;
    case KIEU_DU_LIEU.VAN_BAN:
      return String;
  }
}

export async function updateSchemaModel(modelName) {
  try {
    const modelChange = mongoose.model(modelName);
    const modelSchema = modelChange.schema;
    const allTruongDuLieu = await TRUONG_DU_LIEU.find({ model: modelName, is_deleted: false, is_active: true }).lean();
    modelSchema.remove('extra');
    const duLieuThemSchema = {};
    allTruongDuLieu.forEach(truongDuLieu => {
      duLieuThemSchema[truongDuLieu.field_key] = { type: getTypeOfTruongDuLieu(truongDuLieu.field_type) };
    });
    modelSchema.add({ 'extra': duLieuThemSchema });
  } catch (e) {
    console.log(e);
  }
}
