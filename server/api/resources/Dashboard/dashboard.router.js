import express from 'express';
import passport from 'passport';
import * as Controller from './dashboard.controller';
import { authorizationMiddleware } from '../RBAC/middleware';
import ReportPermission from '../RBAC/permissions/ReportPermission';
import { banDoLuoi } from './dashboard.controller';
import { loggerMiddleware } from '../../logs/middleware';

export const dashboardRouter = express.Router();

dashboardRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

dashboardRouter
  .route('/phieugiaoviec')
  .get(Controller.getAllPhieuGiaoViec);

dashboardRouter
  .route('/chamtiendo')
  .get(Controller.getAllChamTienDo);

dashboardRouter
  .route('/locationschecked')
  .get(Controller.getDataLocationsChecked);

dashboardRouter
  .route('/congviechoanthanh')
  .get(Controller.getDataCongViecHoanThanh);

dashboardRouter
  .route('/bandoluoi')
  .get(Controller.banDoLuoi);

dashboardRouter
  .route('/bandoluoi/realtime')
  .get(Controller.banDoLuoiRealtime);

dashboardRouter
  .route('/dieukienantoan')
  .get(Controller.getDataDieuKienAnToan);

dashboardRouter
  .route('/dieukienantoan/download')
  .get(Controller.downloadPhieuCoDieuKienAnToan);

dashboardRouter
  .route('/trambienap')
  .get(Controller.tramBienAp);

dashboardRouter
  .route('/nhamaydien')
  .get(Controller.nhaMayDien);