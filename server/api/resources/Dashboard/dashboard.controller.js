import momentTimezone from 'moment-timezone';

import { extractIds, extractKeys } from '../../utils/dataconverter';
import { getFilePath } from '../../utils/fileUtils';

import * as responseHelper from '../../helpers/responseHelper';
import queryHelper from '../../helpers/queryHelper';

import { LOAI_DIEU_KIEN_AN_TOAN, TEMPLATES_DIRS } from '../../constant/constant';
import { TRANG_THAI_HOAN_THANH } from '../DanhMuc/TrangThaiHoanThanh';
import { TRANG_THAI_PHIEU } from '../DanhMuc/TrangThaiCongViec';
import { KIEM_TRA, LOAI_CONG_VIEC, NHOM_CONG_VIEC } from '../DanhMuc/LoaiCongViec';

import { generateDocument } from '../Report/GenerateFile/generate.controller';
import { addIndexToListData, downlineData, formatUnique, groupBy } from '../../common/functionCommons';

import PHIEU_GIAO_VIEC from '../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.model';
import NHA_MAY_DIEN from '../TongKe/NhaMayDien/nhamaydien.model';
import VAN_HANH from '../TongKe/VanHanh/vanHanh.model';
import VI_TRI, { LOAI_VI_TRI } from '../TongKe/ViTri/viTri.model';

import * as DonViService from '../DonVi/donVi.service';
import * as DuongDayService from '../TongKe/DuongDay/duongDay.service';
import * as DashboardService from './dashboard.service';
import * as ViTriCongViecService from '../QuanLyVanHanh/ViTriCongViec/viTriCongViec.service';
import * as PhieuGiaoViecService from '../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';
import * as TramBienApService from '../TongKe/TramBienAp/trambienap.service';

import quanLyVanHanhCommons from '../QuanLyVanHanh/quanLyVanHanhCommons';
import { TINH_TRANG_VAN_HANH } from '../DanhMuc/TinhTrangVanHanh';


async function buildPhieuGiaoViecQuery(req, criteria) {
  criteria.don_vi_giao_phieu_id = await DonViService.getDonViQuery(req, criteria.don_vi_giao_phieu_id, true);
  return quanLyVanHanhCommons.buildThoiGianCongTacQuery(criteria);
}

export async function getAllPhieuGiaoViec(req, res) {
  try {
    const { t } = req;
    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;
    let congViec = undefined;
    for (let [key, value] of Object.entries(criteria)) {
      if (key === 'id_ton_tai' || key === 'tinh_trang_phieu' || key === 'loai_kiem_tra' || key === 'phuong_phap_thuc_hien') {
        delete criteria[key];
      }
      if (key === 'cong_viec') {
        congViec = value;
        delete criteria[key];
      }
    }
    criteria = await buildPhieuGiaoViecQuery(req, criteria);
    let data = await PHIEU_GIAO_VIEC.find(criteria).select('loai_cong_viec trang_thai_cong_viec').lean();
    congViec && (data = data.filter(item => LOAI_CONG_VIEC[item.loai_cong_viec]?.type === congViec));
    const groupByTrangThaiCongViec = groupBy(data, 'trang_thai_cong_viec');

    let groupTinhTrang = [], groupLoaiCongViec = [], mapNhomCongViec = {};
    Object.values(TRANG_THAI_PHIEU).forEach(status => {
      status.soluong = groupByTrangThaiCongViec[status.code]?.length || 0;
      groupTinhTrang = [...groupTinhTrang, status];
    });

    const groupByLoaiCongViec = groupBy(data, 'loai_cong_viec');
    Object.values(LOAI_CONG_VIEC).forEach(item => {
      item.soluong = groupByLoaiCongViec[item.code]?.length || 0;
      item.nameType = t(NHOM_CONG_VIEC[item.type].code);
      groupLoaiCongViec = [...groupLoaiCongViec, item];
      if (!mapNhomCongViec[item.type]) {
        mapNhomCongViec[item.type] = {
          type: item.type,
          name: t(NHOM_CONG_VIEC[item.type].code),
          soluong: item.soluong,
        };
      } else {
        mapNhomCongViec[item.type].soluong += item.soluong;
      }
    });
    let groupNhomCongViec = [];
    Object.values(mapNhomCongViec).forEach(nhomCongViec => {
      groupNhomCongViec.push(nhomCongViec);
    });
    let dataResponse = {
      loai_cong_viec: groupNhomCongViec,
      chi_tiet_tinh_trang: groupLoaiCongViec,
      tinh_trang: groupTinhTrang,
    };
    return responseHelper.success(res, dataResponse);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function getAllChamTienDo(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;
    let tinhTrangPhieu = undefined;
    criteria.cong_viec && delete criteria.cong_viec;
    criteria.tinh_trang_phieu && (tinhTrangPhieu = criteria.tinh_trang_phieu) && delete criteria.tinh_trang_phieu;
    criteria = await buildPhieuGiaoViecQuery(req, criteria);

    const tongHopPhieuChamTienDo = await PhieuGiaoViecService.tongHopPhieuGiaoViecChamTienDo(criteria);
    let huyPhieu = 0, thucHienCham = 0, xacNhanKhoaCham = 0;
    let phieuHuyIds = [], phieuThucHienChamIds = [], phieuXacNhanKhoaChamIds = [];
    tongHopPhieuChamTienDo.forEach(item => {
      item.phieu_da_huy_do_tiep_nhan_cham === 1.0 && phieuHuyIds.push(item.phieu_giao_viec_id);
      item.phieu_dang_thuc_hien_cham === 1.0 && phieuThucHienChamIds.push(item.phieu_giao_viec_id);
      item.phieu_dang_xac_nhan_khoa_cham === 1.0 && phieuXacNhanKhoaChamIds.push(item.phieu_giao_viec_id);
      huyPhieu += item.phieu_da_huy_do_tiep_nhan_cham;
      thucHienCham += item.phieu_dang_thuc_hien_cham;
      xacNhanKhoaCham += item.phieu_dang_xac_nhan_khoa_cham;
    });

    const phieuGiaoViecIds = tinhTrangPhieu === 'HUY_PHIEU_DO_TIEP_NHAN_CHAM' ? phieuHuyIds
      : tinhTrangPhieu === 'DANG_THUC_HIEN_CHAM' ? phieuThucHienChamIds : phieuXacNhanKhoaChamIds;

    const allPhieuGiaoViec = await PHIEU_GIAO_VIEC.find({ _id: { $in: phieuGiaoViecIds } })
      .populate({ path: 'chi_huy_truc_tiep_id', select: 'full_name' })
      .select('chi_huy_truc_tiep_id loai_cong_viec so_phieu thoi_gian_cong_tac_ket_thuc thoi_gian_cong_tac_bat_dau')
      .select('qua_thoi_gian_tiep_nhan trang_thai_cong_viec')
      .select('thoi_gian_tiep_nhan thoi_gian_xac_nhan_khoa_phieu thoi_gian_khoa_phieu thoi_gian_tao_phieu created_at')
      .lean();

    let result = {
      phieu_da_huy_do_tiep_nhan_cham: huyPhieu,
      phieu_dang_thuc_hien_cham: thucHienCham,
      phieu_dang_xac_nhan_khoa_cham: xacNhanKhoaCham,
    };

    let docs = {
      phieu_giao_viec: result,
      cham_tien_do: allPhieuGiaoViec || [],
    };
    return responseHelper.success(res, docs);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getDataLocationsChecked(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;
    criteria.cong_viec && delete criteria.cong_viec;
    criteria.tinh_trang_phieu && delete criteria.tinh_trang_phieu;
    criteria = await buildPhieuGiaoViecQuery(req, criteria);
    criteria.thoi_gian_cong_tac_bat_dau ||= criteria.created_at;
    delete criteria.created_at;
    const viTriIds = await VI_TRI.distinct('_id',
      {
        don_vi_id: criteria.don_vi_giao_phieu_id,
        loai_vi_tri: { $ne: LOAI_VI_TRI.XA_POOCTIC },
        is_deleted: false,
      });

    const allVanHanh = await VAN_HANH.find({
      is_deleted: false,
      vi_tri_id: viTriIds,
    }).select('duong_day_id vi_tri_id').lean();

    const groupVanHanhByDuongDayId = groupBy(allVanHanh, 'duong_day_id');
    const allDuongDay = await DuongDayService.getAll({
      _id: extractKeys(allVanHanh, 'duong_day_id'),
      is_deleted: false,
    }).select('ten_duong_day');
    const duongDayIds = extractIds(allDuongDay);

    const mapViTriByDuongDay = {};
    for (let duongDayId of duongDayIds) {
      mapViTriByDuongDay[duongDayId] = formatUnique(extractKeys(groupVanHanhByDuongDayId[duongDayId], 'vi_tri_id'));
    }
    const loaiCongViecCodes = Object.values(LOAI_CONG_VIEC).flatMap(cv => cv.type === KIEM_TRA ? [cv.code] : []);

    criteria.loai_cong_viec = { $in: loaiCongViecCodes };
    criteria.duong_day_ids = { $in: duongDayIds };
    criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code;

    const allPhieuGiaoViec = await PHIEU_GIAO_VIEC.find(criteria).select('duong_day_ids');

    const allViTriCongViecs = await ViTriCongViecService.getAll({
      phieu_giao_viec_id: { $in: extractIds(allPhieuGiaoViec) },
      vi_tri_id: { $exists: true },
      khoang_cot_id: { $exists: false },
      is_deleted: false,
      trang_thai_hoan_thanh: TRANG_THAI_HOAN_THANH.DA_HOAN_THANH,
    });

    const mapViTriByPhieu = {};
    allViTriCongViecs.forEach(el => {
      const viTriId = el.vi_tri_id.toString();
      mapViTriByPhieu[el.phieu_giao_viec_id] ||= [];
      if (!mapViTriByPhieu[el.phieu_giao_viec_id].includes(viTriId)) {
        mapViTriByPhieu[el.phieu_giao_viec_id].push(viTriId);
      }
    });

    const mapDuongDay = {};

    allPhieuGiaoViec.forEach(phieu => {
      phieu.duong_day_ids.forEach(duongDayId => {
        mapDuongDay[duongDayId] ||= [];
        const matchingData = mapViTriByPhieu[phieu._id]
          ?.filter(e => mapViTriByDuongDay[duongDayId]?.includes(e));

        if (matchingData) {
          mapDuongDay[duongDayId] = formatUnique([...mapDuongDay[duongDayId], ...matchingData]);
        }
      });
    });

    for (const duongDay of allDuongDay) {
      duongDay.so_luong_vi_tri = mapViTriByDuongDay[duongDay._id]?.length || 0;
      duongDay.vi_tri_da_kiem_tra = mapDuongDay[duongDay._id]?.length || 0;
      duongDay.vi_tri_chua_kiem_tra = duongDay.so_luong_vi_tri - duongDay.vi_tri_da_kiem_tra;

    }
    return responseHelper.success(res, allDuongDay);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getDataCongViecHoanThanh(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;

    criteria.cong_viec && delete criteria.cong_viec;
    criteria.tinh_trang_phieu && delete criteria.tinh_trang_phieu;

    criteria = await buildPhieuGiaoViecQuery(req, criteria);

    criteria.trang_thai_cong_viec = TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code;
    const allPhieuGiaoViec = await PhieuGiaoViecService.thongKeCongViecHoanThanh(criteria);
    return responseHelper.success(res, allPhieuGiaoViec);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getDataDieuKienAnToan(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;
    criteria.cong_viec && delete criteria.cong_viec;
    criteria.tinh_trang_phieu && delete criteria.tinh_trang_phieu;
    criteria = await buildPhieuGiaoViecQuery(req, criteria);
    const allPhieuGiaoViecIds = await PHIEU_GIAO_VIEC.distinct('_id', criteria);
    const dieuKienAggregate = await DashboardService.thongKeDieuKienAnToanCongViec({
      phieu_giao_viec_id: { $in: allPhieuGiaoViecIds },
      is_deleted: false,
    });
    const phieuGiaoViecData = await DashboardService.thongKePhieuCoDieuKienAnToan(allPhieuGiaoViecIds);
    const dataResponse = {
      chart: dieuKienAggregate,
      table: phieuGiaoViecData,
    };
    return responseHelper.success(res, dataResponse);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function downloadPhieuCoDieuKienAnToan(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    let { criteria } = query;
    criteria.cong_viec && delete criteria.cong_viec;
    criteria.tinh_trang_phieu && delete criteria.tinh_trang_phieu;
    criteria = await buildPhieuGiaoViecQuery(req, criteria);
    const allPhieuGiaoViecIds = await PHIEU_GIAO_VIEC.distinct('_id', criteria);
    const phieuGiaoViecData = await DashboardService.thongKePhieuCoDieuKienAnToan(allPhieuGiaoViecIds);


    function convertData(row) {
      let stringCoLapDuongDay, stringDieuKienAnToan;
      row.dieu_kien?.forEach(item => {
        const strTempDieuKien = item.dieu_kien_an_toan_id.ten_dieu_kien_an_toan;
        const strTempCoLap = LOAI_DIEU_KIEN_AN_TOAN[item?.dieu_kien_an_toan_id?.loai_dieu_kien]?.label;
        if (!stringDieuKienAnToan) {
          stringDieuKienAnToan = `- ${strTempDieuKien}`;
        } else {
          stringDieuKienAnToan = downlineData(stringDieuKienAnToan, `- ${strTempDieuKien}`);
        }
        if (!stringCoLapDuongDay) {
          stringCoLapDuongDay = `- ${strTempCoLap}`;
        } else {
          stringCoLapDuongDay = downlineData(stringCoLapDuongDay, `- ${strTempCoLap}`);
        }

      });
      return {
        loai_cong_viec: LOAI_CONG_VIEC[row.loai_cong_viec]?.name,
        co_lap_duong_day: stringCoLapDuongDay,
        dieu_kien_an_toan: stringDieuKienAnToan,
        so_phieu: row.so_phieu,
      };
    }

    const templateFilePath = getFilePath('co_lap_duong_day.xlsx', TEMPLATES_DIRS.REPORT);
    await generateDocument(res, addIndexToListData(phieuGiaoViecData.map(convertData)), templateFilePath);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function banDoLuoi(req, res) {
  try {
    let banDoLuoi = await DashboardService.getCacheBanDoLuoi();
    if (!banDoLuoi) {
      banDoLuoi = await DashboardService.getAllBanDoLuoi();
      DashboardService.setCacheBanDoLuoi(banDoLuoi);
    }
    return responseHelper.success(res, banDoLuoi);
  } catch (err) {
    console.log('err', err);
    return responseHelper.error(res, err);
  }
}

export async function banDoLuoiRealtime(req, res) {
  try {
    const banDoLuoi = await DashboardService.getAllBanDoLuoi();
    DashboardService.setCacheBanDoLuoi(banDoLuoi);
    return responseHelper.success(res, banDoLuoi);
  } catch (err) {
    console.log('err', err);
    return responseHelper.error(res, err);
  }
}

export async function nhaMayDien(req, res) {
  try {
    const donViId = req?.query?.don_vi_id || req?.query?.donViId;
    const donViIdIncludeChild = await DonViService.getAllDonViByParent(req, donViId, true);

    const query = {
      is_deleted: false,
      tinh_trang_van_hanh: TINH_TRANG_VAN_HANH.VAN_HANH,
      don_vi_id: donViIdIncludeChild,
    };
    const data = await NHA_MAY_DIEN.find(query).populate({ path: 'don_vi_id', select: 'ten_don_vi' }).lean();
    return responseHelper.success(res, data);
  } catch (err) {
    console.log('err', err);
    return responseHelper.error(res, err);
  }
}

export async function tramBienAp(req, res) {
  try {
    const donViId = req?.query?.don_vi_id || req?.query?.donViId;
    const donViIdIncludeChild = await DonViService.getAllDonViByParent(req, donViId, true);

    const query = {
      is_deleted: false,
      tinh_trang_van_hanh: TINH_TRANG_VAN_HANH.VAN_HANH,
      don_vi_id: donViIdIncludeChild,
    };
    const data = await TramBienApService.getAll(query)
      .populate({ path: 'don_vi_id', select: 'ten_don_vi' });
    return responseHelper.success(res, data || []);
  } catch (err) {
    console.log('err', err);
    return responseHelper.error(res, err);
  }
}
