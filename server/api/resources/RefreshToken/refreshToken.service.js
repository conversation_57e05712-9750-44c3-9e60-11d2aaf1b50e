import * as ValidatorHelper from '../../helpers/validatorHelper';
import REFRESH_TOKEN from './refreshToken.model';

const Joi = require('joi');
const objSchema = Joi.object({});

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return REFRESH_TOKEN.create(value);
}

export async function deleteOne(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return REFRESH_TOKEN.deleteOne(value);
}

export async function deleteMany(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return REFRESH_TOKEN.deleteMany(value);
}

export function getAll(query, projection = {}) {
  return REFRESH_TOKEN.find(query, projection).lean();
}

export async function updateAll(dataUpdate) {
  for (const row of dataUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await REFRESH_TOKEN.findByIdAndUpdate(value._id, value);
  }
}

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};
