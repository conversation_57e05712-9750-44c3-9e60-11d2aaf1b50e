import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { REFRESH_TOKEN, USER } from '../../constant/dbCollections';

const schema = new Schema({
  user_id: { type: Schema.Types.ObjectId, ref: USER },
  refresh_token: { type: String, required: true },
  expires_date:{ type: Date, required: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized compound indexes for RefreshToken queries
// Primary index for logout function - search by refresh_token with soft delete
schema.index({ 
  refresh_token: 1, 
  is_deleted: 1 
}, { 
  background: true,
  name: 'idx_refresh_token_deleted'
});

// Index for user-specific refresh token operations
schema.index({ 
  user_id: 1, 
  is_deleted: 1,
  created_at: -1
}, { 
  background: true,
  name: 'idx_user_deleted_time'
});

// Index for automatic cleanup job - find expired tokens
schema.index({ 
  expires_date: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_expires_deleted'
});

// Compound index for user-specific refresh token with exclusion (changePassword)
schema.index({ 
  user_id: 1, 
  refresh_token: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_user_refresh_token_deleted'
});

// Index for refresh token validation queries
schema.index({ 
  refresh_token: 1, 
  expires_date: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_refresh_token_expires_deleted'
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(REFRESH_TOKEN, schema, REFRESH_TOKEN);
