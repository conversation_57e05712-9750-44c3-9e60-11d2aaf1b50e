import * as responseHelper from '../../helpers/responseHelper';
import { getMenuConfig } from '../../../config/configChung';
import i18next from 'i18next';
import Setting from '../CaiDatHeThong/caiDatHeThong.model';

export async function findOne(req, res) {
  try {
    i18next.changeLanguage(req.headers.i18nextlng);
    let menuConfig = getMenuConfig;
    const caiDatHeThong = await Setting.findOne();
    menuConfig = {
      ...menuConfig,
      wrong_pass_to_capcha: caiDatHeThong.wrong_pass_to_capcha,
      activate_wrong_pass_to_capcha: caiDatHeThong.activate_wrong_pass_to_capcha,
    };
    return responseHelper.success(res, menuConfig);
  } catch (err) {
    responseHelper.error(res, err);
  }
}
