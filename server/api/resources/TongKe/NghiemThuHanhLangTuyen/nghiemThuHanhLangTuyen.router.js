import express from 'express';
import passport from 'passport';
import * as ntHanhLangTuyenController from './nghiemThuHanhLangTuyen.controller';
import { loggerMiddleware } from '../../../logs/middleware';
import { checkTempFolder, multipartMiddleware } from '../../../utils/fileUtils';
import { authorizationMiddleware } from '../../RBAC/middleware';
import TongKePermission from '../../RBAC/permissions/TongKePermission';
import { duongDayRouter } from '../DuongDay/duongDay.router';

export const ntHanhLangTuyenRouter = express.Router();

ntHanhLangTuyenRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
duongDayRouter.post('*', authorizationMiddleware([TongKePermission.CREATE]));
duongDayRouter.put('*', authorizationMiddleware([TongKePermission.UPDATE]));
duongDayRouter.delete('*', authorizationMiddleware([TongKePermission.DELETE]));

ntHanhLangTuyenRouter
  .route('/')
  .get(ntHanhLangTuyenController.getAll)
  .post(ntHanhLangTuyenController.create);

ntHanhLangTuyenRouter
  .route('/importone')
  .post(ntHanhLangTuyenController.importOne);

ntHanhLangTuyenRouter
  .route('/importmany')
  .post(ntHanhLangTuyenController.importMany);

ntHanhLangTuyenRouter
  .route('/checkimport')
  .post(checkTempFolder, multipartMiddleware, ntHanhLangTuyenController.checkImport);

ntHanhLangTuyenRouter
  .route('/checkimportbydata')
  .post(ntHanhLangTuyenController.checkImportData);

ntHanhLangTuyenRouter
  .route('/:id')
  .get(ntHanhLangTuyenController.findOne)
  .delete(ntHanhLangTuyenController.remove)
  .put(ntHanhLangTuyenController.update);
