import <PERSON><PERSON> from 'joi';
import * as ValidatorHelper from '../../../helpers/validatorHelper';

import { headerNghiemThu } from './nghiemThuHanhLangTuyen.controller';

import NGHIEM_THU_HANH_LANG_TUYEN from './nghiemThuHanhLangTuyen.model';
import DUONG_DAY from '../DuongDay/duongDay.model';

import * as DuongDayService from '../DuongDay/duongDay.service';
import * as VanHanhService from '../VanHanh/vanHanh.service';
import * as KhoangCotService from '../KhoangCot/khoangCot.service';

import { convertObject, extractIds, extractKeys } from '../../../utils/dataconverter';
import { formatUnique } from '../../../common/functionCommons';

export function getAll(query, projection = {}) {
  return NGHIEM_THU_HANH_LANG_TUYEN.find(query, projection).lean();
}

export async function createMany(dataList = []) {
  if (!Array.isArray(dataList)) {
    throw new Error('Input must be an array');
  }

  const validRecords = [];

  for (const data of dataList) {
    const { error, value } = validate(data);
    if (!error) {
      validRecords.push(value);
    }
  }

  return NGHIEM_THU_HANH_LANG_TUYEN.insertMany(validRecords);
}

export async function updateAll(dataUpdate, options = {}) {
  options.new = true;
  const results = [];
  for (const row of dataUpdate) {
    try {
      const { error, value } = validate(row);
      if (!error) {
        const updated = await NGHIEM_THU_HANH_LANG_TUYEN.findByIdAndUpdate(value._id, value, options);
        if (updated) results.push(updated);
      }
    } catch {
    }
  }
  return results;
}

export async function checkImportNghiemThu(t, sheetData) {
  if (!sheetData || !sheetData.duongDay) return null;
  const { rows, duongDay } = sheetData;

  const normalizedDuongDay = duongDay.map(s => s.trim());

  const allDuongDay = await DUONG_DAY.aggregate([
    {
      $addFields: {
        ten_duong_day_trimmed: { $trim: { input: '$ten_duong_day' } },
      },
    },
    {
      $match: {
        is_deleted: false,
        ten_duong_day_trimmed: { $in: normalizedDuongDay },
      },
    },
    { $project: { ten_duong_day: 1 } },
  ]);

  const matchedTenDuongDay = allDuongDay.map(d => d.ten_duong_day.trim());

  const notFound = normalizedDuongDay.find(ten => !matchedTenDuongDay.includes(ten));
  if (notFound) {
    sheetData.error = t('line_operation_incorrect_or_dont_create');
    return sheetData;
  }

  const duongDayIds = extractIds(allDuongDay);

  const allVanHanh = await VanHanhService.getAll({ duong_day_id: duongDayIds })
    .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });

  const allViTriId = [], allTenViTri = [];

  allVanHanh.forEach(vanHanh => {
    allViTriId.push(vanHanh.vi_tri_id?._id);
    allTenViTri.push(vanHanh.vi_tri_id?.ten_vi_tri);
  });

  const uniqueViTriId = formatUnique(allViTriId);
  const uniqueTenViTri = formatUnique(allTenViTri);

  const allKhoangCot = await KhoangCotService.getAll({ vi_tri_id: uniqueViTriId });
  const allTenKhoangCot = extractKeys(allKhoangCot, 'ten_khoang_cot');
  const uniqueTenKhoangCot = formatUnique(allTenKhoangCot);

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row) {
    let errorString = [];
    const viTriKhoangCotValue = row[headerNghiemThu.VI_TRI]?.toString();

    if (!viTriKhoangCotValue) {
      errorString.push(createError(headerNghiemThu.VI_TRI, t('missing_location_info')));
    } else {
      const isKhoangCot = checkKhoangCotByName(viTriKhoangCotValue);
      if (isKhoangCot && !uniqueTenKhoangCot.includes(viTriKhoangCotValue)) {
        errorString.push(createError(headerNghiemThu.VI_TRI, t('tower_not_belong_line')));
      } else if (!isKhoangCot && !uniqueTenViTri.includes(viTriKhoangCotValue)) {
        errorString.push(createError(headerNghiemThu.VI_TRI, t('location_not_belong_line')));
      }
    }

    row['Lỗi'] = errorString.length ? errorString : null;
    return row;
  }

  sheetData.rows = rows.map(row => validateRow(t, row));
  sheetData.error = null;

  return sheetData;
}

export async function importData(t, sheetData) {
  const { rows, duongDay } = sheetData;

  const allDuongDay = await DuongDayService.getAll({ ten_duong_day: duongDay, is_deleted: false });

  const duongDayIds = extractIds(allDuongDay);


  const allVanHanh = await VanHanhService.getAll({ duong_day_id: duongDayIds })
    .populate({ path: 'vi_tri_id', select: 'ten_vi_tri' });

  const objTenViTri = {}, allViTriId = [];
  allVanHanh.forEach(vanHanh => {
    objTenViTri[vanHanh.vi_tri_id?.ten_vi_tri] = vanHanh.vi_tri_id?._id;
    allViTriId.push(vanHanh.vi_tri_id?._id);
  });
  const allKhoangCot = await KhoangCotService.getAll({ vi_tri_id: formatUnique(allViTriId) }, { ten_khoang_cot: 1 });
  const objTenKhoangCot = convertObject(allKhoangCot, 'ten_khoang_cot');


  function convertToDB(row) {
    const viTriKhoangCot = row[headerNghiemThu.VI_TRI]?.toString();
    const isKhoangCot = checkKhoangCotByName(viTriKhoangCot);

    function mapFields(row, field) {
      return {
        theo_pa_den_bu: row[field][headerNghiemThu.THEO_PA_DEN_BU],
        theo_thuc_te_kiem_tra: row[field][headerNghiemThu.THEO_THUC_TE_KIEM_TRA],
      };
    }

    function normalizeSpacing(text) {
      return text
        .replace(/([^\s])([\(\[\{])/g, '$1 $2')
        .replace(/([\)\]\}])([^\s\)\]\}\,\.\:\;])/g, '$1 $2')
        .replace(/([\,\:\;])([^\s\)\]\}])/g, '$1 $2')
        .replace(/\s+/g, ' ')
        .trim();
    }

    function normalizeObjectKeys(obj) {
      if (Array.isArray(obj)) return obj.map(normalizeObjectKeys);
      if (obj && typeof obj === 'object') {
        return Object.fromEntries(
          Object.entries(obj).map(([key, value]) => [
            normalizeSpacing(key),
            normalizeObjectKeys(value),
          ]),
        );
      }
      return obj;
    }

    const cleanedData = normalizeObjectKeys(row);

    return {
      duong_day_id: duongDayIds,
      vi_tri_id: !isKhoangCot ? objTenViTri[viTriKhoangCot] : null,
      khoang_cot_id: isKhoangCot ? objTenKhoangCot[viTriKhoangCot]._id : null,
      dia_diem: cleanedData[headerNghiemThu.DIA_DIEM],
      phe_duyet: cleanedData[headerNghiemThu.PHE_DUYET],
      chu_so_huu: cleanedData[headerNghiemThu.CHU_SO_HUU],
      boi_thuong_nha_vat_kien_truc: mapFields(cleanedData, headerNghiemThu.BOI_THUONG_NHA_VAT_KIEN_TRUC),
      ho_tro_dat_chuyen_doi: mapFields(cleanedData, headerNghiemThu.HO_TRO_DAT_CHUYEN_DOI),
      boi_thuong_chat_tia_cay: mapFields(cleanedData, headerNghiemThu.BOI_THUONG_CHAT_TIA_CAY),
      y_kien_danh_gia: cleanedData[headerNghiemThu.Y_KIEN_DANH_GIA],
      kien_nghi: cleanedData[headerNghiemThu.KIEN_NGHI],
      ghi_chu: cleanedData[headerNghiemThu.GHI_CHU],
      is_deleted: false,
    };
  }

  const dataToDB = rows.map(row => convertToDB(row)).flat();
  return await NGHIEM_THU_HANH_LANG_TUYEN.bulkWrite(
    dataToDB.map((nghiemthu) =>
      ({
        updateOne: {
          filter: {
            vi_tri_id: nghiemthu.vi_tri_id,
            khoang_cot_id: nghiemthu.khoang_cot_id,
            duong_day_id: nghiemthu.duong_day_id,
            chu_so_huu: nghiemthu.chu_so_huu,
          },
          update: { $set: nghiemthu },
          upsert: true,
        },
      }),
    ),
  );
}

const objSchema = Joi.object({
  ten_vi_tri: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên vị trí')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}


function checkKhoangCotByName(name) {
  return /^[\s\S]*\S-\S[\s\S]*$/.test(name);
}
