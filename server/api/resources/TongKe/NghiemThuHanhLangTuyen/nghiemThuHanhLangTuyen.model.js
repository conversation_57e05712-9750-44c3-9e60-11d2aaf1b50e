import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { NGHIEM_THU_HANH_LANG_TUYEN, VI_TRI, DUONG_DAY,KHOANG_COT } from '../../../constant/dbCollections';

const schema = new Schema({
  duong_day_id: [{ type: Schema.Types.ObjectId, ref: DUONG_DAY }],
  khoang_cot_id: { type: Schema.Types.ObjectId, ref: KHOANG_COT },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  dia_diem: { type: String },
  phe_duyet: { type: String },
  chu_so_huu: { type: String },
  boi_thuong_nha_vat_kien_truc: { type: Schema.Types.Mixed },
  ho_tro_dat_chuyen_doi: { type: Schema.Types.Mixed },
  boi_thuong_chat_tia_cay: { type: Schema.Types.Mixed },
  y_kien_danh_gia: { type: String },
  kien_nghi: { type: String },
  ghi_chu: { type: String },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(NGHIEM_THU_HANH_LANG_TUYEN, schema, NGHIEM_THU_HANH_LANG_TUYEN);
