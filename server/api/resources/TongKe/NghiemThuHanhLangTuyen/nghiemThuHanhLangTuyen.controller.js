import * as responseHelper from '../../../helpers/responseHelper';
import * as controllerHelper from '../../../helpers/controllerHelper';

import exelUtils from '../../../utils/exelUtils';
import Model from './nghiemThuHanhLangTuyen.model';
import { trimData } from '../../../common/functionCommons';

import * as DuongDayService from '../DuongDay/duongDay.service';
import * as Service from './nghiemThuHangLangTuyen.service';

export const headerNghiemThu = {
  VI_TRI: 'Vị trí/ Khoảng cột',
  DIA_DIEM: 'Địa điểm (xã, phường- huyện, TP)',
  PHE_DUYET: 'QĐ phê duyệt PA bồi thường, hỗ trợ GPMB (kèm theo PA)',
  CHU_SO_HUU: 'Tổ chức, cá nhân sở hữu tài sản',
  BOI_THUONG_NHA_VAT_KIEN_TRUC: '<PERSON><PERSON><PERSON> thườ<PERSON>, hỗ trợ nhà, vật kiến trúc',
  HO_TRO_DAT_CHUYEN_DOI: 'Hỗ trợ đất chuyển đổi mục đích cây trồng (m²)',
  BOI_THUONG_CHAT_TIA_CAY: 'Bồi thường chặt tỉa cây',
  Y_KIEN_DANH_GIA: 'Ý kiến đánh giá',
  KIEN_NGHI: 'Kiến nghị (khối lượng ngoài PA)',
  GHI_CHU: 'Ghi chú',
  THEO_PA_DEN_BU: 'Theo PA đền bù',
  THEO_THUC_TE_KIEM_TRA: 'Theo thực tế kiểm tra',
};

const populateOpts = [
  { path: 'vi_tri_id', select: 'ten_vi_tri' },
  { path: 'duong_day_id', select: 'ten_duong_day' },
  { path: 'khoang_cot_id', select: 'ten_khoang_cot' },
];

const sortOpts = { vi_tri_id: 1, khoang_cot_id: 1, chu_so_huu: 1 };

export const getAll = controllerHelper.createGetAllFunction(Model, null, populateOpts, sortOpts);
export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts);

export async function importOne(req, res) {
  try {
    const { t } = req;
    const sheetData = trimData(req.body);
    const result = await Service.importData(t, sheetData);
    return responseHelper.success(res, result);
  } catch (e) {
    console.log(e);
    return responseHelper.error(res, e);
  }
}

export async function importMany(req, res) {
  try {
    let result = {};
    const { t } = req;
    for (let sheet of req.body) {
      result = { ...result, ...await Service.importData(t, sheet) };
    }
    return responseHelper.success(res, result);
  } catch (e) {
    console.log(e);
    return responseHelper.error(res, e);
  }
}

export async function checkImport(req, res) {
  try {
    const { t } = req;
    let filePath = req.files.file.path;
    const sheetData = await exelUtils.transformFileNghiemThu(filePath);
    const resultArray = await checkImportByData(t, sheetData);
    return responseHelper.success(res, resultArray);
  } catch (e) {
    console.log(e);
    return responseHelper.error(res, e);
  }
}

export async function checkImportData(req, res) {

  try {
    const { t } = req;
    const sheetData = req.body;
    const resultArray = await checkImportByData(t, sheetData);
    return responseHelper.success(res, resultArray);
  } catch (e) {
    console.log(e);
    return responseHelper.error(res, e);
  }
}

export async function checkImportByData(t, sheetData) {
  let resultArray = [];

  for (const sheet of sheetData) {
    const result = await Service.checkImportNghiemThu(t, sheet);
    resultArray.push(result);
  }

  return resultArray;
}
