import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DAY_DAN, DON_VI, VAN_HANH } from '../../../constant/dbCollections';
import { TINH_TRANG_VAN_HANH } from '../../DanhMuc/TinhTrangVanHanh';

const schema = new Schema({
  ma_day_dan: { type: String, required: true, unique: true, validate: /\S+/ },
  ma_thiet_bi_cha: { type: String, validate: /\S+/ },
  ma_tim_kiem: { type: String, validate: /\S+/ },
  ma_tim_kiem_cha: { type: String, validate: /\S+/ },
  van_hanh_id: { type: Schema.Types.ObjectId, ref: VAN_HANH },
  ten_day_dan: { type: String },
  thu_tu: { type: Number },
  so_the_tscd: String,
  ngay_van_hanh: { type: Date },
  tinh_trang_van_hanh: {
    type: String,
    enum: Object.values(TINH_TRANG_VAN_HANH),
    default: TINH_TRANG_VAN_HANH.VAN_HANH,
  },
  hang_san_xuat: String,
  nha_cung_cap: String,
  nuoc_san_xuat: String,
  so_huu: String,
  ngay_lap_dat: { type: Date },
  nam_san_xuat: String,
  ngay_sua_doi: String,
  ghi_chu: String,
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  duong_day: String,
  pha: String,
  ma_hieu_day: String,
  so_luong_day: String,
  ky_hieu_day: String,
  duong_kinh_day: String,
  duong_kinh_loi: String,
  cau_tao: String,
  trong_luong_day: String,
  luc_chiu_keo: String,
  dien_tro_mot_chieu: String,
  i_max: String,
  loai_chong_rung: String,
  so_luong_chong_rung: String,
  so_luong_moi_noi: String,
  so_luong_khung_dinh_vi: String,
  so_luong_thiet_bi_canh_bao: String,

  thong_tin_khac: String,
  vi_tri_dat: String,
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.index({ van_hanh_id: 1 });
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(DAY_DAN, schema, DAY_DAN);
