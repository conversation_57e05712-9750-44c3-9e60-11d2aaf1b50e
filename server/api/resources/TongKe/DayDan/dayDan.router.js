import express from 'express';
import passport from 'passport';
import * as donviController from './dayDan.controller';

export const dayDanRouter = express.Router();
dayDanRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), donviController.getAll)
  .post(passport.authenticate('jwt', { session: false }), donviController.create);

dayDanRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), donviController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), donviController.remove)
  .put(passport.authenticate('jwt', { session: false }), donviController.update);
