import * as ValidatorHelper from '../../../helpers/validatorHelper';
import DayDanModel from './dayDan.model';

import { convertDate, trim } from '../../../common/functionCommons';
import { checkNumber, convertYear } from '../../../helpers/checkDataHelper';
import { checkCommonInfo, checkThietBiCommonInfo, thietBiVanHanhCommonInfo } from '../TongKe.service';
import createBaseService from '../../../base/baseService';

const Headers = {
  MA_THIET_BI: 'Mã thiết bị',
  DUONG_DAY: 'Đường dây/TBA',
  THIET_BI_CHA: 'Thiết bị/Công trình cha',
  TEN_THIET_BI: 'Thiết bị/Công trình',
  STT: 'STT',
  SERIAL: 'Số chế tạo/Serial',
  NGAY_VAN_HANH: '<PERSON><PERSON><PERSON> vận hành',
  SO_TSCD: 'Số TSCĐ',
  TINH_TRANG_VAN_HANH: 'Tình trạng vận hành',
  MA_LIEN_KET_KHAC: 'Mã liên kết khác',
  HANG_SAN_XUAN: 'Hãng sản xuất',
  NHA_CUNG_CAP: 'Nhà cung cấp',
  NUOC_SAN_XUAT: 'Nước sản xuất',
  SO_HUU: 'Sở hữu',
  NGAY_LAP_DAT: 'Ngày lắp đặt',
  NAM_SAN_XUAT: 'Năm sản xuất',
  NGAY_SUA_DOI: 'Ngày sửa đổi',
  GHI_CHU: 'Ghi chú',
  DON_VI: 'Đơn vị',

  KY_HIEU_DUONG_DAY: 'Đường dây',
  PHA: 'Pha',
  MA_HIEU_DAY: 'Mã hiệu dây',
  SO_LUONG: 'Số lượng',
  KY_HIEU: 'Ký hiệu',
  DUONG_KINH_DAY: 'Đường kính dây',
  DUONG_KINH_LOI: 'Đường kính lõi',
  CAU_TAO: 'Cấu tạo  (số sợi x đường kính sợi): Phần nhôm + Lõi thép',
  TRONG_LUONG_DAY: 'Trọng lượng dây',
  LUC_CHIU_KEO: 'Lực chịu kéo',
  DIEN_TRO_MOT_CHIEU: 'Điện trở 1 chiều',
  IMAX: 'I max',
  LOAI_CHONG_RUNG: 'Loại chống rung',
  SO_LUONG_CHONG_RUNG: 'Số lượng chống rung',
  SO_LUONG_MOI_NOI: 'Số lượng mối nối',
  SO_LUONG_KHUNG_DINH_VI: 'Số lượng khung định vị',
  SO_LUONG_THIET_BI_CANH_BAO: 'Số lượng thiết bị cảnh báo',
  THONG_TIN_KHAC: 'Thông tin khác',
  VI_TRI_KHAC: 'Vị trí đặt',
};

export async function importData(sheetData, mapDonVi, mapVanHanh) {
  const { rows } = sheetData;

  function convertToDB(row) {
    const thietBiVanHanhInfo = thietBiVanHanhCommonInfo(row, mapDonVi, mapVanHanh);
    return {
      ma_day_dan: row[Headers.MA_THIET_BI]?.trim(),
      ten_day_dan: trim(row[Headers.TEN_THIET_BI]),
      ...thietBiVanHanhInfo,
      so_the_tscd: row[Headers.SO_TSCD],
      thu_tu: row[Headers.STT],
      ngay_van_hanh: convertDate(row[Headers.NGAY_VAN_HANH]),
      tinh_trang_van_hanh: row[Headers.TINH_TRANG_VAN_HANH],
      hang_san_xuat: row[Headers.HANG_SAN_XUAN],
      nuoc_san_xuat: row[Headers.NUOC_SAN_XUAT],
      nam_san_xuat: convertYear(row[Headers.NAM_SAN_XUAT]),
      so_huu: row[Headers.SO_HUU],
      ngay_lap_dat: convertDate(row[Headers.NGAY_LAP_DAT]),

      duong_day: row[Headers.KY_HIEU_DUONG_DAY],
      pha: row[Headers.PHA],
      ma_hieu_day: row[Headers.MA_HIEU_DAY],
      so_luong_day: row[Headers.SO_LUONG],
      ky_hieu_day: row[Headers.KY_HIEU],
      duong_kinh_day: row[Headers.DUONG_KINH_DAY],
      duong_kinh_loi: row[Headers.DUONG_KINH_LOI],
      cau_tao: row[Headers.CAU_TAO],
      trong_luong_day: row[Headers.TRONG_LUONG_DAY],
      luc_chiu_keo: row[Headers.LUC_CHIU_KEO],
      dien_tro_mot_chieu: row[Headers.DIEN_TRO_MOT_CHIEU],
      i_max: row[Headers.IMAX],
      loai_chong_rung: row[Headers.LOAI_CHONG_RUNG],
      so_luong_chong_rung: row[Headers.SO_LUONG_CHONG_RUNG],
      so_luong_moi_noi: row[Headers.SO_LUONG_MOI_NOI],
      so_luong_khung_dinh_vi: row[Headers.SO_LUONG_KHUNG_DINH_VI],
      so_luong_thiet_bi_canh_bao: row[Headers.SO_LUONG_THIET_BI_CANH_BAO],
      thong_tin_khac: row[Headers.THONG_TIN_KHAC],
      vi_tri_dat: row[Headers.VI_TRI_KHAC],
    };
  }

  const dataToDB = rows.map(row => convertToDB(row)).filter(element => element.ten_day_dan);
  const result = await DayDanModel.bulkWrite(
    dataToDB.map((element) =>
      ({
        updateOne: {
          filter: { ma_day_dan: element.ma_day_dan },
          update: { $set: element },
          upsert: true,
        },
      }),
    ),
  );
  return result;
}

export function checkImport(t, sheetData, mapDonVi, mapVanHanh) {
  if (!sheetData) return null;
  const { rows } = sheetData;

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row) {
    let errors = [];
    errors = [...errors, ...checkCommonInfo(t, row)];
    errors = [...errors, ...checkThietBiCommonInfo(t, row, mapVanHanh)];
    if (!row[Headers.DON_VI]?.trim()) {
      errors = [...errors, createError(Headers.DON_VI, t('missing_unit_management'))];
    } else {
      if (!mapDonVi[row[Headers.DON_VI]?.trim()]) {
        errors = [...errors, createError(Headers.DON_VI, t('management_unit_incorrect_or_dont_create'))];
      }
    }
    if (!row[Headers.SO_LUONG] || !checkNumber(row[Headers.SO_LUONG])) {
      errors = [...errors, createError(Headers.SO_LUONG, t('incorrect_or_not_available'))];
    }

    if (errors.length) {
      row['Lỗi'] = errors;
    } else {
      row['Lỗi'] = null;
    }
    return row;
  }

  sheetData.rows = rows.map(row => validateRow(t, row));
  return sheetData;
}

const baseService = createBaseService(DayDanModel);

export const createMulti = baseService.createMulti;
export const getAll = baseService.getAll;
export const getOne = baseService.getOne;
export const count = baseService.count;
export const remove = baseService.remove;
export const distinctId = baseService.distinctId;
export const updateByQuery = baseService.updateByQuery;
export const updateByRows = baseService.updateByRows;
export const deleteAll = baseService.deleteAll;
export const updateAll = baseService.updateAll;

const Joi = require('joi');

const objSchema = Joi.object({
  ma_day_dan: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã tiếp địa')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
