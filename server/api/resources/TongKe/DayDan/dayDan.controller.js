import * as Service from './dayDan.service';
import Model from './dayDan.model';
import * as controllerHelper from '../../../helpers/controllerHelper';

const searchLike = ['ma_day_dan', 'ten_day_dan'];
const populateOpts = [];
const uniqueOpts = [{ field: 'ma_day_dan', message: 'Mã dây dẫn' }];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, searchLike, populateOpts);

