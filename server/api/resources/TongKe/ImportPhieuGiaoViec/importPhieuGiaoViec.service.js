import * as ValidatorHelper from '../../../helpers/validatorHelper';
import { generateSoPhieu } from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';
import * as DonViService from '../../DonVi/donVi.service';
import * as UserService from '../../User/user.service';
import * as DuongDayService from '../../TongKe/DuongDay/duongDay.service';
import * as ViTriService from '../../TongKe/ViTri/viTri.service';
import * as KhoangCotService from '../../TongKe/KhoangCot/khoangCot.service';
import * as VanHanhService from '../../TongKe/VanHanh/vanHanh.service';
import PHIEU_GIAO_VIEC from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.model';
import NGUOI_CONG_TAC from '../../QuanLyVanHanh/NguoiCongTac/nguoiCongTac.model';
import VI_TRI_CONG_VIEC from '../../QuanLyVanHanh/ViTriCongViec/viTriCongViec.model';
import DIEU_KIEN_AN_TOAN from '../../DanhMuc/DieuKienAnToan/dieuKienAnToan.model';
import BIEN_PHAP_AN_TOAN from '../../DanhMuc/BienPhapAnToan/bienPhapAnToan.model';
import DIEU_KIEN_AN_TOAN_CONG_VIEC from '../../QuanLyVanHanh/DieuKienAnToanCongViec/dieuKienAnToanCongViec.model';
import BIEN_PHAP_AN_TOAN_CONG_VIEC from '../../QuanLyVanHanh/BienPhapAnToanCongViec/bienPhapAnToanCongViec.model';
import { convertDateTime, trim } from '../../../common/functionCommons';
import { LOAI_CONG_VIEC, THANG_KIEM_TRA } from '../../DanhMuc/LoaiCongViec';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import moment from 'moment';
import { Types } from 'mongoose';

const Joi = require('joi');

const objSchema = Joi.object({
  ma_vi_tri: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã vị trí')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

function convertMonth(text) {
  return text
    .replace(/Tháng (\d+)/i, (_, num) => `THANG_${num}`) // Thay "Tháng X" thành "THANG_X"
    .toUpperCase(); // Chuyển thành chữ in hoa (phòng trường hợp chữ 'tháng' viết thường)
}

const Headers = {
  STT: 'STT',
  DON_VI_GIAO_PHIEU: 'Đơn vị giao phiếu',
  NGUOI_CAP_PHIEU: 'Người cấp phiếu',
  LOAI_CONG_VIEC: 'Loại công việc',
  THANG_KIEM_TRA: 'Tháng kiểm tra',
  SU_DUNG_THIET_BI_BAY: 'Sử dụng thiết bị bay',
  NOI_DUNG_CONG_TAC: 'Nội dung công tác',
  THOI_GIAN_BAT_DAU: 'Thời gian bắt đầu',
  THOI_GIAN_KET_THUC: 'Thời gian kết thúc',
  NHAN_VIEC_SAU_DU_KIEN: 'Nhận việc sau dự kiến',
  LY_DO_NHAN_VIEC_SAU_DU_KIEN: 'Lý do nhận việc sau dự kiến',
  CHI_HUY: 'Chỉ huy',
  NHAN_VIEN_TO_CONG_TAC: 'Nhân viên tổ công tác',
  TINH_TRANG_VAN_HANH: 'Tình trạng vận hành',
  DUONG_DAY: 'Đường dây',
  VI_TRI: 'Vị trí',
  KHOANG_COT: 'Khoảng cột',
  DIEU_KIEN_AN_TOAN: 'Điều kiện an toàn',
  BIEN_PHAP_AN_TOAN: 'Biện pháp an toàn',
};

export async function getAll(query, projection = {}) {
  return PHIEU_GIAO_VIEC.find(query, projection).lean();
}

export function getOne(query, projection = {}) {
  return PHIEU_GIAO_VIEC.findOne(query, projection).lean();
}

export async function importData(sheetData) {
  const { rows } = sheetData;

  const allDonVi = await DonViService.getAll({ is_deleted: false });
  const mapDonVi = {};
  allDonVi.forEach(donVi => {
    mapDonVi[donVi.ten_don_vi] = donVi._id;
  });

  const allUser = await UserService.getAll({ is_deleted: false });
  const mapUser = {};
  allUser.forEach(user => {
    mapUser[user.full_name] = user._id;
  });

  const allDuongDay = await DuongDayService.getAll({ is_deleted: false });
  const mapDuongDay = {};
  allDuongDay.forEach(duongDay => {
    mapDuongDay[duongDay.ten_duong_day] = duongDay._id;
  });

  const allDieuKienAnToan = await DIEU_KIEN_AN_TOAN.find({ is_deleted: false });
  const mapDieuKienAnToan = {};
  allDieuKienAnToan.forEach(dieuKienAnToan => {
    mapDieuKienAnToan[dieuKienAnToan.ten_dieu_kien_an_toan] = dieuKienAnToan._id;
  });

  const allBienPhapAnToan = await BIEN_PHAP_AN_TOAN.find({ is_deleted: false });
  const mapBienPhapAnToan = {};
  allBienPhapAnToan.forEach(bienPhapAnToan => {
    mapBienPhapAnToan[bienPhapAnToan.ten_bien_phap_an_toan] = bienPhapAnToan._id;
  });

  async function convertToDB(row) {
    const donViId = mapDonVi[trim(row[Headers.DON_VI_GIAO_PHIEU])];
    const loaiCongViec = Object.values(LOAI_CONG_VIEC).find(item => item.name === trim(row[Headers.LOAI_CONG_VIEC]))?.code || null;
    
    const dsNhanVienCT = trim(row[Headers.NHAN_VIEN_TO_CONG_TAC])?.split('\r\n');
    const dsNguoiCongTacId = dsNhanVienCT?.map(item => mapUser[item] || null)?.filter(item => item);

    const dsDuongDay = trim(row[Headers.DUONG_DAY])?.split('\r\n')?.map(item => item.trim().replace(/^- /, ""));
    const dsDuongDayId = dsDuongDay?.map(item => mapDuongDay[item] || null)?.filter(item => item);

    const dsVanHanh = await VanHanhService.getAll({ is_deleted: false, duong_day_id: { $in: dsDuongDayId } });
    const dsViTriVanHanh = dsVanHanh?.map(item => item.vi_tri_id)?.filter(item => item);

    const allViTri = await ViTriService.getAll({ is_deleted: false, don_vi_id: donViId, _id: { $in: dsViTriVanHanh } });
    const mapViTri = {};
    allViTri.forEach(viTri => {
      mapViTri[viTri.ten_vi_tri] = viTri._id;
    });

    const dsViTri = trim(row[Headers.VI_TRI])?.split(', ');
    const dsViTriId = dsViTri?.map(item => mapViTri[item] || null)?.filter(item => item);

    const allKhoangCot = await KhoangCotService.getAll({ is_deleted: false, duong_day_id: { $in: dsDuongDayId } });
    const mapKhoangCot = {};
    allKhoangCot.forEach(khoangCot => {
      mapKhoangCot[khoangCot.ten_khoang_cot] = khoangCot._id;
    });
    const dsKhoangCot = trim(row[Headers.KHOANG_COT])?.split(', ');
    const dsKhoangCotId = dsKhoangCot?.map(item => mapKhoangCot[item] || null)?.filter(item => item);

    const dsDieuKienAnToan = trim(row[Headers.DIEU_KIEN_AN_TOAN])?.split('\r\n')?.map(item => item.trim().replace(/^- /, ""));
    const dsDieuKienAnToanId = dsDieuKienAnToan?.map(item => mapDieuKienAnToan[item] || null)?.filter(item => item);

    const dsBienPhapAnToan = trim(row[Headers.BIEN_PHAP_AN_TOAN])?.split('\r\n')?.map(item => item.trim().replace(/^- /, ""));
    const dsBienPhapAnToanId = dsBienPhapAnToan?.map(item => mapBienPhapAnToan[item] || null)?.filter(item => item);

    let totalDistance = 0;
    if (trim(row[Headers.SU_DUNG_THIET_BI_BAY])?.toLowerCase() === 'có') {
      for (const khoangCotId of dsKhoangCotId) {
        const dataKhoangCot = allKhoangCot.filter(data => data._id === khoangCotId);
        // Số mạch chính là số đường dây đi qua cùng 1 vị trí
        let soMach = dsDuongDayId.map(id => id.toString()).filter(item => new Set(dataKhoangCot[0]?.duong_day_id?.map(id => id.toString())).has(item)).length;
        if (soMach <= 1) soMach = 1;
        totalDistance += (dataKhoangCot[0]?.chieu_dai || 0) * soMach;
      }
      totalDistance = totalDistance.toFixed(1);
    }

    return {
      thu_tu: row[Headers.STT],
      trang_thai_cong_viec: TRANG_THAI_PHIEU.DANG_TAO_PHIEU.code,
      don_vi_giao_phieu_id: mapDonVi[trim(row[Headers.DON_VI_GIAO_PHIEU])],
      nguoi_cap_phieu_id: mapUser[trim(row[Headers.NGUOI_CAP_PHIEU])],
      loai_cong_viec: loaiCongViec,
      thang_kiem_tra: convertMonth(trim(row[Headers.THANG_KIEM_TRA])),
      su_dung_drone: trim(row[Headers.SU_DUNG_THIET_BI_BAY])?.toLowerCase() === 'có' ? true : false,
      noi_dung_cong_tac: trim(row[Headers.NOI_DUNG_CONG_TAC]),
      thoi_gian_cong_tac_bat_dau: moment(trim(row[Headers.THOI_GIAN_BAT_DAU])),
      thoi_gian_cong_tac_ket_thuc: moment(trim(row[Headers.THOI_GIAN_KET_THUC])),
      nhan_viec_sau_du_kien: trim(row[Headers.NHAN_VIEC_SAU_DU_KIEN])?.toLowerCase() === 'có' ? true : false,
      ly_do_nhan_viec_sau_du_kien: trim(row[Headers.LY_DO_NHAN_VIEC_SAU_DU_KIEN]),
      chi_huy_truc_tiep_id: mapUser[trim(row[Headers.CHI_HUY])],
      nhan_vien_ct_ids: dsNguoiCongTacId,
      tinh_trang_van_hanh_duong_day: trim(row[Headers.TINH_TRANG_VAN_HANH])?.toLowerCase() === 'đang mang điện' ? 'MANG_DIEN' : 'KHONG_MANG_DIEN',
      duong_day_ids: dsDuongDayId,
      vi_tri_ids: dsViTriId,
      khoang_cot_ids: dsKhoangCotId,
      dieu_kien_an_toan_ids: dsDieuKienAnToanId,
      bien_phap_an_toan_ids: dsBienPhapAnToanId,
      pham_vi_bay_du_kien: totalDistance,
      is_deleted: false,
    };
  }

  const dataToDB = await Promise.all(rows.map(row => convertToDB(row)));
  if (dataToDB) {
    for (const phieuGiaoViec of dataToDB) {
      const soPhieu = await generateSoPhieu(PHIEU_GIAO_VIEC, phieuGiaoViec?.don_vi_giao_phieu_id);
      phieuGiaoViec.so_phieu = soPhieu;
      const result = await PHIEU_GIAO_VIEC.create(phieuGiaoViec);
      
      if (result) {
        if (phieuGiaoViec?.nhan_vien_ct_ids) {
          for (const userId of phieuGiaoViec.nhan_vien_ct_ids) {
            const data = {
              user_id: Types.ObjectId(userId),
              phieu_giao_viec_id: Types.ObjectId(result?._id),
              don_vi_id: Types.ObjectId(phieuGiaoViec?.don_vi_giao_phieu_id),
              is_deleted: false
            };

            await NGUOI_CONG_TAC.create(data);
          }
        }

        if (phieuGiaoViec?.vi_tri_ids) {
          for (const viTriId of phieuGiaoViec.vi_tri_ids) {
            await VI_TRI_CONG_VIEC.create({
              vi_tri_id: Types.ObjectId(viTriId),
              phieu_giao_viec_id: Types.ObjectId(result?._id),
              is_deleted: false
            });
          }
        }

        if (phieuGiaoViec?.khoang_cot_ids) {
          for (const khoangCotId of phieuGiaoViec.khoang_cot_ids) {
            await VI_TRI_CONG_VIEC.create({
              khoang_cot_id: Types.ObjectId(khoangCotId),
              phieu_giao_viec_id: Types.ObjectId(result?._id),
              is_deleted: false
            });
          }
        }

        if (phieuGiaoViec?.dieu_kien_an_toan_ids) {
          for (const dieuKienAnToanId of phieuGiaoViec.dieu_kien_an_toan_ids) {
            await DIEU_KIEN_AN_TOAN_CONG_VIEC.create({
              dieu_kien_an_toan_id: Types.ObjectId(dieuKienAnToanId),
              phieu_giao_viec_id: Types.ObjectId(result?._id),
              is_deleted: false
            });
          }
        }

        if (phieuGiaoViec?.bien_phap_an_toan_ids) {
          for (const bienPhapAnToanId of phieuGiaoViec.bien_phap_an_toan_ids) {
            await BIEN_PHAP_AN_TOAN_CONG_VIEC.create({
              bien_phap_an_toan_id: Types.ObjectId(bienPhapAnToanId),
              phieu_giao_viec_id: Types.ObjectId(result?._id),
              is_deleted: false
            });
          }
        }
      }
    }
  }
}

export function checkImport(t, sheetData, objMapData, myDonViId) {
  if (!sheetData) return null;
  const {mapDonVi, mapUser, mapDuongDay} = objMapData;

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row, myDonViId) {
    let errors = [];
    if (!row[Headers.DON_VI_GIAO_PHIEU]?.trim()) {
      errors = [...errors, createError(Headers.DON_VI_GIAO_PHIEU, t('missing_unit'))];
    }
    if (!mapDonVi[row[Headers.DON_VI_GIAO_PHIEU]?.trim()]) {
      errors = [...errors, createError(Headers.DON_VI_GIAO_PHIEU, t('management_unit_incorrect_or_dont_create'))];
    }

    // Đơn vị giao phiếu hiện tại và người dùng nhập vào khác nhau
    if (mapDonVi[row[Headers.DON_VI_GIAO_PHIEU]?.toString()?.trim()]?.toString()?.trim() !== myDonViId?.toString()?.trim()) {
      errors = [...errors, createError(Headers.DON_VI_GIAO_PHIEU, t('Không hợp lệ!'))];
    }

    // Nhập sai tháng kiểm tra
    const thangKiemTra = row[Headers.THANG_KIEM_TRA]?.toString()?.trim();
    if (!thangKiemTra) {
      errors = [...errors, createError(Headers.THANG_KIEM_TRA, t('Để trống!'))];
    }
    else {
      const thangHopLe = new Set([
        "Tháng 1", "Tháng 2", "Tháng 3", "Tháng 4", "Tháng 5", "Tháng 6",
        "Tháng 7", "Tháng 8", "Tháng 9", "Tháng 10", "Tháng 11", "Tháng 12"
      ]);
      if (!thangHopLe.has(thangKiemTra.trim())) {
        errors = [...errors, createError(Headers.THANG_KIEM_TRA, t('Không đúng định dạng!'))];
      }
    }

    // Người cấp phiếu bị bỏ trống
    if (!row[Headers.NGUOI_CAP_PHIEU]?.trim()) {
      errors = [...errors, createError(Headers.NGUOI_CAP_PHIEU, t('Để trống!'))];
    }
    else {
      if (!mapUser[row[Headers.NGUOI_CAP_PHIEU]?.trim()]) {
        errors = [...errors, createError(Headers.NGUOI_CAP_PHIEU, t('Không tồn tại!'))];
      }
    }
    
    // Chỉ huy bị bỏ trống hoặc không tồn tại
    if (!row[Headers.CHI_HUY]?.trim()) {
      errors = [...errors, createError(Headers.CHI_HUY, t('Để trống!'))];
    }
    else {
      if (!mapUser[row[Headers.CHI_HUY]?.trim()]) {
        errors = [...errors, createError(Headers.CHI_HUY, t('Không tồn tại!'))];
      }
    }

    // Người dùng vừa là chỉ huy vừa là nhân viên
    if (row[Headers.NHAN_VIEN_TO_CONG_TAC]?.trim()) {
      const dsNhanVienCT = trim(row[Headers.NHAN_VIEN_TO_CONG_TAC])?.split('\r\n');
      if (dsNhanVienCT?.includes(row[Headers.CHI_HUY]?.trim())) {
        errors = [...errors, createError(Headers.NHAN_VIEN_TO_CONG_TAC, t('Trùng với chỉ huy!'))];
      }
    }

    // Đường dây bị bỏ trống hoặc nhập sai
    if (!row[Headers.DUONG_DAY]?.trim()) {
      errors = [...errors, createError(Headers.DUONG_DAY, t('Để trống!'))];
    }
    else {
      const dsDuongDay = trim(row[Headers.DUONG_DAY])?.split('\r\n')?.map(item => item.trim().replace(/^- /, ""));
      const dsDuongDayId = dsDuongDay?.map(item => mapDuongDay[item] || null)?.filter(item => item);
      if (dsDuongDayId?.length < 1) {
        errors = [...errors, createError(Headers.DUONG_DAY, t('Không hợp lệ!'))];
      }
      else
      {
        if (!row[Headers.VI_TRI]?.trim() && !row[Headers.KHOANG_COT]?.trim()) {
          errors = [...errors, createError(Headers.VI_TRI + " Và " + Headers.KHOANG_COT, t('Để trống!'))];
        }
      }
    }

    // Bỏ trống hoặc nhập sai loại công việc
    const loaiCongViec = row[Headers.LOAI_CONG_VIEC]?.toString()?.trim();
    if (!loaiCongViec) {
      errors = [...errors, createError(Headers.LOAI_CONG_VIEC, t('Để trống!'))];
    }
    else {
      const loaiCongViecValidate = Object.values(LOAI_CONG_VIEC).find(item => item.name === loaiCongViec)?.code || null;
      if(!loaiCongViecValidate){
        errors = [...errors, createError(Headers.LOAI_CONG_VIEC, t('Không hợp lệ!'))];
      }
    }

    // Bỏ trống hoặc nhập sai Thời gian công tác bắt đầu
    const thoiGianBatDau = row[Headers.THOI_GIAN_BAT_DAU]?.toString().trim();
    if (!thoiGianBatDau) {
      errors = [...errors, createError(Headers.THOI_GIAN_BAT_DAU, t('Để trống!'))];
    }
    else {
      let parsedDate = moment(thoiGianBatDau, moment.ISO_8601, true);
      if (!parsedDate.isValid()) {
        const dateObj = new Date(thoiGianBatDau);
        if (!isNaN(dateObj.getTime())) {
          parsedDate = moment(dateObj);
        }

        if (!parsedDate.isValid()) {
          errors = [...errors, createError(Headers.THOI_GIAN_BAT_DAU, t('Không hợp lệ!'))];
        }
      }
    }

    // Bỏ trống hoặc nhập sai Thời gian công tác kết thúc
    const thoiGianKetThuc = row[Headers.THOI_GIAN_KET_THUC];
    if (!thoiGianKetThuc) {
      errors = [...errors, createError(Headers.THOI_GIAN_KET_THUC, t('Để trống!'))];
    }
    else {
      const parsedDate = moment(thoiGianKetThuc, moment.ISO_8601, true);
      if (!parsedDate.isValid()) {
        const dateObj = new Date(thoiGianKetThuc);
        if (!isNaN(dateObj.getTime())) {
          parsedDate = moment(dateObj);
        }

        if (!parsedDate.isValid()) {
          errors = [...errors, createError(Headers.THOI_GIAN_KET_THUC, t('Không hợp lệ!'))];
        }
      }
    }

    if (errors.length) {
      row['Lỗi'] = errors;
    } else {
      row['Lỗi'] = null;
    }
    return row;
  }

  return sheetData?.map(e => {
    e.rows = e.rows.map(row => validateRow(t, row, myDonViId));
    return e;
  });
}

