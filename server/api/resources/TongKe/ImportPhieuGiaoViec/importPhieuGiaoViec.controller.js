import * as responseAction from '../../../helpers/responseHelper';
import * as ImportPhieuGiaoViecService from './importPhieuGiaoViec.service';
import * as DonViService from '../../DonVi/donVi.service';
import * as UserService from '../../User/user.service';
import * as DuongDayService from '../../TongKe/DuongDay/duongDay.service';
import * as ViTriService from '../../TongKe/ViTri/viTri.service';
import * as KhoangCotService from '../../TongKe/KhoangCot/khoangCot.service';
import exelUtils from '../../../utils/exelUtils';

const SHEET_NAMES = {
  PHIEU_KIEM_TRA: 'Phiếu kiểm tra',
};


export async function importPhieuGiaoViec(req, res) {
  function getSheetByName(sheetData, name) {
    return sheetData.find((sheet) => sheet.name.toLowerCase().includes(name.toLowerCase()));
  }

  try {
    let filePath = req.files.file.path;
    const sheetData = await exelUtils.transformFile(filePath);
    const result = {};
    const allDonVi = await DonViService.getAll({ is_deleted: false });
    const mapDonVi = {};
    allDonVi.forEach(donVi => {
      mapDonVi[donVi.ten_don_vi] = donVi._id;
    });
    const allPhieuGiaoViec = await ImportPhieuGiaoViecService.getAll({ is_deleted: false });
    const mapCongTrinhCha = {};
    allPhieuGiaoViec.forEach(congTrinh => {
      mapCongTrinhCha[congTrinh.ten_cong_trinh] = congTrinh._id;
    });

    result.cong_trinh = await ImportPhieuGiaoViecService.importData(getSheetByName(sheetData, 'Phiếu kiểm tra'), mapDonVi, mapCongTrinhCha);

    const allCongTrinh = await ImportPhieuGiaoViecService.getAll({ is_deleted: false });
    const mapCongTrinh = {};
    allCongTrinh.forEach(congTrinh => {
      mapCongTrinh[congTrinh.ten_cong_trinh] = congTrinh._id;
    });

    responseAction.success(res, result);
  } catch (e) {
    responseAction.error(res, e);
  }
}

async function importData(name, sheetData) {
  sheetData.forEach(sheet => {
    trimData(sheet);
  });

  function getSheetByName(sheetData, sheetName) {
    return sheetData.find((sheet) => sheet.name.toLowerCase().includes(sheetName.toLowerCase()));
  }

  const result = {};
  if (getSheetByName(sheetData, SHEET_NAMES.PHIEU_KIEM_TRA)) {
    result[name] = await ImportPhieuGiaoViecService.importData(getSheetByName(sheetData, SHEET_NAMES.PHIEU_KIEM_TRA));
  }

  return result;
}

export async function importOne(req, res) {

  try {
    const sheetData = [trimData(req.body)];
    const result = await importData(req.body.ma_cong_trinh, req.body.name, sheetData);
    responseAction.success(res, result);
  } catch (e) {
    responseAction.error(res, e);
  }
}

export async function importMany(req, res) {
  try {
    let result = {};
    for (let sheet of req.body) {
      result = { ...result, ...await importData(sheet.name, [sheet]) };
    }
    responseAction.success(res, result);
  } catch (e) {
    responseAction.error(res, e);
  }
}


async function checkImportByData(t, sheetData, myDonViId) {
  function getSheetByName(sheetData, name) {
    return sheetData.filter((sheet) => sheet.name.toLowerCase().includes(name.toLowerCase()));
  }

  let resultArray = [];
  const allDonVi = await DonViService.getAll({ is_deleted: false });
  const mapDonVi = {};
  allDonVi.forEach(donVi => {
    mapDonVi[donVi.ten_don_vi] = donVi._id;
  });

  const allUser = await UserService.getAll({ is_deleted: false });
  const mapUser = {};
  allUser.forEach(user => {
    mapUser[user.full_name] = user._id;
  });

  const allDuongDay = await DuongDayService.getAll({ is_deleted: false });
  const mapDuongDay = {};
  allDuongDay.forEach(duongDay => {
    mapDuongDay[duongDay.ten_duong_day] = duongDay._id;
  });

  const objMapData = {
    mapDonVi : mapDonVi,
    mapUser : mapUser,
    mapDuongDay : mapDuongDay
  }

  resultArray = [...resultArray, ImportPhieuGiaoViecService.checkImport(t, getSheetByName(sheetData, SHEET_NAMES.PHIEU_KIEM_TRA), objMapData, myDonViId)];
  return resultArray;
}

export async function checkImport(req, res) {
  try {
    const { t } = req;
    const myDonViId = req.user?.don_vi_id;
    let filePath = req.files.file.path;
    if(!filePath.includes(".xls") && !filePath.includes(".xlsx")){
      responseAction.success(res, [
        [
          {
            "name": "Phiếu kiểm tra",
            "rows": [
              {
                "Lỗi": [
                  {
                    "col": "File",
                    "error": "Không đúng định dạng xls hoặc xlsx. Hãy tải xuống file mẫu tin nhập liệu!"
                  }
                ]
              }
            ]
          }
        ]
      ]);
      return;
    }
    const sheetData = await exelUtils.transformFile(filePath);
    const resultArray = await checkImportByData(t, sheetData, myDonViId);
    responseAction.success(res, resultArray);
  } catch (e) {
    responseAction.error(res, e);
  }
}

export async function checkImportData(req, res) {

  try {
    const sheetData = req.body;
    const { t } = req;
    const myDonViId = req.user?.don_vi_id;
    const resultArray = await checkImportByData(t, sheetData, myDonViId);
    responseAction.success(res, resultArray);
  } catch (e) {
    responseAction.error(res, e);
  }
}

function trimData(dataInput) {
  if (!Array.isArray(dataInput?.rows)) return dataInput;
  dataInput.rows.forEach(row => {
    Object.entries(row).forEach(([key, value]) => {
      row[key] = (!!value && typeof value === 'string') ? value.trim() : value;
    });
  });
  return dataInput;
}
