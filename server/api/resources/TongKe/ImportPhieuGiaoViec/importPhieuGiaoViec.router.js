import express from 'express';
import passport from 'passport';
import * as phieuGiaoViecTempController from './importPhieuGiaoViec.controller';
import { checkTempFolder, multipartMiddleware } from '../../../utils/fileUtils';
import { loggerMiddleware } from '../../../logs/middleware';
import { authorizationMiddleware } from '../../RBAC/middleware';
import PhieuGiaoViecPermission from '../../RBAC/permissions/WorkPermission';

export const importPhieuGiaoViecRouter = express.Router();
importPhieuGiaoViecRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
importPhieuGiaoViecRouter.post('*', authorizationMiddleware([PhieuGiaoViecPermission.CREATE]));
importPhieuGiaoViecRouter.put('*', authorizationMiddleware([PhieuGiaoViecPermission.UPDATE]));
importPhieuGiaoViecRouter.delete('*', authorizationMiddleware([PhieuGiaoViecPermission.DELETE]));

importPhieuGiaoViecRouter
  .route('/import')
  .post(passport.authenticate('jwt', { session: false }),
    checkTempFolder, multipartMiddleware, phieuGiaoViecTempController.importPhieuGiaoViec);

importPhieuGiaoViecRouter
  .route('/importone')
  .post(passport.authenticate('jwt', { session: false }),
    phieuGiaoViecTempController.importOne);
importPhieuGiaoViecRouter
  .route('/importmany')
  .post(passport.authenticate('jwt', { session: false }), phieuGiaoViecTempController.importMany);

importPhieuGiaoViecRouter
  .route('/checkimport')
  .post(passport.authenticate('jwt', { session: false }),
    checkTempFolder, multipartMiddleware, phieuGiaoViecTempController.checkImport);

importPhieuGiaoViecRouter
  .route('/checkimportbydata')
  .post(passport.authenticate('jwt', { session: false }), phieuGiaoViecTempController.checkImportData);
