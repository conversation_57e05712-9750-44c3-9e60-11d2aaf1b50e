import PMIS_SOAP_NAME from '../soapName';
import { convertInt, extractKeys, formatArrayUnique } from '../../../../utils/dataconverter';
import { convertMoment } from '../../../../helpers/checkDataHelper';
import { cleanEscapedJsonString, cloneObj } from '../../../../common/functionCommons';
import {
  checkDataThietBiPmis,
  getDataPmis,
  getObjDuongDay,
  getObjVanHanh,
  logSyncPmis,
  logSyncPmisByCongTrinh,
} from '../syncPmis.service';
import DAY_DAN from '../../DayDan/dayDan.model';
import * as VanHanhService from '../../VanHanh/vanHanh.service';
import * as ViTriService from '../../ViTri/viTri.service';


const queryStartTime = '01/01/2000';


async function syncDayDanByCongTrinh(maCongTrinh, thoiGian = queryStartTime) {
  try {
    const query = { ID: maCongTrinh, TU_NGAY: thoiGian || queryStartTime };
    let pmisResponse = await getDataPmis(PMIS_SOAP_NAME.DAY_DAN_BY_CONG_TRINH_ID, query);
    if (!pmisResponse.success) return logSyncPmisByCongTrinh('DAY_DAN', maCongTrinh, null, pmisResponse);

    const dataPmis = formatArrayUnique(pmisResponse.data?.flat(), 'ID');
    const objVanHanh = await getObjVanHanh(dataPmis);
    const objDuongDay = await getObjDuongDay(dataPmis);


    await checkDataThietBiPmis(dataPmis);
    const validData = dataPmis.filter(dayDan => !dayDan.error?.length);
    const invalidData = dataPmis.filter(dayDan => !!dayDan.error?.length);

    if (!validData?.length) {
      logSyncPmisByCongTrinh('DAY_DAN', maCongTrinh, null, { invalid_data: true });
      return invalidData;
    }

    const dataSync = validData.map(dayDan => {
      return {
        ma_day_dan: dayDan.ID,
        van_hanh_id: objVanHanh[dayDan.MA_VH]?._id,
        ten_day_dan: cleanEscapedJsonString(dayDan.TEN_DDAN),
        thu_tu: convertInt(dayDan.STT),
        ngay_van_hanh: convertMoment(dayDan.NGAY_VHANH),
        tinh_trang_van_hanh: dayDan.TINH_TRANG,
        duong_day: objDuongDay[dayDan.MA_DZ]?.ten_duong_day,
        pha: dayDan.PHA,
        ma_hieu_day: cleanEscapedJsonString(dayDan.MA_HIEU),
        so_luong_day: dayDan.SO_LUONG,
        ky_hieu_day: cleanEscapedJsonString(dayDan.KY_HIEU),
        cau_tao: cleanEscapedJsonString(dayDan.CAU_TAO),
        trong_luong_day: dayDan.TRONG_LUONG,
        luc_chiu_keo: dayDan.LUC_CKEO,
        dien_tro_mot_chieu: dayDan.DIEN_TRO,
        loai_chong_rung: dayDan.LCHONG_RUNG,
        so_luong_chong_rung: dayDan.SLUONG_CRUNG,
        so_luong_moi_noi: dayDan.SO_LUONG_MNOI,
        so_luong_khung_dinh_vi: dayDan.SLUONG_KDVI,
        so_luong_thiet_bi_canh_bao: dayDan.SLUONG_TBI_CBAO,
        is_deleted: false,
      };
    }).filter(dayDan => !!dayDan.van_hanh_id);
    DAY_DAN.bulkWrite(
      cloneObj(dataSync).map((row) =>
        ({
          updateOne: {
            filter: { ma_day_dan: row.ma_day_dan },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
    return logSyncPmisByCongTrinh('DAY_DAN', maCongTrinh, { success: true });
  } catch (e) {
    console.log('e', e);
    return logSyncPmis('DAY_DAN', null, e);
  }
}

async function syncDayDanByViTri(viTriData, thoiGian = queryStartTime) {
  if (!viTriData) return;
  try {
    const { vanHanhData } = viTriData;

    const maCongTrinh = vanHanhData?.[0]?.vi_tri_id?.cong_trinh_id?.ma_cong_trinh;
    if (!maCongTrinh) return;

    const maVanHanh = extractKeys(vanHanhData, 'ma_van_hanh');


    const query = { ID: maCongTrinh, TU_NGAY: thoiGian || queryStartTime };
    let pmisResponse = await getDataPmis(PMIS_SOAP_NAME.DAY_DAN_BY_CONG_TRINH_ID, query);
    if (!pmisResponse.success) return logSyncPmisByCongTrinh('DAY_DAN', viTriData.ma_vi_tri, null, pmisResponse);

    let dataPmis = formatArrayUnique(pmisResponse.data?.flat(), 'ID');

    dataPmis = dataPmis.filter(data => maVanHanh.includes(data.MA_VH));

    const objVanHanh = await getObjVanHanh(dataPmis);
    const objDuongDay = await getObjDuongDay(dataPmis);


    await checkDataThietBiPmis(dataPmis);
    const validData = dataPmis.filter(dayDan => !dayDan.error?.length);
    const invalidData = dataPmis.filter(dayDan => !!dayDan.error?.length);

    if (!validData?.length) {
      logSyncPmisByCongTrinh('DAY_DAN', viTriData.ma_vi_tri, null, { invalid_data: true });
      return invalidData;
    }

    const dataSync = validData.map(dayDan => {
      return {
        ma_day_dan: dayDan.ID,
        van_hanh_id: objVanHanh[dayDan.MA_VH]?._id,
        ten_day_dan: cleanEscapedJsonString(dayDan.TEN_DDAN),
        thu_tu: convertInt(dayDan.STT),
        ngay_van_hanh: convertMoment(dayDan.NGAY_VHANH),
        tinh_trang_van_hanh: dayDan.TINH_TRANG,
        duong_day: objDuongDay[dayDan.MA_DZ]?.ten_duong_day,
        pha: dayDan.PHA,
        ma_hieu_day: cleanEscapedJsonString(dayDan.MA_HIEU),
        so_luong_day: dayDan.SO_LUONG,
        ky_hieu_day: cleanEscapedJsonString(dayDan.KY_HIEU),
        cau_tao: cleanEscapedJsonString(dayDan.CAU_TAO),
        trong_luong_day: dayDan.TRONG_LUONG,
        luc_chiu_keo: dayDan.LUC_CKEO,
        dien_tro_mot_chieu: dayDan.DIEN_TRO,
        loai_chong_rung: dayDan.LCHONG_RUNG,
        so_luong_chong_rung: dayDan.SLUONG_CRUNG,
        so_luong_moi_noi: dayDan.SO_LUONG_MNOI,
        so_luong_khung_dinh_vi: dayDan.SLUONG_KDVI,
        so_luong_thiet_bi_canh_bao: dayDan.SLUONG_TBI_CBAO,
        is_deleted: false,
      };
    }).filter(dayDan => !!dayDan.van_hanh_id);
    DAY_DAN.bulkWrite(
      cloneObj(dataSync).map((row) =>
        ({
          updateOne: {
            filter: { ma_day_dan: row.ma_day_dan },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
    await logSyncPmisByCongTrinh('DAY_DAN', viTriData.ma_vi_tri, { success: true });
    return dataSync;
  } catch (e) {
    console.log('e', e);
    return await logSyncPmis('DAY_DAN', null, e);
  }
}

export default {
  syncDayDanByCongTrinh,
  syncDayDanByViTri,
};
