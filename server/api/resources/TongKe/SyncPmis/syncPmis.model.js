import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { SYNC_PMIS } from '../../../constant/dbCollections';

const schema = new Schema({
  type: { type: String },
  code: { type: String },
  result: { type: Object },
  error: { type: Object },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
schema.index({ code: 1 });
export default mongoose.model(SYNC_PMIS, schema, SYNC_PMIS);
