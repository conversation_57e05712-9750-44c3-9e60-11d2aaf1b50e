import PMIS_SOAP_NAME from '../soapName';
import { convertInt, extractKeys, formatArrayUnique } from '../../../../utils/dataconverter';
import { convertMoment } from '../../../../helpers/checkDataHelper';
import { cleanEscapedJsonString, cloneObj } from '../../../../common/functionCommons';
import {
  checkDataThietBiPmis,
  getDataPmis,
  getObjVanHanh,
  logSyncPmis,
  logSyncPmisByCongTrinh,
} from '../syncPmis.service';

import * as ViTriService from '../../ViTri/viTri.service';
import * as VanHanhService from '../../VanHanh/vanHanh.service';
import CACH_DIEN from '../../CachDien/cachDien.model';
import { getAll } from '../../VanHanh/vanHanh.service';


const queryStartTime = '01/01/2000';


async function getCachDienPmis(maCongTrinh, thoiGian) {

  const query = { ID: maCongTrinh, TU_NGAY: thoiGian || queryStartTime };
  const pmisResponse = await getDataPmis(PMIS_SOAP_NAME.CACH_DIEN_BY_CONG_TRINH_ID, query);
  if (pmisResponse.data) {
    pmisResponse.data = formatArrayUnique(pmisResponse.data?.flat(), 'ID');
  }

  return pmisResponse;
}

async function syncCachDienByCongTrinh(maCongTrinh, thoiGian = queryStartTime) {
  try {

    const pmisResponse = await getCachDienPmis(maCongTrinh, thoiGian);
    if (!pmisResponse.success) return logSyncPmisByCongTrinh('CACH_DIEN', maCongTrinh, null, pmisResponse);

    const dataPmis = pmisResponse.data;
    const objVanHanh = await getObjVanHanh(dataPmis);

    await checkDataThietBiPmis(dataPmis);
    const validData = dataPmis.filter(cachDien => !cachDien.error?.length);
    const invalidData = dataPmis.filter(cachDien => !!cachDien.error?.length);

    if (!validData?.length) {
      await logSyncPmisByCongTrinh('CACH_DIEN', maCongTrinh, null, { invalid_data: true });
      return invalidData;
    }


    const dataSync = validData.map(cachDien => {
      return {
        ma_cach_dien: cachDien.ID,
        van_hanh_id: objVanHanh[cachDien.MA_VH]?._id,
        ten_cach_dien: cachDien.TEN_CDIEN,
        thu_tu: convertInt(cachDien.STT),
        ngay_van_hanh: convertMoment(cachDien.NGAY_VHANH),
        tinh_trang_van_hanh: cachDien.TINH_TRANG,
        pha: cachDien.PHA,
        chuc_nang: cachDien.CHUC_NANG,
        loai_cach_dien: cachDien.LOAI_CDIEN,
        ma_hieu_cach_dien: cleanEscapedJsonString(cachDien.MA_HIEU),
        huong_lap_dat: cachDien.HUONG_LDAT,
        so_luong_chuoi: cachDien.SLUONG_CHUOI,
        so_luong_bat_tren_chuoi: cachDien.SLUONG_BAT,
        chieu_dai_duong_ro: cachDien.CHIEU_DAI,
        vong_vang_quang: cachDien.VONG_QUANG,
        mo_phong: cachDien.MO_PHONG,

        is_deleted: false,
      };
    }).filter(cachDien => !!cachDien.van_hanh_id);
    CACH_DIEN.bulkWrite(
      cloneObj(dataSync).map((row) =>
        ({
          updateOne: {
            filter: { ma_cach_dien: row.ma_cach_dien },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
    return logSyncPmisByCongTrinh('CACH_DIEN', maCongTrinh, { success: true });
  } catch (e) {
    console.log('e', e);
    return logSyncPmis('CACH_DIEN', null, e);
  }
}

async function syncCachDienByViTri(viTriData, thoiGian = queryStartTime) {
  if (!viTriData) return;
  try {
    const { vanHanhData } = viTriData;

    const maCongTrinh = vanHanhData?.[0]?.vi_tri_id?.cong_trinh_id?.ma_cong_trinh;
    if (!maCongTrinh) return;

    const maVanHanh = extractKeys(vanHanhData, 'ma_van_hanh');

    const pmisResponse = await getCachDienPmis(maCongTrinh, thoiGian);
    if (!pmisResponse.success) return logSyncPmisByCongTrinh('CACH_DIEN', viTriData.ma_vi_tri, null, pmisResponse);

    const dataPmis = pmisResponse.data.filter(data => maVanHanh.includes(data.MA_VH));
    const objVanHanh = await getObjVanHanh(dataPmis);

    await checkDataThietBiPmis(dataPmis);
    const validData = dataPmis.filter(cachDien => !cachDien.error?.length);
    const invalidData = dataPmis.filter(cachDien => !!cachDien.error?.length);

    if (!validData?.length) {
      await logSyncPmisByCongTrinh('CACH_DIEN', viTriData.ma_vi_tri, null, { invalid_data: true });
      return invalidData;
    }

    const dataSync = validData.map(cachDien => {
      return {
        ma_cach_dien: cachDien.ID,
        van_hanh_id: objVanHanh[cachDien.MA_VH]?._id,
        ten_cach_dien: cachDien.TEN_CDIEN,
        thu_tu: convertInt(cachDien.STT),
        ngay_van_hanh: convertMoment(cachDien.NGAY_VHANH),
        tinh_trang_van_hanh: cachDien.TINH_TRANG,
        pha: cachDien.PHA,
        chuc_nang: cachDien.CHUC_NANG,
        loai_cach_dien: cachDien.LOAI_CDIEN,
        ma_hieu_cach_dien: cleanEscapedJsonString(cachDien.MA_HIEU),
        huong_lap_dat: cachDien.HUONG_LDAT,
        so_luong_chuoi: cachDien.SLUONG_CHUOI,
        so_luong_bat_tren_chuoi: cachDien.SLUONG_BAT,
        chieu_dai_duong_ro: cachDien.CHIEU_DAI,
        vong_vang_quang: cachDien.VONG_QUANG,
        mo_phong: cachDien.MO_PHONG,

        is_deleted: false,
      };
    }).filter(cachDien => !!cachDien.van_hanh_id);
    CACH_DIEN.bulkWrite(
      cloneObj(dataSync).map((row) =>
        ({
          updateOne: {
            filter: { ma_cach_dien: row.ma_cach_dien },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
    await logSyncPmisByCongTrinh('CACH_DIEN', viTriData.ma_vi_tri, { success: true });
    return dataSync;
  } catch (e) {
    console.log('e', e);
    return await logSyncPmis('CACH_DIEN', null, e);
  }
}

export default {
  syncCachDienByCongTrinh,
  syncCachDienByViTri,
};
