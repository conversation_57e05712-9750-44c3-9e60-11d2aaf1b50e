import * as CongTrinhService from '../../CongTrinh/congTrinh.service';
import PMIS_SOAP_NAME from '../soapName';
import { convertInt, extractKeys, formatArrayUnique } from '../../../../utils/dataconverter';
import { convertMoment } from '../../../../helpers/checkDataHelper';
import * as ViTriService from '../../ViTri/viTri.service';
import { cleanEscapedJsonString, cloneObj } from '../../../../common/functionCommons';
import * as KhoangCotService from '../../KhoangCot/khoangCot.service';
import {
  checkDataThietBiPmis,
  getDataPmis, getHashMapVanHanh,
  getObjDonVi, getObjDuongDay, getObjViTri,
  logSyncPmisByCongTrinh,
} from '../syncPmis.service';
import { TINH_TRANG_VAN_HANH } from '../../../DanhMuc/TinhTrangVanHanh';

import GiaoCheoModel from '../../GiaoCheo/giaoCheo.model';
import TiepDatModel from '../../TiepDat/tiepDat.model';
import VanHanhModel from '../../VanHanh/vanHanh.model';
import CotDienModel from '../../CotDien/cotDien.model';
import ViTriModel from '../../ViTri/viTri.model';


const queryStartTime = '01/01/2000';

async function syncViTriByMaCongTrinh(maCongTrinh, autoCreateKhoangCot = false) {
  const thoiGian = queryStartTime;
  const syncCode = maCongTrinh;
  try {
    const congTrinh = await CongTrinhService.getOne({ ma_cong_trinh: maCongTrinh, is_deleted: false },
      { _id: 1, ma_cong_trinh: 1 });

    const query = { ID: maCongTrinh, TU_NGAY: thoiGian || queryStartTime };
    let pmisResponse = await getDataPmis(PMIS_SOAP_NAME.VI_TRI_BY_CONG_TRINH_ID, query);
    if (!pmisResponse.success) return logSyncPmisByCongTrinh('VI_TRI', syncCode, null, pmisResponse);

    const dataPmis = formatArrayUnique(pmisResponse.data?.flat(), 'ID');
    const objDonVi = await getObjDonVi(dataPmis);

    if (!dataPmis?.length) return logSyncPmisByCongTrinh('VI_TRI', syncCode, null, { no_data_pmis: true });

    await checkDataThietBiPmis(dataPmis);
    const validData = dataPmis.filter(viTri => !viTri.error?.length);
    const invalidData = dataPmis.filter(viTri => !!viTri.error?.length);

    if (!validData?.length) {
      await logSyncPmisByCongTrinh('VI_TRI', syncCode, null, { invalid_data: true });
      return invalidData;
    }

    const dataSync = dataPmis.map(viTri => {
      return {
        ma_vi_tri: viTri.ID,
        cong_trinh_id: congTrinh?._id,
        ten_vi_tri: cleanEscapedJsonString(viTri.TEN_VITRI),
        thu_tu: convertInt(viTri.STT),
        ngay_van_hanh: convertMoment(viTri.NGAY_VHANH),
        tinh_trang_van_hanh: viTri.TINH_TRANG,
        so_huu: '',
        don_vi_id: objDonVi[viTri.MA_DVI_QLY]?._id,
        goc_lai: viTri.GOC_LAI,
        khoang_cot: viTri.KHOANG_COT,
        khoang_neo: viTri.KHOANG_NEO,
        khu_vuc: viTri.KHU_VUC,
        tinh: viTri.TINH,
        huyen: viTri.HUYEN,
        xa: viTri.XA,
        so_do_bo_tri_day: cleanEscapedJsonString(viTri.SO_DO_BTRI_DAY),
        kinh_do: viTri.Y,
        vi_do: viTri.X,
        is_deleted: false,
      };
    }).filter(viTri => !!viTri.cong_trinh_id);

    const allMaViTri = extractKeys(dataSync, 'ma_vi_tri');

    const viTriExist = await ViTriService.getAll({
      ma_vi_tri: { $in: allMaViTri },
      don_vi_id: { '$type': 7 }, // don_vi_id has type === ObjectId
    });

    const maViTriExist = extractKeys(viTriExist, 'ma_vi_tri');

    const dataSyncUpdate = dataSync.filter(viTri => maViTriExist.includes(viTri.ma_vi_tri))
      .map(viTri => {
        delete viTri.don_vi_id;
        return viTri;
      });


    const dataSyncCreate = dataSync.filter(viTri => !maViTriExist.includes(viTri.ma_vi_tri));

    await ViTriModel.bulkWrite(
      cloneObj(dataSyncUpdate).map((row) =>
        ({
          updateOne: {
            filter: { ma_vi_tri: row.ma_vi_tri },
            update: { $set: row },
            upsert: false,
          },
        }),
      ),
    );

    await ViTriModel.bulkWrite(
      cloneObj(dataSyncCreate).map((row) =>
        ({
          updateOne: {
            filter: { ma_vi_tri: row.ma_vi_tri },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );

    const viTriSync = await logSyncPmisByCongTrinh('VI_TRI', syncCode, { success: true });

    if (autoCreateKhoangCot) {
      const khoangCot = await KhoangCotService.createOrUpdateKhoangCot([congTrinh?._id]);
      await logSyncPmisByCongTrinh('KHOANG_COT', syncCode, khoangCot.result);
    }
    return viTriSync;
  } catch (e) {
    console.log('e', e);
    return await logSyncPmisByCongTrinh('VI_TRI', syncCode, null, e);
  }
}

async function syncGiaoCheoByMaCongTrinh(maCongTrinh, thoiGian = queryStartTime, maViTri) {
  const syncCode = maViTri || maCongTrinh;
  try {
    const query = { ID: maCongTrinh, TU_NGAY: thoiGian || queryStartTime };
    let pmisResponse = await getDataPmis(PMIS_SOAP_NAME.GIAO_CHEO_BY_CONG_TRINH_ID, query);
    if (!pmisResponse.success) return logSyncPmisByCongTrinh('GIAO_CHEO', syncCode, null, pmisResponse);

    let dataPmis = formatArrayUnique(pmisResponse.data?.flat(), 'ID');
    if (maViTri) {
      dataPmis = dataPmis.filter(giaoCheo => giaoCheo.MA_VITRI === maViTri);
    }
    if (!dataPmis?.length)
      return logSyncPmisByCongTrinh('GIAO_CHEO', syncCode, null, { no_data_pmis: true });

    const objViTri = await getObjViTri(dataPmis);

    await checkDataThietBiPmis(dataPmis);
    const validData = dataPmis.filter(giaoCheo => !giaoCheo.error?.length);
    const invalidData = dataPmis.filter(giaoCheo => !!giaoCheo.error?.length);

    if (!validData?.length) {
      await logSyncPmisByCongTrinh('GIAO_CHEO', syncCode, null, { invalid_data: true });
      return invalidData;
    }

    const dataSync = validData.map(giaoCheo => {
      return {
        ma_giao_cheo: giaoCheo.ID,
        vi_tri_id: objViTri[giaoCheo.MA_VITRI]?._id,
        ten_giao_cheo: giaoCheo.TEN_GC,
        thu_tu: convertInt(giaoCheo.STT),
        ngay_van_hanh: convertMoment(giaoCheo.NGAY_VHANH),
        tinh_trang_van_hanh: giaoCheo.TINH_TRANG,
        ngay_lap_dat: convertMoment(giaoCheo.NGAY_LDAT),
        nam_san_xuat: giaoCheo.NAM_SXUAT,
        ngay_sua_doi: convertMoment(giaoCheo.NGAY_SDOI),
        ghi_chu: giaoCheo.GHI_CHU,
        // don_vi_id: objDonVi[giaoCheo.MA_DVI_QLY]?._id,
        doi_tuong_giao_cheo: giaoCheo.DTUONG_GCHEO,
        thong_tin_khac: giaoCheo.TTIN_KHAC,
        is_deleted: false,
      };
    }).filter(giaoCheo => !!giaoCheo.vi_tri_id);
    await GiaoCheoModel.bulkWrite(
      cloneObj(dataSync).map((row) =>
        ({
          updateOne: {
            filter: { ma_giao_cheo: row.ma_giao_cheo },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
    return logSyncPmisByCongTrinh('GIAO_CHEO', syncCode, { success: true });
  } catch (e) {
    console.log('e', e);
    return await logSyncPmisByCongTrinh('GIAO_CHEO', syncCode, null, e);
  }
}

async function syncTiepDatPmis(maCongTrinh, thoiGian = queryStartTime, maViTri) {
  const syncCode = maViTri || maCongTrinh;
  try {
    const query = { ID: maCongTrinh, TU_NGAY: thoiGian || queryStartTime };
    let pmisResponse = await getDataPmis(PMIS_SOAP_NAME.TIEP_DAT_BY_CONG_TRINH_ID, query);
    if (!pmisResponse.success) return logSyncPmisByCongTrinh('TIEP_DAT', syncCode, null, pmisResponse);

    let dataPmis = formatArrayUnique(pmisResponse.data?.flat(), 'ID');
    if (maViTri) {
      dataPmis = dataPmis.filter(giaoCheo => giaoCheo.MA_VITRI === maViTri);
    }
    if (!dataPmis?.length) return logSyncPmisByCongTrinh('TIEP_DAT', syncCode, null, { no_data_pmis: true });

    const objViTri = await getObjViTri(dataPmis);

    await checkDataThietBiPmis(dataPmis);
    const validData = dataPmis.filter(tiepDat => !tiepDat.error?.length);
    const invalidData = dataPmis.filter(tiepDat => !!tiepDat.error?.length);

    if (!validData?.length) {
      await logSyncPmisByCongTrinh('TIEP_DAT', syncCode, null, { invalid_data: true });
      return invalidData;
    }

    const dataSync = validData.map(tiepDat => {
      return {
        ma_tiep_dat: tiepDat.ID,
        ten_tiep_dat: tiepDat.TEN_TDIA,
        vi_tri_id: objViTri[tiepDat.MA_VITRI]?._id,
        thu_tu: convertInt(tiepDat.STT),
        ngay_van_hanh: convertMoment(tiepDat.NGAY_VHANH),
        tinh_trang_van_hanh: tiepDat.TINH_TRANG,
        hang_san_xuat: tiepDat.HANG_SX,
        nha_cung_cap: tiepDat.NHA_CCAP,
        nuoc_san_xuat: tiepDat.NUOC_SXUAT,
        ngay_lap_dat: tiepDat.NGAYLLDAT,
        nam_san_xuat: tiepDat.NAM_SXUAT,
        ghi_chu: tiepDat.GHI_CHU,
        ma_hieu_noi_dat: tiepDat.MA_HIEU,
        so_tia: tiepDat.SO_TIA,
        chieu_dai_tia: tiepDat.CDAI_TIA,
        vat_lieu_tia: tiepDat.VLIEU_TIA,
        so_coc: tiepDat.SO_COC,
        chieu_dai_coc: tiepDat.CDAI_COC,
        khoang_cach_cac_coc: tiepDat.KCACH_COC,
        vat_lieu_coc: tiepDat.VLIEU_COC,
        dien_tro_tai_thoi_diem_ban_dau: tiepDat.DTRO_TDIA,
        is_deleted: false,
      };
    }).filter(tiepDat => !!tiepDat.vi_tri_id);
    await TiepDatModel.bulkWrite(
      cloneObj(dataSync).map((row) =>
        ({
          updateOne: {
            filter: { ma_tiep_dat: row.ma_tiep_dat },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
    return logSyncPmisByCongTrinh('TIEP_DAT', syncCode, { success: true });
  } catch (e) {
    console.log('e', e);
    return await logSyncPmisByCongTrinh('TIEP_DAT', syncCode, null, e);
  }
}

async function syncVanHanhPmis(maCongTrinh, thoiGian = queryStartTime, maViTri) {
  const syncCode = maViTri || maCongTrinh;
  try {
    const query = { ID: maCongTrinh, TU_NGAY: '01/01/2000' };
    let pmisResponse = await getDataPmis(PMIS_SOAP_NAME.VAN_HANH_BY_CONG_TRINH_ID, query, 5000);
    if (!pmisResponse.success) return logSyncPmisByCongTrinh('VAN_HANH', syncCode, null, pmisResponse);

    let dataPmis = formatArrayUnique(pmisResponse.data?.flat(), 'ID');
    if (maViTri) {
      dataPmis = dataPmis.filter(giaoCheo => giaoCheo.MA_VITRI?.toString() === maViTri?.toString());
    }
    if (!dataPmis?.length) return logSyncPmisByCongTrinh('VAN_HANH', syncCode, null, { no_data_pmis: true });

    const objDuongDay = await getObjDuongDay(dataPmis);
    const objViTri = await getObjViTri(dataPmis);
    // const objVanHanh = await getObjVanHanh(dataPmis, 'ID');
    const mapVanHanh = await getHashMapVanHanh(dataPmis, 'ID');
    await checkDataThietBiPmis(dataPmis);
    const validData = dataPmis.filter(vanHanh => !vanHanh.error?.length);
    const invalidData = dataPmis.filter(vanHanh => !!vanHanh.error?.length);
    if (!validData?.length) {
      await logSyncPmisByCongTrinh('VAN_HANH', syncCode, null, { invalid_data: true });
      return invalidData;
    }
    const dataSync = validData.map(vanHanh => {
      return {
        ma_van_hanh: vanHanh.ID,
        vi_tri_id: objViTri[vanHanh.MA_VITRI]?._id,
        ten_van_hanh: cleanEscapedJsonString(vanHanh.TEN_VH),
        thu_tu: convertInt(vanHanh.STT),
        ngay_van_hanh: convertMoment(vanHanh.NGAY_VHANH),
        tinh_trang_van_hanh: vanHanh.TINH_TRANG,
        duong_day_id: objDuongDay[vanHanh.MA_DZ]?._id,
        is_deleted: false,
      };
    }).filter(vanHanh => !!vanHanh.vi_tri_id);
    //Xóa vận hành đang sai vị trí
    for (let i = 0; i < cloneObj(dataSync).length; ++i) {
      const vanHanh = cloneObj(dataSync)[i];
      if (mapVanHanh[vanHanh.ma_van_hanh] !== vanHanh.duong_day_id + vanHanh.vi_tri_id) {
        console.log(1);
        // await VanHanhModel.remove({ ma_van_hanh: vanHanh.ma_van_hanh });

        const countSai = await VanHanhModel.count({
          ma_van_hanh: {
            '$regex': vanHanh.ma_van_hanh + '-SAI',
            '$options': 'i',
          },
        });

        await VanHanhModel.updateOne({ ma_van_hanh: vanHanh.ma_van_hanh }, {
          $set: {
            ma_van_hanh: vanHanh.ma_van_hanh + '-SAI-' + (countSai + 1),
            is_deleted: false,
          },
        });
      }
    }

    await VanHanhModel.bulkWrite(
      cloneObj(dataSync).map((row) =>
        ({
          updateOne: {
            filter: { ma_van_hanh: row.ma_van_hanh },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
    return logSyncPmisByCongTrinh('VAN_HANH', syncCode, { success: true });
  } catch (e) {
    console.log('e', e);
    return logSyncPmisByCongTrinh('VAN_HANH', syncCode, null, e);
  }
}

async function syncCotDienPmis(maCongTrinh, thoiGian = queryStartTime, maViTri) {
  const syncCode = maViTri || maCongTrinh;
  try {
    const query = { ID: maCongTrinh, TU_NGAY: thoiGian || queryStartTime };
    let pmisResponse = await getDataPmis(PMIS_SOAP_NAME.COT_DIEN_BY_CONG_TRINH_ID, query);
    if (!pmisResponse.success) return logSyncPmisByCongTrinh('COT_DIEN', syncCode, null, pmisResponse);

    let dataPmis = formatArrayUnique(pmisResponse.data?.flat(), 'ID');
    if (maViTri) {
      dataPmis = dataPmis.filter(giaoCheo => giaoCheo.MA_VITRI === maViTri);
    }
    if (!dataPmis?.length) return logSyncPmisByCongTrinh('COT_DIEN', syncCode, null, { no_data_pmis: true });

    const objViTri = await getObjViTri(dataPmis);

    await checkDataThietBiPmis(dataPmis);
    const validData = dataPmis.filter(cotDien => !cotDien.error?.length);
    const invalidData = dataPmis.filter(cotDien => !!cotDien.error?.length);

    if (!validData?.length) {
      await logSyncPmisByCongTrinh('COT_DIEN', syncCode, null, { invalid_data: true });
      return invalidData;
    }

    function convertData(cotDien) {
      return {
        ma_cot_dien: cotDien.ID,
        vi_tri_id: objViTri[cotDien.MA_VITRI]?._id,
        ten_cot_dien: cotDien.TEN_CD,
        thu_tu: convertInt(cotDien.STT),
        tinh_trang_van_hanh: TINH_TRANG_VAN_HANH.VAN_HANH,
        hang_san_xuat: cotDien.HANG_SX,
        nha_cung_cap: cotDien.NHA_CCAP,
        nuoc_san_xuat: cotDien.NUOC_SXUAT,
        ngay_lap_dat: convertMoment(cotDien.NGAYLLDAT),
        nam_san_xuat: cotDien.NAM_SXUAT,
        ma_hieu_cot: cotDien.MA_HIEU,
        cong_dung_cot: cotDien.CONG_DUNG,
        chieu_cao: cotDien.CHIEU_CAO,
        trong_luong: cotDien.TRONG_LUONG,
        so_mach_day_dan: cotDien.SO_MACH,
        so_mach_dcs: cotDien.SO_MACH_DCS,
        do_rong_chan_cot: cotDien.DO_RONG,
        loai_bulong_neo_mong: cotDien.LOAI_BLONG,
        is_deleted: false,
      };
    }

    const dataSync = validData.map(convertData).filter(viTri => !!viTri.vi_tri_id);
    await CotDienModel.bulkWrite(
      cloneObj(dataSync).map((row) =>
        ({
          updateOne: {
            filter: { ma_cot_dien: row.ma_cot_dien },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
    return logSyncPmisByCongTrinh('COT_DIEN', syncCode, { success: true });
  } catch (e) {
    console.log('e', e);
    return logSyncPmisByCongTrinh('COT_DIEN', syncCode, null, e);
  }
}

export default {
  syncViTriByMaCongTrinh,
  syncGiaoCheoByMaCongTrinh,
  syncTiepDatPmis,
  syncVanHanhPmis,
  syncCotDienPmis,

};
