import express from 'express';
import passport from 'passport';
import * as Controller from './syncPmis.controller';
import { loggerMiddleware } from '../../../logs/middleware';
import { authorizationMiddleware } from '../../RBAC/middleware';
import TongKePermission from '../../RBAC/permissions/TongKePermission';

export const syncPmisRouter = express.Router();
syncPmisRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

syncPmisRouter
  .route('/')
  .get(Controller.getAll);

syncPmisRouter.route('/test').get(Controller.test);

syncPmisRouter
  .post(
    '/congtrinh',
    authorizationMiddleware([TongKePermission.CREATE]),
    Controller.syncCongTrinhByDonViPmis);

syncPmisRouter
  .post('/congtrinh/:id',
    authorizationMiddleware([TongKePermission.CREATE]),
    Controller.syncCongTrinhPmis);

syncPmisRouter
  .post(
    '/congtrinh/:id/thiet-bi',
    authorizationMiddleware([TongKePermission.CREATE]),
    Controller.syncAllData);

syncPmisRouter
  .post(
    '/duongday',
    authorizationMiddleware([TongKePermission.CREATE]),
    Controller.syncDuongDayPmis,
  );

syncPmisRouter
  .route('/vitri')
  .get(Controller.syncViTriPmis);

syncPmisRouter
  .route('/vitri/:id')
  .post(Controller.syncViTriPmisById);

syncPmisRouter
  .route('/toadovitri')
  .get(Controller.syncToaDoViTriPmis);

syncPmisRouter
  .route('/cotdien')
  .get(Controller.syncCotDienPmis);

syncPmisRouter
  .route('/tiepdat')
  .get(Controller.syncTiepDatPmis);

syncPmisRouter
  .route('/chongsetcapquang')
  .get(Controller.syncChongSetCapQuangPmis);

syncPmisRouter
  .route('/giaocheo')
  .get(Controller.syncGiaoCheoPmis);

syncPmisRouter
  .route('/vanhanh')
  .get(Controller.syncVanHanh);

syncPmisRouter
  .route('/daydan')
  .get(Controller.syncDayDan);

syncPmisRouter
  .route('/cachdien')
  .get(Controller.syncCachDien);

syncPmisRouter
  .route('/suco')
  .get(Controller.syncSuCoPmis);

syncPmisRouter
  .route('/thongsoduongday')
  .get(Controller.syncThongSoDuongDayPmis);

syncPmisRouter
  .route('/download')
  .post(Controller.downloadInvalidDataSync);

// pmisTongKeRouter
//   .route('/:id')
