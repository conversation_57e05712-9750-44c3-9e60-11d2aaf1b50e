import CommonError from '../../../error/CommonError';
import queryHelper from '../../../helpers/queryHelper';
import * as responseHelper from '../../../helpers/responseHelper';

import Error from '../../RBAC/Error';
import PMIS_SOAP_NAME from './soapName';
import { TEMPLATES_DIRS } from '../../../constant/constant';
import { downlineData } from '../../../common/functionCommons';

import { extractKeys } from '../../../utils/dataconverter';
import { getFilePath } from '../../../utils/fileUtils';
import { generateDocument } from '../../Report/GenerateFile/generate.controller';

import SYNC_PMIS from './syncPmis.model';

import * as CongTrinhService from '../CongTrinh/congTrinh.service';
import * as SyncPmisService from './syncPmis.service';
import * as CaiDatPmisService from '../../CaiDatPmis/caiDatPmis.service';
import * as ViTriService from '../../TongKe/ViTri/viTri.service';
import * as VanHanhService from '../VanHanh/vanHanh.service';

import SyncCachDienService from './SyncCachDien';
import SyncDayDanService from './SyncDayDan';
import SyncThietBiCongTrinhService from './SyncThietBiCongTrinh';
import { syncThietBiCongTrinh } from './syncPmis.service';


export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    options.sort = { created_at: -1 };
    if (!options.limit) options.limit = 10;
    const data = await SYNC_PMIS.paginate(criteria, options);

    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }

}

export async function syncAllData(req, res) {
  try {
    const caiDatPmis = await CaiDatPmisService.getConfig();
    if (!caiDatPmis.allow_pmis_sync || !caiDatPmis.allow_sync_construction_devices) {
      return responseHelper.error(res, Error.INSUFFICIENT_PERMISSION);
    }

    const { id } = req.params;
    const { options } = req.body;

    if (!options) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    const congTrinhCurrent = await CongTrinhService.getById(id);
    if (!congTrinhCurrent) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    const result = await SyncPmisService.syncThietBiCongTrinh(congTrinhCurrent.ma_cong_trinh, options);
    return responseHelper.success(res, result);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function syncDonVi(req, res) {
  try {
    SyncPmisService.syncDonVi();
    return responseHelper.success(res, { success: true });
  } catch (err) {
    return responseHelper.error(res, err);
  }

}

export async function syncSuCoPmis(req, res) {
  try {
    SyncPmisService.syncSuCoPmis();
    return responseHelper.success(res, { success: true });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function syncCongTrinhPmis(req, res) {
  const { t } = req;
  try {
    const caiDatPmis = await CaiDatPmisService.getConfig();
    if (!caiDatPmis.allow_pmis_sync || !caiDatPmis.allow_sync_construction_info) {
      return responseHelper.error(res, Error.INSUFFICIENT_PERMISSION);
    }


    const { id } = req.params;
    const syncResult = await SyncPmisService.syncCongTrinhByIdPmis(id);

    if (syncResult.error) {
      return responseHelper.error(res, { message: t(syncResult.error.message) }, 400);
    } else if (!syncResult.success) {
      return responseHelper.error(res, { message: t('have_unexpected_error') }, 400);
    }

    return responseHelper.success(res, syncResult.data);

  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function syncCongTrinhByDonViPmis(req, res) {
  const { t } = req;
  try {
    const caiDatPmis = await CaiDatPmisService.getConfig();
    if (!caiDatPmis.allow_pmis_sync || !caiDatPmis.allow_sync_construction_list) {
      return responseHelper.error(res, Error.INSUFFICIENT_PERMISSION);
    }

    const { don_vi_id } = req.body;

    if (!don_vi_id) {
      return responseHelper.error(res, { message: t('have_unexpected_error') }, 404);
    }

    const syncResult = await SyncPmisService.syncCongTrinhByDonViPmis(don_vi_id);
    if (syncResult.error) {
      return responseHelper.error(res, { message: t(syncResult.error.message) }, 400);
    }
    return responseHelper.success(res, syncResult.data);

  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function syncDuongDayPmis(req, res) {
  try {
    const { t } = req;
    const syncResult = await SyncPmisService.syncDuongDayPmis(req.user?.don_vi_id);
    if (syncResult.error) {
      return responseHelper.error(res, { message: t(syncResult.error.message) }, 400);
    }
    return responseHelper.success(res, { success: true, data: syncResult });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function syncViTriPmis(req, res) {
  try {
    const { t } = req;
    const { cong_trinh_id, thoi_gian } = req.query;
    const result = await SyncPmisService.syncViTriPmisByCongTrinh(t, [cong_trinh_id], ['khoang_cot'], false, thoi_gian);
    return responseHelper.success(res, { success: true, result });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function syncViTriPmisById(req, res) {
  try {
    const { t } = req;
    const { id } = req.params; // Mã vị trí
    const { sync_devices } = req.body;

    const caiDatPmis = await CaiDatPmisService.getConfig();
    if (!caiDatPmis.allow_pmis_sync || !caiDatPmis.allow_sync_location) {
      return responseHelper.error(res, Error.INSUFFICIENT_PERMISSION);
    }

    const viTriData = await ViTriService.getOne({ _id: id });

    if (!viTriData) return responseHelper.error(res, CommonError.NOT_FOUND);

    const maViTri = viTriData.ma_vi_tri;
    const syncViTriResult = await SyncPmisService.syncThongTinViTri(maViTri);

    if (syncViTriResult?.error) {
      await SYNC_PMIS.create({
        code: maViTri,
        type: 'VI_TRI',
        result: null,
        error: syncViTriResult.error,
      });

      const { server_not_responding, no_data_pmis } = syncViTriResult.error;
      const message = server_not_responding
        ? 'THE_PMIS_SERVER_IS_NOT_RESPONDING'
        : no_data_pmis
          ? 'NO_PMIS_DATA_AVAILABLE'
          : 'DATA_NOT_FOUND';

      return responseHelper.error(res, { message: t(message) }, 400);
    }

    const viTriSynced = await ViTriService.getOne({ _id: id })
      .populate({ path: 'cong_trinh_id', select: 'ma_cong_trinh' });

    const maCongTrinh = viTriSynced?.cong_trinh_id?.ma_cong_trinh;

    if (maCongTrinh) {
      const syncPromises = [];
      // Đồng bộ dữ liệu vị trí (Cột điện) từ PMIS về hệ thống
      if (sync_devices.includes('COT_DIEN')) {
        syncPromises.push(SyncThietBiCongTrinhService.syncCotDienPmis(maCongTrinh, '', maViTri));
      }

      // Đồng bộ dữ liệu vị trí (Chống sét) từ PMIS về hệ thống
      if (sync_devices.includes('CHONG_SET')) {
        syncPromises.push(SyncPmisService.syncChongSetPmis(maCongTrinh, '', maViTri));
      }

      // Đồng bộ dữ liệu vị trí (Giao chéo) từ PMIS về hệ thống
      if (sync_devices.includes('GIAO_CHEO')) {
        syncPromises.push(SyncThietBiCongTrinhService.syncGiaoCheoByMaCongTrinh(maCongTrinh, '', maViTri));
      }

      // Đồng bộ dữ liệu vị trí (Tiếp đất) từ PMIS về hệ thống
      if (sync_devices.includes('TIEP_DAT')) {
        syncPromises.push(SyncThietBiCongTrinhService.syncTiepDatPmis(maCongTrinh, '', maViTri));
      }

      // Đồng bộ dữ liệu vị trí (Vận hành) từ PMIS về hệ thống
      if (sync_devices.includes('VAN_HANH')) {
        syncPromises.push(SyncThietBiCongTrinhService.syncVanHanhPmis(maCongTrinh, '', maViTri));
      }
      await Promise.all(syncPromises);

      if (sync_devices.includes('VAN_HANH')) {

        viTriData.vanHanhData = await VanHanhService.getAll({ vi_tri_id: viTriData._id })
          .populate({
            path: 'vi_tri_id', select: 'cong_trinh_id',
            populate: { path: 'cong_trinh_id', select: 'ma_cong_trinh' },
          });

        await Promise.all([
          SyncCachDienService.syncCachDienByViTri(viTriData),
          SyncDayDanService.syncDayDanByViTri(viTriData),
        ]);
      }
    }

    return responseHelper.success(res, { success: true });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function syncToaDoViTriPmis(req, res) {
  try {
    const { t } = req;
    const { cong_trinh_id, thoi_gian } = req.query;
    const result = await SyncPmisService.syncToaDoViTriPmisByCongTrinh(t, [cong_trinh_id], ['kinh_do', 'vi_do'], false, thoi_gian);
    return responseHelper.success(res, { success: true, result });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function syncCotDienPmis(req, res) {
  try {
    SyncThietBiCongTrinhService.syncCotDienPmis();
    return responseHelper.success(res, { success: true });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function syncChongSetCapQuangPmis(req, res) {
  try {
    SyncPmisService.syncChongSetCapQuangPmis();
    return responseHelper.success(res, { success: true });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function syncTiepDatPmis(req, res) {
  try {
    SyncThietBiCongTrinhService.syncTiepDatPmis();
    return responseHelper.success(res, { success: true });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function syncGiaoCheoPmis(req, res) {
  try {
    SyncThietBiCongTrinhService.syncGiaoCheoByMaCongTrinh();
    return responseHelper.success(res, { success: true });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function syncVanHanh(req, res) {
  try {
    SyncThietBiCongTrinhService.syncVanHanhPmis();
    return responseHelper.success(res, { success: true });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function syncDayDan(req, res) {
  try {
    SyncDayDanService.syncDayDanByCongTrinh();
    return responseHelper.success(res, { success: true });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function syncCachDien(req, res) {
  try {
    SyncCachDienService.syncCachDienByCongTrinh();
    return responseHelper.success(res, { success: true });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function syncThongSoDuongDayPmis(req, res) {
  try {
    SyncPmisService.syncThongSoDuongDayPmis();
    return responseHelper.success(res, { success: true });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function downloadInvalidDataSync(req, res) {
  try {
    const dataInvalid = req.body;

    function convertData(row, index) {
      let noiDungLoi = '';
      row.error.forEach(err => {
        let strErr = `${err.col} - ${err.error}`;
        !noiDungLoi ? noiDungLoi = strErr : noiDungLoi = downlineData(noiDungLoi, strErr);
      });
      return {
        stt: index + 1,
        ma_vi_tri: row.ma_vi_tri,
        ma_thiet_bi_cha: row.ma_thiet_bi_cha,
        noi_dung_loi: noiDungLoi,
      };
    }

    const templateFilePath = getFilePath('invalid_data_sync_pmis.xlsx', TEMPLATES_DIRS.REPORT);
    await generateDocument(res, dataInvalid.map(convertData), templateFilePath);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function test(req, res) {
  try {
    const { congTrinhId, maCongTrinh } = req.body;

    const congTrinhData = CongTrinhService.getOne({ _id: congTrinhId });


    const query = { ID: maCongTrinh, TU_NGAY: '01/01/2000' };
    const vanHanhPmis = await SyncPmisService.getDataPmis(PMIS_SOAP_NAME.VAN_HANH_BY_CONG_TRINH_ID, query, 5000);
    const dayDanPmis = await SyncPmisService.getDataPmis(PMIS_SOAP_NAME.DAY_DAN_BY_CONG_TRINH_ID, query, 5000);
    const cachDienPmis = await SyncPmisService.getDataPmis(PMIS_SOAP_NAME.CACH_DIEN_BY_CONG_TRINH_ID, query, 5000);

    const maVanHanhList = extractKeys(vanHanhPmis, 'ID');

    const vanHanhObj = {};

    dayDanPmis?.forEach(dayDan => {
      vanHanhObj[dayDan.MA_VH] ||= {};
      vanHanhObj[dayDan.MA_VH].dayDan ||= 0;
      vanHanhObj[dayDan.MA_VH].dayDan++;
    });

    cachDienPmis?.forEach(cachDien => {
      vanHanhObj[cachDien.MA_VH] ||= {};
      vanHanhObj[cachDien.MA_VH].cachDien ||= 0;
      vanHanhObj[cachDien.MA_VH].cachDien++;
    });

    return responseHelper.success(res, {
      success: true,
      maVanHanhList,
      vanHanhObj,
      vanHanhPmis,
      dayDanPmis,
      cachDienPmis,
    });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}
