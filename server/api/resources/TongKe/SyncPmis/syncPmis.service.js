import mongoose from 'mongoose';
import moment from 'moment';
import momentTimezone from 'moment-timezone';

import PMIS_SOAP_NAME from './soapName';
import { CAP_DON_VI } from '../../../constant/constant';
import { convertMoment, momentValid } from '../../../helpers/checkDataHelper';
import {
  cleanEscapedJsonString,
  cloneObj,
  convertParam,
  formatUnique,
  getPastDateFromToday,
} from '../../../common/functionCommons';
import { convertInt, convertObject, extractIds, extractKeys, formatArrayUnique } from '../../../utils/dataconverter';

import { TINH_TRANG_VAN_HANH } from '../../DanhMuc/TinhTrangVanHanh';

import SYNC_PMIS from './syncPmis.model';
import DON_VI from '../../DonVi/donVi.model';
import SU_CO_DUONG_DAY, { DOI_TUONG_SU_CO } from '../SuCoDuongDay/suCoDuongDay.model';
import DUONG_DAY from '../DuongDay/duongDay.model';
import CONG_TRINH from '../CongTrinh/congTrinh.model';
import VI_TRI from '../ViTri/viTri.model';
import COT_DIEN from '../CotDien/cotDien.model';
import TIEP_DAT from '../TiepDat/tiepDat.model';
import GIAO_CHEO from '../GiaoCheo/giaoCheo.model';
import VAN_HANH from '../VanHanh/vanHanh.model';
import CHONG_SET from '../DayChongSet/dayChongSet.model';

import * as requestHelper from '../../../helpers/requestHelper';
import * as DonViService from '../../DonVi/donVi.service';
import * as LoaiDuongDayService from '../../DanhMuc/LoaiDuongDay/loaiDuongDay.service';
import * as CongTrinhService from '../CongTrinh/congTrinh.service';
import * as DuongDayService from '../DuongDay/duongDay.service';
import * as KhoangCotService from '../KhoangCot/khoangCot.service';
import * as ViTriService from '../ViTri/viTri.service';
import * as VanHanhService from '../VanHanh/vanHanh.service';
import * as TraoLuuCongSuatService from '../../QuanLyVanHanh/TraoLuuCongSuat/traoLuuCongSuat.service';
import * as CaiDatPmisService from '../../CaiDatPmis/caiDatPmis.service';

import SyncCachDienService from './SyncCachDien';
import SyncDayDanService from './SyncDayDan';
import SyncThietBiCongTrinhService from './SyncThietBiCongTrinh';

const queryStartTime = '01/01/2000';


export async function getDataPmis(soapName, query, timeout) {
  const caiDatPmis = await CaiDatPmisService.getOne();
  if (!caiDatPmis?.pmis_service || !caiDatPmis?.pmis_pdkey) return null;

  const pmisService = caiDatPmis.pmis_service;
  const pmisPdkey = caiDatPmis.pmis_pdkey;

  const api = pmisService + `?SOAP_NAME=${soapName}&PDKEY=${pmisPdkey}`;
  const param = convertParam(query, '&');

  const reachable = await requestHelper.isServerReachable(api + param);
  if (!reachable) {
    return { success: false, server_not_responding: true, message: 'THE_PMIS_SERVER_IS_NOT_RESPONDING' };
  }

  const data = await requestHelper.get(api + param, null, timeout);
  if (!data?.[0]?.lst?.length) {
    return { success: false, no_data_pmis: true, message: 'NO_PMIS_DATA_AVAILABLE' };
  }
  return { success: true, data: data[0].lst };
}

export async function getObjViTri(dataPmis, key = 'MA_VITRI') {
  const allViTri = await ViTriService.getAll(
    { ma_vi_tri: extractKeys(dataPmis, key), is_deleted: false },
    { ma_vi_tri: 1 },
  );
  return convertObject(allViTri, 'ma_vi_tri');
}

export async function getObjDonVi(dataPmis, donViId) {
  const query = {
    ma_don_vi: extractKeys(dataPmis, 'MA_DVI_QLY'),
    is_deleted: false,
  };
  if (donViId) {
    query._id = await DonViService.getDonViInScope(donViId);
  }

  const allDonVi = await DonViService.getAll(query, { ma_don_vi: 1 });
  return convertObject(allDonVi, 'ma_don_vi');
}

export async function getObjVanHanh(dataPmis, key = 'MA_VH') {
  const maVanHanh = formatUnique(extractKeys(dataPmis, key));
  const allVanHanh = await VanHanhService.getAll(
    { ma_van_hanh: maVanHanh, is_deleted: false },
    { ma_van_hanh: 1, duong_day_id: 1, vi_tri_id: 1 },
  );
  return convertObject(allVanHanh, 'ma_van_hanh');
}

export async function getHashMapVanHanh(dataPmis, key = 'MA_VH') {
  const maVanHanh = formatUnique(extractKeys(dataPmis, key));
  const allVanHanh = await VanHanhService.getAll(
    { ma_van_hanh: maVanHanh },
    { ma_van_hanh: 1, duong_day_id: 1, vi_tri_id: 1 },
  );
  const mapMaVanHanh = {};
  allVanHanh.forEach(vanHanh => {
    mapMaVanHanh[vanHanh.ma_van_hanh] = vanHanh.duong_day_id + vanHanh.vi_tri_id;
  });
  return mapMaVanHanh;
}

export async function getObjDuongDay(dataPmis, key = 'MA_DZ') {
  const maDuongDay = formatUnique(extractKeys(dataPmis, key));
  const allDuongDay = await DuongDayService.getAll(
    { ma_duong_day: maDuongDay, is_deleted: false },
    { ma_duong_day: 1, ten_duong_day: 1 },
  );
  return convertObject(allDuongDay, 'ma_duong_day');
}

export async function checkDataThietBiPmis(dataPmis) {
  function createError(col, error) {
    return { col, error };
  }

  const allViTri = await ViTriService.getAll({ is_deleted: false }, { ma_vi_tri: 1 });
  const allVanHanh = await VanHanhService.getAll({ is_deleted: false }, { ma_van_hanh: 1 });
  const mapViTri = convertObject(allViTri, 'ma_vi_tri');
  const mapVanHanh = convertObject(allVanHanh, 'ma_van_hanh');

  function validateRow(row) {
    let errors = [];
    if (!!row.MA_VITRI && !mapViTri[row.MA_VITRI]) {
      errors = [...errors, createError('Vị trí', 'Mã vị trí không đúng hoặc chưa được tạo')];
    }
    if (!!row.MA_VH && !mapVanHanh[row.MA_VH]) {
      errors = [...errors, createError('Vận hành', 'Mã vận hành không đúng hoặc chưa được tạo')];
    }

    if (errors.length) {
      row['error'] = errors;
    } else {
      row['error'] = null;
    }
    return row;
  }

  return dataPmis.map(validateRow);
}


export async function syncThietBiCongTrinh(maCongTrinh, options) {
  try {
    const caiDatPmis = await CaiDatPmisService.getConfig();


    let dataViTri, dataTiepDat, dataGiaoCheo, dataVanHanh, dataDayDan, dataCachDien, dataCotDien;

    async function syncViTri() {
      const isAutoUpdateKhoangCot = caiDatPmis.tu_dong_cap_nhat_khoang_cot && options.includes('TAO_KHOANG_COT');
      dataViTri = await SyncThietBiCongTrinhService.syncViTriByMaCongTrinh(maCongTrinh, isAutoUpdateKhoangCot);
    }

    async function syncGiaoCheo() {
      dataGiaoCheo = await SyncThietBiCongTrinhService.syncGiaoCheoByMaCongTrinh(maCongTrinh);
    }

    async function syncTiepDat() {
      dataTiepDat = await SyncThietBiCongTrinhService.syncTiepDatPmis(maCongTrinh);
    }

    async function syncVanHanh() {
      dataVanHanh = await SyncThietBiCongTrinhService.syncVanHanhPmis(maCongTrinh);
    }

    async function syncDayDan() {
      dataDayDan = await SyncDayDanService.syncDayDanByCongTrinh(maCongTrinh);
    }

    async function syncCachDien() {
      dataCachDien = await SyncCachDienService.syncCachDienByCongTrinh(maCongTrinh);
    }

    async function syncCotDien() {
      dataCotDien = await SyncThietBiCongTrinhService.syncCotDienPmis(maCongTrinh);
    }

    if (options.includes('VI_TRI')) {
      await syncViTri();
    }

    const syncThietBiPromises = [];
    if (options.includes('COT_DIEN')) syncThietBiPromises.push(syncCotDien());
    if (options.includes('GIAO_CHEO')) syncThietBiPromises.push(syncGiaoCheo());
    if (options.includes('TIEP_DAT')) syncThietBiPromises.push(syncTiepDat());
    if (options.includes('VAN_HANH')) syncThietBiPromises.push(syncVanHanh());

    await Promise.all(syncThietBiPromises);

    if (options.includes('VAN_HANH')) {
      await Promise.all([syncDayDan(), syncCachDien()]);
    }
    return [
      dataViTri,
      dataTiepDat,
      dataGiaoCheo,
      dataVanHanh,
      dataCachDien,
      dataCotDien,
      dataDayDan,
    ].filter(Boolean);
  } catch (e) {
    console.log('e', e);
  }
}

export async function syncDonVi() {
  try {
    const apiResponse = await getDataPmis(PMIS_SOAP_NAME.DANH_SACH_DON_VI);
    if (!apiResponse.success) return logSyncPmis('DON_VI', null, apiResponse);

    const dataPmis = apiResponse.data;
    const allDonVi = await DonViService.getAll({ is_deleted: false });
    const objDonVi = convertObject(allDonVi, 'ma_don_vi');

    dataPmis.forEach(donVi => {
      donVi.ma_don_vi = donVi.MA_DONVI;
      donVi.ten_don_vi = donVi.TEN_DONVI;
      donVi.cap_don_vi = Object.values(CAP_DON_VI)[donVi.CAP_DONVI - 1];
      donVi.thu_tu = donVi.SAPXEP;
      if (objDonVi[donVi.MA_DONVI]?._id) {
        donVi._id = objDonVi[donVi.MA_DONVI]._id;
      } else {
        donVi._id = new mongoose.Types.ObjectId();
        donVi.ma_in = donVi.MA_DONVI;
        donVi.is_root = donVi.cap_don_vi === CAP_DON_VI.TONG_CONG_TY;
        donVi.is_deleted = false;
      }

      delete donVi.MA_DONVI;
      delete donVi.TEN_DONVI;
      delete donVi.CAP_DONVI;
      delete donVi.SAPXEP;
    });

    const objDataPmis = convertObject(dataPmis, 'ma_don_vi');

    dataPmis.forEach(donVi => {
      donVi.don_vi_cha_id = objDataPmis[donVi.MA_DONVI_CHA]?._id;
      delete donVi.MA_DONVI_CHA;
    });

    const bulkWriteResult = await DON_VI.bulkWrite(
      dataPmis.map((row) =>
        ({
          updateOne: {
            filter: { _id: row._id },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
    return await logSyncPmis('DON_VI', bulkWriteResult.result);
  } catch (e) {
    console.log('e', e);
    return await logSyncPmis('DON_VI', null, e);
  }
}

export async function syncSuCoPmis(thoiGian = queryStartTime) {
  try {
    const query = {
      THOI_GIAN: thoiGian,
      TRANG_THAI: '', //CLOSED; NEW; EXIST; INPRG
      TRANG_SO: 1,
      SO_DONG: 0,
    };
    const apiResponse = await getDataPmis(PMIS_SOAP_NAME.SU_CO_DUONG_DAY, query);
    if (!apiResponse.success) return logSyncPmis('SU_CO_DUONG_DAY', null, apiResponse);

    const suCoPmis = apiResponse.data;
    const objDuongDay = await getObjDuongDay(suCoPmis);
    const dataSuCo = suCoPmis.map(suCo => {
      const daXyLy = momentValid(suCo.TGIAN_SUA);
      return {
        pmis_id: suCo.ID,
        phan_mem_phat_hien: 'PMIS',
        doi_tuong: DOI_TUONG_SU_CO.TAI_SAN_DON_VI,
        nguyen_nhan: cleanEscapedJsonString(suCo.NGUYEN_NHAN),
        dien_bien: cleanEscapedJsonString(suCo.DIEN_BIEN),
        mo_ta: cleanEscapedJsonString(suCo.DANG_SC_CTIET),
        bien_phap_khac_phuc: cleanEscapedJsonString(suCo.BPHAP_KPHUC),
        thoi_gian_phat_hien: convertMoment(suCo.TDIEM_SCO),
        thoi_gian_xu_ly: convertMoment(suCo.TGIAN_SUA),
        duong_day_id: objDuongDay?.[suCo.MA_DZ]?._id,
        xac_nhan: daXyLy,
        da_xu_ly: daXyLy,
        is_deleted: false,
      };
    }).filter(suCo => !!suCo.duong_day_id);

    const bulkWriteResult = await SU_CO_DUONG_DAY.bulkWrite(
      dataSuCo.map((row) =>
        ({
          updateOne: {
            filter: { pmis_id: row.pmis_id },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
    return await logSyncPmis('SU_CO_DUONG_DAY', bulkWriteResult.result);
  } catch (e) {
    console.log('e', e);
    return await logSyncPmis('SU_CO_DUONG_DAY', null, e);
  }
}

export async function syncThongSoDuongDayPmis() {
  try {
    const allDuongDay = await DuongDayService.getAll({ is_deleted: false }, { ma_duong_day: 1, asset_id: 1 });
    const timeNow = moment().set({ minute: 0, second: 0 });
    const startTime = moment(timeNow).subtract(1, 'minute').format('MM/DD/YYYY HH:mm:ss');
    const endTime = moment(timeNow).add(1, 'minute').format('MM/DD/YYYY HH:mm:ss');

    const promises = allDuongDay.map(duongDay => {
      const query = { ID: duongDay.asset_id, TU_NGAY: startTime, DEN_NGAY: endTime };
      return getDataPmis(PMIS_SOAP_NAME.THONG_SO_BY_DUONG_DAY_ID, query, 10000);
    });

    const dataPmis = await Promise.all(promises).then(results => {
      results = results.filter(result => !!result?.length);
      return results.map(result => {
        return result.reduce((prevValue, currentValue) => {
          if (currentValue) {
            prevValue.asset_id = currentValue.ASSETID;
            prevValue.thoi_diem_thong_so = prevValue.thoi_diem_thong_so || currentValue.THOI_GIAN;
            prevValue.thoi_gian_dong_bo = momentTimezone().tz('Etc/GMT-7');
            prevValue[currentValue.THONG_SO?.toLowerCase()] = currentValue.GIA_TRI;
          }
          return prevValue;
        }, null);
      });
    });
    if (!dataPmis.length) {
      await DUONG_DAY.bulkWrite(
        allDuongDay.map((row) =>
          ({
            updateOne: {
              filter: { _id: row?._id },
              update: { $set: { thoi_gian_dong_bo: momentTimezone().tz('Etc/GMT-7') } },
              upsert: false,
            },
          }),
        ),
      );
      await TraoLuuCongSuatService.updateTraoLuuCongSuat();
    } else {
      await DUONG_DAY.bulkWrite(
        cloneObj(dataPmis).map((row) =>
          ({
            updateOne: {
              filter: { asset_id: row.asset_id },
              update: { $set: row },
              upsert: false,
            },
          }),
        ),
      );

      await TraoLuuCongSuatService.updateTraoLuuCongSuat();
    }

  } catch (e) {
    console.log('e', e);
    return await logSyncPmis('DUONG_DAY', null, e);
  }
}

export async function syncCongTrinhByIdPmis(congTrinhId, thoiGian = queryStartTime) {
  try {
    const congTrinhData = await CongTrinhService.getOne({ _id: congTrinhId, is_deleted: false });
    if (!congTrinhData) {
      return logSyncPmis('CONG_TRINH', null, { message: 'Không tồn tại công trình' });
    }

    const query = { TU_NGAY: thoiGian };
    const apiResponse = await getDataPmis(PMIS_SOAP_NAME.DANH_SACH_CONG_TRINH, query);
    if (!apiResponse.success) return logSyncPmis('CONG_TRINH', null, apiResponse);

    const pmisData = apiResponse.data;
    const congTrinhPmis = pmisData.find(a => a.MA_CTRINH === congTrinhData.ma_cong_trinh);
    if (!congTrinhPmis) {
      return logSyncPmis('CONG_TRINH', null,
        { success: false, no_data_pmis: true, message: 'NO_PMIS_DATA_AVAILABLE' });
    }

    const congTrinhCha = await CongTrinhService.getOne({
      ma_cong_trinh: congTrinhPmis.MA_CTRINH_CHA,
      is_deleted: false,
    });

    const donViData = await DonViService.getOne({ ma_don_vi: congTrinhPmis.MA_DVI_QLY, is_deleted: false });


    const dataUpdate = {
      ten_cong_trinh: cleanEscapedJsonString(congTrinhPmis.TEN_CTRINH),
      thu_tu: convertInt(congTrinhPmis.STT),
      ngay_van_hanh: congTrinhPmis.NGAY_VAN_HANH,
      tinh_trang_van_hanh: congTrinhPmis.TRANG_THAI,
      nam_san_xuat: congTrinhPmis.NAM_SAN_XUAT,
      don_vi_id: donViData?._id,
      nam_xay_dung: congTrinhPmis.NAM_XDUNG,
      ngay_dong_dien: congTrinhPmis.NGAY_DONG_DIEN,
      don_vi_quan_ly_du_an: congTrinhPmis.DVI_QLY_DUAN,
      don_vi_quan_ly_van_hanh: congTrinhPmis.DVI_VAN_HANH,
      ngay_ban_giao_tai_san: convertMoment(congTrinhPmis.NGAY_BGIAO),
      nhom_cong_trinh_du_an: congTrinhPmis.NHOM_CTRINH,
      trang_thai_ban_giao: !!momentValid(congTrinhPmis.NGAY_BGIAO),
      cong_trinh_chinh_id: congTrinhCha?._id,
      dong_bo_lan_cuoi: new Date(),
      is_deleted: false,
    };

    const dataUpdated = await CongTrinhService.findOneAndUpdate({ _id: congTrinhData._id }, dataUpdate);
    if (dataUpdated) {
      return { success: true, data: dataUpdated };
    } else {
      return { success: false };
    }
  } catch (e) {
    await logSyncPmis('CONG_TRINH', null, e);
    return { success: false };
  }
}

export async function syncCongTrinhByDonViPmis(donViId, thoiGian = queryStartTime) {
  try {
    const query = { TU_NGAY: thoiGian };
    const apiResponse = await getDataPmis(PMIS_SOAP_NAME.DANH_SACH_CONG_TRINH, query);
    if (!apiResponse.success) return logSyncPmis('DANH_SACH_CONG_TRINH', null, apiResponse);

    const donViData = await DonViService.getOne({ _id: donViId, is_deleted: false }, { ma_don_vi: 1 });
    if (!donViData) return { success: false, message: 'Không có đơn vị' };

    const dataPmis = formatArrayUnique(apiResponse.data, 'MA_CTRINH')
      ?.filter(congTrinhPmis => congTrinhPmis.MA_DVI_QLY === donViData.ma_don_vi);

    const allCongTrinh = await CongTrinhService.getAll();

    const objCongTrinh = convertObject(allCongTrinh, 'ma_cong_trinh');
    const objDonVi = await getObjDonVi(dataPmis);

    const dataSync = dataPmis.map(congTrinh => {
      return {
        _id: objCongTrinh[congTrinh.MA_CTRINH]?._id || new mongoose.Types.ObjectId(),
        ma_cong_trinh: congTrinh.MA_CTRINH,
        ten_cong_trinh: cleanEscapedJsonString(congTrinh.TEN_CTRINH),
        thu_tu: convertInt(congTrinh.STT),
        ngay_van_hanh: congTrinh.NGAY_VAN_HANH,
        tinh_trang_van_hanh: congTrinh.TRANG_THAI,
        nam_san_xuat: congTrinh.NAM_SAN_XUAT,
        ghi_chu: '',
        don_vi_id: objDonVi[congTrinh.MA_DVI_QLY]?._id,
        nam_xay_dung: congTrinh.NAM_XDUNG,
        ngay_dong_dien: congTrinh.NGAY_DONG_DIEN,
        don_vi_quan_ly_du_an: congTrinh.DVI_QLY_DUAN,
        don_vi_quan_ly_van_hanh: congTrinh.DVI_VAN_HANH,
        ngay_ban_giao_tai_san: convertMoment(congTrinh.NGAY_BGIAO),
        nhom_cong_trinh_du_an: congTrinh.NHOM_CTRINH,
        trang_thai_ban_giao: !!momentValid(congTrinh.NGAY_BGIAO),
        is_deleted: false,
        dong_bo_lan_cuoi: new Date(),
        MA_CTRINH_CHA: congTrinh.MA_CTRINH_CHA,
      };
    }).filter(congTrinh => !!congTrinh.don_vi_id);

    const objDataSync = dataSync.reduce(function(prevValue, currentValue) {
      prevValue[currentValue.ma_cong_trinh] = currentValue;
      return prevValue;
    }, {});

    dataSync.forEach(congTrinh => {
      congTrinh.cong_trinh_chinh_id = objDataSync[congTrinh.MA_CTRINH_CHA]?._id;
      delete congTrinh.MA_CTRINH_CHA;
    });

    const bulkWriteResult = await CONG_TRINH.bulkWrite(
      cloneObj(dataSync).map((row) =>
        ({
          updateOne: {
            filter: { _id: row._id },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
    let dataUpdated = [];
    if (!!bulkWriteResult?.result?.ok) {
      dataUpdated = await CongTrinhService.getAll({ _id: extractIds(dataSync) });
    }

    await logSyncPmis('CONG_TRINH', bulkWriteResult.result);
    return { success: true, data: dataUpdated };
  } catch (e) {
    console.log('e', e);
    await logSyncPmis('CONG_TRINH', null, e);
    return { success: false };
  }
}

export async function syncDuongDayPmis(donViId = null, thoiGian = queryStartTime) {
  function formatTenLoai(str) {
    return str?.replace(/ /g, '')?.toLowerCase();
  }

  try {
    const query = { TU_NGAY: thoiGian };
    const apiResponse = await getDataPmis(PMIS_SOAP_NAME.DANH_SACH_DUONG_DAY, query);
    if (!apiResponse.success) return logSyncPmis('DUONG_DAY', null, apiResponse);

    const dataPmis = formatArrayUnique(apiResponse.data, 'MA_DZ');

    const objDonVi = await getObjDonVi(dataPmis, donViId);
    const objDuongDay = await getObjDuongDay(dataPmis);

    const allLoaiDuongDay = await LoaiDuongDayService.getAll({ is_deleted: false }, { ten_loai: 1 });
    allLoaiDuongDay.forEach(loaiDuongDay => {
      loaiDuongDay.code = formatTenLoai(loaiDuongDay.ten_loai);
    });
    const objLoaiDuongDay = convertObject(allLoaiDuongDay, 'code');

    const dataSync = dataPmis.map(duongDay => {
      return {
        _id: objDuongDay[duongDay.MA_DZ]?._id || new mongoose.Types.ObjectId(),
        don_vi_id: objDonVi[duongDay.MA_DVI_QLY]?._id,
        i_dinh_muc: duongDay.I_MINI_CPHEP,
        ten_duong_day: cleanEscapedJsonString(duongDay.TEN_DZ),
        ma_duong_day: duongDay.MA_DZ,
        loai_duong_day_id: objLoaiDuongDay[formatTenLoai(duongDay.ULEVELDESC)]?._id,
        chieu_dai: duongDay.TONG_CDAI,
        thu_tu: convertInt(duongDay.STT),
        ghi_chu: duongDay.TTIN_KHAC,
        is_deleted: false,
        MA_CTRINH_CHA: duongDay.MA_CTRINH_CHA,
      };
    }).filter(duongDay => !!duongDay.don_vi_id);

    const objDataSync = dataSync.reduce(function(prevValue, currentValue) {
      prevValue[currentValue.ma_duong_day] = currentValue;
      return prevValue;
    }, {});

    dataSync.forEach(duongDay => {
      duongDay.duong_day_chinh_id = objDataSync[duongDay.MA_CTRINH_CHA]?._id;
      delete duongDay.MA_CTRINH_CHA;
    });

    const bulkWriteResult = await DUONG_DAY.bulkWrite(
      cloneObj(dataSync).map((row) =>
        ({
          updateOne: {
            filter: { _id: row._id },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
    return await logSyncPmis('DUONG_DAY', bulkWriteResult.result);
  } catch (e) {
    return await logSyncPmis('DUONG_DAY', null, e);
  }
}


export async function syncViTriPmisByCongTrinh(t, congTrinhIds, dataFields = [], upsert = false, thoiGian = queryStartTime) {
  try {
    const allCongTrinh = await CongTrinhService.getAll({ is_deleted: false, _id: { $in: congTrinhIds } }, {
      _id: 1,
      ma_cong_trinh: 1,
    });
    const objCongTrinh = convertObject(allCongTrinh, 'ma_cong_trinh');
    const promises = allCongTrinh.map(congTrinh => {
      const query = { ID: congTrinh.ma_cong_trinh, TU_NGAY: thoiGian };
      return getDataPmis(PMIS_SOAP_NAME.VI_TRI_BY_CONG_TRINH_ID, query);
    });
    const dataPmis = await Promise.all(promises).then(results => {
      results = results.filter(result => !!result?.length);
      return formatArrayUnique(results.flat(), 'ID');
    });
    if (!dataPmis?.length) {
      return logSyncPmis('VI_TRI', null,
        { success: false, no_data_pmis: true, message: 'NO_PMIS_DATA_AVAILABLE' });
    }

    const dataSync = dataPmis.map((viTri, index) => {
      const fullData = {
        ma_vi_tri: viTri.ID,
        ma_thiet_bi_cha: viTri.MA_CHA,
        cong_trinh_id: objCongTrinh[viTri.MA_CHA]?._id,
        ten_vi_tri: cleanEscapedJsonString(viTri.TEN_VITRI),
        thu_tu: convertInt(viTri.STT),
        ngay_van_hanh: convertMoment(viTri.NGAY_VHANH),
        tinh_trang_van_hanh: viTri.TINH_TRANG,
        so_huu: '',
        // don_vi_id: objDonVi[viTri.MA_DVI_QLY]?._id,
        goc_lai: viTri.GOC_LAI,
        khoang_cot: viTri.KHOANG_COT,
        khoang_neo: viTri.KHOANG_NEO,
        khu_vuc: viTri.KHU_VUC,
        tinh: viTri.TINH,
        huyen: viTri.HUYEN,
        xa: viTri.XA,
        so_do_bo_tri_day: cleanEscapedJsonString(viTri.SO_DO_BTRI_DAY),
        kinh_do: viTri.Y,
        vi_do: viTri.X,
        is_deleted: false,
      };

      const selectedData = {
        ma_vi_tri: viTri.ID,
        ma_thiet_bi_cha: viTri.MA_CHA,
      };
      dataFields.forEach(key => {
        selectedData[key] = fullData[key];
      });

      return selectedData;
    });
    const validData = dataSync.filter(item => Number.isFinite(item.khoang_cot) || Number.isFinite(item.khoang_cot));
    const invalidData = dataSync.filter(item => !Number.isFinite(item.khoang_cot) || !Number.isFinite(item.khoang_cot));

    const bulkWriteResult = await VI_TRI.bulkWrite(
      cloneObj(validData).map((row) =>
        ({
          updateOne: {
            filter: { ma_vi_tri: row.ma_vi_tri },
            update: { $set: row },
            upsert: upsert,
          },
        }),
      ),
    );

    await logSyncPmis('VI_TRI', bulkWriteResult.result);
    const khoangCot = await KhoangCotService.createOrUpdateKhoangCot(congTrinhIds);
    const dataRes = await logSyncPmis('KHOANG_COT', khoangCot.result);
    if (invalidData.length > 0) {
      return checkErrorDataSyncKhoangCot(t, invalidData);
    }
    return dataRes;

  } catch (e) {
    console.log('e', e);
    return await logSyncPmis('VI_TRI', null, e);
  }
}

export async function syncToaDoViTriPmisByCongTrinh(t, congTrinhIds, dataFields = [], upsert = false, thoiGian = queryStartTime) {
  try {
    const allCongTrinh = await CongTrinhService.getAll({ is_deleted: false, _id: { $in: congTrinhIds } }, {
      _id: 1,
      ma_cong_trinh: 1,
    });
    const objCongTrinh = convertObject(allCongTrinh, 'ma_cong_trinh');
    const promises = allCongTrinh.map(congTrinh => {
      const query = { ID: congTrinh.ma_cong_trinh, TU_NGAY: thoiGian };
      return getDataPmis(PMIS_SOAP_NAME.VI_TRI_BY_CONG_TRINH_ID, query);
    });
    const dataPmis = await Promise.all(promises).then(results => {
      results = results.filter(result => !!result?.length);
      return formatArrayUnique(results.flat(), 'ID');
    });

    if (!dataPmis?.length) {
      return logSyncPmis('VI_TRI', null,
        { success: false, no_data_pmis: true, message: 'NO_PMIS_DATA_AVAILABLE' });
    }

    const dataSync = dataPmis.map((viTri, index) => {
      const fullData = {
        ma_vi_tri: viTri.ID,
        ma_thiet_bi_cha: viTri.MA_CHA,
        cong_trinh_id: objCongTrinh[viTri.MA_CHA]?._id,
        ten_vi_tri: cleanEscapedJsonString(viTri.TEN_VITRI),
        thu_tu: convertInt(viTri.STT),
        ngay_van_hanh: convertMoment(viTri.NGAY_VHANH),
        tinh_trang_van_hanh: viTri.TINH_TRANG,
        so_huu: '',
        // don_vi_id: objDonVi[viTri.MA_DVI_QLY]?._id,
        goc_lai: viTri.GOC_LAI,
        khoang_cot: viTri.KHOANG_COT,
        khoang_neo: viTri.KHOANG_NEO,
        khu_vuc: viTri.KHU_VUC,
        tinh: viTri.TINH,
        huyen: viTri.HUYEN,
        xa: viTri.XA,
        so_do_bo_tri_day: cleanEscapedJsonString(viTri.SO_DO_BTRI_DAY),
        kinh_do: viTri.Y,
        vi_do: viTri.X,
        is_deleted: false,
      };

      const selectedData = {
        ma_vi_tri: viTri.ID,
        ma_thiet_bi_cha: viTri.MA_CHA,
      };
      dataFields.forEach(key => {
        selectedData[key] = fullData[key];
      });

      return selectedData;
    });
    const validData = dataSync.filter(item => Number.isFinite(item.kinh_do) || Number.isFinite(item.vi_do));
    const invalidData = dataSync.filter(item => !Number.isFinite(item.kinh_do) || !Number.isFinite(item.vi_do));

    const bulkWriteResult = await VI_TRI.bulkWrite(
      cloneObj(validData).map((row) =>
        ({
          updateOne: {
            filter: { ma_vi_tri: row.ma_vi_tri },
            update: { $set: row },
            upsert: upsert,
          },
        }),
      ),
    );
    const dataRes = await logSyncPmis('VI_TRI', bulkWriteResult.result);
    if (invalidData.length > 0) {
      return checkErrorDataSyncCoordinate(t, invalidData);
    }
    return dataRes;
  } catch (e) {
    console.log('e', e);
    return await logSyncPmis('VI_TRI', null, e);
  }
}

export function checkErrorDataSyncCoordinate(t, dataCheck) {
  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row) {
    let errors = [];
    if (!row.kinh_do?.trim()) {
      errors = [...errors, createError(t('KINH_DO'), t('MISSING_DATA'))];
    } else {
      if (!Number.isFinite(row.kinh_do)) {
        errors = [...errors, createError(t('KINH_DO'), t('IS_NOT_NUMBER'))];
      }
    }

    if (!row.vi_do?.trim()) {
      errors = [...errors, createError(t('VI_DO'), t('MISSING_DATA'))];
    } else {
      if (!Number.isFinite(row.vi_do)) {
        errors = [...errors, createError(t('VI_DO'), t('IS_NOT_NUMBER'))];
      }
    }

    if (errors.length) {
      row['error'] = errors;
    } else {
      row['error'] = null;
    }
    return row;
  }

  return dataCheck.map(row => validateRow(t, row));
}

export function checkErrorDataSyncKhoangCot(t, dataCheck) {
  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row) {
    let errors = [];
    if (!row.khoang_cot?.trim()) {
      errors = [...errors, createError(t('KHOANG_COT'), t('MISSING_DATA'))];
    } else {
      if (!Number.isFinite(row.kinh_do)) {
        errors = [...errors, createError(t('KHOANG_COT'), t('IS_NOT_NUMBER'))];
      }
    }

    if (errors.length) {
      row['error'] = errors;
    } else {
      row['error'] = null;
    }
    return row;
  }

  return dataCheck.map(row => validateRow(t, row));
}

export async function syncChongSetCapQuangPmis() {
  try {
    // todo:
    const allCongTrinh = await CongTrinhService.getAll({ is_deleted: false }, { _id: 1, ma_cong_trinh: 1 });
    const promises = allCongTrinh.map(congTrinh => {
      const query = { ID: congTrinh.ma_cong_trinh, TU_NGAY: '' };
      return getDataPmis(PMIS_SOAP_NAME.CHONG_SET_BY_CONG_TRINH_ID, query);
    });
    const dataPmis = await Promise.all(promises).then(results => {
      results = results.filter(result => !!result?.length);
      return formatArrayUnique(results.flat(), 'ID');
    });
    return dataPmis;
    if (!dataPmis?.length) return logSyncPmis('CHONG_SET', null, { no_data_pmis: true });

    const objViTri = await getObjViTri(dataPmis);
    const objDonVi = await getObjDonVi(dataPmis);

    const dataSync = dataPmis.map(cotDien => {
      return {
        ma_cot_dien: cotDien.ID,
        vi_tri_id: objViTri[cotDien.MA_VITRI]?._id,
        ten_cot_dien: cotDien.TEN_CD,
        thu_tu: convertInt(cotDien.STT),
        tinh_trang_van_hanh: TINH_TRANG_VAN_HANH.VAN_HANH,
        hang_san_xuat: cotDien.HANG_SX,
        nha_cung_cap: cotDien.NHA_CCAP,
        nuoc_san_xuat: cotDien.NUOC_SXUAT,
        ngay_lap_dat: convertMoment(cotDien.NGAYLLDAT),
        nam_san_xuat: cotDien.NAM_SXUAT,
        don_vi_id: objDonVi[cotDien.MA_DVI_QLY]?._id,
        ma_hieu_cot: cotDien.MA_HIEU,
        cong_dung_cot: cotDien.CONG_DUNG,
        chieu_cao: cotDien.CHIEU_CAO,
        trong_luong: cotDien.TRONG_LUONG,
        so_mach_day_dan: cotDien.SO_MACH,
        so_mach_dcs: cotDien.SO_MACH_DCS,
        do_rong_chan_cot: cotDien.DO_RONG,
        loai_bulong_neo_mong: cotDien.LOAI_BLONG,
        is_deleted: false,
      };
    }).filter(viTri => !!viTri.don_vi_id && !!viTri.vi_tri_id);

    const bulkWriteResult = await COT_DIEN.bulkWrite(
      cloneObj(dataSync).map((row) =>
        ({
          updateOne: {
            filter: { ma_cot_dien: row.ma_cot_dien },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
    return await logSyncPmis('CHONG_SET', bulkWriteResult.result);
  } catch (e) {
    console.log('e', e);
    return await logSyncPmis('CHONG_SET', null, e);
  }
}


export async function syncChongSetPmis(maCongTrinh, thoiGian = queryStartTime, maViTri) {
  try {
    const query = { ID: maCongTrinh, TU_NGAY: thoiGian || queryStartTime };
    let pmisResponse = await getDataPmis(PMIS_SOAP_NAME.CHONG_SET_BY_CONG_TRINH_ID, query);
    let dataPmis = formatArrayUnique(pmisResponse.data?.flat(), 'ID');
    if (maViTri) {
      dataPmis = dataPmis.filter(chongSet => chongSet.MA_CHA === maViTri);
    }
    if (!dataPmis?.length) return logSyncPmisByCongTrinh('CHONG_SET', maCongTrinh, null, { no_data_pmis: true });

    const objViTri = await getObjViTri(dataPmis, 'MA_CHA');

    await checkDataThietBiPmis(dataPmis);
    const validData = dataPmis.filter(chongSet => !chongSet.error?.length);
    const invalidData = dataPmis.filter(chongSet => !!chongSet.error?.length);

    if (!validData?.length) {
      await logSyncPmisByCongTrinh('CHONG_SET', maCongTrinh, null, { invalid_data: true });
      return invalidData;
    }

    const dataSync = validData.map(chongSet => {
      return {
        ma_day_chong_set: chongSet.ID,
        vi_tri_id: objViTri[chongSet.MA_CHA]?._id,
        ten_day_chong_set: chongSet.TEN_CS,
        thu_tu: convertInt(chongSet.STT),
        ngay_van_hanh: convertMoment(chongSet.NGAY_VHANH),
        tinh_trang_van_hanh: chongSet.TINH_TRANG,
        hang_san_xuat: chongSet.HANG_SX,
        nha_cung_cap: chongSet.NHA_CCAP,
        nuoc_san_xuat: chongSet.NUOC_SXUAT,
        ngay_lap_dat: convertMoment(chongSet.NGAY_LDAT),
        nam_san_xuat: chongSet.NAM_SXUAT,
        ngay_sua_doi: convertMoment(chongSet.NGAY_SDOI),
        ghi_chu: chongSet.GHI_CHU,
        // don_vi_id: objDonVi[chongSet.MA_DVI_QLY]?._id,
        tiet_dien: chongSet.TIET_DIEN,
        luc_keo_dut: chongSet.LUC_KEO,
        he_so_dan_dai: chongSet.HSO_DDAI,
        trong_luong_day: chongSet.TLUONG_DAY,
        dien_tro_mot_chieu: chongSet.DIEN_TRO,
        vi_tri_dat: chongSet.VTRI_LDAT,
        is_deleted: false,
      };
    }).filter(chongSet => !!chongSet.vi_tri_id);

    CHONG_SET.bulkWrite(
      cloneObj(dataSync).map((row) =>
        ({
          updateOne: {
            filter: { ma_day_chong_set: row.ma_day_chong_set },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
    await logSyncPmisByCongTrinh('CHONG_SET', maCongTrinh, { success: true });
    return dataSync;
  } catch (e) {
    console.log('e', e);
    return await logSyncPmis('CHONG_SET', null, e);
  }
}

export function logSyncPmis(type, result = null, error = null) {
  if (result?.upserted) {
    delete result.upserted;
  }
  return SYNC_PMIS.create({ type, result, error });
}

export function logSyncPmisByCongTrinh(type, code, result = null, error = null) {
  if (result?.upserted) {
    delete result.upserted;
  }
  return SYNC_PMIS.create({ code, type, result, error });
}

export async function deleteSyncPmisLog() {
  try {
    const twoWeekAgo = getPastDateFromToday(14);
    return await SYNC_PMIS.deleteMany({
      created_at: { $lt: twoWeekAgo },
      code: { $exists: false },
    });
  } catch (err) {
    return err;
  }
}

export async function syncThongTinViTri(maViTri, thoiGian = queryStartTime) {
  try {
    const query = { MA_VTRI: maViTri, TU_NGAY: thoiGian };
    const apiResponse = await getDataPmis(PMIS_SOAP_NAME.THONG_TIN_VI_TRI, query);
    if (!apiResponse.success) return logSyncPmisByCongTrinh('VI_TRI', maViTri, null, apiResponse);

    const dataPmis = apiResponse.data?.[0];
    if (!dataPmis) return logSyncPmisByCongTrinh('VI_TRI', maViTri, null, { no_data_pmis: true });

    const congTrinhData = await CongTrinhService.getOne({ ma_cong_trinh: dataPmis.ASSETID_PARENT });
    // const donViData = await DonViService.getOne({ ma_don_vi: dataPmis.orgid });

    const dataSync = {
      ma_thiet_bi_cha: dataPmis.ASSETID_PARENT,
      cong_trinh_id: congTrinhData?._id,
      ten_vi_tri: cleanEscapedJsonString(dataPmis.VI_TRI),
      thu_tu: convertInt(dataPmis.ASSETORD),
      ngay_van_hanh: convertMoment(dataPmis.NGAY_VAN_HANH),
      tinh_trang_van_hanh: dataPmis.USESTATUS_LAST_ID === 'USING'
        ? TINH_TRANG_VAN_HANH.VAN_HANH
        : dataPmis.USESTATUS_LAST_ID === 'RETIRE' ? TINH_TRANG_VAN_HANH.KHONG_VAN_HANH : '',
      // don_vi_id: donViData?._id, // không sync đơn vị vì đưa về truyền tải
      goc_lai: dataPmis.GOC_LAI,
      khoang_cot: dataPmis.KHOANG_COT,
      khoang_neo: dataPmis.KHOANG_NEO,
      khu_vuc: dataPmis.TEN_KHU_VUC,
      tinh: dataPmis.TINH,
      huyen: dataPmis.HUYEN,
      xa: dataPmis.XA,
      so_do_bo_tri_day: cleanEscapedJsonString(dataPmis.SO_DO_DAY),
      kinh_do: dataPmis.Y,
      vi_do: dataPmis.X,
      duong_vao: dataPmis.DUONG_VAO,
      hanh_lang_tuyen: dataPmis.HANH_LANG,
      so_huu: '',
      is_deleted: false,
    };

    const dataUpdated = await VI_TRI.findOneAndUpdate({ ma_vi_tri: maViTri }, dataSync);
    return await logSyncPmisByCongTrinh('VI_TRI', maViTri, dataUpdated);

  } catch (e) {
    return await logSyncPmisByCongTrinh('VI_TRI', maViTri, null, e);
  }
}
