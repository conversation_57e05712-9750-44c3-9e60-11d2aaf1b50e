import mongoose from 'mongoose';
import momentTimezone from 'moment-timezone';
import exelUtils from '../../../utils/exelUtils';
import { extractIds, extractKeys, groupBy } from '../../../utils/dataconverter';
import { createBadRequestError } from '../../../helpers/errorHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';

import * as Service from './viTri.service';
import * as CotDienService from '../CotDien/cotDien.service';
import * as KhoangCotService from '../KhoangCot/khoangCot.service';
import * as TiepDatService from '../TiepDat/tiepDat.service';
import * as DayChongSetService from '../DayChongSet/dayChongSet.service';
import * as DayCapQuangService from '../DayCapQuang/dayCapQuang.service';
import * as GiaoCheoService from '../GiaoCheo/giaoCheo.service';
import * as VanHanhService from '../VanHanh/vanHanh.service';
import * as TaiLieuService from '../../TaiLieu/taiLieu.service';
import * as DonViService from '../../DonVi/donVi.service';
import * as ViTriCongViecService from '../../QuanLyVanHanh/ViTriCongViec/viTriCongViec.service';
import * as DuongDayService from '../DuongDay/duongDay.service';
import * as DayDanService from '../DayDan/dayDan.service';

import Model from './viTri.model';
import KHOANG_COT from '../KhoangCot/khoangCot.model';
import VI_TRI_CONG_VIEC from '../../QuanLyVanHanh/ViTriCongViec/viTriCongViec.model';
import CAU_HINH_BAY from '../../CauHinhBay/cau_hinh_bay.model';

import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import { TINH_TRANG_VAN_HANH } from '../../DanhMuc/TinhTrangVanHanh';

import CommonError from '../../../error/CommonError';
import { getMenuConfig } from '../../../../config/configChung';
import { CAP_DON_VI } from '../../../constant/constant';
import { KIEM_TRA, LOAI_CONG_VIEC } from '../../DanhMuc/LoaiCongViec';

momentTimezone.tz.setDefault('Asia/Ho_Chi_Minh');

const populateOpts = [
  { path: 'don_vi_id', select: 'ten_don_vi ma_in', populate: { path: 'don_vi_cha_id', select: 'ten_don_vi ma_in' } },
  { path: 'cong_trinh_id', select: 'ten_cong_trinh' },
];


export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id)
      .populate({ path: 'don_vi_id', select: 'ten_don_vi' })
      .populate({ path: 'cong_trinh_id', select: 'ten_cong_trinh ma_cong_trinh' })
      .lean();
    data.cot_dien = await CotDienService.getOne({ vi_tri_id: data._id, is_deleted: false });
    data.khoang_cot_id = await KhoangCotService.getAll({ vi_tri_id: data._id, is_deleted: false });
    data.tiep_dat = await TiepDatService.getOne({ vi_tri_id: data._id, is_deleted: false });
    data.day_chong_set = await DayChongSetService.getAll({ vi_tri_id: data._id, is_deleted: false });
    data.day_cap_quang = await DayCapQuangService.getAll({ vi_tri_id: data._id, is_deleted: false });
    data.giao_cheo = await GiaoCheoService.getAll({ vi_tri_id: data._id, is_deleted: false });

    const vanHanh = await VanHanhService.findAllInOne({ vi_tri_id: data._id, is_deleted: false });
    data.van_hanh = vanHanh?.sort((a, b) => {
      const aHasSAI = a.ma_van_hanh.includes('SAI');
      const bHasSAI = b.ma_van_hanh.includes('SAI');

      // Ưu tiên không có "SAI"
      if (aHasSAI !== bHasSAI) return aHasSAI ? 1 : -1;

      // Cùng nhóm => sort theo created_at
      return new Date(b.created_at) - new Date(a.created_at);
    });


    data.tai_lieu = await TaiLieuService.getAll({ doi_tuong_id: data._id, is_deleted: false })
      .populate('hoso_id');
    data.khoang_cot_list = await KhoangCotService.getAll({ vi_tri_id: data._id, is_deleted: false })
      .populate(' vi_tri_ket_thuc_id vi_tri_bat_dau_id duong_day_id')
      .populate({ path: 'vi_tri_id', populate: 'don_vi_id cong_trinh_id' });

    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const khoangCotLienQuan = await KHOANG_COT.find({ vi_tri_id: id }).select('_id');
    const khoangCotIds = extractIds(khoangCotLienQuan);
    const phieuDaGiaoCount = await VI_TRI_CONG_VIEC.count({
      $or: [{ khoang_cot_id: khoangCotIds }, { vi_tri_id: id }],
      is_deleted: false,
    });
    if (phieuDaGiaoCount) {
      return responseHelper.error(res, createBadRequestError(t('error_delete_already_operated_location')));
    }
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    await KHOANG_COT.findOneAndUpdate({ vi_tri_id: id }, { is_deleted: true });
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

async function checkUnique(t, res, value) {
  const query = { is_deleted: false };
  if (value._id) {
    query._id = { $ne: value._id };
  }

  // const checkTenUnique = await Model.findOne({ ...query, ten_vi_tri: value.ten_vi_tri }, { _id: 1 });
  // if (checkTenUnique) {
  //   return responseHelper.error(res, { message: 'Tên vị trí đã tồn tại, vui lòng kiểm tra và thử lại' });
  // }

  const checkMaUnique = await Model.findOne({ ...query, ma_vi_tri: value.ma_vi_tri }, { _id: 1 });
  if (checkMaUnique) {
    responseHelper.error(res, { message: t('location_code_existed') });
    return false;
  } else {
    return true;
  }
}

export async function update(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    if (!await checkUnique(t, res, value)) {
      return;
    }

    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true })
      .populate({ path: 'don_vi_id', select: 'ten_don_vi' })
      .populate({ path: 'cong_trinh_id', select: 'ten_cong_trinh ma_cong_trinh' })
      .lean();

    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.log('err', err);
    return responseHelper.error(res, err);
  }
}

export async function importViTri(req, res) {
  try {
    let filePath = req.files.file.path;
    const sheetData = await exelUtils.transformFile(filePath);
    return responseHelper.success(res, sheetData);
  } catch (e) {
    console.log(e);
    return responseHelper.error(res, e);
  }
}

export async function create(req, res) {
  try {
    const { t } = req;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    if (!await checkUnique(t, res, value)) {
      return;
    }

    const data = await Model.create(value);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    if (req.query.ma_vi_tri) {
      req.query.ma_vi_tri = `"${req.query.ma_vi_tri}"`;
    }
    if (req.query.ten_vi_tri) {
      req.query.ten_vi_tri = `"${req.query.ten_vi_tri}"`;
    }
    const query = queryHelper.extractQueryParam(req, ['ma_vi_tri', 'ten_vi_tri']);
    const { criteria, options } = query;
    options.populate = populateOpts;
    if (!options.sort) {
      options.sort = { cong_trinh_id: 1, thu_tu: 1 };
    }
    criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);
    const data = await Model.paginate(criteria, options);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllViTriCapTruyenTaiDien(req, res) {
  try {

    ['ma_vi_tri', 'ten_vi_tri'].forEach(param => {
      if (req.query[param]) {
        req.query[param] = `"${req.query[param]}"`;
      }
    });

    const query = queryHelper.extractQueryParam(req, ['ma_vi_tri', 'ten_vi_tri']);
    const { criteria, options } = query;
    options.populate = populateOpts;
    options.sort = { cong_trinh_id: 1, thu_tu: 1 };

    const dontInScope = await DonViService.getArrayDonViQuery(req, criteria.don_vi_id);

    const donViCapTruyenTaiDien = await DonViService.getAll({
      cap_don_vi: CAP_DON_VI.TRUYEN_TAI_DIEN,
      is_deleted: false,
    });

    const donViIdsCapTruyenTaiDienSet = new Set(donViCapTruyenTaiDien.map(item => item._id.toString()));

    criteria.don_vi_id = dontInScope.filter(item => donViIdsCapTruyenTaiDienSet.has(item.toString()));

    const data = await Model.paginate(criteria, options);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function diChuyenViTriSangDonViKhac(req, res) {
  try {
    const { t } = req;
    const { vi_tri_ids, don_vi_id } = req.body;
    //Update don_vi_id cho vi tri

    await Model.updateMany({ _id: { $in: vi_tri_ids } }, { don_vi_id });
    const viTris = await Model.find({ _id: { $in: vi_tri_ids } }).lean();

    return responseHelper.success(res, viTris);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getCenterToaDo(req, res) {
  try {
    const configToaDo = getMenuConfig.configToaDo;
    const query = queryHelper.extractQueryParam(req, ['ma_vi_tri', 'ten_vi_tri']);
    const { criteria } = query;
    if (!criteria.duong_day_id) {
      const objectIdDonvi = await DonViService.getAllDonViByParent(req, criteria.don_vi_id, true);

      const maxMinKinhDo = await Model.aggregate([
        {
          $match: {
            $and: [
              { don_vi_id: objectIdDonvi },
              { kinh_do: { $ne: '' } },
              { kinh_do: { $regex: /^\d*\.?\d*$/ } },
            ],
          },
        },
        {
          $addFields: {
            maxLng: {
              $cond: [
                {
                  $and: [
                    { $lte: [{ $toDouble: '$kinh_do' }, parseFloat(configToaDo.maxLng)] },
                  ],
                },
                '$kinh_do',
                null,
              ],
            },
            minLng: {
              $cond: [
                {
                  $and: [
                    { $gte: [{ $toDouble: '$kinh_do' }, parseFloat(configToaDo.minLng)] },
                  ],
                },
                '$kinh_do',
                null,
              ],
            },
          },
        },
        {
          $group: {
            _id: null,
            maxLng: {
              '$max': {
                '$convert': { 'input': '$maxLng', 'to': 'double', onError: '' },
              },
            },
            minLng: {
              '$min': {
                '$convert': { 'input': '$minLng', 'to': 'double', onError: '' },
              },
            },
          },
        },
      ]);

      const maxMinViDo = await Model.aggregate([
        {
          $match: {
            $and: [
              { don_vi_id: objectIdDonvi },
              { vi_do: { $ne: '' } },
              { vi_do: { $regex: /^\d*\.?\d*$/ } },
            ],
          },
        },
        {
          $addFields: {
            maxLat: {
              $cond: [
                {
                  $and: [
                    { $lte: [{ $toDouble: '$vi_do' }, parseFloat(configToaDo.maxLat)] },
                  ],
                },
                '$vi_do',
                null,
              ],
            },
            minLat: {
              $cond: [
                {
                  $and: [
                    { $gte: [{ $toDouble: '$vi_do' }, parseFloat(configToaDo.minLat)] },
                  ],
                },
                '$vi_do',
                null,
              ],
            },
          },
        },
        {
          $group: {
            _id: null,
            maxLat: {
              '$max': {
                '$convert': { 'input': '$maxLat', 'to': 'double', onError: '' },
              },
            },
            minLat: {
              '$min': {
                '$convert': { 'input': '$minLat', 'to': 'double', onError: '' },
              },
            },
          },
        },
      ]);

      const zoomLevel = getBoundsZoomLevel(maxMinKinhDo[0], maxMinViDo[0]);
      const maxLongitude = maxMinKinhDo[0]?.maxLng || parseFloat(configToaDo.maxLng);
      const minLongitude = maxMinKinhDo[0]?.minLng || parseFloat(configToaDo.minLng);
      const maxLatitude = maxMinViDo[0]?.maxLat || parseFloat(configToaDo.maxLat);
      const minLatitude = maxMinViDo[0]?.minLat || parseFloat(configToaDo.minLat);
      const data = {
        avgKinhDo: (maxLongitude + minLongitude) / 2,
        avgViDo: (maxLatitude + minLatitude) / 2,
        zoomLevel: zoomLevel,
      };
      responseHelper.success(res, data);
    } else {
      const { criteria } = queryHelper.extractQueryParam(req);
      let allInScopeId = [];
      const scope = await DuongDayService.getAllInScope(criteria.duong_day_id);
      allInScopeId = [...allInScopeId, ...scope];
      const allDuongDayInScopeId = Array.from(new Set(allInScopeId));
      const allVanHanh = await VanHanhService.getAll(
        { duong_day_id: allDuongDayInScopeId, is_deleted: false },
        { duong_day_id: 1, vi_tri_id: 1, thu_tu: 1 },
      );
      const allViTriByDuongDayId = extractKeys(allVanHanh, 'vi_tri_id');

      const objectIdViTri = allViTriByDuongDayId.map(function(el) {
        return mongoose.Types.ObjectId(el);
      });
      const maxMinKinhDo = await Model.aggregate([
        {
          $match: {
            $and: [
              { _id: { $in: objectIdViTri } },
              { kinh_do: { $ne: '' } },
              { kinh_do: { $regex: /^\d*\.?\d*$/ } },
            ],
          },
        },
        {
          $addFields: {
            maxLng: {
              $cond: [
                { $lte: [{ $toDouble: '$kinh_do' }, parseFloat(configToaDo.maxLng)] },
                '$kinh_do',
                null,
              ],
            },
            minLng: {
              $cond: [
                { $gte: [{ $toDouble: '$kinh_do' }, parseFloat(configToaDo.minLng)] },
                '$kinh_do',
                null,
              ],
            },
          },
        },
        {
          $group: {
            _id: null,
            maxLng: {
              '$max': {
                '$convert': { 'input': '$maxLng', 'to': 'double', onError: '' },
              },
            },
            minLng: {
              '$min': {
                '$convert': { 'input': '$minLng', 'to': 'double', onError: '' },
              },
            },
          },
        },
      ]);

      const maxMinViDo = await Model.aggregate([
        {
          $match: {
            $and: [
              { _id: { $in: objectIdViTri } },
              { vi_do: { $ne: '' } },
              { vi_do: { $regex: /^\d*\.?\d*$/ } },
            ],
          },
        },
        {
          $addFields: {
            maxLat: {
              $cond: [
                { $lte: [{ $toDouble: '$vi_do' }, parseFloat(configToaDo.maxLat)] },
                '$vi_do',
                null,
              ],
            },
            minLat: {
              $cond: [
                { $gte: [{ $toDouble: '$vi_do' }, parseFloat(configToaDo.minLat)] },
                '$vi_do',
                null,
              ],
            },
          },
        },
        {
          $group: {
            _id: null,
            maxLat: {
              '$max': {
                '$convert': { 'input': '$maxLat', 'to': 'double', onError: '' },
              },
            },
            minLat: {
              '$min': {
                '$convert': { 'input': '$minLat', 'to': 'double', onError: '' },
              },
            },
          },
        },
      ]);

      const zoomLevel = getBoundsZoomLevel(maxMinKinhDo[0], maxMinViDo[0]);
      const maxLongitude = maxMinKinhDo[0]?.maxLng || parseFloat(configToaDo.maxLng);
      const minLongitude = maxMinKinhDo[0]?.minLng || parseFloat(configToaDo.minLng);
      const maxLatitude = maxMinViDo[0]?.maxLat || parseFloat(configToaDo.maxLat);
      const minLatitude = maxMinViDo[0]?.minLat || parseFloat(configToaDo.minLat);
      const data = {
        avgKinhDo: (maxLongitude + minLongitude) / 2,
        avgViDo: (maxLatitude + minLatitude) / 2,
        zoomLevel: zoomLevel,
      };
      return responseHelper.success(res, data);
    }
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

function getBoundsZoomLevel(maxMinKinhDo, maxMinViDo) {
  const WORLD_DIM = { height: 256, width: 256 };
  const ZOOM_MAX = 21;
  const mapDim = { height: 815, width: 1052 };

  function latRad(lat) {
    const sin = Math.sin(lat * Math.PI / 180);
    const radX2 = Math.log((1 + sin) / (1 - sin)) / 2;
    return Math.max(Math.min(radX2, Math.PI), -Math.PI) / 2;
  }

  function zoom(mapPx, worldPx, fraction) {
    return Math.floor(Math.log(mapPx / worldPx / fraction) / Math.LN2);
  }

  const latFraction = (latRad(maxMinViDo?.maxLat) - latRad(maxMinViDo?.minLat)) / Math.PI;

  const lngDiff = maxMinKinhDo?.maxLng - maxMinKinhDo?.minLng;
  const lngFraction = ((lngDiff < 0) ? (lngDiff + 360) : lngDiff) / 360;

  const latZoom = zoom(mapDim.height, WORLD_DIM.height, latFraction) || 6;
  const lngZoom = zoom(mapDim.width, WORLD_DIM.width, lngFraction) || 6;

  return Math.min(latZoom, lngZoom, ZOOM_MAX);
}

export async function getAllByDuongDayId(req, res) {
  try {
    const { ids } = req.params;

    const duongDayIds = ids.split(',');
    const data = await getViTriByDuongDayList(req, duongDayIds);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllInReportByDuongDayId(req, res) {
  try {
    const { ids } = req.params;

    const duongDayIds = ids.split(',');
    const data = await getViTriByDuongDayReport(req, duongDayIds);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllByCongTrinhId(req, res) {
  try {
    const { id } = req.params;

    const data = await Service.getAll(
      { cong_trinh_id: id, is_deleted: false },
      { ten_vi_tri: 1, khoang_cot: 1, ma_vi_tri: 1 },
    ).sort({ thu_tu: 1 });

    const allVanHanh = await VanHanhService.getAll(
      { vi_tri_id: extractIds(data), is_deleted: false },
      { duong_day_id: 1, vi_tri_id: 1 })
      .populate({ path: 'duong_day_id', select: 'ten_duong_day' });
    const vanHanhGroupViTriId = groupBy(allVanHanh, 'vi_tri_id');
    data.forEach(viTri => {
      viTri.van_hanh_id = vanHanhGroupViTriId[viTri._id];
    });
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function getViTriByDuongDayList(req, duongDayIds) {
  const assignType = req?.query?.assign_type;
  delete req?.query?.assign_type;

  async function getDataViTri() {
    const donViInScope = await DonViService.getDonViInScope(req.query.don_vi_id || req.user?.don_vi_id);
    delete req.query.don_vi_id;
    const { criteria } = queryHelper.extractQueryParam(req);
    let allInScopeId = [];
    for (let i = 0; i < duongDayIds.length; i++) {
      const scope = await DuongDayService.getAllInScope(duongDayIds[i]);
      allInScopeId = [...allInScopeId, ...scope];
    }
    const allDuongDayInScopeId = Array.from(new Set(allInScopeId));
    const allVanHanh = await VanHanhService.getAll(
      { duong_day_id: allDuongDayInScopeId, is_deleted: false },
      { duong_day_id: 1, vi_tri_id: 1, thu_tu: 1 },
    );
    const allViTriByDuongDayId = extractKeys(allVanHanh, 'vi_tri_id');
    const viTriQuery = {
      _id: allViTriByDuongDayId,
      tinh_trang_van_hanh: TINH_TRANG_VAN_HANH.VAN_HANH,
      ...criteria,
    };
    if (donViInScope.length) {
      viTriQuery.don_vi_id = donViInScope;
    }
    const allViTri = await Service.getAll(
      viTriQuery,
      { ten_vi_tri: 1, loai_vi_tri: 1, thu_tu: 1, ma_vi_tri: 1, kinh_do: 1, vi_do: 1 })
      .sort('thu_tu');

    let allKhoangCot = await KHOANG_COT.find(
      {
        vi_tri_id: extractIds(allViTri),
        $or: duongDayIds.map(duongDay => ({ duong_day_id: duongDay })),
        is_deleted: false,
      },
      {
        ten_khoang_cot: 1,
        vi_tri_bat_dau_id: 1,
        vi_tri_ket_thuc_id: 1,
        vi_tri_id: 1,
        chieu_dai: 1,
        ma_khoang_cot: 1,
      })
      .populate({
        path: 'vi_tri_bat_dau_id vi_tri_ket_thuc_id',
        select: 'ten_vi_tri kinh_do vi_do tinh_trang_van_hanh',
      })
      .lean();

    if (Array.isArray(allKhoangCot)) {
      allKhoangCot = allKhoangCot
        .filter(khoangCot => {
          return khoangCot.vi_tri_bat_dau_id?.tinh_trang_van_hanh === TINH_TRANG_VAN_HANH.VAN_HANH
            && khoangCot.vi_tri_ket_thuc_id?.tinh_trang_van_hanh === TINH_TRANG_VAN_HANH.VAN_HANH;
        })
        .map(khoangCot => {
          delete khoangCot.vi_tri_bat_dau_id?.tinh_trang_van_hanh;
          delete khoangCot.vi_tri_ket_thuc_id?.tinh_trang_van_hanh;
          return khoangCot;
        });
    }

    return {
      allVanHanh,
      // allViTri: allViTri.filter(viTri => viTri.loai_vi_tri !== LOAI_VI_TRI.XA_POOCTIC),
      allViTri: allViTri,
      allKhoangCot,
    };
  }

  async function sortViTri(viTriInput) {
    const viTriObject = {};
    for (let i = 0; i < duongDayIds.length; i++) {
      const duongDayId = duongDayIds[i];
      viTriInput
        .filter(viTri => !!viTri.van_hanh.find(vh => vh?.duong_day_id?.toString() === duongDayId?.toString()))
        .sort((a, b) => {
          const sttVanHanhA = a.van_hanh.find(vh => vh.duong_day_id.toString() === duongDayId.toString())?.thu_tu;
          const sttVanHanhB = b.van_hanh.find(vh => vh.duong_day_id.toString() === duongDayId.toString())?.thu_tu;
          return sttVanHanhA - sttVanHanhB;
        })
        .forEach(doc => {
          doc?.khoang_cot_id?.forEach(khoangCot => {
            viTriObject[doc._id]?.khoang_cot_id?.forEach(khoangCotCurrent => {
              if (khoangCot?._id === khoangCotCurrent?._id) {
                khoangCot.so_mach = khoangCotCurrent?.so_mach + 1;
              }
            });

            if (!khoangCot.so_mach) khoangCot.so_mach = 1;
          });
          viTriObject[doc._id] = doc;
        });
    }
    return Object.values(viTriObject);
  }

  async function countPhieuKiemTra(viTriData, khoangCotData) {
    const viTriId = extractIds(viTriData);
    const khoangCotId = extractIds(khoangCotData);

    const congViecKiemTra = await ViTriCongViecService.getAll(
      {
        $or: [{ vi_tri_id: viTriId }, { khoang_cot_id: khoangCotId }],
        is_deleted: false,
      },
      { vi_tri_id: 1, khoang_cot_id: 1, phieu_giao_viec_id: 1 })
      .populate({
        path: 'phieu_giao_viec_id',
        select: 'trang_thai_cong_viec thoi_gian_cong_tac_bat_dau so_phieu loai_cong_viec',
        populate: [{
          path: 'chi_huy_truc_tiep_id',
          select: 'full_name',
        }, {
          path: 'nguoi_giao_phieu_id',
          select: 'full_name',
        }],
        match: {
          loai_cong_viec: LOAI_CONG_VIEC.KIEM_TRA_DINH_KY_NGAY.code,
          trang_thai_cong_viec: { $ne: TRANG_THAI_PHIEU.HUY_PHIEU.code },
          thoi_gian_cong_tac_bat_dau: {
            $gte: momentTimezone().startOf('month'),
            $lt: momentTimezone().endOf('month'),
          },
          is_deleted: false,
        },
      });
    const congViecKiemTraViTri = congViecKiemTra.filter(cv => cv.phieu_giao_viec_id && !!cv.vi_tri_id);
    const congViecKiemTraKhoangCot = congViecKiemTra.filter(cv => cv.phieu_giao_viec_id && !!cv.khoang_cot_id);

    const groupKiemTraByViTri = groupBy(congViecKiemTraViTri, 'vi_tri_id');
    const groupKiemTraByKhoangCot = groupBy(congViecKiemTraKhoangCot, 'khoang_cot_id');
    return { groupKiemTraByViTri, groupKiemTraByKhoangCot };
  }

  async function countCauHinhBay(viTriData) {
    const cauHinhBayData = await CAU_HINH_BAY.find({ vi_tri_id: { $in: extractIds(viTriData) } }).lean();
    return groupBy(cauHinhBayData, 'vi_tri_id');
  }

  const { allVanHanh, allViTri, allKhoangCot } = await getDataViTri();
  const { groupKiemTraByViTri, groupKiemTraByKhoangCot } = await countPhieuKiemTra(allViTri, allKhoangCot);

  const groupCauHinhBay = await countCauHinhBay(allViTri);
  const vanHanhGroupByViTri = groupBy(allVanHanh, 'vi_tri_id');

  // cal mối nối
  const allVanHanhId = extractIds(allVanHanh);
  const allDayDan = await DayDanService.getAll(
    { van_hanh_id: allVanHanhId, so_luong_moi_noi: { $exists: true }, is_deleted: false },
    { van_hanh_id: 1, so_luong_moi_noi: 1 },
  ).populate({ path: 'van_hanh_id', select: 'vi_tri_id' });

  const dayDanGroupByViTri = allDayDan.reduce(function(grouped, element) {
    if (element.van_hanh_id.vi_tri_id) {
      grouped[element.van_hanh_id.vi_tri_id] ||= [];
      grouped[element.van_hanh_id.vi_tri_id].push(element);
    }
    return grouped;
  }, {});

  for (const khoangCot of allKhoangCot) {
    khoangCot.so_phieu_kiem_tra_trong_thang_hien_tai = groupKiemTraByKhoangCot[khoangCot._id]?.length || 0;
    khoangCot.list_phieu_kiem_tra = groupKiemTraByKhoangCot[khoangCot._id]?.map((viTriCongViec) => viTriCongViec.phieu_giao_viec_id);
    if (assignType === 'DO_THONG_SO') {
      const allDayDan = dayDanGroupByViTri[khoangCot.vi_tri_id];
      if (Array.isArray(allDayDan)) {
        khoangCot.so_moi_noi_day_dan = allDayDan.reduce((sum, element) => {
          const value = parseInt(element.so_luong_moi_noi);
          return !isNaN(value) ? sum + value : sum;
        }, 0);
      }
    }
  }

  const khoangCotGroupByViTri = groupBy(allKhoangCot, 'vi_tri_id');

  allViTri.forEach(viTri => {
    viTri.khoang_cot_id = khoangCotGroupByViTri[viTri._id] || [];
    viTri.van_hanh = vanHanhGroupByViTri[viTri._id];
    viTri.so_phieu_kiem_tra_trong_thang_hien_tai = groupKiemTraByViTri[viTri._id]?.length || 0;
    viTri.so_cau_hinh_bay_tu_dong = groupCauHinhBay[viTri._id]?.length || 0;
    viTri.list_phieu_kiem_tra = groupKiemTraByViTri[viTri._id]?.map((viTriCongViec) => viTriCongViec.phieu_giao_viec_id);
  });
  return sortViTri(allViTri);
}

export async function getViTriByDuongDayReport(req, duongDayIds) {

  async function getDataViTri() {
    const donViInScope = await DonViService.getDonViInScope(req.query.don_vi_id || req.user?.don_vi_id);
    let allInScopeId = [];
    for (let i = 0; i < duongDayIds.length; i++) {
      const scope = await DuongDayService.getAllInScope(duongDayIds[i]);
      allInScopeId = [...allInScopeId, ...scope];
    }
    const allDuongDayInScopeId = Array.from(new Set(allInScopeId));
    const allVanHanh = await VanHanhService.getAll(
      { duong_day_id: allDuongDayInScopeId, is_deleted: false },
      { duong_day_id: 1, vi_tri_id: 1, thu_tu: 1 },
    );
    const allViTriByDuongDayId = extractKeys(allVanHanh, 'vi_tri_id');
    const viTriQuery = { _id: allViTriByDuongDayId, is_deleted: false };
    if (donViInScope.length) {
      viTriQuery.don_vi_id = donViInScope;
    }
    const allViTri = await Service.getAll(viTriQuery, { ten_vi_tri: 1, thu_tu: 1 }).sort('thu_tu');

    return { allVanHanh, allViTri };
  }

  async function sortViTri(viTriInput) {
    const viTriObject = {};
    for (let i = 0; i < duongDayIds.length; i++) {
      const duongDayId = duongDayIds[i];
      viTriInput
        .sort((a, b) => {
          const sttVanHanhA = a.van_hanh.find(vh => vh.duong_day_id.toString() === duongDayId.toString())?.thu_tu;
          const sttVanHanhB = b.van_hanh.find(vh => vh.duong_day_id.toString() === duongDayId.toString())?.thu_tu;
          return sttVanHanhA - sttVanHanhB;
        })
        .forEach(doc => viTriObject[doc._id] = doc);
    }
    return Object.values(viTriObject);
  }


  const { allVanHanh, allViTri } = await getDataViTri();

  const vanHanhGroupByViTri = groupBy(allVanHanh, 'vi_tri_id');

  const allCotDien = await CotDienService.getAll(
    { vi_tri_id: allViTri, is_deleted: false },
    { vi_tri_id: 1, chieu_cao: 1 },
  );

  const allTiepDia = await TiepDatService.getAll(
    { vi_tri_id: allViTri, is_deleted: false },
    { vi_tri_id: 1, ten_tiep_dat: 1 },
  );

  const mapCotDien = {};
  allCotDien.forEach(cotDien => {
    mapCotDien[cotDien.vi_tri_id] = cotDien;
  });

  const mapTiepDia = {};
  allTiepDia.forEach(tiepDia => {
    mapTiepDia[tiepDia.vi_tri_id] = tiepDia;
  });

  allViTri.forEach(viTri => {
    viTri.van_hanh = vanHanhGroupByViTri[viTri._id];
    viTri.cot_dien = mapCotDien[viTri._id];
    viTri.tiep_dia = mapTiepDia[viTri._id];
  });
  return sortViTri(allViTri);
}

export async function findDistinct(req, res) {
  try {
    if (req.query.ma_vi_tri) {
      req.query.ma_vi_tri = `"${req.query.ma_vi_tri}"`;
    }
    if (req.query.ten_vi_tri) {
      req.query.ten_vi_tri = `"${req.query.ten_vi_tri}"`;
    }
    const query = queryHelper.extractQueryParam(req, ['ma_vi_tri', 'ten_vi_tri']);
    const { criteria } = query;
    let field = '';
    if (criteria.ma_vi_tri) field = 'ma_vi_tri';
    if (criteria.ten_vi_tri) field = 'ten_vi_tri';
    criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);
    let data = await Model.distinct(field, criteria).lean();
    if (data.length) {
      data = data.map((row) => {
        return { [field]: row };
      });
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}
