import express from 'express';
import passport from 'passport';
import * as vitriController from './viTri.controller';
import { getAllInReportByDuongDayId, getAllViTriCapTruyenTaiDien } from './viTri.controller';

export const viTriRouter = express.Router();

viTriRouter.use(passport.authenticate('jwt', { session: false }));

viTriRouter
  .route('/')
  .get(vitriController.getAll)
  .post(vitriController.create);

viTriRouter.route('/truyentaidien')
  .get(vitriController.getAllViTriCapTruyenTaiDien);

viTriRouter.route('/move')
  .put(vitriController.diChuyenViTriSangDonViKhac);

viTriRouter.route('/finddistinct')
  .get(vitriController.findDistinct);

viTriRouter.route('/congtrinh/:id')
  .get(vitriController.getAllByCongTrinhId);

viTriRouter.route('/toado')
  .get(vitriController.getCenterToaDo);

viTriRouter
  .route('/giaoviec/duongday/:ids')
  .get(vitriController.getAllByDuongDayId);

viTriRouter
  .route('/report/duongday/:ids')
  .get(vitriController.getAllInReportByDuongDayId);

viTriRouter
  .route('/:id')
  .get(vitriController.findOne)
  .delete(vitriController.remove)
  .put(vitriController.update);
