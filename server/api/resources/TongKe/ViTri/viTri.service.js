import * as ValidatorHelper from '../../../helpers/validatorHelper';
import VI_TRI, { LOAI_VI_TRI } from './viTri.model';
import * as KhoangCotService from '../KhoangCot/khoangCot.service';
import { convertDate } from '../../../helpers/checkDataHelper';
import { checkCommonInfo } from '../TongKe.service';
import TRUONG_DU_LIEU from '../../TruongDuLieu/truongDuLieu.model';
import * as CongTrinhService from '../CongTrinh/congTrinh.service';
import { groupBy, trim } from '../../../common/functionCommons';
import VAN_HANH from '../VanHanh/vanHanh.model';
import { CommonHeaders } from '../ThietBi/thietBi.controller';
import * as DuongDayService from '../DuongDay/duongDay.service';
import { extractIds, extractKeys } from '../../../utils/dataconverter';

const Headers = {
  MA_THIET_BI: 'Mã thiết bị',
  DUONG_DAY: 'Đường dây/TBA',
  THIET_BI_CHA: 'Thiết bị/Công trình cha',
  TEN_THIET_BI: 'Thiết bị/Công trình',
  STT: 'STT',
  SERIAL: 'Số chế tạo/Serial',
  NGAY_VAN_HANH: 'Ngày vận hành',
  SO_TSCD: 'Số TSCĐ',
  TINH_TRANG_VAN_HANH: 'Tình trạng vận hành',
  MA_LIEN_KET_KHAC: 'Mã liên kết khác',
  HANG_SAN_XUAN: 'Hãng sản xuất',
  NHA_CUNG_CAP: 'Nhà cung cấp',
  NUOC_SAN_XUAT: 'Nước sản xuất',
  SO_HUU: 'Sở hữu',
  NGAY_LAP_DAT: 'Ngày lắp đặt',
  NAM_SAN_XUAT: 'Năm sản xuất',
  NGAY_SUA_DOI: 'Ngày sửa đổi',
  GHI_CHU: 'Ghi chú',
  DON_VI: 'Đơn vị',


  CONG_TRINH_CHINH: 'Công trình chính',
  NAM_XAY_DUNG: 'Năm xây dựng',
  GOC_LAI: 'Góc lái',
  KHOANG_COT: 'Khoảng cột',
  KHOANG_NEO: 'Khoảng néo',
  KHU_VUC: 'Khu vực',
  TINH: 'Tỉnh',
  HUYEN: 'Huyện',
  XA: 'Xã',
  TUYEN_DI_CHUNG: 'Tuyến đi chung',
  HANH_LANG_TUYEN: 'Hành lang tuyến',
  DUONG_VAO: 'Đường vào',
  SO_DO_BO_TRI_DAY: 'Sơ đồ bố trí dây',
  DAC_DIEM_DIA_HINH: 'Đặc điểm địa hình',
  KINH_DO: 'Kinh độ',
  VI_DO: 'Vĩ độ',
};

export async function importData(sheetData, mapDonVi, mapCongTrinh) {
  console.log(JSON.stringify(mapCongTrinh, null, 2));
  const { rows } = sheetData;
  const extraFields = await TRUONG_DU_LIEU.find({ is_deleted: false, model: VI_TRI.modelName }).lean();
  const congTrinhIdsSet = new Set();

  function convertToDB(row) {
    congTrinhIdsSet.add(mapCongTrinh[trim(row[CommonHeaders.MA_THIET_BI_CHA])]);
    const dbData = {
      ma_vi_tri: trim(row[CommonHeaders.MA_THIET_BI]),
      ten_vi_tri: trim(row[CommonHeaders.TEN_THIET_BI]),
      ma_thiet_bi_cha: trim(row[CommonHeaders.MA_THIET_BI_CHA]),
      ma_tim_kiem: trim(row[CommonHeaders.MA_TIM_KIEM_THIET_BI]),
      ma_tim_kiem_cha: trim(row[CommonHeaders.MA_TIM_KIEM_CHA]),
      don_vi_id: mapDonVi[row[Headers.DON_VI]?.trim()],
      cong_trinh_id: mapCongTrinh[trim(row[CommonHeaders.MA_THIET_BI_CHA])],
      so_the_tscd: row[Headers.SO_TSCD],
      thu_tu: row[Headers.STT],
      ngay_van_hanh: convertDate(row[Headers.NGAY_VAN_HANH]),
      tinh_trang_van_hanh: row[Headers.TINH_TRANG_VAN_HANH],
      so_huu: row[Headers.SO_HUU],
      goc_lai: row[Headers.GOC_LAI],
      khoang_cot: row[Headers.KHOANG_COT],
      khoang_neo: row[Headers.KHOANG_NEO],
      khu_vuc: row[Headers.KHU_VUC],
      tinh: row[Headers.TINH],
      huyen: row[Headers.HUYEN],
      xa: row[Headers.XA],
      tuyen_di_chung: row[Headers.TUYEN_DI_CHUNG],
      hanh_lang_tuyen: row[Headers.HANH_LANG_TUYEN],
      duong_vao: row[Headers.DUONG_VAO],
      so_do_bo_tri_day: row[Headers.SO_DO_BO_TRI_DAY],
      dac_diem_dia_hinh: row[Headers.DAC_DIEM_DIA_HINH],
      kinh_do: row[Headers.KINH_DO],
      vi_do: row[Headers.VI_DO],
      is_deleted: false,
    };
    if (extraFields) {
      const extraData = {};
      extraFields.forEach(field => {
        extraData[field.field_key] = row[field.field_name];
      });
      dbData.extra = extraData;
    }
    return dbData;
  }

  const dataToDB = rows.map(row => convertToDB(row)).filter(element => element.ten_vi_tri);
  const result = await VI_TRI.bulkWrite(
    dataToDB.map((viTri) =>
      ({
        updateOne: {
          filter: { ma_vi_tri: viTri.ma_vi_tri },
          update: { $set: viTri },
          upsert: true,
        },
      }),
    ),
  );
  await KhoangCotService.createOrUpdateKhoangCot([...congTrinhIdsSet]);
  return result;

}

export async function checkImport(t, sheetData, mapDonVi, congtrinhSheetChecked) {
  if (!sheetData) return null;
  const congTrinhChaList = congtrinhSheetChecked.rows.map(row => row[CommonHeaders.MA_THIET_BI]);
  const allCongTrinh = await CongTrinhService.getAll({ is_deleted: false, ma_cong_trinh: { $in: congTrinhChaList } });
  const mapCongTrinh = {};
  allCongTrinh.forEach(congTrinh => {
    mapCongTrinh[congTrinh.ma_cong_trinh] = congTrinh._id;
  });
  const { rows } = sheetData;

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row) {
    let errorString = [];
    errorString = [...errorString, ...checkCommonInfo(t, row)];

    if (!row[Headers.DON_VI]) {
      errorString = [...errorString, createError(CommonHeaders.DON_VI, t('missing_unit'))];
    } else {
      if (!mapDonVi[row[Headers.DON_VI]?.trim()]) {
        errorString = [...errorString, createError(Headers.DON_VI, t('unit_incorrect_or_dont_create'))];
      }
    }

    if (!row[CommonHeaders.MA_THIET_BI_CHA]) {
      errorString = [...errorString, createError(CommonHeaders.MA_THIET_BI_CHA, t('missing_construction'))];
    } else {
      if (!mapCongTrinh[trim(row[CommonHeaders.MA_THIET_BI_CHA])]) {
        errorString = [...errorString, createError(CommonHeaders.MA_THIET_BI_CHA, t('construction_incorrect_or_dont_create'))];
      }
    }

    if (errorString.length) {
      row['Lỗi'] = errorString;
    } else {
      row['Lỗi'] = null;
    }
    return row;
  }

  sheetData.rows = rows.map(row => validateRow(t, row));
  return sheetData;
}

export function getAll(query, projection = {}) {
  return VI_TRI.find(query, projection).lean();
}

export function getOne(query, projection = {}) {
  return VI_TRI.findOne(query, projection).lean();
}

export function remove(query) {
  return VI_TRI.deleteMany(query).lean();
}

export function distinctId(query) {
  return VI_TRI.distinct('_id', query);
}

export async function updateAll(dataUpdate, options = {}) {
  options.new = true;
  const results = [];
  for (const row of dataUpdate) {
    try {
      const { error, value } = validate(row);
      if (!error) {
        const updated = await VI_TRI.findByIdAndUpdate(value._id, value, options);
        if (updated) results.push(updated);
      }
    } catch {
    }
  }
  return results;
}

export async function getAllIdMap(query) {
  const allRecord = await getAll(query);
  const mapIdRecord = {};
  allRecord.forEach(record => {
    mapIdRecord[record.ma_vi_tri] = record._id;
  });
  return mapIdRecord;
}

const Joi = require('joi');

const objSchema = Joi.object({
  ma_vi_tri: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã vị trí')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function deleteAll(query) {
  return VI_TRI.remove(query);
}


export async function getHashMapViTriByDuongDay(viTriIds) {
  const mapViTriByDuongDay = {};
  const allVanHanh = await VAN_HANH.find({ is_deleted: false, vi_tri_id: viTriIds })
    .select('duong_day_id vi_tri_id').lean();

  const groupVanHanhByDuongDayId = groupBy(allVanHanh, 'duong_day_id');
  const allDuongDay = await DuongDayService.getAll({
    _id: extractKeys(allVanHanh, 'duong_day_id'), is_deleted: false,
  }).select('ten_duong_day');

  const duongDayIds = extractIds(allDuongDay);
  for (let duongDayId of duongDayIds) {
    mapViTriByDuongDay[duongDayId] = extractKeys(groupVanHanhByDuongDayId[duongDayId], 'vi_tri_id');
  }
  return mapViTriByDuongDay;
}
