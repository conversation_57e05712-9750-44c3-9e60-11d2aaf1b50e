import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CONG_TRINH, DON_VI, VI_TRI } from '../../../constant/dbCollections';
import { TINH_TRANG_VAN_HANH } from '../../DanhMuc/TinhTrangVanHanh';

export const LOAI_VI_TRI = { COT_DIEN: 'COT_DIEN', XA_POOCTIC: 'XA_POOCTIC' };

const schema = new Schema({
  ma_vi_tri: { type: String, required: true, unique: true, validate: /\S+/ },
  ma_thiet_bi_cha: { type: String, validate: /\S+/ },
  ma_tim_kiem: { type: String, validate: /\S+/ },
  ma_tim_kiem_cha: { type: String, validate: /\S+/ },
  cong_trinh_id: { type: Schema.Types.ObjectId, ref: CONG_TRINH },
  loai_vi_tri: {
    type: String,
    enum: Object.values(LOAI_VI_TRI),
    default: LOAI_VI_TRI.COT_DIEN,
  },
  ten_vi_tri: { type: String },
  thu_tu: { type: Number },
  ngay_van_hanh: { type: Date },
  tinh_trang_van_hanh: {
    type: String,
    enum: Object.values(TINH_TRANG_VAN_HANH),
    default: TINH_TRANG_VAN_HANH.VAN_HANH,
  },
  so_huu: String,
  ngay_lap_dat: { type: Date },
  nam_san_xuat: { type: Date },
  ngay_sua_doi: { type: Date },
  ghi_chu: String,
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  goc_lai: String,
  khoang_cot: String,
  khoang_neo: String,
  khu_vuc: String,
  tinh: { type: String },
  huyen: { type: String },
  xa: { type: String },
  hanh_lang_tuyen: String,
  duong_vao: String,
  so_do_bo_tri_day: String,
  dac_diem_dia_hinh: String,
  kinh_do: String,
  vi_do: String,
  dac_diem_khu_vuc: { type: String, default: '' },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
// Existing indexes
schema.index({ cong_trinh_id: 1 });
schema.index({ don_vi_id: 1, is_deleted: 1 });
schema.index({ ma_vi_tri: 1 });
schema.index({ ten_vi_tri: 1 });

// Optimized indexes for getCenterToaDo function
// Index for aggregation queries filtering by don_vi_id with kinh_do conditions
schema.index({ don_vi_id: 1, kinh_do: 1, is_deleted: 1 });
// Index for aggregation queries filtering by don_vi_id with vi_do conditions
schema.index({ don_vi_id: 1, vi_do: 1, is_deleted: 1 });
// Index for queries filtering by _id array (when duong_day_id is provided)
schema.index({ _id: 1, kinh_do: 1 });
schema.index({ _id: 1, vi_do: 1 });
// Compound index for coordinate-based queries with regex patterns
schema.index({ kinh_do: 1, vi_do: 1, don_vi_id: 1 });
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(VI_TRI, schema, VI_TRI);
