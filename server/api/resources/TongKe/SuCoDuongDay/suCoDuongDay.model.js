import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { SU_CO_DUONG_DAY, VI_TRI, KHOANG_COT, DUONG_DAY } from '../../../constant/dbCollections';

export const DOI_TUONG_SU_CO = {
  NGOAI_EVN: 'NGOAI_EVN',
  THUOC_EVN: 'THUOC_EVN',
  TAI_SAN_DON_VI: 'TAI_SAN_DON_VI',
};

const schema = new Schema({
  kinkei_id: { type: String },
  pmis_id: { type: String },
  duong_day_id: { type: Schema.Types.ObjectId, ref: DUONG_DAY },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  khoang_cot_id: { type: Schema.Types.ObjectId, ref: KHOANG_COT },
  pha: [{ type: String }],
  doi_tuong: {
    type: String,
    enum: Object.keys(DOI_TUONG_SU_CO),
  },
  nguyen_nhan: { type: String },
  dien_bien: { type: String },
  mo_ta: { type: String },
  bien_phap_khac_phuc: { type: String },
  phan_mem_phat_hien: { type: String },
  xac_nhan: { type: Boolean, default: false },
  da_xu_ly: { type: Boolean, default: false },
  thoi_gian_phat_hien: Date,
  thoi_gian_xu_ly: Date,
  ghi_chu: String,
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(SU_CO_DUONG_DAY, schema, SU_CO_DUONG_DAY);
