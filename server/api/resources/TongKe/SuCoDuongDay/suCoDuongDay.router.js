import express from 'express';
import passport from 'passport';
import * as Controller from './suCoDuongDay.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import Permission from '../../RBAC/permissions/SuCoDuongDayPermission';
import { huyXacNhanSuCo, xacNhanSuCo } from './suCoDuongDay.controller';
import { loggerMiddleware } from '../../../logs/middleware';

export const suCoDuongDayRouter = express.Router();

suCoDuongDayRouter.route('/syncsuco').get(Controller.getDataSuCo);

suCoDuongDayRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
suCoDuongDayRouter.post('*', authorizationMiddleware([Permission.CREATE]));
suCoDuongDayRouter.put('*', authorizationMiddleware([Permission.UPDATE]));
suCoDuongDayRouter.delete('*', authorizationMiddleware([Permission.DELETE]));
suCoDuongDayRouter.get('*', authorizationMiddleware([Permission.READ]));

suCoDuongDayRouter
  .route('/')
  .get(Controller.getAll)
  .post(Controller.create);

suCoDuongDayRouter
  .route('/:id/thoigianxuly').put(Controller.updateThoiGianXuLy);

suCoDuongDayRouter
  .route('/:id/xacnhan').put(Controller.xacNhanXuLy);
suCoDuongDayRouter
  .route('/:id/huyxacnhan').put(Controller.huyXacNhanXuLy);

suCoDuongDayRouter
  .route('/:id/xacnhansuco').put(Controller.xacNhanSuCo);
suCoDuongDayRouter
  .route('/:id/huyxacnhansuco').put(Controller.huyXacNhanSuCo);

suCoDuongDayRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
