import * as Service from './suCoDuongDay.service';
import Model from './suCoDuongDay.model';
import UserService from '../../User/user.service';
import * as controllerHelper from '../../../helpers/controllerHelper';
import { findOneById } from '../../../helpers/controllerHelper';
import * as responseAction from '../../../helpers/responseHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';
import CommonError from '../../../error/CommonError';
import { extractIds, formatTimeCriteria } from '../../../utils/dataconverter';

import * as dbCollections from '../../../constant/dbCollections';
import * as CaiDatVanHanhService from '../../../resources/CaiDatVanHanh/caiDatVanHanh.service';
import * as DonViService from '../../DonVi/donVi.service';
import * as DuongDayService from '../../TongKe/DuongDay/duongDay.service';
import * as NotificationService from '../../Notification/notification.service';
import { NOTIFICATION_ACTION, NOTIFICATION_TYPE } from '../../Notification/notification.constants';
import { CAP_DON_VI } from '../../../constant/constant';

const populateOpts = [{ path: 'duong_day_id' }, { path: 'vi_tri_id' }, { path: 'khoang_cot_id' }];
const sortOpts = { thoi_gian_phat_hien: -1 };
const uniqueOpts = [];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;

    if (!criteria.duong_day_id) {
      let donViQuery;
      const donViUser = await DonViService.getById(req.user?.don_vi_id);
      if (donViUser?.cap_don_vi === CAP_DON_VI.DOI_TRUYEN_TAI_DIEN) {
        donViQuery = [donViUser._id.toString(), donViUser.don_vi_cha_id?.toString()];
      } else {
        donViQuery = await DonViService.getDonViInScope(req.user?.don_vi_id);
      }
      const allDuongDay = await DuongDayService.getAll({ don_vi_id: donViQuery }, { _id: 1 });
      criteria.duong_day_id = extractIds(allDuongDay);
    }

    formatTimeCriteria(criteria, 'thoi_gian_phat_hien', 'phat_hien_tu_ngay', 'phat_hien_den_ngay');

    options.populate = populateOpts;
    options.sort = sortOpts;
    const data = await Model.paginate(criteria, options);
    responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const currentData = await Model.findById(id).lean();
    if (currentData.da_xu_ly) {
      return responseHelper.error(res, { message: 'Sự cố đã xử lý, không được xóa' }, 400);
    }

    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    const currentData = await Model.findById(id).lean();
    if (currentData.da_xu_ly) {
      return responseHelper.error(res, { message: 'Sự cố đã xử lý, không được sửa' }, 400);
    }

    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true });
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    const updatedData = await findOneById(Model, id, populateOpts, true);
    return responseHelper.success(res, updatedData);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function xacNhanXuLy(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { da_xu_ly: true }, { new: true })
      .populate(populateOpts)
      .lean();

    if (!data) {
      return responseAction.error(res, null, 404);
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function huyXacNhanXuLy(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate(
      { _id: id },
      { da_xu_ly: false, thoi_gian_xu_ly: null },
      { new: true })
      .populate(populateOpts)
      .lean();

    if (!data) {
      return responseAction.error(res, null, 404);
    }
    return responseAction.success(res, data);
  } catch (err) {
    console.log('err', err);
    responseAction.error(res, err);
  }
}

export async function updateThoiGianXuLy(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const data = await Model.findOneAndUpdate({ _id: id }, { thoi_gian_xu_ly: value.thoi_gian_xu_ly }, { new: true })
      .populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getDataSuCo(req, res) {
  try {
    const data = await Service.getDataSuCo();
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function xacNhanSuCo(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { xac_nhan: true }, { new: true })
      .populate(populateOpts)
      .lean();

    if (!data) {
      return responseAction.error(res, null, 404);
    }

    notificationXacNhanSuCo(req, data, NOTIFICATION_ACTION.XAC_NHAN_SU_CO_DUONG_DAY, 'XacNhanSuCoDuongDay', t);
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function huyXacNhanSuCo(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const data = await Model.findOneAndUpdate(
      { _id: id },
      { xac_nhan: false },
      { new: true })
      .populate(populateOpts)
      .lean();

    if (!data) {
      return responseAction.error(res, null, 404);
    }
    notificationXacNhanSuCo(req, data, NOTIFICATION_ACTION.HUY_XAC_NHAN_SU_CO_DUONG_DAY, 'HuyXacNhanSuCoDuongDay', t);
    return responseAction.success(res, data);
  } catch (err) {
    console.log('err', err);
    responseAction.error(res, err);
  }
}

async function notificationXacNhanSuCo(req, suCoData, notificationType, payloadType, t) {
  if (!notificationType) return;
  const caiDat = await CaiDatVanHanhService.getOne({}, { vai_tro_nhan_thong_bao_su_co_id: 1 });

  const donViId = suCoData?.duong_day_id?.don_vi_id;
  const vaiTroNhanThongBaoId = caiDat?.vai_tro_nhan_thong_bao_su_co_id;
  if (donViId && vaiTroNhanThongBaoId) {
    let userData = [];
    for (let i = 0; i < vaiTroNhanThongBaoId.length; i++) {
      const vaiTroId = vaiTroNhanThongBaoId[i];
      const userResponse = await UserService.getAll({ role_id: vaiTroId, don_vi_id: donViId }, { full_name: 1 });
      userData = [...userData, ...userResponse];
    }

    const userNotiId = extractIds(userData).filter(userId => userId?.toString() !== req.user._id?.toString());
    await NotificationService.notification(NOTIFICATION_TYPE.SYSTEM_TO_USER, dbCollections.SU_CO_DUONG_DAY,
      { _id: suCoData._id }, userNotiId, notificationType, suCoData, payloadType, t);
  }
}
