import request from 'request';
import SU_CO_DUONG_DAY, { DOI_TUONG_SU_CO } from './suCoDuongDay.model';
import CAI_DAT_HE_THONG from '../../CaiDatHeThong/caiDatHeThong.model';
import * as dbCollections from '../../../constant/dbCollections';

import { extractIds, extractKeys } from '../../../utils/dataconverter';
import { formatUnique } from '../../../common/functionCommons';
import * as DuongDayService from '../DuongDay/duongDay.service';
import * as ValidatorHelper from '../../../helpers/validatorHelper';
import * as VanHanhService from '../VanHanh/vanHanh.service';
import * as ViTriService from '../ViTri/viTri.service';
import * as KhoangCotService from '../KhoangCot/khoangCot.service';
import * as RoleService from '../../Role/role.service';
import * as DonViService from '../../DonVi/donVi.service';
import * as NotificationService from '../../Notification/notification.service';

import { create } from '../../RBAC/permissionHelper';
import resources from '../../RBAC/Resources';
import actions from '../../RBAC/Actions';
import UserService from '../../User/user.service';
import { NOTIFICATION_ACTION, NOTIFICATION_TYPE } from '../../Notification/notification.constants';
import { getAllParentChildDonVi } from '../../DonVi/donVi.service';

export function getAll(query, projection = {}) {
  return SU_CO_DUONG_DAY.find(query, projection).lean();
}

export async function updateAllByKinkeiId(arrData) {
  await SU_CO_DUONG_DAY.bulkWrite(
    arrData.map((row) =>
      ({
        updateOne: {
          filter: { kinkei_id: row.kinkei_id },
          update: { $set: row },
          upsert: true,
        },
      }),
    ),
  );
}

export async function createMany(dataList = []) {
  if (!Array.isArray(dataList)) {
    throw new Error('Input must be an array');
  }

  const validRecords = [];

  for (const data of dataList) {
    const { error, value } = validate(data);
    if (!error) {
      validRecords.push(value);
    }
  }

  return SU_CO_DUONG_DAY.insertMany(validRecords);
}

export async function updateAll(dataUpdate, options = {}) {
  options.new = true;
  const results = [];
  for (const row of dataUpdate) {
    try {
      const { error, value } = validate(row);
      if (!error) {
        const updated = await SU_CO_DUONG_DAY.findByIdAndUpdate(value._id, value, options);
        if (updated) results.push(updated);
      }
    } catch {
    }
  }
  return results;
}

const Joi = require('joi');

const objSchema = Joi.object({
  duong_day_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Đường dây')),
  vi_tri_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Vị trí')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}


export async function getDataSuCo() {
  try {
    const setting = await CAI_DAT_HE_THONG.findOne({}, { api_kinkei: 1 }).lean();
    if (!setting || !setting.api_kinkei) return null;
    return new Promise((resolve, reject) => {
      request(setting.api_kinkei, { json: true, timeout: 10000 }, (err, res, body) => {
        if (err) {
          reject(err);
        } else {
          resolve(body?.fl_results || body?.data);
        }
      });
    });
  } catch (e) {
    console.log('e', e);
    return null;
  }
}

async function getViTriByDuongDay(duongDayId) {
  const allDuongDayInScopeId = await DuongDayService.getAllInScope(duongDayId);
  const allVanHanh = await VanHanhService.getAll({
    duong_day_id: { $in: allDuongDayInScopeId },
    is_deleted: false,
  }).lean();
  const allVitriInDuongDayIds = allVanHanh.map(vanHanh => vanHanh.vi_tri_id);
  const viTrisQuery = { _id: { $in: allVitriInDuongDayIds } };
  return ViTriService.getAll(viTrisQuery, { ten_vi_tri: 1 });
}

export async function autoSyncSuCo() {
  const apiResponse = await getDataSuCo();
  if (!apiResponse || !Array.isArray(apiResponse)) return;

  const lineName = formatUnique(extractKeys(apiResponse, 'line_name'));
  const allDuongDay = await DuongDayService.getAll(
    { ten_kinkei: lineName },
    { ten_kinkei: 1 },
  );

  let dataSync = [];
  for (let i = 0; i < apiResponse.length; i++) {
    const suCo = apiResponse[i];
    for (let j = 0; j < allDuongDay.length; j++) {
      const duongDay = allDuongDay[j];
      if (suCo.line_name === duongDay.ten_kinkei) {
        const dataPush = {
          kinkei_id: suCo.id,
          pha: [suCo.fault_phase],
          thoi_gian_phat_hien: suCo.trigger_time,
          duong_day_id: duongDay._id,
          phan_mem_phat_hien: 'Kinkei',
          doi_tuong: DOI_TUONG_SU_CO.TAI_SAN_DON_VI,
          mo_ta: suCo.fault_location.map(fault => `${fault.distance_km} (km) từ ${fault.from}`).join(', '),
          is_deleted: false,
          da_xu_ly: false,
          suCo,
        };
        dataSync.push(dataPush);
      }
    }
  }
  const kinkeiId = extractKeys(dataSync, 'kinkei_id');
  const suCoExist = await getAll({ kinkei_id: kinkeiId }, { kinkei_id: 1 });
  const kinkeiIdExist = extractKeys(suCoExist, 'kinkei_id');

  dataSync = dataSync.filter(data => !kinkeiIdExist.includes(data.kinkei_id?.toString()));

  // find khoang cot
  for (let i = 0; i < dataSync.length; i++) {
    const viTriId = await getViTriByDuongDay(dataSync[i].duong_day_id);

    const viTriBatDauId = viTriId.find(viTri => viTri.ten_vi_tri === dataSync[i].suCo.fault_section?.[1].from);
    const viTriKetThucId = viTriId.find(viTri => viTri.ten_vi_tri === dataSync[i].suCo.fault_section?.[1].to);
    delete dataSync[i].suCo;
    if (viTriBatDauId && viTriKetThucId) {
      const khoangCotId = await KhoangCotService.getOne(
        { vi_tri_bat_dau_id: viTriBatDauId._id, vi_tri_ket_thuc_id: viTriKetThucId._id },
        { _id: 1 },
      );
      if (khoangCotId) {
        dataSync[i].khoang_cot_id = khoangCotId?._id;
      }
    }
  }

  if (dataSync.length) {
    await updateAllByKinkeiId(dataSync);
    await handleNotification(dataSync);
  }
}

async function handleNotification(dataSync) {
  const duongDayId = extractKeys(dataSync, 'duong_day_id');
  const allDuongDay = await DuongDayService.getAll({ _id: duongDayId }, { don_vi_id: 1 });
  const allDonViId = extractKeys(allDuongDay, 'don_vi_id');
  const donViInScope = await DonViService.getAllParentChildDonVi(allDonViId);

  const queryRermissions = {
    $or: [
      { permissions: create(resources.SU_CO_DUONG_DAY, actions.CREATE) },
      { permissions: create(resources.SU_CO_DUONG_DAY, actions.UPDATE) },
    ],
  };

  const roleData = await RoleService.getAll(queryRermissions, { _id: 1 });
  const roleDataId = extractIds(roleData);

  const userRoleSuCo = [];
  for (let i = 0; i < roleDataId.length; i++) {
    const userData = await UserService.getAll({ role_id: roleDataId[i], don_vi_id: donViInScope }, { _id: 1 });
    userRoleSuCo.push(...userData);
  }

  const userPermissionSuCo = await UserService.getAll({ ...queryRermissions, don_vi_id: donViInScope }, { _id: 1 });
  userRoleSuCo.push(...userPermissionSuCo);

  const userRoleSuCoId = formatUnique(extractIds(userRoleSuCo));

  if (!userRoleSuCoId.length) return;
  await NotificationService.notification(NOTIFICATION_TYPE.SYSTEM_TO_USER, dbCollections.SU_CO_DUONG_DAY,
    { so_luong_su_co: dataSync.length }, userRoleSuCoId, NOTIFICATION_ACTION.SU_CO_DUONG_DAY,
    null, 'SuCoDuongDayMoi', null);
}
