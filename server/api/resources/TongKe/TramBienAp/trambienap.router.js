import express from 'express';
import passport from 'passport';
import * as tramBienApController from './trambienap.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import TongKePermission from '../../RBAC/permissions/TongKePermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const tramBienApRouter = express.Router();
tramBienApRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
tramBienApRouter.post('*', authorizationMiddleware([TongKePermission.CREATE]));
tramBienApRouter.get('*', authorizationMiddleware([TongKePermission.READ]));
tramBienApRouter.put('*', authorizationMiddleware([TongKePermission.UPDATE]));
tramBienApRouter.delete('*', authorizationMiddleware([TongKePermission.DELETE]));
tramBienApRouter
  .route('/')
  .get(tramBienApController.getAll)
  .post(tramBienApController.create);

tramBienApRouter
  .route('/:id')
  .get(tramBienApController.findOne)
  .delete(tramBienApController.remove)
  .put(tramBienApController.update);
