import * as ValidatorHelper from '../../../helpers/validatorHelper';
import TramBienAp from './trambienap.model';
import { checkNumber, convertDate, convertYear } from '../../../helpers/checkDataHelper';
import { checkCommonInfo, checkThietBiCommonInfo, thietBiCommonInfo } from '../TongKe.service';

export function getAll(query, projection = {}) {
  return TramBienAp.find(query, projection).lean();
}

export function count(query) {
  return TramBienAp.count(query);
}

export function getOne(query) {
  return TramBienAp.findOne(query).lean();
}

export async function updateAll(arrData) {
  await TramBienAp.bulkWrite(
    arrData.map((row) =>
      ({
        updateOne: {
          filter: { _id: row._id },
          update: { $set: row },
          upsert: false,
        },
      }),
    ),
  );
}

const Joi = require('joi');

const objSchema = Joi.object({
  ma_tba: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã trạm biến áp')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function deleteAll(query) {
  return TramBienAp.remove(query)
}
