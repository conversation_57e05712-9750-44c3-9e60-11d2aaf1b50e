import * as Service from './trambienap.service';
import * as controllerHelper from '../../../helpers/controllerHelper';

import Model from './trambienap.model';


const populateOpts = [{ path: 'don_vi_id', select: 'ten_don_vi' }];
const uniqueOpts = [{ field: 'ma_tba', message: 'TBA_CODE', checkDeleted: true }];
const searchLike = ['ten_tba', 'ma_tba'];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, searchLike, populateOpts, null, 'don_vi_id');
