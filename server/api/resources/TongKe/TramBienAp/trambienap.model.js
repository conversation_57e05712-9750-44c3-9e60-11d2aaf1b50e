import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import { TRAM_BIEN_AP, DON_VI } from '../../../constant/dbCollections';
import { TINH_TRANG_VAN_HANH } from '../../DanhMuc/TinhTrangVanHanh';

const schema = new Schema({
  ma_tba: { type: String, required: true, unique: true, validate: /\S+/ },
  ten_tba: { type: String },
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  tinh_trang_van_hanh: {
    type: String,
    enum: Object.values(TINH_TRANG_VAN_HANH),
    default: TINH_TRANG_VAN_HANH.VAN_HANH,
  },
  cap_tba: { type: String },
  kinh_do: { type: String },
  vi_do: { type: String },
  polygon: {
    type: [{
      lng: { type: Number, required: true },
      lat: { type: Number, required: true },
    }],
    validate: {
      validator: function(v) {
        return Array.isArray(v) && v.every(point =>
          typeof point.lng === 'number' &&
          typeof point.lat === 'number',
        );
      },
      message: 'Mỗi điểm phải có dạng { lng: Number, lat: Number }',
    },
  },
  ghi_chu: String,

  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.index({ don_vi_id: 1 });
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(TRAM_BIEN_AP, schema, TRAM_BIEN_AP);
