
import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import { DON_VI, DUONG_DAY, DUONG_DAY_CHUNG, LOAI_DUONG_DAY } from '../../../constant/dbCollections';
import { removeAccents } from '../../../common/functionCommons';
import { TINH_TRANG_VAN_HANH } from '../../DanhMuc/TinhTrangVanHanh';

const schema = new Schema({
  ten_duong_day: { type: String, required: true, validate: /\S+/ },
  ma_thiet_bi_cha: { type: String, validate: /\S+/ },
  ma_tim_kiem: { type: String, validate: /\S+/ },
  ma_tim_kiem_cha: { type: String, validate: /\S+/ },
  ten_khong_dau: { type: String },
  ma_duong_day: { type: String, required: true, validate: /\S+/ },
  ten_kinkei: { type: String },
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  duong_day_chinh_id: { type: Schema.Types.ObjectId, ref: DUONG_DAY },
  duong_day_chung_id: { type: Schema.Types.ObjectId, ref: DUONG_DAY_CHUNG },
  duong_day_cu_id: { type: Schema.Types.ObjectId, ref: DUONG_DAY },
  loai_duong_day_id: { type: Schema.Types.ObjectId, ref: LOAI_DUONG_DAY },
  chieu_dai: { type: Number },
  is_mach: Boolean,
  thu_tu: { type: Number },
  ghi_chu: String,
  asset_id: String,
  dao_nguoc_thong_so: { type: Boolean, default: false },
  i: String,
  p: String,
  q: String,
  u: String,
  thoi_diem_thong_so: String,
  thoi_gian_dong_bo: Date,
  tinh_trang_van_hanh: {
    type: String,
    enum: Object.values(TINH_TRANG_VAN_HANH),
    default: TINH_TRANG_VAN_HANH.VAN_HANH,
  },
  i_dinh_muc: String,
  is_deleted: { type: Boolean, default: false, select: false },
  ngay_do_dien_tro_ke_tiep: { type: Date },
  ngay_do_nhiet_do_ke_tiep: { type: Date },
  so_ngay_dinh_ky_do_dien_tro: { type: Number },
  so_ngay_dinh_ky_do_nhiet_do: { type: Number },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.pre('save', function(next) {
  const user = formatName(this);
  next();
});

schema.pre('findOneAndUpdate', function(next) {
  this._update = formatName(this._update);
  next();
});

function formatName(dataInput) {
  if (dataInput?.ten_duong_day) {
    dataInput.ten_khong_dau = removeAccents(dataInput.ten_duong_day);
  }
  return dataInput;
}

// Optimized compound indexes for DuongDay queries
// Primary index for organizational filtering with soft delete
schema.index({ 
  don_vi_id: 1, 
  is_deleted: 1, 
  ten_duong_day: 1 
}, { 
  background: true,
  name: 'idx_don_vi_deleted_ten'
});

// Index for type-based filtering with organizational context
schema.index({ 
  loai_duong_day_id: 1, 
  don_vi_id: 1, 
  is_deleted: 1 
}, { 
  background: true,
  name: 'idx_loai_don_vi_deleted'
});

// Index for name-based searches and populate operations
schema.index({ 
  ten_duong_day: 1, 
  is_deleted: 1, 
  created_at: -1 
}, { 
  background: true,
  name: 'idx_ten_deleted_time'
});

// Index for asset-based queries
schema.index({ 
  asset_id: 1, 
  is_deleted: 1 
}, { 
  background: true,
  sparse: true,
  name: 'idx_asset_deleted'
});

// Index for operational status filtering
schema.index({ 
  tinh_trang_van_hanh: 1, 
  don_vi_id: 1, 
  is_deleted: 1 
}, { 
  background: true,
  name: 'idx_tinh_trang_don_vi_deleted'
});

// Index for hierarchical queries (parent-child relationships)
schema.index({ 
  duong_day_chinh_id: 1, 
  is_deleted: 1 
}, { 
  background: true,
  sparse: true,
  name: 'idx_duong_day_chinh_deleted'
});

// Index for code-based searches
schema.index({ 
  ma_duong_day: 1, 
  don_vi_id: 1, 
  is_deleted: 1 
}, { 
  background: true,
  name: 'idx_ma_don_vi_deleted'
});
schema.plugin(mongoosePaginate);
export default mongoose.model(DUONG_DAY, schema, DUONG_DAY);
