import express from 'express';
import passport from 'passport';
import * as Controller from './duongDay.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import TongKePermission from '../../RBAC/permissions/TongKePermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const duongDayRouter = express.Router();
duongDayRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
duongDayRouter.post('*', authorizationMiddleware([TongKePermission.CREATE]));
duongDayRouter.put('*', authorizationMiddleware([TongKePermission.UPDATE]));
duongDayRouter.delete('*', authorizationMiddleware([TongKePermission.DELETE]));
duongDayRouter
  .route('/')
  .get(Controller.getAll)
  .post(Controller.create);
duongDayRouter
  .route('/tree/dashboard')
  .get(Controller.getAllDuongDayTree);
duongDayRouter
  .route('/tree')
  .get(Controller.getAllTree);
duongDayRouter
  .route('/tree/doitruyentai')
  .get(Controller.getDuongDayByDonViQuanLy);

duongDayRouter
  .route('/tree/hosovanhanh')
  .get(Controller.getDuongDayByDonViQuanLy);

duongDayRouter
  .route('/duongday')
  .get(Controller.getAllDuongDay);

duongDayRouter
  .route('/download')
  .get(Controller.downloadDanhSachDuongDay);

duongDayRouter
  .route('/traoluucongsuat')
  .get(Controller.getDataTraoLuuCongSuat);

duongDayRouter
  .route('/downloadcongsuat')
  .get(Controller.downloadTraoLuuCongSuat);

duongDayRouter
  .route('/thongsomulti')
  .get(Controller.syncThongSoMulti);

duongDayRouter
  .route('/donvi/:id/duongdaychinh')
  .get(Controller.getAllDuongDayChinhByDonViId);

duongDayRouter
  .route('/sucotrenday')
  .get(Controller.getAllSuCoTrenDay);

duongDayRouter
  .route('/incorrect')
  .get(Controller.getAllInCorrectData);

duongDayRouter
  .route('/danh-sach-duong-day')
  .get(Controller.getDanhSachDuongDay);

duongDayRouter
  .route('/gop-duong-day')
  .post(Controller.gopDuongDay);

duongDayRouter
  .route('/:id/tach-duong-day')
  .post(authorizationMiddleware([TongKePermission.CREATE]), Controller.tachDuongDay);

duongDayRouter
  .route('/:id/khoi-phuc')
  .put(Controller.khoiPhucDuongDayDaXoa);

duongDayRouter
  .route('/:id/thongso')
  .get(Controller.syncThongSoById);

duongDayRouter
  .route('/:id/thong-so-theo-thoi-gian')
  .get(Controller.getThongSoTheoThoiGian);

duongDayRouter
  .route('/:id/tontai')
  .get(Controller.getAllPowerlineIdWithOldId);

duongDayRouter
  .route('/:id/vitri')
  .get(Controller.getAllViTri);

duongDayRouter
  .route('/:id/vitriwithoutchild')
  .get(Controller.getAllViTriWithoutChild);

duongDayRouter
  .route('/:id/giaocheo')
  .get(Controller.getAllGiaoCheo);

duongDayRouter
  .route('/:id/tongke')
  .get(Controller.tongKeDuongDay);

duongDayRouter
  .route('/:id/baocaokhoangcachphadat')
  .get(Controller.getDataBaoCaoKhoangCachPhaDat);

duongDayRouter
  .route('/:id/baocaodientrotiepdia')
  .get(Controller.getDataBaoCaoDienTroTiepDia);

duongDayRouter
  .route('/:id/baocaonhietdotiepxuc')
  .get(Controller.getDataBaoCaoNhietDoTiepXuc);

duongDayRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
