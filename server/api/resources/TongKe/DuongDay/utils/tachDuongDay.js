import { convertObject, extractIds, extractKeyObjectIds, extractKeys } from '../../../../utils/dataconverter';
import CommonError from '../../../../error/CommonError';
import { TINH_TRANG_VAN_HANH } from '../../../DanhMuc/TinhTrangVanHanh';
import { cloneObj, formatUnique } from '../../../../common/functionCommons';

import * as DuongDayService from '../duongDay.service';
import * as VanHanhService from '../../VanHanh/vanHanh.service';
import * as KetQuaKiemTraService from '../../../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.service';
import * as KhoangCotService from '../../KhoangCot/khoangCot.service';
import * as PhieuGiaoViecService from '../../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';
import * as CongViecPhatSinhService from '../../../QuanLyVanHanh/CongViecPhatSinh/congViecPhatSinh.service';
import * as DieuKienTienHanhService
  from '../../../QuanLyVanHanh/PhieuCongTac/DieuKienTienHanh/dieuKienTienHanh.service';
import * as KetQuaSuaChuaKhongKeHoachService
  from '../../../QuanLyVanHanh/KetQuaSuaChuaKhongKeHoach/ketQuaSuaChuaKhongKeHoach.service';
import * as ThuTucCatDienService from '../../../QuanLyVanHanh/PhieuCongTac/ThuTucCatDien/thuTucCatDien.service';
import * as TonTaiCapTrenService from '../../../QuanLyVanHanh/PhieuGiaoViec/TonTaiCapTren/tonTaiCapTren.service';
import * as DongBoDuLieuDoService from '../../../Report/DongBoDuLieuDo/dongBo.service';
import * as HanhLangTuyenService from '../../HanhLangTuyen/hanhLangTuyen.service';
import * as NghiemThuHanhLangTuyenService from '../../NghiemThuHanhLangTuyen/nghiemThuHangLangTuyen.service';
import * as SuCoDuongDayService from '../../SuCoDuongDay/suCoDuongDay.service';
import * as TongKeMoiNoiService from '../../TongKeMoiNoi/tongKeMoiNoi.service';

import DUONG_DAY from '../duongDay.model';
import { Schema } from 'mongoose';
import { KHOANG_COT, VI_TRI } from '../../../../constant/dbCollections';

async function taoDuongDayMoi(values) {
  const viTriIdMaDuongDay = convertObject(values, 'ma_duong_day');

  const viTriId = values.map(element => element.vi_tri_id).flat();
  const allKhoangCot = await KhoangCotService.getAll(
    { vi_tri_id: viTriId, is_deleted: false },
    { vi_tri_id: 1 },
  );
  console.log('allKhoangCot', allKhoangCot);
  const dataCreated = await DuongDayService.createAll(values);
  return dataCreated
    .map(duongDay => {
      duongDay = cloneObj(duongDay);

      const viTriId = viTriIdMaDuongDay[duongDay.ma_duong_day].vi_tri_id;
      const khoangCotId = allKhoangCot
        .filter(kc => viTriId.includes(kc.vi_tri_id.toString()))
        .map(kc => kc._id.toString());


      duongDay.vi_tri_id = viTriId;
      duongDay.khoang_cot_id = khoangCotId;
      return duongDay;
    });
}

async function chuyenVanHanh(duongDayGocId, duongDayMoiByViTri, viTriId) {
  // vi_tri_id      ObjectId
  // duong_day_id   ObjectId

  return updateByViTriAndDuongDay(duongDayGocId, duongDayMoiByViTri, viTriId, VanHanhService);
}

async function updateByViTriAndDuongDay(duongDayGocId, duongDayMoiByViTri, viTriId, service) {
  const dataCurrent = await service.getAll(
    { vi_tri_id: viTriId, duong_day_id: duongDayGocId, is_deleted: false },
  );
  const dataUpdate = cloneObj(dataCurrent).map(doc => {
    doc.duong_day_id = duongDayMoiByViTri[doc.vi_tri_id];
    return doc;
  });

  return service.updateAll(dataUpdate);
}

async function chuyenKhoangCot(duongDayGocId, duongDayMoiByViTri, viTriId) {
  // vi_tri_id      ObjectId
  // duong_day_id   List ObjectId
  return updateByViTriAndDuongDayList(duongDayGocId, duongDayMoiByViTri, viTriId, KhoangCotService);
}


async function updateByViTriAndDuongDayList(duongDayGocId, duongDayMoiByViTri, viTriId, service) {
  const dataCurrent = await service.getAll(
    { vi_tri_id: viTriId, duong_day_id: duongDayGocId, is_deleted: false },
  );
  const dataUpdate = dataCurrent.map(doc => {
    const duongDayId = doc.duong_day_id.map(duongDayId => {
      return duongDayId.toString() === duongDayGocId.toString() ? duongDayMoiByViTri[doc.vi_tri_id] : duongDayId;
    });
    doc.duong_day_id = formatUnique(duongDayId);
    return doc;
  });
  await service.updateAll(dataUpdate, { timestamps: false });
}


async function updateByKhoangCotAndDuongDay(duongDayGocId, duongDayMoiByKhoangCot, khoangCotId, service) {
  const orQuery = khoangCotId.map(khoangCot => ({ khoang_cot_id: khoangCot }));
  const query = { duong_day_id: duongDayGocId, is_deleted: false };
  if (orQuery.length) query.$or = orQuery;

  const dataCurrent = await service.getAll(query);

  const dataUpdate = cloneObj(dataCurrent).map(doc => {
    doc.duong_day_id = duongDayMoiByKhoangCot[doc.khoang_cot_id];
    return doc;
  });

  return service.updateAll(dataUpdate, { timestamps: false });
}

async function updateByViTriAndKhoangCotAndDuongDayList(duongDayGocId, duongDayMoiByViTri, duongDayMoiByKhoangCot, viTriId, khoangCotId, service) {
  const orQuery = [
    ...viTriId.map(viTri => ({ vi_tri_id: viTri })),
    ...khoangCotId.map(khoangCot => ({ khoang_cot_id: khoangCot })),
  ];
  const query = { duong_day_id: duongDayGocId, is_deleted: false };
  if (orQuery.length) query.$or = orQuery;

  const dataCurrent = await service.getAll(query);

  const dataClone = [];
  dataCurrent.forEach(item => {
    const currentItem = cloneObj(item);
    delete currentItem._id;

    item.duong_day_id?.forEach(duongDayId => {
      if (item.vi_tri_id) {
        dataClone.push({
          ...currentItem,
          khoang_cot_id: null,
          duong_day_id: [duongDayMoiByViTri[item.vi_tri_id] || duongDayId],
        });
      }
      if (item.khoang_cot_id) {
        dataClone.push({
          ...currentItem,
          vi_tri_id: null,
          duong_day_id: [duongDayMoiByKhoangCot[item.khoang_cot_id] || duongDayId],
        });
      }
    });
  });

  const dataUpdate = cloneObj(dataCurrent);
  dataUpdate.forEach(item => item.is_deleted = true);
  dataClone.forEach(item => item.is_clone = true);

  await service.updateAll(dataUpdate, { timestamps: false });
  return service.createMany(dataClone);
}

async function chuyenPhieuGiaoViec(duongDayGocId, duongDayMoiId) {
  const dataCurrent = await PhieuGiaoViecService
    .getAll({ duong_day_ids: duongDayGocId, is_deleted: false }, { duong_day_ids: 1 });

  const dataUpdate = cloneObj(dataCurrent).map(doc => {
    const duongDayIds = [...doc.duong_day_ids, ...duongDayMoiId];
    doc.duong_day_ids = formatUnique(duongDayIds);
    return doc;
  });
  return PhieuGiaoViecService.updateAll(dataUpdate);
}

async function chuyenTonTai(duongDayGocId, duongDayMoiByViTri, duongDayMoiByKhoangCot, viTriId, khoangCotId) {
  // vi_tri_id      ObjectId
  // khoang_cot_id  ObjectId
  // duong_day_id   List ObjectId
  return updateByViTriAndKhoangCotAndDuongDayList(duongDayGocId, duongDayMoiByViTri, duongDayMoiByKhoangCot, viTriId, khoangCotId, KetQuaKiemTraService);
}


async function updateByViTriListAndKhoangCotListAndDuongDay(duongDayGocId, duongDayMoiByViTri, duongDayMoiByKhoangCot, viTriId, khoangCotId, service) {
  const orQuery = [
    ...viTriId.map(viTri => ({ vi_tri_id: viTri })),
    ...khoangCotId.map(khoangCot => ({ khoang_cot_id: khoangCot })),
  ];
  const query = { duong_day_id: duongDayGocId, is_deleted: false };
  if (orQuery.length) query.$or = orQuery;

  const dataCurrent = await service.getAll(query);

  // tách record multi vị trí, khoảng cột ra từng record có 1 vị trí hoặc 1 khoảng cột,
  const dataClone = [];
  dataCurrent.forEach(item => {
    const currentItem = cloneObj(item);
    delete currentItem._id;

    const viTriArr = Array.isArray(currentItem.vi_tri_id) ? currentItem.vi_tri_id : [];
    const khoangCotArr = Array.isArray(currentItem.khoang_cot_id) ? currentItem.khoang_cot_id : [];

    if (viTriArr.length) {
      viTriArr.forEach(vtId => {
        dataClone.push({
          ...currentItem,
          vi_tri_id: vtId,
          khoang_cot_id: null,
          duong_day_id: duongDayMoiByViTri[vtId] || duongDayGocId,
        });
      });
    }

    if (khoangCotArr.length) {
      khoangCotArr.forEach(kcId => {
        dataClone.push({
          ...currentItem,
          vi_tri_id: null,
          khoang_cot_id: kcId,
          duong_day_id: duongDayMoiByKhoangCot[kcId] || duongDayGocId,
        });
      });
    }
  });
  const dataUpdate = cloneObj(dataCurrent);
  dataUpdate.forEach(item => item.is_deleted = true);
  dataClone.forEach(item => item.is_clone = true);

  await service.updateAll(dataUpdate, { timestamps: false });
  return service.createMany(dataClone);
}

async function chuyenCvPhatSinh(duongDayGocId, duongDayMoiByViTri, duongDayMoiByKhoangCot, viTriId, khoangCotId) {
  // duong_day_id   ObjectId
  // vi_tri_id      List ObjectId
  // khoang_cot_id: List ObjectId
  return updateByViTriListAndKhoangCotListAndDuongDay(duongDayGocId, duongDayMoiByViTri, duongDayMoiByKhoangCot, viTriId, khoangCotId, CongViecPhatSinhService);
}

async function chuyenCvKoKeHoach(duongDayGocId, duongDayMoiByViTri, duongDayMoiByKhoangCot, viTriId, khoangCotId) {
  // duong_day_id   List ObjectId
  // vi_tri_id      ObjectId
  // khoang_cot_id: ObjectId
  return updateByViTriAndKhoangCotAndDuongDayList(duongDayGocId, duongDayMoiByViTri, duongDayMoiByKhoangCot, viTriId, khoangCotId, KetQuaSuaChuaKhongKeHoachService);
}

async function updateByDuongDay(duongDayGocId, duongDayMoiId, service) {
  const dataCurrent = await service.getAll({ duong_day_id: duongDayGocId, is_deleted: false });

  const dataClone = [];

  dataCurrent.forEach(item => {
    const currentItem = cloneObj(item);
    delete currentItem._id;

    duongDayMoiId.forEach(duongDayId => {
      dataClone.push({
        ...currentItem,
        duong_day_id: duongDayId,
      });
    });
  });

  const dataUpdate = cloneObj(dataCurrent);
  dataUpdate.forEach(item => item.is_deleted = true);
  dataClone.forEach(item => item.is_clone = true);

  await service.updateAll(dataUpdate, { timestamps: false });
  return service.createMany(dataClone);
}

async function updateByKhoangCotListAndDuongDay(duongDayGocId, duongDayMoiByKhoangCot, khoangCotId, service) {
  const orQuery = khoangCotId.map(khoangCot => ({ khoang_cot_id: khoangCot }));
  const query = { duong_day_id: duongDayGocId, is_deleted: false };
  if (orQuery.length) query.$or = orQuery;

  const dataCurrent = await service.getAll(query);

  const dataClone = [];
  dataCurrent.forEach(item => {
    const currentItem = cloneObj(item);
    delete currentItem._id;

    const khoangCotArr = Array.isArray(currentItem.khoang_cot_id) ? currentItem.khoang_cot_id : [];
    if (khoangCotArr.length) {
      khoangCotArr.forEach(kcId => {
        dataClone.push({
          ...currentItem,
          khoang_cot_id: kcId,
          duong_day_id: duongDayMoiByKhoangCot[kcId] || duongDayGocId,
        });
      });
    }
  });

  const dataUpdate = cloneObj(dataCurrent);
  dataUpdate.forEach(item => item.is_deleted = true);
  dataClone.forEach(item => item.is_clone = true);

  await service.updateAll(dataUpdate, { timestamps: false });
  return service.createMany(dataClone);

}

async function chuyenTongKeMoiNoi(duongDayGocId, duongDayMoiByKhoangCot, khoangCotId) {
  // duong_day_id   ObjectId
  return updateByKhoangCotAndDuongDay(duongDayGocId, duongDayMoiByKhoangCot, khoangCotId, TongKeMoiNoiService);
}

async function chuyenSuCoDuongDay(duongDayGocId, duongDayMoiByViTri, viTriId) {
  // vi_tri_id      ObjectId
  // duong_day_id   ObjectId
  return updateByViTriAndDuongDay(duongDayGocId, duongDayMoiByViTri, viTriId, SuCoDuongDayService);
}

async function chuyenNghiemThuHanhLangTuyen(duongDayGocId, duongDayMoiByKhoangCot, khoangCotId) {
  // duong_day_id   List ObjectId
  // khoang_cot_id ObjectId

  return updateByKhoangCotListAndDuongDay(duongDayGocId, duongDayMoiByKhoangCot, khoangCotId, NghiemThuHanhLangTuyenService);
}

async function chuyenHanhLangTuyen(duongDayGocId, duongDayMoiByKhoangCot, khoangCotId) {
  // duong_day_id   ObjectId
  return updateByKhoangCotAndDuongDay(duongDayGocId, duongDayMoiByKhoangCot, khoangCotId, HanhLangTuyenService);
}

async function chuyenDongBoDuLieuDo(duongDayGocId, duongDayMoiId) {
  // duong_day_id   ObjectId
  return updateByDuongDay(duongDayGocId, duongDayMoiId, DongBoDuLieuDoService);
}

async function chuyenTonTaiCapTren(duongDayGocId, duongDayMoiId) {
  // duong_day_id   ObjectId
  return updateByDuongDay(duongDayGocId, duongDayMoiId, TonTaiCapTrenService);
}

async function chuyenThuTucCatDien(duongDayGocId, duongDayMoiId) {
  // duong_day_id   ObjectId
  return updateByDuongDay(duongDayGocId, duongDayMoiId, ThuTucCatDienService);
}

async function chuyenDieuKienTienHanh(duongDayGocId, duongDayMoiId) {
  // duong_day_id   ObjectId
  return updateByDuongDay(duongDayGocId, duongDayMoiId, DieuKienTienHanhService);
}

async function handleTachDuongDay(duongDayGocId, duongDayMoiData) {
  const duongDayCurrent = await DuongDayService.getById(duongDayGocId);
  if (!duongDayCurrent) return { error: CommonError.NOT_FOUND };

  const viTriId = duongDayMoiData.map(element => element.vi_tri_id).flat();

  const duongDayCreate = duongDayMoiData.map(duongDay => {
    duongDay.tinh_trang_van_hanh = TINH_TRANG_VAN_HANH.VAN_HANH;
    duongDay.duong_day_cu_id = duongDayGocId;
    duongDay.don_vi_id = duongDayCurrent.don_vi_id;
    return duongDay;
  });

  // console.log('duongDayCreate', duongDayCreate);

  const duongDayMoi = await taoDuongDayMoi(duongDayCreate);

  console.log('duongDayMoi', duongDayMoi);

  // group đường dây mới theo mã vị trí
  const duongDayMoiByViTri = {}, duongDayMoiByKhoangCot = {};
  duongDayMoi.forEach(duongDay => {
    duongDay.vi_tri_id.forEach(vtId => {
      duongDayMoiByViTri[vtId] = duongDay._id;
    });
    duongDay.khoang_cot_id.forEach(kcId => {
      duongDayMoiByKhoangCot[kcId] = duongDay._id;
    });
  });
  const duongDayMoiId = extractIds(duongDayMoi);
  const khoangCotId = extractKeyObjectIds(duongDayMoi, 'khoang_cot_id').flat();


  const [
    kq_chuyen_van_hanh,
    kq_chuyen_khoang_cot,
    kq_chuyen_phieu_giao_viec,
    kq_chuyen_ton_tai,
    kq_chuyen_cv_phat_sinh,
    kq_chuyen_cv_ko_ke_hoach,
    kq_chuyen_dieu_kien_tien_hanh,
    kq_chuyen_thu_tuc_cat_dien,
    kq_chuyen_ton_tai_cap_tren,
    kq_chuyen_dong_bo_du_lieu_do,
    kq_chuyen_hanh_lang_tuyen,
    kq_chuyen_nghiem_thu_hanh_lang_tuyen,
    kq_chuyen_su_co_duong_day,
    kq_chuyen_tong_ke_moi_noi,
  ] = await Promise.all([
    chuyenVanHanh(duongDayGocId, duongDayMoiByViTri, viTriId),
    chuyenKhoangCot(duongDayGocId, duongDayMoiByViTri, viTriId),
    chuyenPhieuGiaoViec(duongDayGocId, duongDayMoiId),
    chuyenTonTai(duongDayGocId, duongDayMoiByViTri, duongDayMoiByKhoangCot, viTriId, khoangCotId),
    chuyenCvPhatSinh(duongDayGocId, duongDayMoiByViTri, duongDayMoiByKhoangCot, viTriId, khoangCotId),
    chuyenCvKoKeHoach(duongDayGocId, duongDayMoiByViTri, duongDayMoiByKhoangCot, viTriId, khoangCotId),
    chuyenDieuKienTienHanh(duongDayGocId, duongDayMoiId),
    chuyenThuTucCatDien(duongDayGocId, duongDayMoiId),
    chuyenTonTaiCapTren(duongDayGocId, duongDayMoiId),
    chuyenDongBoDuLieuDo(duongDayGocId, duongDayMoiId),
    chuyenHanhLangTuyen(duongDayGocId, duongDayMoiByKhoangCot, khoangCotId),
    chuyenNghiemThuHanhLangTuyen(duongDayGocId, duongDayMoiByKhoangCot, khoangCotId),
    chuyenSuCoDuongDay(duongDayGocId, duongDayMoiByViTri, viTriId),
    chuyenTongKeMoiNoi(duongDayGocId, duongDayMoiByKhoangCot, khoangCotId),
  ]);

  return {
    kq_chuyen_van_hanh,
    kq_chuyen_khoang_cot,
    kq_chuyen_phieu_giao_viec,
    kq_chuyen_ton_tai,
    kq_chuyen_cv_phat_sinh,
    kq_chuyen_cv_ko_ke_hoach,
    kq_chuyen_dieu_kien_tien_hanh,
    kq_chuyen_thu_tuc_cat_dien,
    kq_chuyen_ton_tai_cap_tren,
    kq_chuyen_dong_bo_du_lieu_do,
    kq_chuyen_hanh_lang_tuyen,
    kq_chuyen_nghiem_thu_hanh_lang_tuyen,
    kq_chuyen_su_co_duong_day,
    kq_chuyen_tong_ke_moi_noi,
  };
}

export default handleTachDuongDay;
