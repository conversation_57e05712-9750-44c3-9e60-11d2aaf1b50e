import momentTimezone from 'moment-timezone';
import * as ValidatorHelper from '../../../helpers/validatorHelper';
import DUONG_DAY from './duongDay.model';
import * as CotDienService from '../CotDien/cotDien.service';
import * as KhoangCotService from '../KhoangCot/khoangCot.service';
import * as TiepDatService from '../TiepDat/tiepDat.service';
import * as DayChongSetService from '../DayChongSet/dayChongSet.service';
import * as DayCapQuangService from '../DayCapQuang/dayCapQuang.service';
import * as GiaoCheoService from '../GiaoCheo/giaoCheo.service';
import * as VanHanhService from '../VanHanh/vanHanh.service';
import * as ViTriCongViecService from '../../QuanLyVanHanh/ViTriCongViec/viTriCongViec.service';
import * as PhieuKiemTraService from '../../QuanLyVanHanh/PhieuGiaoViec/PhieuKiemTra/phieuKiemTra.service';
import { getDonViQuery } from '../../DonVi/donVi.service';
import VI_TRI from '../ViTri/viTri.model';
import { fisrtDayOfCurrentMonth } from '../../../utils/dateUtil';
import { extractIds, groupBy } from '../../../utils/dataconverter';
import { TRANG_THAI_PHIEU } from '../../DanhMuc/TrangThaiCongViec';
import { getDataPmis, logSyncPmis } from '../SyncPmis/syncPmis.service';
import PMIS_SOAP_NAME from '../SyncPmis/soapName';
import { downlineData } from '../../../common/functionCommons';
import * as PhieuGiaoViecService from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';
import * as PhieuCongTacService from '../../QuanLyVanHanh/PhieuCongTac/phieuCongTac.service';
import { CAP_DON_VI } from '../../../constant/constant';
import { Types } from 'mongoose';
import { DO_THONG_SO, KIEM_TRA, LOAI_CONG_VIEC } from '../../DanhMuc/LoaiCongViec';

export async function createAll(dataCreate) {
  const promiseCreate = [];
  let errorCreate = null;
  for (const row of dataCreate) {
    const { error, value } = validate(row);
    if (error) {
      errorCreate = error;
      break;
    }
    promiseCreate.push(DUONG_DAY.create(value));
  }

  if (errorCreate) throw errorCreate;

  return Promise.all(promiseCreate);
}

export function getById(id, projection = {}) {
  return DUONG_DAY.findById(id, projection).lean();
}

export function getOneAndUpdate(filter, update = {}) {
  return DUONG_DAY.findOneAndUpdate(filter, update).lean();
}

export function getAll(query, projection = {}) {
  return DUONG_DAY.find(query, projection).lean();
}

export function getOne(query, projection = {}) {
  return DUONG_DAY.findOne(query, projection).lean();
}

export function remove(query) {
  return DUONG_DAY.deleteMany(query).lean();
}

export function deleteAll(query) {
  return DUONG_DAY.remove(query);
}

export function updateMany(filter, update) {
  return DUONG_DAY.updateMany(filter, update);
}


export async function getThongSo(assetId, isDaoNguoc) {
  let thongSo = await getDataThongSoDuongDayPmis(assetId, isDaoNguoc);
  if (!thongSo) {
    thongSo = await getDataThongSoDuongDayPmis(assetId, isDaoNguoc);
  }
  return thongSo;
}

async function getDataThongSoDuongDayPmis(assetId, isDaoNguoc = false) {
  if (!assetId) return null;
  const timeNow = momentTimezone().tz('Etc/GMT-7').set({ minute: 0, second: 0 });
  const startTime = momentTimezone(timeNow).tz('Etc/GMT-7')
    .subtract(1, 'minute')
    .subtract(24, 'hours')
    .format('MM/DD/YYYY HH:mm:ss');
  const endTime = momentTimezone(timeNow).tz('Etc/GMT-7').add(1, 'minute').format('MM/DD/YYYY HH:mm:ss');
  const query = { ID: assetId, TU_NGAY: startTime, DEN_NGAY: endTime };

  const dataPmis = await getDataPmis(PMIS_SOAP_NAME.THONG_SO_BY_DUONG_DAY_ID, query, 1000)
    .then(results => results.success ? results.data : [])
    .then(results => {
      if (!results) return [];
      const objDataPmis = groupBy(results, 'THOI_GIAN');
      return Object.values(objDataPmis).map(value => {
        return value?.reduce((prevValue, currentValue) => {
          if (currentValue) {
            prevValue.thoi_diem_thong_so = prevValue.thoi_diem_thong_so || currentValue.THOI_GIAN;
            prevValue.asset_id = currentValue.ASSETID;
            prevValue.thoi_gian_dong_bo = momentTimezone().tz('Etc/GMT-7');
            prevValue[currentValue.THONG_SO?.toLowerCase()] = currentValue.GIA_TRI;
          }
          return prevValue;
        }, {});
      });
    });

  dataPmis.sort((a, b) => b.thoi_diem_thong_so.localeCompare(a.thoi_diem_thong_so));
  const dataNewest = dataPmis[0];
  await logSyncPmis('THONG_SO_DUONG_DAY', { query, dataNewest });

  if (isDaoNguoc && dataNewest) {
    if (!isNaN(parseFloat(dataNewest.p))) {
      dataNewest.p = dataNewest.p * -1;
    }
    if (!isNaN(parseFloat(dataNewest.q))) {
      dataNewest.q = dataNewest.q * -1;
    }
  }
  return dataNewest;
}

const Joi = require('joi');

const objSchema = Joi.object({
  ten_duong_day: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên đường dây')),
  ma_duong_day: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã đường dây')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function getViTriByDuongDay(req, duongDayId, isReport = false) {
  const donViQuery = await getDonViQuery(req);
  const allDuongDayInScopeId = !isReport ? await getAllInScope(duongDayId) : duongDayId;
  const allVanHanh = await VanHanhService.getAll({
    duong_day_id: { $in: allDuongDayInScopeId },
    is_deleted: false,
  }).lean();
  const allVitriInDuongDayIds = allVanHanh.map(vanHanh => vanHanh.vi_tri_id);
  const viTrisQuery = {
    $and: [
      { _id: { $in: allVitriInDuongDayIds } },
      donViQuery.$in.length > 0 ? { don_vi_id: donViQuery } : {},
    ],
  };
  const allViTri = await VI_TRI.find(viTrisQuery).sort('thu_tu')
    .populate({ path: 'don_vi_id', populate: 'don_vi_cha_id' })
    .populate('cong_trinh_id')
    .lean();
  const viTriIds = allViTri.map(viTri => viTri._id);
  const vanHanhGroupByViTri = groupBy(allVanHanh, 'vi_tri_id');

  const allPhieuKiemTra = await getAllPhieuKiemTraTrongThang(duongDayId);
  const allPhieuKiemTraIds = extractIds(allPhieuKiemTra);
  const allViTriKiemTraTrongThang = await ViTriCongViecService.getAll({
    phieu_giao_viec_id: { $in: allPhieuKiemTraIds },
    is_deleted: false,
    vi_tri_id: { $exists: true },
  });
  const groupKiemTraByVitri = groupBy(allViTriKiemTraTrongThang, 'vi_tri_id');
  const allKhoangCotKiemTraTrongThang = await ViTriCongViecService.getAll({
    phieu_giao_viec_id: { $in: allPhieuKiemTraIds },
    is_deleted: false,
    khoang_cot_id: { $exists: true },
  });
  const groupKiemTraByKhoangCot = groupBy(allKhoangCotKiemTraTrongThang, 'khoang_cot_id');

  const khoangCotData = await KhoangCotService.getAll({ vi_tri_id: viTriIds, is_deleted: false })
    .populate('vi_tri_bat_dau_id vi_tri_ket_thuc_id');
  khoangCotData.forEach(khoangCot => {
    khoangCot.key_group = khoangCot.vi_tri_id?._id;
    khoangCot.so_phieu_kiem_tra_trong_thang_hien_tai = groupKiemTraByKhoangCot[khoangCot._id]?.length || 0;
  });
  const khoangCotGroupByViTri = groupBy(khoangCotData, 'key_group');

  allViTri.forEach(viTri => {
    const khoangCotId = khoangCotGroupByViTri[viTri._id] || [];
    viTri.khoang_cot_id = khoangCotId.map(khoangCot => {
      delete khoangCot.key_group;
      return khoangCot;
    });
    viTri.van_hanh = vanHanhGroupByViTri[viTri._id];
    viTri.so_phieu_kiem_tra_trong_thang_hien_tai = groupKiemTraByVitri[viTri._id]?.length || 0;
  });
  let dataRes = {};
  dataRes.vi_tris = allViTri;
  dataRes.vi_tri_ids = viTriIds;
  return dataRes;
}

async function getAllPhieuKiemTraTrongThang(duongDayId) {

  const loaiCongViecKiemTra = Object.values(LOAI_CONG_VIEC)
    .filter(congViec => congViec.type === KIEM_TRA)
    .map(loai => loai.code);

  return PhieuKiemTraService.getAll({
    // loai_cong_viec: loaiCongViecKiemTra,
    duong_day_ids: duongDayId,
    is_deleted: false,
    trang_thai_cong_viec: { $ne: TRANG_THAI_PHIEU.HUY_PHIEU.code },
    thoi_gian_cong_tac_bat_dau: { $gte: fisrtDayOfCurrentMonth() },
  });
}

export async function getViTriNameByDuongDay(req, duongDayId) {
  const donViQuery = await getDonViQuery(req);
  const allDuongDayInScopeId = await getAllInScope(duongDayId);
  const allVanHanh = await VanHanhService.getAll({
    duong_day_id: { $in: allDuongDayInScopeId },
  }).select('vi_tri_id').lean();
  const allVitriInDuongDayIds = allVanHanh.map(vanHanh => vanHanh.vi_tri_id);
  const viTrisQuery = {
    $and: [
      { _id: { $in: allVitriInDuongDayIds } },
      { don_vi_id: donViQuery },
    ],
  };
  const allViTri = await VI_TRI.find(viTrisQuery).sort('thu_tu')
    .select('ma_vi_tri ten_vi_tri thu_tu')
    .lean();
  const viTriIds = allViTri.map(viTri => viTri._id);

  const allPhieuKiemTra = await getAllPhieuKiemTraTrongThang(duongDayId);
  const allPhieuKiemTraIds = extractIds(allPhieuKiemTra);
  const allViTriKiemTraTrongThang = await ViTriCongViecService.getAll({
    phieu_giao_viec_id: { $in: allPhieuKiemTraIds },
    is_deleted: false,
    vi_tri_id: { $exists: true },
  });
  const groupKiemTraByVitri = groupBy(allViTriKiemTraTrongThang, 'vi_tri_id');
  const allKhoangCotKiemTraTrongThang = await ViTriCongViecService.getAll({
    phieu_giao_viec_id: { $in: allPhieuKiemTraIds },
    is_deleted: false,
    khoang_cot_id: { $exists: true },
  });
  const groupKiemTraByKhoangCot = groupBy(allKhoangCotKiemTraTrongThang, 'khoang_cot_id');

  const khoangCotData = await KhoangCotService.getAll({ vi_tri_id: viTriIds, is_deleted: false })
    .populate({ path: 'vi_tri_bat_dau_id', select: 'ma_vi_tri ten_vi_tri thu_tu' })
    .populate({ path: 'vi_tri_ket_thuc_id', select: 'ma_vi_tri ten_vi_tri thu_tu' })
    .populate({ path: 'vi_tri_id', select: 'ma_vi_tri ten_vi_tri thu_tu' })
    .select('vi_tri_bat_dau_id vi_tri_ket_thuc_id name ten_khoang_cot vi_tri_id');
  khoangCotData.forEach(khoangCot => {
    khoangCot.key_group = khoangCot.vi_tri_id?._id;
    khoangCot.so_phieu_kiem_tra_trong_thang_hien_tai = groupKiemTraByKhoangCot[khoangCot._id]?.length || 0;
  });
  const khoangCotGroupByViTri = groupBy(khoangCotData, 'key_group');
  allViTri.forEach(viTri => {
    viTri.khoang_cot_id = Array.isArray(khoangCotGroupByViTri[viTri._id])
      ? khoangCotGroupByViTri[viTri._id].map(khoangCot => {
        delete khoangCot.key_group;
        return khoangCot;
      })
      : [];
    viTri.so_phieu_kiem_tra_trong_thang_hien_tai = groupKiemTraByVitri[viTri._id]?.length || 0;
  });
  let dataRes = {};
  dataRes.vi_tris = allViTri;
  dataRes.vi_tri_ids = viTriIds;
  return dataRes;
}

export async function getViTriOnlyOnceDuongDay(req, duongDayId) {
  const donViQuery = await getDonViQuery(req);
  const allVanHanh = await VanHanhService.getAll({
    duong_day_id: duongDayId,
    is_deleted: false,
  }).select('vi_tri_id thu_tu').lean();

  const allVitriInDuongDayIds = allVanHanh.map(vanHanh => vanHanh.vi_tri_id);
  const mapThuTuVanHanh = Object.create(null);
  allVanHanh.forEach(vanHanh => {
    mapThuTuVanHanh[vanHanh.vi_tri_id] = vanHanh.thu_tu;
  });

  const viTrisQuery = {
    $and: [
      { _id: { $in: allVitriInDuongDayIds } },
      { don_vi_id: donViQuery },
    ],
  };
  const allViTri = await VI_TRI.find(viTrisQuery).sort('thu_tu')
    .select('ma_vi_tri ten_vi_tri thu_tu don_vi_id')
    .populate({ path: 'don_vi_id', select: 'ten_don_vi' })
    .populate({ path: 'cong_trinh_id', select: 'ten_cong_trinh' })
    .lean();

  allViTri.forEach(viTri => {
    viTri.thu_tu_van_hanh = mapThuTuVanHanh[viTri._id];
  });

  return allViTri;
}

export async function bangTongHopGiaoCheo(req, duongDayId) {

  const data = await getViTriByDuongDay(req, duongDayId);
  let allViTri = data.vi_tris;
  let viTriIds = data.vi_tri_ids;
  const giaoCheoData = (await GiaoCheoService
    .getAll({ vi_tri_id: viTriIds, is_deleted: false }))
    .filter(giaoCheo => giaoCheo.doi_tuong_giao_cheo?.trim().toLowerCase() !== 'không');
  const giaoCheoGroupByViTriId = groupBy(giaoCheoData, 'vi_tri_id');
  allViTri.forEach(viTri => {
    viTri.giao_cheo = giaoCheoGroupByViTriId[viTri._id];
  });


  function convertDataToRows(viTri, index) {
    let tenKhoangCot, chieuDaiKhoangCot;
    viTri.khoang_cot_id?.map(khoangCot => {
      tenKhoangCot ? tenKhoangCot = [tenKhoangCot, khoangCot.ten_khoang_cot].join('\n') : tenKhoangCot = khoangCot.ten_khoang_cot;
      chieuDaiKhoangCot ? chieuDaiKhoangCot = [chieuDaiKhoangCot, khoangCot.chieu_dai].join('\n') : chieuDaiKhoangCot = khoangCot.chieu_dai;
    });
    let doiTuongGiaoCheo, khoangCachGiaoCheo, giaoCheoDenViTri, giaoCheoDuongBo;
    let giaoCheoDuongThuy, bienCanhBaoAnToan, ghiChu;
    viTri.giao_cheo?.map(giaoCheo => {
      doiTuongGiaoCheo = downlineData(doiTuongGiaoCheo, giaoCheo?.doi_tuong_giao_cheo);
      khoangCachGiaoCheo = downlineData(khoangCachGiaoCheo, giaoCheo?.khoang_cach_giao_cheo);
      giaoCheoDenViTri = downlineData(giaoCheoDenViTri, giaoCheo?.khoang_cach_diem_giao_cheo_den_vi_tri);
      giaoCheoDuongBo = downlineData(giaoCheoDuongBo, giaoCheo?.bien_bao_giao_cheo_duong_bo);
      giaoCheoDuongThuy = downlineData(giaoCheoDuongThuy, giaoCheo?.bien_bao_giao_cheo_duong_thuy);
      bienCanhBaoAnToan = downlineData(bienCanhBaoAnToan, giaoCheo?.bien_canh_bao_an_toan);
      ghiChu = downlineData(ghiChu, giaoCheo?.ghi_chu);
    });

    return {
      stt: index + 1,
      ten_khoang_cot: tenKhoangCot || viTri?.ten_vi_tri,
      khoang_cot_id: viTri.khoang_cot_id,
      chieu_dai_khoang_cot: chieuDaiKhoangCot,
      giao_cheo: viTri.giao_cheo,
      doi_tuong_giao_cheo: doiTuongGiaoCheo,
      khoang_cach_giao_cheo: khoangCachGiaoCheo,
      khoang_cach_diem_giao_cheo_den_vi_tri: giaoCheoDenViTri,
      bien_bao_giao_cheo_duong_bo: giaoCheoDuongBo,
      bien_bao_giao_cheo_duong_thuy: giaoCheoDuongThuy,
      bien_canh_bao_an_toan: bienCanhBaoAnToan,
      ghi_chu: ghiChu,
    };
  }

  return allViTri
    .filter(vitri => vitri?.giao_cheo?.length > 0)
    .map(convertDataToRows);
}

export async function bangTongKeDuongDay(req, duongDayId) {
  const dataResponse = await getViTriByDuongDay(req, duongDayId);
  const allDuongDayInScopeId = await getAllInScope(duongDayId);
  let allViTri = dataResponse.vi_tris, viTriIds = dataResponse.vi_tri_ids;

  const giaoCheoData = await GiaoCheoService.getAll({ vi_tri_id: viTriIds, is_deleted: false });
  const dayCapQuangData = await DayCapQuangService.getAll({ vi_tri_id: viTriIds, is_deleted: false });
  const cotDienData = await CotDienService.getAll({ vi_tri_id: viTriIds, is_deleted: false });
  const tiepDatData = await TiepDatService.getAll({ vi_tri_id: viTriIds, is_deleted: false });
  const dayChongSetData = await DayChongSetService.getAll({ vi_tri_id: viTriIds, is_deleted: false });
  const vanHanhData = await VanHanhService.findAllInOne({
    vi_tri_id: { $in: viTriIds },
    duong_day_id: { $in: allDuongDayInScopeId },
    is_deleted: false,
  }) || [];
  const tiepDatGroupByViTriId = groupBy(tiepDatData, 'vi_tri_id');
  const giaoCheoGroupByViTriId = groupBy(giaoCheoData, 'vi_tri_id');
  const cotDienDataGroupByViTriId = groupBy(cotDienData, 'vi_tri_id');
  const dayCapQuangGroupByViTriId = groupBy(dayCapQuangData, 'vi_tri_id');
  const dayChongSetGroupByViTriId = groupBy(dayChongSetData, 'vi_tri_id');

  const mapVanHanh = {};
  vanHanhData.forEach(vanHanh => {
    mapVanHanh[vanHanh.vi_tri_id] = vanHanh;
  });

  let tongDoDai = 0;
  allViTri.forEach(viTri => {
    viTri.van_hanh = mapVanHanh[viTri._id] || {};
    viTri.day_cap_quang = dayCapQuangGroupByViTriId[viTri._id] || [];
    viTri.giao_cheo = giaoCheoGroupByViTriId[viTri._id] || [];
    viTri.day_chong_set = dayChongSetGroupByViTriId[viTri._id] || [];
    viTri.tiep_dat = tiepDatGroupByViTriId[viTri._id] || [];
    viTri.cot_dien = cotDienDataGroupByViTriId[viTri._id] || [];
    tongDoDai += parseFloat(viTri.khoang_cot) || 0;
  });

  let congDonTuDauDay = 0, congDonTuCuoiDay = tongDoDai;

  function convertDataToRows(viTri, index) {
    let doiTuongGiaoCheo, khoangCachGiaoCheo, giaoCheoDenViTri;
    let giaoCheoDuongBo, giaoCheoDuongThuy, bienCanhBaoAnToan;
    let tenKhoangCot;

    viTri.khoang_cot_id.map(khoangCot => {
      tenKhoangCot = downlineData(tenKhoangCot, khoangCot.ten_khoang_cot);
    });

    viTri.giao_cheo.forEach(giaoCheo => {
      giaoCheoDuongBo = downlineData(giaoCheoDuongBo, giaoCheo?.bien_bao_giao_cheo_duong_bo);
      doiTuongGiaoCheo = downlineData(doiTuongGiaoCheo, giaoCheo?.doi_tuong_giao_cheo);
      giaoCheoDenViTri = downlineData(giaoCheoDenViTri, giaoCheo?.khoang_cach_diem_giao_cheo_den_vi_tri);
      giaoCheoDuongThuy = downlineData(giaoCheoDuongThuy, giaoCheo?.bien_bao_giao_cheo_duong_thuy);
      bienCanhBaoAnToan = downlineData(bienCanhBaoAnToan, giaoCheo?.bien_canh_bao_an_toan);
      khoangCachGiaoCheo = downlineData(khoangCachGiaoCheo, giaoCheo?.khoang_cach_giao_cheo);

    });

    congDonTuDauDay += parseFloat(viTri.khoang_cot) || 0;
    congDonTuCuoiDay = tongDoDai - congDonTuDauDay;

    function getCachDienNeo(cachDienList = [], pha, huongLapDat) {
      return cachDienList.find(cd =>
        ((cd?.pha?.trim().toUpperCase() === pha) && (cd.chuc_nang?.trim().toUpperCase() === 'CHUỖI NÉO') && (cd?.huong_lap_dat?.trim().toUpperCase()) !== huongLapDat),
      ) || {};
    }

    function getCachDienChuoiDo(cachDienList = [], pha, type = 'CHUOI_DO_LEO') {
      if (type === 'CHUOI_DO_LEO') {
        return cachDienList.find(cd =>
          (cd?.pha?.trim().toUpperCase() === pha && cd?.chuc_nang?.trim().toUpperCase()) === 'CHUỖI ĐỠ LÈO',
        ) || {};
      } else {
        return viTri.van_hanh?.cach_dien_id?.find(cd =>
          (cd?.pha?.trim().toUpperCase() === pha && cd?.chuc_nang?.trim().toUpperCase().includes('CHUỖI ĐỠ') && !cd?.chuc_nang?.trim().toUpperCase().includes('CHUỖI ĐỠ LÈO')),
        ) || {};
      }
    }

    return {
      stt: index + 1,
      _id: viTri._id,
      ten_duong_day: viTri.van_hanh?.duong_day_id?.ten_duong_day,
      khoang_cot: viTri.khoang_cot,
      ten_vi_tri: viTri?.ten_vi_tri,
      ten_khoang_cot: tenKhoangCot,
      khoang_cot_id: viTri.khoang_cot_id,
      cong_don_tu_dau_day: congDonTuDauDay - viTri.khoang_cot,
      cong_don_tu_cuoi_day: congDonTuCuoiDay,
      goc_lai: viTri.goc_lai,
      khoang_neo: viTri.khoang_neo,
      khu_vuc: viTri.khu_vuc,
      xa: viTri.xa,
      huyen: viTri.huyen,
      tinh: viTri.tinh,
      hanh_lang_tuyen: viTri.hanh_lang_tuyen,
      so_do_bo_tri_day: viTri.so_do_bo_tri_day,
      dac_diem_dia_hinh: viTri.dac_diem_dia_hinh,
      vi_do: viTri.vi_do,
      kinh_do: viTri.kinh_do,
      ma_hieu_noi_dat: viTri.tiep_dat[0]?.ma_hieu_noi_dat,
      loai_noi_dat: viTri.tiep_dat[0]?.loai_noi_dat,
      dien_tro_tai_thoi_diem_ban_dau: viTri.tiep_dat[0]?.dien_tro_tai_thoi_diem_ban_dau,
      ma_hieu_cot: viTri.cot_dien[0]?.ma_hieu_cot,
      cong_dung_cot: viTri.cot_dien[0]?.cong_dung_cot,
      chieu_cao: viTri.cot_dien[0]?.chieu_cao,
      trong_luong: viTri.cot_dien[0]?.trong_luong,
      so_mach_day_dan: viTri.cot_dien[0]?.so_mach_day_dan,
      so_mach_dcs: viTri.cot_dien[0]?.so_mach_dcs,
      do_rong_chan_cot: viTri.cot_dien[0]?.do_rong_chan_cot,
      mong_cot: viTri.cot_dien[0]?.mong_cot,
      loai_bulong_neo_mong: viTri.cot_dien[0]?.loai_bulong_neo_mong,
      loai_mong: viTri.cot_dien[0]?.loai_mong,
      ke_mong: viTri.cot_dien[0]?.ke_mong,
      tuong_chan: viTri.cot_dien[0]?.tuong_chan,
      muong_thoat_nuoc: viTri.cot_dien[0]?.muong_thoat_nuoc,
      den_canh_bao_hang_khong: viTri.cot_dien[0]?.den_canh_bao_hang_khong,
      ttd_khu_vuc: viTri.don_vi_id?.don_vi_cha_id?.ten_don_vi,
      doi_duong_day: viTri.don_vi_id?.ten_don_vi,
      cap_dien_ap: viTri.van_hanh?.duong_day_id?.loai_duong_day_id?.ten_loai,
      giao_cheo: viTri.giao_cheo,
      doi_tuong_giao_cheo: doiTuongGiaoCheo,
      khoang_cach_giao_cheo: khoangCachGiaoCheo,
      khoang_cach_diem_giao_cheo_den_vi_tri: giaoCheoDenViTri,
      bien_bao_giao_cheo_duong_bo: giaoCheoDuongBo,
      bien_bao_giao_cheo_duong_thuy: giaoCheoDuongThuy,
      bien_canh_bao_an_toan: bienCanhBaoAnToan,
      day_cap_quang: viTri.day_cap_quang,
      day_chong_set: viTri.day_chong_set,
      day_dan_pha_a: viTri.van_hanh?.day_dan_id?.find(danDan => danDan?.pha?.trim().toUpperCase() === 'A') || {},
      day_dan_pha_b: viTri.van_hanh?.day_dan_id?.find(danDan => danDan?.pha?.trim().toUpperCase() === 'B') || {},
      day_dan_pha_c: viTri.van_hanh?.day_dan_id?.find(danDan => danDan?.pha?.trim().toUpperCase() === 'C') || {},
      cach_dien_neo_pha_a_truoc: getCachDienNeo(viTri.van_hanh?.cach_dien_id, 'A', 'TRƯỚC'),
      cach_dien_neo_pha_b_truoc: getCachDienNeo(viTri.van_hanh?.cach_dien_id, 'B', 'TRƯỚC'),
      cach_dien_neo_pha_c_truoc: getCachDienNeo(viTri.van_hanh?.cach_dien_id, 'C', 'TRƯỚC'),
      cach_dien_neo_pha_a_sau: getCachDienNeo(viTri.van_hanh?.cach_dien_id, 'A', 'SAU'),
      cach_dien_neo_pha_b_sau: getCachDienNeo(viTri.van_hanh?.cach_dien_id, 'B', 'SAU'),
      cach_dien_chuoi_do_leo_pha_a: getCachDienChuoiDo(viTri.van_hanh?.cach_dien_id, 'A'),
      cach_dien_chuoi_do_leo_pha_b: getCachDienChuoiDo(viTri.van_hanh?.cach_dien_id, 'B'),
      cach_dien_chuoi_do_leo_pha_c: getCachDienChuoiDo(viTri.van_hanh?.cach_dien_id, 'C'),
      cach_dien_chuoi_do_pha_a: getCachDienChuoiDo(viTri.van_hanh?.cach_dien_id, 'A', 'CHUOI_DO'),
      cach_dien_chuoi_do_pha_b: getCachDienChuoiDo(viTri.van_hanh?.cach_dien_id, 'B', 'CHUOI_DO'),
      cach_dien_chuoi_do_pha_c: getCachDienChuoiDo(viTri.van_hanh?.cach_dien_id, 'C', 'CHUOI_DO'),
    };
  }

  return allViTri.map(convertDataToRows);
}

export async function getAllDuongDayExpandInScope(allMach) {
  let allChaInScope = [];
  let allMachChaIds = allMach.map(mach => mach.duong_day_chinh_id).filter(machChaId => machChaId);  // lấy allCha và khác null
  while (allMachChaIds.length > 0) {
    const allCha = await DUONG_DAY.find({ _id: { $in: allMachChaIds } }).lean();
    const allMachCha = allCha.filter(cha => cha.is_mach);
    allChaInScope = [...allChaInScope, ...allCha];
    allMachChaIds = allMachCha.map(cha => cha.duong_day_chinh_id).filter(machChaId => machChaId);
  }
  return [...new Set([...allMach, ...allChaInScope].map(mach => JSON.stringify(mach)))].map(itemStr => JSON.parse(itemStr));
}

export async function getAllInScope(duongDayId) {
  let allChildInScope = [duongDayId];
  let allDuongDayChaIds = [duongDayId];
  const allDuongDays = await DUONG_DAY.find({ is_deleted: false }).lean();
  while (allDuongDayChaIds.length > 0) {
    const oneParentId = allDuongDayChaIds.shift();
    if (allChildInScope.includes(oneParentId)) {
      continue;
    }
    const allOneChilds = allDuongDays.filter(item => item.duong_day_chinh_id == oneParentId);
    const allOneChildIds = allOneChilds.map(cha => cha._id);
    allChildInScope = [...allChildInScope, ...allOneChildIds];
    allDuongDayChaIds = [...allDuongDayChaIds, ...allOneChildIds];
  }
  return allChildInScope;
}

export async function updateAll(arrData) {
  return await DUONG_DAY.bulkWrite(
    arrData.map((row) =>
      ({
        updateOne: {
          filter: { _id: row._id },
          update: { $set: row },
          upsert: false,
        },
      }),
    ),
  );
}

export async function getViTriIdByDuongDay(req, duongDayId) {
  const donViQuery = await getDonViQuery(req);
  const allDuongDayInScopeId = await getAllInScope(duongDayId);
  const allVanHanh = await VanHanhService.getAll({
    duong_day_id: { $in: allDuongDayInScopeId },
    is_deleted: false,
  }).lean();
  const allVitriInDuongDayIds = allVanHanh.map(vanHanh => vanHanh.vi_tri_id);
  const viTrisQuery = {
    $and: [
      { _id: { $in: allVitriInDuongDayIds } },
      donViQuery.$in.length > 0 ? { don_vi_id: donViQuery } : {},
    ],
  };
  const allViTri = await VI_TRI.find(viTrisQuery).sort('thu_tu').select('ten_vi_tri').lean();
  const viTriIds = allViTri.map(viTri => viTri._id);

  const khoangCotData = await KhoangCotService.getAll({
    vi_tri_id: viTriIds,
    is_deleted: false,
  }).select('vi_tri_id ten_khoang_cot');
  khoangCotData.forEach(khoangCot => {
    khoangCot.key_group = khoangCot.vi_tri_id?._id;
  });
  const khoangCotGroupByViTri = groupBy(khoangCotData, 'key_group');

  allViTri.forEach(viTri => {
    const khoangCotId = khoangCotGroupByViTri[viTri._id] || [];
    viTri.khoang_cot_id = khoangCotId.map(khoangCot => {
      delete khoangCot.key_group;
      return khoangCot;
    });
  });
  return allViTri;
}

export async function getViTriIdByOneDuongDay(req, duongDayId) {
  const donViQuery = await getDonViQuery(req);
  const allVanHanh = await VanHanhService.getAll({
    duong_day_id: duongDayId,
    is_deleted: false,
  }).lean();
  const allVitriInDuongDayIds = allVanHanh.map(vanHanh => vanHanh.vi_tri_id);
  const viTrisQuery = {
    $and: [
      { _id: { $in: allVitriInDuongDayIds } },
      donViQuery.$in.length > 0 ? { don_vi_id: donViQuery } : {},
    ],
  };
  const allViTri = await VI_TRI.find(viTrisQuery).sort('thu_tu').select('ten_vi_tri').lean();
  const viTriIds = allViTri.map(viTri => viTri._id);

  const khoangCotData = await KhoangCotService.getAll({
    vi_tri_id: viTriIds,
    is_deleted: false,
  }).select('vi_tri_id ten_khoang_cot');
  khoangCotData.forEach(khoangCot => {
    khoangCot.key_group = khoangCot.vi_tri_id?._id;
  });
  const khoangCotGroupByViTri = groupBy(khoangCotData, 'key_group');

  allViTri.forEach(viTri => {
    const khoangCotId = khoangCotGroupByViTri[viTri._id] || [];
    viTri.khoang_cot_id = khoangCotId.map(khoangCot => {
      delete khoangCot.key_group;
      return khoangCot;
    });
  });
  return allViTri;
}

export async function getOldPowerlineIds(duongDayId) {

  const duongDayObj = await DUONG_DAY.findById(duongDayId, {}).lean();
  const allDuongDay = await DUONG_DAY.find({ don_vi_id: duongDayObj.don_vi_id, is_deleted: false });
  const mapPowerline = {};
  let oldPowerlineIds = [duongDayId];
  allDuongDay.forEach(plData => mapPowerline[plData._id] = plData);

  let nextOldPowerlineId = mapPowerline[duongDayId].duong_day_cu_id;

  while (!oldPowerlineIds.includes(nextOldPowerlineId.toString())) {
    oldPowerlineIds.push(nextOldPowerlineId.toString());
    nextOldPowerlineId = mapPowerline[nextOldPowerlineId].duong_day_cu_id;
  }

  return oldPowerlineIds;
}

export async function checkUpdateTinhTrangVanHanh(duongDayId) {

  const invalidPhieuGiaoViecs = await PhieuGiaoViecService.getAllPopulate({
    duong_day_ids: duongDayId,
    is_deleted: false,
    $and: [
      { trang_thai_cong_viec: { $ne: TRANG_THAI_PHIEU.HUY_PHIEU.code } },
      { trang_thai_cong_viec: { $ne: TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code } }],
  });

  const allPhieuByDuongDay = await PhieuGiaoViecService.getAllPopulate({
    duong_day_ids: duongDayId,
    is_deleted: false,
  });

  const invalidPhieuCongTacs = await PhieuCongTacService.getAll({
    phieu_giao_viec_id: extractIds(allPhieuByDuongDay),
    is_deleted: false, hoan_thanh_cong_tac: false, huy_phieu: false,
  });
  return !!invalidPhieuGiaoViecs.length || !!invalidPhieuCongTacs.length;
}

export async function getDuongDayIdByViTri(viTriQuery) {
  const allVanHanhs = await VI_TRI.aggregate([
    {
      '$match': viTriQuery,
    },
    {
      '$project': {
        '_id': 1.0,
      },
    },
    {
      '$lookup': {
        'from': 'VanHanh',
        'localField': '_id',
        'foreignField': 'vi_tri_id',
        'as': 'vanhanhs',
      },
    },
    {
      '$project': {
        'vanhanhs': 1.0,
      },
    },
    {
      '$unwind': {
        'path': '$vanhanhs',
      },
    },
    {
      '$replaceRoot': {
        'newRoot': '$vanhanhs',
      },
    },
    {
      '$match': {
        'is_deleted': false,
      },
    },
    {
      '$group': {
        '_id': '$duong_day_id',
      },
    },
  ]);
  return allVanHanhs.map(item => item._id);
}

export async function getDataTraoLuuCongSuat(criteria) {
  if (criteria.don_vi_id) {
    criteria.don_vi_id = Types.ObjectId(criteria.don_vi_id);
  }
  if (criteria.loai_duong_day_id) {
    criteria.loai_duong_day_id = Types.ObjectId(criteria.loai_duong_day_id);
  }
  return DUONG_DAY.aggregate([
    { $match: criteria },
    {
      $lookup: {
        from: 'DonVi',
        let: { don_vi_id: '$don_vi_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$_id', '$$don_vi_id'] },
                  { $eq: ['$cap_don_vi', CAP_DON_VI.CONG_TY] },
                ],
              },
            },
          },
          { $project: { _id: 1, thu_tu: 1 } },
        ],
        as: 'don_vi_id',
      },
    },
    {
      $unwind: { path: '$don_vi_id' },
    },
    { $sort: { 'don_vi_id.thu_tu': 1, thu_tu: 1 } },
    {
      $project: {
        ten_duong_day: 1, i: 1, p: 1, u: 1, q: 1, thoi_gian_dong_bo: 1, thoi_diem_thong_so: 1, i_dinh_muc: 1,
      },
    },

  ]);
}

