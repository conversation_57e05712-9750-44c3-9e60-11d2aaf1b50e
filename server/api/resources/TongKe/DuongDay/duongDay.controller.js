import moment from 'moment/moment';
import { Types } from 'mongoose';
import momentTimezone from 'moment-timezone';

import CommonError from '../../../error/CommonError';
import { extractIds, extractKeys } from '../../../utils/dataconverter';
import * as controllerHelper from '../../../helpers/controllerHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';
import { convertMoment, momentValid } from '../../../helpers/checkDataHelper';
import { createDataTree } from '../../../common/DataStructureHelper';
import { getFilePath } from '../../../utils/fileUtils';
import { generateDocument } from '../../Report/GenerateFile/generate.controller';
import { floorMinutes, formatDatePmiss, formatTimeDate } from '../../../common/formatUTCDateToLocalDate';
import { cloneObj, formatUnique } from '../../../common/functionCommons';

import * as DuongDayService from './duongDay.service';
import * as DonViService from '../../DonVi/donVi.service';
import * as ReportPhaDatService from '../../Report/ReportDoPhaDat/doPhaDat.service';
import * as ReportDienTroService from '../../Report/ReportDoDienTro/doDienTro.service';
import * as ReportNhietDoService from '../../Report/ReportDoNhietDo/doNhietDo.service';
// import * as SuCoDuongDayService from '../SuCoDuongDay/suCoDuongDay.service';
import * as SyncPmisService from '../SyncPmis/syncPmis.service';
import * as TraoLuuCongSuatService from '../../QuanLyVanHanh/TraoLuuCongSuat/traoLuuCongSuat.service';
import * as VanHanhService from '../../TongKe/VanHanh/vanHanh.service';
import * as KhoangCotService from '../../TongKe/KhoangCot/khoangCot.service';
import * as KetQuaKiemTraService from '../../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.service';
import * as PhieuGiaoViecService from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';
import * as CongViecPhatSinhService from '../../QuanLyVanHanh/CongViecPhatSinh/congViecPhatSinh.service';
import * as KetQuaSuaChuaKhongKeHoachService
  from '../../QuanLyVanHanh/KetQuaSuaChuaKhongKeHoach/ketQuaSuaChuaKhongKeHoach.service';
import * as DieuKienTienHanhService from '../../QuanLyVanHanh/PhieuCongTac/DieuKienTienHanh/dieuKienTienHanh.service';
import * as ThuTucCatDienService from '../../QuanLyVanHanh/PhieuCongTac/ThuTucCatDien/thuTucCatDien.service';
import * as TonTaiCapTrenService from '../../QuanLyVanHanh/PhieuGiaoViec/TonTaiCapTren/tonTaiCapTren.service';
import * as DongBoDuLieuDoService from '../../Report/DongBoDuLieuDo/dongBo.service';
import * as HanhLangTuyenService from '../HanhLangTuyen/hanhLangTuyen.service';
import * as NghiemThuHanhLangTuyenService from '../NghiemThuHanhLangTuyen/nghiemThuHangLangTuyen.service';
import * as SuCoDuongDayService from '../SuCoDuongDay/suCoDuongDay.service';
import * as TongKeMoiNoiService from '../TongKeMoiNoi/tongKeMoiNoi.service';
import * as LogGopDuongDayService from '../../Log/LogGopDuongDay/logGopDuongDay.service';

import Model from './duongDay.model';
import DON_VI from '../../DonVi/donVi.model';
import LOAI_DUONG_DAY from '../../DanhMuc/LoaiDuongDay/loaiDuongDay.model';
import PHIEU_GIAO_VIEC from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.model';
import CONG_SUAT_PMISS from '../../QuanLyVanHanh/TraoLuuCongSuat/CongSuatPmiss/congSuatPmiss.model';

import PMIS_SOAP_NAME from '../SyncPmis/soapName';
import { CAP_DON_VI, TEMPLATES_DIRS } from '../../../constant/constant';
import { TINH_TRANG_VAN_HANH } from '../../DanhMuc/TinhTrangVanHanh';

import handleTachDuongDay from './utils/tachDuongDay';

momentTimezone.tz.setDefault('Asia/Ho_Chi_Minh');

const populateOpts = [
  { path: 'loai_duong_day_id' },
  { path: 'duong_day_chinh_id' },
  { path: 'don_vi_id' },
  { path: 'cong_trinh_id' },
  { path: 'duong_day_cu_id' },
];
const uniqueOpts = [{ field: 'ma_duong_day', message: 'Mã đường dây' }];

export const create = controllerHelper.createCreateFunction(Model, DuongDayService, populateOpts, uniqueOpts);

export async function update(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const { error, value } = DuongDayService.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);

    if (value.tinh_trang_van_hanh === TINH_TRANG_VAN_HANH.KHONG_VAN_HANH) {
      const checkTinhTrang = await DuongDayService.checkUpdateTinhTrangVanHanh(id);
      if (checkTinhTrang) {
        return responseHelper.error(res, { message: t('complete_or_cancel_task_assign_collaborate') }, 404);
      }
    }

    const checkUnique = await Model.findOne({ ma_duong_day: value.ma_duong_day, _id: { $ne: id }, is_deleted: false });
    if (checkUnique) {
      return responseHelper.error(res, { message: t('POWER_LINE_CODE_EXISTED') }, 404);
    }
    //
    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true })
      .populate(populateOpts);

    if (!data) return responseHelper.error(res, CommonError.NOT_FOUND);

    if (value.tinh_trang_van_hanh === TINH_TRANG_VAN_HANH.KHONG_VAN_HANH) {
      await VanHanhService.updateMaVanHanhByDuongDay(id, false);
    } else if (value.tinh_trang_van_hanh === TINH_TRANG_VAN_HANH.VAN_HANH) {
      await VanHanhService.updateMaVanHanhByDuongDay(id);
    }

    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await controllerHelper.findOneById(Model, id, populateOpts, true);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    const duration = moment.duration(moment().diff(convertMoment(data.thoi_diem_thong_so)));
    const hours = duration.asHours();
    if (!data.thoi_diem_thong_so || isNaN(hours) || hours > 1) {
      const dataPmis = await DuongDayService.getThongSo(data.asset_id, data.dao_nguoc_thong_so);
      if (dataPmis && Object.keys(dataPmis).length) {
        const dataUpdate = await Model.findOneAndUpdate({ _id: id }, dataPmis, { new: true }).populate(populateOpts);
        return responseHelper.success(res, dataUpdate);
      }
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllViTri(req, res) {
  try {
    const { id } = req.params;
    const { only_name } = req.query;
    const data = only_name ? await DuongDayService.getViTriNameByDuongDay(req, id) : await DuongDayService.getViTriByDuongDay(req, id);
    let viTriData = data.vi_tris;
    return responseHelper.success(res, viTriData);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function getAllViTriWithoutChild(req, res) {
  try {
    const { id } = req.params;
    const data = await DuongDayService.getViTriOnlyOnceDuongDay(req, id);
    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function getAllGiaoCheo(req, res) {
  try {
    const { id } = req.params;

    const viTriData = await DuongDayService.bangTongHopGiaoCheo(req, id);
    return responseHelper.success(res, viTriData);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function getDataBaoCaoKhoangCachPhaDat(req, res) {
  try {
    const { id } = req.params;

    const data = await ReportPhaDatService.bangTongHopBaoCaoKhoangCachPhaDat(req, id);
    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function getDataBaoCaoDienTroTiepDia(req, res) {
  try {
    const { id } = req.params;

    const data = await ReportDienTroService.bangTongHopBaoCaoDienTroTiepDia(req, id);
    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function getDataBaoCaoNhietDoTiepXuc(req, res) {
  try {
    const { id } = req.params;

    const data = await ReportNhietDoService.bangTongHopBaoCaoNhietDoTiepXuc(req, id);
    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function tongKeDuongDay(req, res) {
  try {
    const { id } = req.params;

    const tongKeDuongDayData = await DuongDayService.bangTongKeDuongDay(req, id);
    return responseHelper.success(res, tongKeDuongDayData);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_duong_day', 'ma_duong_day']);
    const { criteria, options } = query;
    if (populateOpts) {
      options.populate = populateOpts;
    }

    criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);
    if (req.query.all) {
      let queryPromise = Model.find(criteria);
      if (criteria.lean) {
        queryPromise = queryPromise.lean();
      }
      if (populateOpts) {
        populateOpts.forEach(populateOp => {
          queryPromise = queryPromise.populate(populateOp);
        });
      }
      const data = await queryPromise;
      return responseHelper.success(res, data);
    } else {
      const data = await Model.paginate(criteria, options);
      return responseHelper.success(res, data);
    }
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllTree(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_duong_day', 'ma_duong_day']);
    const { criteria } = query;
    criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);
    let allDuongDay = [];
    if (req.query.include_parent) {
      allDuongDay = await DuongDayService.getAllDuongDayExpandInScope(allDuongDay);
    } else {
      allDuongDay = await Model.find(criteria).lean();
    }
    const allDuongDayTree = createDataTree(allDuongDay, '_id', 'duong_day_chinh_id');
    return responseHelper.success(res, allDuongDayTree);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllDuongDay(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_duong_day', 'ma_duong_day']);
    const { criteria, options } = query;
    if (populateOpts) {
      options.populate = populateOpts;
    }
    criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);
    if (req.query.all) {
      let queryPromise = Model.find(criteria);
      queryPromise.lean = lean;
      if (populateOpts) {
        populateOpts.forEach(populateOp => {
          queryPromise.populate(populateOp);
        });
      }
      const data = await queryPromise;
      return responseHelper.success(res, data);
    } else {
      const data = await Model.paginate(criteria, options);
      return responseHelper.success(res, data);
    }
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllDuongDayTree(req, res) {
  try {
    const queryDuongDay = await extractQueryForDuongDay(req);
    let allDuongDay = await Model.find(queryDuongDay).lean();
    if (req.query.include_parent) {
      allDuongDay = await DuongDayService.getAllDuongDayExpandInScope(allDuongDay);
    }
    const allDuongDayTree = createDataTree(allDuongDay, '_id', 'duong_day_chinh_id');
    return responseHelper.success(res, allDuongDayTree);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllDuongDayChinhByDonViId(req, res) {
  try {
    const { id } = req.params;
    const donVi = await DonViService.getById(id, { don_vi_cha_id: 1 });
    if (!donVi) return responseHelper.error(res, CommonError.NOT_FOUND());
    if (!donVi.don_vi_cha_id) return responseHelper.success(res, []);
    const data = await Model.find({ don_vi_id: donVi.don_vi_cha_id, is_deleted: false });
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllSuCoTrenDay(req, res) {
  try {
    const dataSuCo = await SuCoDuongDayService.getAll({ da_xu_ly: false, is_deleted: false })
      .populate([
        { path: 'duong_day_id', select: 'ten_duong_day' },
        { path: 'vi_tri_id', select: 'ten_vi_tri kinh_do vi_do' },
        {
          path: 'khoang_cot_id', select: 'ten_khoang_cot vi_tri_bat_dau_id vi_tri_ket_thuc_id',
          populate: [
            { path: 'vi_tri_bat_dau_id', select: 'ten_vi_tri kinh_do vi_do' },
            { path: 'vi_tri_ket_thuc_id', select: 'ten_vi_tri kinh_do vi_do' },
          ],
        },
      ]);
    return responseHelper.success(res, dataSuCo);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getThongSoTheoThoiGian(req, res) {
  try {
    const { id } = req.params;
    const { thoi_gian } = req.query;

    const duongDayCurrent = await DuongDayService.getOne({ _id: id, is_deleted: false, asset_id: { $exists: true } });
    if (!duongDayCurrent) return responseHelper.error(res, CommonError.NOT_FOUND);

    if (!momentValid(thoi_gian)) return responseHelper.error(res, CommonError.NOT_FOUND);

    const assetId = duongDayCurrent.asset_id;

    const filterTime = momentTimezone(thoi_gian).set({ minute: 0, second: 0 }).toISOString();

    const timePmisQuery = momentTimezone(thoi_gian).format('MM/DD/YYYY');
    const query = { ID: assetId, TU_NGAY: timePmisQuery, DEN_NGAY: timePmisQuery };

    const dataPmis = await SyncPmisService.getDataPmis(PMIS_SOAP_NAME.THONG_SO_BY_DUONG_DAY_ID, query, 1000)
      .then(results => results.success ? results.data : [])
      .then(results => results
        .filter(result => !!momentValid(result.THOI_GIAN) && momentTimezone(result.THOI_GIAN).toISOString() === filterTime)
        .map(result => ({
          thoiGian: momentTimezone(result.THOI_GIAN).toISOString(),
          giaTri: result.GIA_TRI,
          thongSo: result.THONG_SO,
        })));

    const thongSoData = dataPmis?.reduce((grouped, element) => {
      grouped[element.thongSo?.toLowerCase()] = element.giaTri;
      return grouped;
    }, {});

    return responseHelper.success(res, {
      _id: duongDayCurrent._id,
      ten_duong_day: duongDayCurrent.ten_duong_day,
      ...thongSoData,
      thoi_gian: filterTime,
    });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function syncThongSoById(req, res) {
  try {
    const { id } = req.params;
    const duongDayData = await DuongDayService.getById(id, {
      ma_duong_day: 1,
      thoi_diem_thong_so: 1,
      thoi_gian_dong_bo: 1,
      asset_id: 1,
      dao_nguoc_thong_so: 1,
      don_vi_id: 1,
    });

    if (!duongDayData) {
      return responseHelper.error(res, CommonError.NOT_FOUND());
    }
    const dataPmis = await DuongDayService.getThongSo(duongDayData.asset_id, duongDayData.dao_nguoc_thong_so);
    let dataUpdate;
    if (dataPmis) {
      const duration = moment.duration(moment(convertMoment(dataPmis.thoi_diem_thong_so)).diff(convertMoment(duongDayData.thoi_diem_thong_so)));
      const hours = duration.asHours();
      if (hours >= 0 || !duongDayData.thoi_diem_thong_so) {
        dataUpdate = await Model.findOneAndUpdate({ _id: id }, dataPmis, { new: true }).populate(populateOpts);
      }
    }
    if (!dataPmis || !dataUpdate) {
      dataUpdate = await Model.findOneAndUpdate({ _id: id }, { thoi_gian_dong_bo: momentTimezone().tz('Etc/GMT-7') }, { new: true }).populate(populateOpts);
    }
    const allDonViCapCongTy = await DON_VI.find({
      cap_don_vi: CAP_DON_VI.CONG_TY, is_deleted: false,
      $and: [
        { tinh_trang_van_hanh: { $exists: true } },
        { tinh_trang_van_hanh: TINH_TRANG_VAN_HANH.VAN_HANH },
      ],
    }).lean();
    const allDonViCapCongTyId = extractIds(allDonViCapCongTy);

    if (allDonViCapCongTyId.includes(duongDayData.don_vi_id.toString())) {
      await TraoLuuCongSuatService.updateOneTraoLuuCongSuat(duongDayData.asset_id);
    }
    return responseHelper.success(res, dataUpdate);
  } catch (err) {
    console.log('err', err);
    return responseHelper.error(res, err);
  }
}

export async function getAllPowerlineIdWithOldId(req, res) {
  try {
    const { id } = req.params;
    let duongDayIds = await DuongDayService.getOldPowerlineIds(id);
    return responseHelper.success(res, duongDayIds);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

async function extractQueryForDuongDay(req) {
  const query = queryHelper.extractQueryParam(req, ['ten_duong_day', 'ma_duong_day']);
  const { criteria } = query;
  const criteriaWithoutDonVi = { ...criteria };
  delete criteriaWithoutDonVi.don_vi_id;
  const queryViTri = { don_vi_id: Types.ObjectId(criteria.don_vi_id || req.user.don_vi_id), is_deleted: false };
  const duongDayIds = await DuongDayService.getDuongDayIdByViTri(queryViTri);
  const duongDayQuery = {
    $or: [
      {
        ...criteriaWithoutDonVi,
        _id: { $in: duongDayIds },
      },
      {
        ...criteria,
        don_vi_id: await DonViService.getDonViInScope(criteria.don_vi_id || req.user.don_vi_id),
      },
    ],
  };
  if (req.query.is_phieu_giao) {
    duongDayQuery.tinh_trang_van_hanh = { $ne: TINH_TRANG_VAN_HANH.KHONG_VAN_HANH };
  }
  return duongDayQuery;
}

export async function getDuongDayByDonViQuanLy(req, res) {
  try {
    const queryDuongDay = await extractQueryForDuongDay(req);
    const allDuongDay = await Model.find(queryDuongDay).lean();
    const allDuongDayTree = createDataTree(allDuongDay, '_id', 'duong_day_chinh_id');
    return responseHelper.success(res, allDuongDayTree);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllDuongDayHoSoVanHanh(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_duong_day', 'ma_duong_day']);
    const { criteria, options } = query;
    const donViId = await DON_VI.findById(criteria?.don_vi_id, { cap_don_vi: 1, don_vi_cha_id: 1 }).lean();
    if (donViId?.cap_don_vi !== CAP_DON_VI.DOI_TRUYEN_TAI_DIEN) {
      criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);
      let allDuongDay = [];
      if (req.query.include_parent) {
        allDuongDay = await DuongDayService.getAllDuongDayExpandInScope(allDuongDay);
      } else {
        allDuongDay = await Model.find(criteria).lean();
      }
      const allDuongDayTree = createDataTree(allDuongDay, '_id', 'duong_day_chinh_id');
      return responseHelper.success(res, allDuongDayTree);
    }
    criteria.don_vi_id = [donViId.don_vi_cha_id?._id, donViId._id];
    const allDuongDay = await Model.find(criteria)
      .populate(options.populate)
      .lean();
    const allDuongDayTree = createDataTree(allDuongDay, '_id', 'duong_day_chinh_id');
    return responseHelper.success(res, allDuongDayTree);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllInCorrectData(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_duong_day', 'ma_duong_day']);
    const { criteria, options } = query;
    if (populateOpts) {
      options.populate = populateOpts;
    }
    criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);
    const criteriaInCorrect = {
      $or: [{ loai_duong_day_id: null }, { tinh_trang_van_hanh: null }, { tinh_trang_van_hanh: '' }],
    };

    if (req.query.all) {
      let queryPromise = Model.find({ ...criteria, ...criteriaInCorrect });
      queryPromise.lean = lean;
      if (populateOpts) {
        populateOpts.forEach(populateOp => {
          queryPromise.populate(populateOp);
        });
      }
      const data = await queryPromise;
      return responseHelper.success(res, data);
    } else {
      const data = await Model.paginate({ ...criteria, ...criteriaInCorrect }, options);
      return responseHelper.success(res, data);
    }
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function downloadDanhSachDuongDay(req, res) {
  try {
    const queryDuongDay = await extractQueryForDuongDay(req);
    const dataRes = await Model.find(queryDuongDay)
      .populate([
        { path: 'loai_duong_day_id', select: 'ten_loai' },
        { path: 'duong_day_chinh_id', select: 'ten_duong_day' },
        {
          path: 'don_vi_id', populate: { path: 'don_vi_cha_id', select: 'ten_don_vi' },
          select: 'don_vi_cha_id ten_don_vi',
        },
        { path: 'cong_trinh_id', select: 'ten_cong_trinh' },
      ]).sort({ don_vi_id: 1, thu_tu: 1 }).lean();
    const templateFilePath = getFilePath('danh_sach_duong_day.xlsx', TEMPLATES_DIRS.TONG_KE);

    await generateDocument(res, dataRes, templateFilePath);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function getDataTraoLuuCongSuat(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    if (criteria.don_vi_id) {
      criteria.don_vi_id = Types.ObjectId(criteria.don_vi_id);
    }
    if (criteria.loai_duong_day_id) {
      criteria.loai_duong_day_id = Types.ObjectId(criteria.loai_duong_day_id);
    }
    const data = await DuongDayService.getDataTraoLuuCongSuat(criteria);

    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function syncThongSoMulti(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;

    const timeNow = moment(criteria.thoi_diem).set({ minute: 0, second: 0 });
    const startTime = moment(timeNow)
      .subtract(1, 'minute')
      .subtract(24, 'hours').format('MM/DD/YYYY HH:mm:ss');
    const endTime = moment(timeNow).add(1, 'minute').format('MM/DD/YYYY HH:mm:ss');
    delete criteria.thoi_diem;
    const duongDayData = await Model.find(criteria).lean();

    const promises = duongDayData.map(duongDay => {
      const query = { ID: duongDay.asset_id, TU_NGAY: startTime, DEN_NGAY: endTime };
      return SyncPmisService.getDataPmis(PMIS_SOAP_NAME.THONG_SO_BY_DUONG_DAY_ID, query, 10000);
    });
    const dataPmis = await Promise.all(promises).then(results => {
      results = results.filter(result => !!result?.length);
      return results.map(result => {
        return result.reduce((prevValue, currentValue) => {
          if (currentValue) {
            prevValue.asset_id = currentValue.ASSETID;
            prevValue.thoi_diem_thong_so = prevValue.thoi_diem_thong_so || currentValue.THOI_GIAN;
            prevValue.thoi_gian_dong_bo = momentTimezone().tz('Etc/GMT-7');
            prevValue[currentValue.THONG_SO?.toLowerCase()] = currentValue.GIA_TRI;
          }
          return prevValue;
        }, null);
      });
    });

    const bulkWriteResult = await Model.bulkWrite(
      cloneObj(dataPmis).map((row) =>
        ({
          updateOne: {
            filter: { asset_id: row.asset_id },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
    await SyncPmisService.logSyncPmis('DUONG_DAY', bulkWriteResult.result);
    return responseHelper.success(res, bulkWriteResult.result);
  } catch (err) {
    console.log('err', err);
    return responseHelper.error(res, err);
  }
}

export async function downloadTraoLuuCongSuat(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const capDienAp = (await LOAI_DUONG_DAY.findById(criteria.loai_duong_day_id)).cap_dien_ap;

    const allDuongDay = await DuongDayService.getAll({ don_vi_id: criteria.don_vi_id, is_deleted: false });
    const queryCongSuat = {
      thoi_diem_thong_so: formatDatePmiss(floorMinutes(criteria.thoi_diem_thong_so)),
      asset_id: { $in: extractKeys(allDuongDay, 'asset_id') },
      loai_duong_day_id: criteria.loai_duong_day_id,
    };
    const data = await CONG_SUAT_PMISS.find(queryCongSuat).lean();
    const templateFilePath = getFilePath('cong_suat_duong_day.xlsx', TEMPLATES_DIRS.TONG_KE);

    function convertData(row, index) {
      return {
        stt: index + 1,
        ten_duong_day: row.ten_duong_day,
        i: row.i, p: row.p, u: row.u, q: row.q,
        i_dinh_muc: row.i_dinh_muc,
        idm: (row.i && row.i_dinh_muc) ? `${Math.round(row.i / row.i_dinh_muc * 100)} %` : '',
        thoi_gian_dong_bo: formatTimeDate(row.thoi_gian_dong_bo),
        thoi_diem_thong_so: momentTimezone(row.thoi_diem_thong_so).format('HH:mm DD/MM/YYYY'),
      };
    }

    const dataToRender = {
      cap_dien_ap: capDienAp,
      duong_day: data.map(convertData),
    };
    await generateDocument(res, dataToRender, templateFilePath);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function remove(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;

    const checkExistsPhieuGiaoViec = await PHIEU_GIAO_VIEC.findOne({
      duong_day_ids: id,
      is_deleted: false,
    }, { _id: true });
    if (checkExistsPhieuGiaoViec) {
      return responseHelper.error(res, { message: `${t('MSG_XOA_DUONG_DAY')} ${t('DA_GIAO_VIEC')}` }, 400);
    }
    const checkExistsViTri = await DuongDayService.getViTriOnlyOnceDuongDay(req, id);
    if (!!checkExistsViTri.length) {
      return responseHelper.error(res, { message: `${t('MSG_XOA_DUONG_DAY')} ${t('CO_VI_TRI')}` }, 400);
    }

    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseHelper.error(res, 404, '');
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function tachDuongDay(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;

    const maDuongDay = extractKeys(req.body, 'ma_duong_day');
    const checkUnique = await DuongDayService.getAll({ ma_duong_day: maDuongDay });
    if (checkUnique.length) {
      const codeExist = extractKeys(checkUnique, 'ma_duong_day').join(', ');
      const message = t('POWER_LINE_CODE_EXIST').format(codeExist);
      return responseHelper.error(res, { message }, 404);
    }

    const result = await handleTachDuongDay(id, req.body);

    if (result?.error) return responseHelper.error(res, result.error);


    return responseHelper.success(res, result);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function gopDuongDay(req, res) {
  try {
    const { t } = req;

    const dataRequest = req.body;

    if (!Array.isArray(dataRequest.duong_day_can_gop)
      || !dataRequest.duong_day_can_gop.includes(dataRequest.duong_day_hop_nhat)) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    const duongDayDich = dataRequest.duong_day_hop_nhat;
    const duongDayCanGop = dataRequest.duong_day_can_gop.filter(duongDayId => duongDayId !== duongDayDich);


    const duongDayCurrent = await DuongDayService.getAll({ _id: dataRequest.duong_day_can_gop }, { chieu_dai: 1 });
    const chieuDaiTong = duongDayCurrent.reduce((sum, dz) => sum + parseFloat(dz.chieu_dai) || 0, 0);

    const logDoc = {
      user_id: req.user._id,
      duong_day_can_gop_id: req.body.duong_day_can_gop,
      duong_day_hop_nhat_id: req.body.duong_day_hop_nhat,
    };


    function replaceDuongDayList(source, duongDayKey = 'duong_day_id') {
      return source
        .filter(sourceItem => Array.isArray(sourceItem[duongDayKey]))
        .map(sourceItem => {
          sourceItem[duongDayKey] = sourceItem[duongDayKey]
            .filter(x => !!x)
            .map(duongDayId => duongDayCanGop.includes(duongDayId.toString()) ? duongDayDich : duongDayId);
          sourceItem[duongDayKey] = formatUnique(sourceItem[duongDayKey]);
          return sourceItem;
        });
    }

    function replaceDuongDayId(source, duongDayKey = 'duong_day_id') {
      return source.map(sourceItem => {
        sourceItem[duongDayKey] = duongDayCanGop.includes(sourceItem[duongDayKey]?.toString())
          ? duongDayDich
          : sourceItem[duongDayKey];
        return sourceItem;
      });
    }

    function updateVanHanh() {
      const VanHanhPromise = duongDayCanGop.map(duongDayId => {
        return VanHanhService.updateMany({ duong_day_id: duongDayId }, { duong_day_id: dataRequest.duong_day_hop_nhat });
      });
      return Promise.all(VanHanhPromise);
    }

    async function updateKhoangCot() {
      const allKhoangCot = await KhoangCotService.getAll(
        {
          $or: duongDayCanGop.map(dzId => ({ duong_day_id: dzId })),
          is_deleted: false,
        },
        { duong_day_id: 1 },
      );
      const khoangCotUpdate = replaceDuongDayList(allKhoangCot);
      return KhoangCotService.updateAll(khoangCotUpdate);
    }

    async function updatePhieuGiaoViec() {
      const allPhieu = await PhieuGiaoViecService.getAll(
        {
          $or: duongDayCanGop.map(dzId => ({ duong_day_ids: dzId })),
          is_deleted: false,
        },
        { duong_day_ids: 1 },
      );
      const phieuUpdate = replaceDuongDayList(allPhieu, 'duong_day_ids');
      return PhieuGiaoViecService.updateAll(phieuUpdate);
    }

    async function updateTonTai() {
      const allTonTai = await KetQuaKiemTraService.getAll(
        {
          $or: duongDayCanGop.map(dzId => ({ duong_day_id: dzId })),
          is_deleted: false,
        },
        { duong_day_id: 1 },
      );
      const tonTaiUpdate = replaceDuongDayList(allTonTai);
      return KetQuaKiemTraService.updateAll(tonTaiUpdate);
    }

    async function updateCvPhatSinh() {
      const allPhatSinh = await CongViecPhatSinhService.getAll(
        {
          $or: duongDayCanGop.map(dzId => ({ duong_day_id: dzId })),
          is_deleted: false,
        },
        { duong_day_id: 1 },
      );
      const phatSinhUpdate = replaceDuongDayId(allPhatSinh);
      return CongViecPhatSinhService.updateAll(phatSinhUpdate);
    }

    async function updateCvKoKeHoach() {
      const allKoKeHoach = await KetQuaSuaChuaKhongKeHoachService.getAll(
        {
          $or: duongDayCanGop.map(dzId => ({ duong_day_id: dzId })),
          is_deleted: false,
        },
        { duong_day_id: 1 },
      );
      const koKeHoachUpdate = replaceDuongDayList(allKoKeHoach);
      return KetQuaSuaChuaKhongKeHoachService.updateAll(koKeHoachUpdate);
    }

    async function updateDieuKienTienHanh() {
      const allDieuKien = await DieuKienTienHanhService.getAll(
        {
          $or: duongDayCanGop.map(dzId => ({ duong_day_id: dzId })),
          is_deleted: false,
        },
        { duong_day_id: 1 },
      );
      const dieuKienUpdate = replaceDuongDayId(allDieuKien);
      return DieuKienTienHanhService.updateAll(dieuKienUpdate);
    }

    async function updateThuTucCatDien() {
      const allCatDien = await ThuTucCatDienService.getAll(
        {
          $or: duongDayCanGop.map(dzId => ({ duong_day_id: dzId })),
          is_deleted: false,
        },
        { duong_day_id: 1 },
      );
      const catDienUpdate = replaceDuongDayId(allCatDien);
      return ThuTucCatDienService.updateAll(catDienUpdate);
    }

    async function updateTonTaiCapTren() {
      const allTonTaiCapTren = await TonTaiCapTrenService.getAll(
        {
          $or: [
            ...duongDayCanGop.map(dzId => ({ duong_day_id: dzId })),
            ...duongDayCanGop.map(dzId => ({ duong_day_ids: dzId })),
          ],
          is_deleted: false,
        },
        { duong_day_ids: 1, duong_day_id: 1 });
      let catDienUpdate = replaceDuongDayList(allTonTaiCapTren, 'duong_day_ids');
      catDienUpdate = replaceDuongDayId(catDienUpdate);
      return TonTaiCapTrenService.updateAll(catDienUpdate);
    }

    async function updateDongBoDuLieuDo() {
      const allDongBoDuLieuDo = await DongBoDuLieuDoService.getAll(
        {
          $or: duongDayCanGop.map(dzId => ({ duong_day_id: dzId })),
          is_deleted: false,
        },
        { duong_day_id: 1 },
      );
      const dongBoDuLieuDoUpdate = replaceDuongDayId(allDongBoDuLieuDo);
      return DongBoDuLieuDoService.updateAll(dongBoDuLieuDoUpdate);
    }

    async function updateHanhLangTuyen() {
      const currentData = await HanhLangTuyenService.getAll(
        {
          $or: duongDayCanGop.map(dzId => ({ duong_day_id: dzId })),
          is_deleted: false,
        },
        { duong_day_id: 1 },
      );
      const dataUpdate = replaceDuongDayId(currentData);
      return HanhLangTuyenService.updateAll(dataUpdate);
    }

    async function updateNghiemThuHanhLangTuyen() {
      const currentData = await NghiemThuHanhLangTuyenService.getAll(
        {
          $or: duongDayCanGop.map(dzId => ({ duong_day_id: dzId })),
          is_deleted: false,
        },
        { duong_day_id: 1 },
      );
      const dataUpdate = replaceDuongDayList(currentData);
      return NghiemThuHanhLangTuyenService.updateAll(dataUpdate);
    }

    async function updateSuCoDuongDay() {
      const currentData = await SuCoDuongDayService.getAll(
        {
          $or: duongDayCanGop.map(dzId => ({ duong_day_id: dzId })),
          is_deleted: false,
        },
        { duong_day_id: 1 },
      );
      const dataUpdate = replaceDuongDayId(currentData);
      return SuCoDuongDayService.updateAll(dataUpdate);
    }

    async function updateTongKeMoiNoi() {
      const currentData = await TongKeMoiNoiService.getAll(
        {
          $or: duongDayCanGop.map(dzId => ({ duong_day_id: dzId })),
          is_deleted: false,
        },
        { duong_day_id: 1 },
      );
      const dataUpdate = replaceDuongDayId(currentData);
      return TongKeMoiNoiService.updateAll(dataUpdate);
    }

    try {
      const [
        duongDayCanGopUpdated,
        duongDayDichUpdated,
        vanHanhUpdated,
        khoangCotUpdated,
        phieuGiaoViecUpdated,
        tonTaiUpdated,
        cvPhatSinhUpdated,
        cvKoKeHoachUpdated,
        dieuKienUpdated,
        catDienUpdated,
        tonTaiCapTrenUpdated,
        dongBoDuLieuDoUpdated,
        hanhLangTuyenUpdated,
        nghiemThuHanhLangTuyenUpdated,
        suCoDuongDayUpdated,
        tongKeMoiNoiUpdated,
      ] = await Promise.all([
        DuongDayService.updateMany({ _id: duongDayCanGop }, { tinh_trang_van_hanh: TINH_TRANG_VAN_HANH.KHONG_VAN_HANH }),
        DuongDayService.updateMany(
          { _id: duongDayDich },
          { chieu_dai: chieuDaiTong, tinh_trang_van_hanh: TINH_TRANG_VAN_HANH.VAN_HANH }),
        updateVanHanh(),
        updateKhoangCot(),
        updatePhieuGiaoViec(),
        updateTonTai(),
        updateCvPhatSinh(),
        updateCvKoKeHoach(),
        updateDieuKienTienHanh(),
        updateThuTucCatDien(),
        updateTonTaiCapTren(),
        updateDongBoDuLieuDo(),

        updateHanhLangTuyen(),
        updateNghiemThuHanhLangTuyen(),
        updateSuCoDuongDay(),
        updateTongKeMoiNoi(),

      ]);

      // console.log('vanHanhUpdated', vanHanhUpdated?.length);
      // console.log('khoangCotUpdated', khoangCotUpdated?.result);
      // console.log('phieuGiaoViecUpdated', phieuGiaoViecUpdated?.length);
      // console.log('tonTaiUpdated', tonTaiUpdated?.length);
      // console.log('cvPhatSinhUpdated', cvPhatSinhUpdated?.length);
      // console.log('cvKoKeHoachUpdated', cvKoKeHoachUpdated?.length);
      // console.log('dieuKienUpdated', dieuKienUpdated?.length);
      // console.log('catDienUpdated', catDienUpdated?.length);
      // console.log('tonTaiCapTrenUpdated', tonTaiCapTrenUpdated?.length);
      // console.log('dongBoDuLieuDoUpdated', dongBoDuLieuDoUpdated?.length);
      // console.log('hanhLangTuyenUpdated', hanhLangTuyenUpdated?.length);
      // console.log('nghiemThuHanhLangTuyenUpdated', nghiemThuHanhLangTuyenUpdated?.length);
      // console.log('suCoDuongDayUpdated', suCoDuongDayUpdated?.length);
      // console.log('tongKeMoiNoiUpdated', tongKeMoiNoiUpdated?.length);


      await LogGopDuongDayService.create({ ...logDoc, status: 'SUCCESS' });

      return responseHelper.success(res, { success: true });
    } catch (e) {
      await LogGopDuongDayService.create({ ...logDoc, status: 'ERROR', result: e });
      return responseHelper.error(res, e);
    }

  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getDanhSachDuongDay(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_duong_day', 'ma_duong_day']);
    const { criteria, options } = query;
    if (req.query.hasOwnProperty('is_deleted')) {
      criteria.is_deleted = req.query.is_deleted;
    } else {
      delete criteria.is_deleted;
    }
    criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);

    options.populate = populateOpts;
    options.select = '+is_deleted';

    const data = await Model.paginate(criteria, options);

    if (!data) return responseHelper.error(res, CommonError.NOT_FOUND);

    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function khoiPhucDuongDayDaXoa(req, res) {
  try {
    if (!req.user.is_system_admin) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    const { id } = req.params;
    const currentData = await Model.findById(id);
    if (!currentData) return responseHelper.error(res, CommonError.NOT_FOUND);


    const duongDayUpdated = await Model.findByIdAndUpdate(id, { is_deleted: false }, { new: true });
    return responseHelper.success(res, duongDayUpdated);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}
