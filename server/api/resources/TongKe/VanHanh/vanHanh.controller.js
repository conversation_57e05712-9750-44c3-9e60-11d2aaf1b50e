import * as Service from './vanHanh.service';
import * as DuongDayService from '../DuongDay/duongDay.service';
import Model from './vanHanh.model';

import * as controllerHelper from '../../../helpers/controllerHelper';
import * as responseHelper from '../../../helpers/responseHelper';

import CommonError from '../../../error/CommonError';
import queryHelper from '../../../helpers/queryHelper';

import { getDonViInScope } from '../../DonVi/donVi.service';

const populateOpts = [
  { path: 'vi_tri_id', populate: 'cong_trinh_id' },
  { path: 'duong_day_id', populate: 'don_vi_id loai_duong_day_id' },
  { path: 'don_vi_id' },
];

const populateDashboardOpts = [
  {
    path: 'vi_tri_id', select: 'ten_vi_tri kinh_do vi_do tinh huyen xa',
    match: {
      kinh_do: { $exists: true },
      vi_do: { $exists: true },
    },
  },
  {
    path: 'duong_day_id',
    select: 'don_vi_id loai_duong_day_id ten_duong_day',
    populate: [
      { path: 'don_vi_id', select: 'ten_don_vi' },
      { path: 'loai_duong_day_id', select: 'ten_loai color' },
    ],
  },
  { path: 'don_vi_id', select: 'ten_don_vi' },
];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);

// export const getAll = controllerHelper.createGetAllFunction(Model, searchLike, populateOpts, [], 'don_vi_id');

export async function getAllVanHanh(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const data = await Service.getAll(criteria, { vi_tri_id: 1, don_vi_id: 1, duong_day_id: 1, thu_tu: 1 })
      .populate(populateDashboardOpts);
    const dataReturn = data.filter(item => item.duong_day_id && item.vi_tri_id);
    return responseHelper.success(res, dataReturn);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function updateSai(req, res) {
  try {
    let dataUpdate = await Service.getAll(
      { ma_van_hanh: /.*\[object Object].*/i },
      { ma_van_hanh: 1 },
    );

    dataUpdate = dataUpdate.map(item => {
      item.ma_van_hanh = item.ma_van_hanh.toString();
      item.ma_van_hanh = item.ma_van_hanh.replace('[object Object]', '');
      return item;
    });

    // return responseHelper.success(res, dataUpdate);
    const result = await Model.bulkWrite(
      dataUpdate.map(row =>
        ({
          updateOne: {
            filter: { _id: row._id },
            update: { $set: { ma_van_hanh: row.ma_van_hanh } },
            upsert: false,
          },
        }),
      ),
    );

    return responseHelper.success(res, result);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_van_hanh', 'ma_van_hanh']);
    const { criteria, options } = query;
    options.populate = populateOpts;
    criteria.don_vi_id = await getDonViInScope(criteria.don_vi_id || req.user?.don_vi_id);
    delete criteria.is_deleted;
    const data = await Model.paginate(criteria, options);

    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function remove(req, res) {
  try {
    const { id } = req.params;
    const dataRes = await Model.findOne({ _id: id });
    const data = await Model.remove({ _id: id });
    const allVanhHanh = await Model.find({ duong_day_id: data.duong_day_id, is_deleted: false }).lean();
    allVanhHanh.sort(Service.sortThuTu);
    allVanhHanh.forEach((hoSo, i) => {
      hoSo.thu_tu = i + 1;
    });
    await Service.updateAll(allVanhHanh);

    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, dataRes);
  } catch (err) {
    return responseHelper.error(res, err);
  }

}

export async function update(req, res) {
  try {
    const { t } = req;
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);

    if (error) return responseHelper.error(res, error, 400);

    const vanHanhCurrent = await Service.getOne({ _id: id, is_deleted: false });
    if (!vanHanhCurrent) return responseHelper.error(res, CommonError.NOT_FOUND);

    const duongDayCurrentData = await DuongDayService.getOne({ _id: vanHanhCurrent.duong_day_id, is_deleted: false });
    if (duongDayCurrentData && duongDayCurrentData._id?.toString() !== value.duong_day_id?.toString()) {
      const checkUpdateVanHanh = await DuongDayService.checkUpdateTinhTrangVanHanh(vanHanhCurrent.duong_day_id);
      if (checkUpdateVanHanh) {
        return responseHelper.error(res, { message: t('complete_or_cancel_task_assign_collaborate_before_update') }, 404);
      }
    }
    const checkUnique = await Model.findOne({ ma_van_hanh: value.ma_van_hanh, _id: { $ne: id } });
    if (checkUnique) {
      return responseHelper.error(res, { message: t('operation_code_existed') }, 404);
    }
    const allBeforeInsert = await Service.getAll({ duong_day_id: value.duong_day_id });
    const index = (value.thu_tu && value.thu_tu <= allBeforeInsert.length) ? value.thu_tu - 1 : allBeforeInsert.length;
    await Service.updateIndex(allBeforeInsert.filter(item => item._id?.toString() !== value._id), index, value);

    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true })
      .populate([{ path: 'vi_tri_id', select: 'ten_vi_tri cong_trinh_id don_vi_id kinh_do vi_do tinh huyen xa' },
        {
          path: 'duong_day_id',
          select: 'don_vi_id loai_duong_day_id ten_duong_day',
          populate: 'don_vi_id loai_duong_day_id',
        },
        { path: 'don_vi_id', select: 'ten_don_vi' }]);
    if (!data) {
      return responseHelper.error(res, null, 404);
    }

    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function create(req, res) {

  try {
    const { t } = req;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    const checkUnique = await Model.findOne({ ma_van_hanh: value.ma_van_hanh });
    if (checkUnique) {
      return responseHelper.error(res, { message: t('operation_code_existed') }, 404);
    }
    const allVanHanh = await Service.getAll({ duong_day_id: value.duong_day_id, is_deleted: false });
    const index = (value.thu_tu && value.thu_tu <= allVanHanh.length) ? value.thu_tu - 1 : allVanHanh.length;
    const dataToCreate = await Service.createIndex(allVanHanh, index, value);

    const data = await Model.create(dataToCreate);
    let dataRtn = await data
      .populate([{ path: 'vi_tri_id', select: 'ten_vi_tri cong_trinh_id don_vi_id kinh_do vi_do tinh huyen xa' },
        {
          path: 'duong_day_id',
          select: 'don_vi_id loai_duong_day_id ten_duong_day',
          populate: 'don_vi_id loai_duong_day_id',
        },
        { path: 'don_vi_id', select: 'ten_don_vi' }]).execPopulate();
    return responseHelper.success(res, dataRtn);
  } catch (err) {
    console.error(err);
    return responseHelper.error(res, err, 500);
  }
}

export async function findOneNoPopulate(req, res) {
  try {
    const { id } = req.params;
    const data = await Service.getById(id);
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}
