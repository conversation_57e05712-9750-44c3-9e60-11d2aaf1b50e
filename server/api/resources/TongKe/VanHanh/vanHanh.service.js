import * as ValidatorHelper from '../../../helpers/validatorHelper';
import VanHanhModel from './vanHanh.model';

import * as DayDanService from '../DayDan/dayDan.service';
import * as CachDienService from '../CachDien/cachDien.service';
import * as KhoangCotService from '../KhoangCot/khoangCot.service';
import * as TongKeService from '../TongKe.service';

import { groupBy, insertObjToArr, trim } from '../../../common/functionCommons';
import { extractKeys } from '../../../utils/dataconverter';

const Joi = require('joi');

export function sortThuTu(a, b) {
  return a.thu_tu - b.thu_tu;
}

export function aggregate(pipeline = [], cb) {
  return VanHanhModel.aggregate(pipeline, cb);
}

export function getAll(query, projection = {}) {
  return VanHanhModel.find(query, projection).lean();
}

export function getOne(query, projection = {}) {
  return VanHanhModel.findOne(query, projection).lean();
}

export function getById(id, projection = {}) {
  return VanHanhModel.findById(id, projection).lean();
}

export function count(query) {
  return VanHanhModel.count(query);
}

export function distinctField(field, filter) {
  return VanHanhModel.distinct(field, filter);
}

export function updateMany(filter, update) {
  return VanHanhModel.updateMany(filter, update);
}

export async function updateAll(dataUpdate) {
  const results = [];
  for (const row of dataUpdate) {
    try {
      const { error, value } = validate(row);
      if (!error) {
        const updated = await VanHanhModel.findByIdAndUpdate(value._id, value, { new: true });
        if (updated) results.push(updated);
      }
    } catch {
    }
  }
  return results;
}

export async function createAll(dataCreate) {
  const promiseCreate = [];
  let errorCreate = null;
  for (const row of dataCreate) {
    const { error, value } = validate(row);
    if (error) {
      errorCreate = error;
      break;
    }
    promiseCreate.push(VanHanhModel.create(value));
  }

  if (errorCreate) throw errorCreate;

  return Promise.all(promiseCreate);
}

export async function createIndex(allData, index, data) {
  allData.sort(sortThuTu);
  const allVanHanhAfterInsert = insertObjToArr(allData, data, index);
  allVanHanhAfterInsert.forEach((vanHanh, i) => {
    vanHanh.thu_tu = i + 1;
  });
  const dataUpdate = allVanHanhAfterInsert.filter(item => item._id);
  await updateAll(dataUpdate);
  return allVanHanhAfterInsert.filter(item => !item._id)[0];
}

export async function updateIndex(allData, index, data) {
  allData.sort(sortThuTu);
  const allVanHanhAfterInsert = insertObjToArr(allData, data, index);
  allVanHanhAfterInsert.forEach((vanHanh, i) => {
    vanHanh.thu_tu = i + 1;
  });
  await updateAll(allVanHanhAfterInsert);
}

const Headers = {
  MA_THIET_BI: 'Mã thiết bị',
  DUONG_DAY: 'Đường dây/TBA',
  THIET_BI_CHA: 'Thiết bị/Công trình cha',
  TEN_THIET_BI: 'Thiết bị/Công trình',
  STT: 'STT',
  SERIAL: 'Số chế tạo/Serial',
  NGAY_VAN_HANH: 'Ngày vận hành',
  SO_TSCD: 'Số TSCĐ',
  TINH_TRANG_VAN_HANH: 'Tình trạng vận hành',
  MA_LIEN_KET_KHAC: 'Mã liên kết khác',
  HANG_SAN_XUAN: 'Hãng sản xuất',
  NHA_CUNG_CAP: 'Nhà cung cấp',
  NUOC_SAN_XUAT: 'Nước sản xuất',
  SO_HUU: 'Sở hữu',
  NGAY_LAP_DAT: 'Ngày lắp đặt',
  NAM_SAN_XUAT: 'Năm sản xuất',
  NGAY_SUA_DOI: 'Ngày sửa đổi',
  GHI_CHU: 'Ghi chú',
  DON_VI: 'Đơn vị',

  DUONG_DAY_VAN_HANH: 'Đường dây',
  MA_HIEU_COT: 'Mã hiệu cột',
  CONG_DUNG_COT: 'Công dụng cột',
  CHIEU_CAO: 'Chiều cao',
  TRONG_LUONG: 'Trọng lượng',
  SO_MACH_DAY_DAN: 'Số mạch dây dẫn',
  SO_MACH_DAY_CHONG_SET: 'Số mạch DCS',
  DO_RONG_CHAN_COT: 'Độ rộng chân cột',
  MONG_COT: 'Móng cột',
  LOAI_BULONG_NEO_MONG: 'Loại bulông neo móng',
  KE_MONG: 'Kè móng',
  DUONG_DAY_CHINH: 'Đường dây chính',
};

export async function importData(sheetData, mapDonVi, mapVitri, mapDuongDay) {
  const { rows } = sheetData;

  function convertToDB(row) {
    const thietBiInfo = TongKeService.thietBiCommonInfo(row, mapDonVi, mapVitri);
    return {
      ma_van_hanh: row[Headers.MA_THIET_BI]?.trim(),
      ten_van_hanh: trim(row[Headers.TEN_THIET_BI]),
      ...thietBiInfo,
      thu_tu: row[Headers.STT],
      tinh_trang_van_hanh: row[Headers.TINH_TRANG_VAN_HANH],
      duong_day_id: mapDuongDay[row[Headers.DUONG_DAY_VAN_HANH]?.trim()],
      duong_day_chinh: mapDuongDay[row[Headers.DUONG_DAY_CHINH]?.trim()],
    };
  }

  const dataToDB = rows.map(row => convertToDB(row)).filter(element => element.ten_van_hanh);

  await VanHanhModel.bulkWrite(
    dataToDB.map((vanHanh) =>
      ({
        updateOne: {
          filter: { ma_van_hanh: vanHanh.ma_van_hanh },
          update: { $set: vanHanh },
          upsert: true,
        },
      }),
    ),
  );

  const viTriId = Array.from(new Set(extractKeys(dataToDB, 'vi_tri_id')));
  const allKhoangCot = await KhoangCotService.getAll({ vi_tri_id: viTriId }, { vi_tri_id: 1 });
  const allVanHanh = await getAll({ vi_tri_id: viTriId }, { duong_day_id: 1, vi_tri_id: 1 });
  const vanHanhGroupViTri = groupBy(allVanHanh, 'vi_tri_id');
  const duongDayIdGroupViTri = Object.keys(vanHanhGroupViTri).reduce(function(prevValue, currentValue) {
    return { ...prevValue, [currentValue]: vanHanhGroupViTri[currentValue].map(vh => vh.duong_day_id) };
  }, {});
  allKhoangCot.forEach(khoangCot => {
    khoangCot.duong_day_id = duongDayIdGroupViTri[khoangCot.vi_tri_id];
  });
  return await KhoangCotService.updateAll(allKhoangCot);
}

export function checkImport(t, sheetData, mapDonVi, mapVitri, mapDuongDay) {
  if (!sheetData) return null;
  const { rows } = sheetData;

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row) {
    let errorString = [];
    errorString = [...errorString, ...TongKeService.checkCommonInfo(t, row)];
    errorString = [...errorString, ...TongKeService.checkThietBiCommonInfo(t, row, mapVitri)];

    if (!row[Headers.DON_VI]?.trim()) {
      errorString = [...errorString, createError(Headers.DON_VI, t('missing_unit_management'))];
    } else {
      if (!mapDonVi[row[Headers.DON_VI]?.trim()]) {
        errorString = [...errorString, createError(Headers.DON_VI, t('management_unit_incorrect_or_dont_create'))];
      }
    }

    if (!row[Headers.DUONG_DAY_VAN_HANH]?.trim()) {
      errorString = [...errorString, createError(Headers.DUONG_DAY_VAN_HANH, t('missing_line_operation'))];
    } else {
      if (!mapDuongDay[row[Headers.DUONG_DAY_VAN_HANH]?.trim()]) {
        errorString = [...errorString, createError(Headers.DUONG_DAY_VAN_HANH, t('line_operation_incorrect_or_dont_create'))];
      }
    }

    if (errorString.length) {
      row['Lỗi'] = errorString;
    } else {
      row['Lỗi'] = null;
    }
    return row;
  }

  sheetData.rows = rows.map(row => validateRow(t, row));
  return sheetData;
}

export async function findOne(query) {
  const vanHanh = await VanHanhModel.findOne(query).lean();
  if (vanHanh) {
    vanHanh.day_dan = await DayDanService.getAll({ van_hanh_id: vanHanh._id, is_deleted: false }).lean();
    vanHanh.cach_dien = await CachDienService.getAll({ van_hanh_id: vanHanh._id, is_deleted: false }).lean();
  }
  return vanHanh;
}

export async function findAllInOne(query, populateOpts = []) {
  const allVanHanh = await VanHanhModel.find(query)
    .populate({ path: 'duong_day_id', populate: 'loai_duong_day_id' })
    .populate(populateOpts)
    .lean();

  const mapVanHanh = {};
  allVanHanh.forEach(vanHanh => {
    mapVanHanh[vanHanh._id] = vanHanh.duong_day_id;
  });
  const allVanHanhIds = allVanHanh.map(vanHanh => vanHanh._id);
  const dayDanData = await DayDanService.getAll({ is_deleted: false, van_hanh_id: { $in: allVanHanhIds } });
  const cachDienData = await CachDienService.getAll({ is_deleted: false, van_hanh_id: { $in: allVanHanhIds } });

  dayDanData.forEach(dayDan => dayDan.duong_day_id = mapVanHanh[dayDan.van_hanh_id]);
  cachDienData.forEach(cachDien => cachDien.duong_day_id = mapVanHanh[cachDien.van_hanh_id]);

  const dayDanGroupByVanHanhId = groupBy(dayDanData, 'van_hanh_id');
  const cachDienGroupByVanHanhId = groupBy(cachDienData, 'van_hanh_id');
  for (let vanHanh of allVanHanh) {
    if (vanHanh) {
      vanHanh.day_dan_id = dayDanGroupByVanHanhId[vanHanh._id];
      vanHanh.cach_dien_id = cachDienGroupByVanHanhId[vanHanh._id];
    }
  }
  return allVanHanh;
}

const objSchema = Joi.object({
  ma_van_hanh: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã vận hành')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function deleteAll(query) {
  return VanHanhModel.remove(query);
}

export async function updateMaVanHanhByDuongDay(duongDayId, isVanHanh = true) {
  let allVanHanh;
  let vanHanhQuery = { duong_day_id: duongDayId, is_deleted: false };
  isVanHanh && (vanHanhQuery = { ...vanHanhQuery, is_old_van_hanh: true });
  allVanHanh = await VanHanhModel.find(vanHanhQuery).lean();
  allVanHanh.map(vanHanh => {
    vanHanh.ma_van_hanh = isVanHanh ? vanHanh.ma_van_hanh.split('/')[0] : [vanHanh.ma_van_hanh, 'OLD'].join('/');
    vanHanh.ten_van_hanh = isVanHanh ? vanHanh.ten_van_hanh.slice(0, -3) : [vanHanh.ten_van_hanh, 'OLD'].join('');
    vanHanh.is_old_van_hanh = !isVanHanh;
    return vanHanh;
  });

  await VanHanhModel.bulkWrite(
    allVanHanh.map((row) =>
      ({
        updateOne: {
          filter: { _id: row._id, is_deleted: false },
          update: { $set: row },
          upsert: false,
        },
      }),
    ),
  );
}
