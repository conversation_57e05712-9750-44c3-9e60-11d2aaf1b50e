import express from 'express';
import passport from 'passport';
import * as donviController from './vanHanh.controller';
import { loggerMiddleware } from '../../../logs/middleware';
import { updateSai } from './vanHanh.controller';

export const vanHanhRouter = express.Router();

vanHanhRouter
  .route('/updateSai')
  .get(donviController.updateSai);

vanHanhRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

vanHanhRouter
  .route('/')
  .get(donviController.getAll)
  .post(donviController.create);

vanHanhRouter
  .route('/dashboard')
  .get(donviController.getAllVanHanh);

vanHanhRouter
  .route('/:id')
  .get(donviController.findOne)
  .delete(donviController.remove)
  .put(donviController.update);


vanHanhRouter
  .route('/:id/nopopulate')
  .get(donviController.findOneNoPopulate)
