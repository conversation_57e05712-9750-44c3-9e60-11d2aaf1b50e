import mongoose, { Schem<PERSON> } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DON_VI, DUONG_DAY, VAN_HANH, VI_TRI } from '../../../constant/dbCollections';
import { TINH_TRANG_VAN_HANH } from '../../DanhMuc/TinhTrangVanHanh';

import * as KetQuaKiemTraService from '../../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.service';
import * as VanHanhService from './vanHanh.service';
import * as KhoangCotService from '../KhoangCot/khoangCot.service';

import KHOANG_COT from '../KhoangCot/khoangCot.model';

const schema = new Schema({
  ma_van_hanh: { type: String, required: true, unique: true, validate: /\S+/ },
  ma_thiet_bi_cha: { type: String, validate: /\S+/ },
  ma_tim_kiem: { type: String, validate: /\S+/ },
  ma_tim_kiem_cha: { type: String, validate: /\S+/ },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI, index: true },
  ten_van_hanh: { type: String },
  thu_tu: { type: Number },
  ngay_van_hanh: Date,
  tinh_trang_van_hanh: {
    type: String,
    enum: Object.values(TINH_TRANG_VAN_HANH),
    default: TINH_TRANG_VAN_HANH.VAN_HANH,
  },
  so_huu: String,
  ngay_lap_dat: Date,
  nam_san_xuat: Date,
  ngay_sua_doi: Date,
  ghi_chu: String,
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  duong_day_id: { type: Schema.Types.ObjectId, ref: DUONG_DAY, index: true },
  duong_day_chinh: { type: Schema.Types.ObjectId, ref: DUONG_DAY },
  is_old_van_hanh: { type: Boolean, default: false, select: false },

  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.pre('findOneAndUpdate', async function(next) {
  const currentData = await VanHanhService.getById(this._update?._id || this._conditions?._id, { vi_tri_id: 1 });
  if (currentData) {
    await updateKhoangCot(currentData);
  }
  next();
});

schema.post('findOneAndUpdate', async function(doc, next) {
  await updateKhoangCot(doc);
});

schema.post('save', async function(doc, next) {
  await updateKhoangCot(doc);
  await KetQuaKiemTraService.updateDuongDayIds(doc);
});

async function updateKhoangCot(currentData) {
  const allVanHanh = await VanHanhService.getAll({
    vi_tri_id: currentData.vi_tri_id,
    is_deleted: false,
  }, { duong_day_id: 1 });
  const allDuongDay = allVanHanh.map(vanHanh => vanHanh.duong_day_id);
  const allKhoangCot = await KhoangCotService.getAll(
    { vi_tri_id: currentData.vi_tri_id, is_deleted: false },
    { _id: 1 });
  await KHOANG_COT.bulkWrite(
    allKhoangCot.map((row) =>
      ({
        updateOne: {
          filter: { _id: row._id },
          update: { $set: { duong_day_id: allDuongDay } },
          upsert: false,
        },
      }),
    ),
  );
}

// Existing indexes
schema.index({ duong_day_id: 1, vi_tri_id: 1 });
schema.index({ don_vi_id: 1 });

// Optimized indexes for updateThongTinKhoangCot and updateThongTinViTri functions
// Index for vi_tri_id queries with is_deleted filter (used in findAllInOne)
schema.index({ vi_tri_id: 1, is_deleted: 1 }, { background: true });
// Index for duong_day_id array queries with is_deleted filter
schema.index({ duong_day_id: 1, is_deleted: 1 }, { background: true });
// Index for ma_van_hanh queries (used in sync operations)
schema.index({ ma_van_hanh: 1, is_deleted: 1 }, { background: true });
// Index for created_at with is_deleted for time-based queries
schema.index({ created_at: 1, is_deleted: 1 }, { background: true });
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(VAN_HANH, schema, VAN_HANH);
