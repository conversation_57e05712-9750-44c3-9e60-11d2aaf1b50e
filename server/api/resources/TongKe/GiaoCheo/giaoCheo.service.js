import * as ValidatorHelper from '../../../helpers/validatorHelper';
import GIAO_CHEO from './giaoCheo.model';
import moment from 'moment';
import { convertDate, trim } from '../../../common/functionCommons';
import { checkNumber, convertYear } from '../../../helpers/checkDataHelper';
import { checkCommonInfo, checkThietBiCommonInfo, thietBiCommonInfo } from '../TongKe.service';
import COT_DIEN from '../../../constant/dbCollections';
import { CommonHeaders } from '../ThietBi/thietBi.controller';

const Joi = require('joi');

const Headers = {
  MA_THIET_BI: 'Mã thiết bị',
  DUONG_DAY: 'Đường dây/TBA',
  THIET_BI_CHA: 'Thiết bị/Công trình cha',
  TEN_THIET_BI: 'Thiết bị/Công trình',
  STT: 'STT',
  SERIAL: 'Số chế tạo/Serial',
  NGAY_VAN_HANH: 'Ngày vận hành',
  SO_TSCD: 'Số TSCĐ',
  TINH_TRANG_VAN_HANH: 'Tình trạng vận hành',
  MA_LIEN_KET_KHAC: 'Mã liên kết khác',
  HANG_SAN_XUAN: 'Hãng sản xuất',
  NHA_CUNG_CAP: 'Nhà cung cấp',
  NUOC_SAN_XUAT: 'Nước sản xuất',
  SO_HUU: 'Sở hữu',
  NGAY_LAP_DAT: 'Ngày lắp đặt',
  NAM_SAN_XUAT: 'Năm sản xuất',
  NGAY_SUA_DOI: 'Ngày sửa đổi',
  GHI_CHU: 'Ghi chú',
  DON_VI: 'Đơn vị',

  DOI_TUONG_GIAO_CHEO: 'Đối tượng giao chéo',
  KHOANG_CACH_GIAO_CHEO: 'Khoảng cách giao chéo (min)',
  KHOANG_CACH_DIEM_GIAO_CHEO_DEN_VI_TRI: 'Khoảng cách điểm giao chéo đến vị trí',
  THONG_TIN_KHAC: 'Thông tin khác',
  BIEN_BAO_GIAO_CHEO_DUONG_THUY: 'Biển báo giao chéo đường thủy',
  BIEN_BAO_GIAO_CHEO_DUONG_BO: 'Biển báo giao chéo đường thủy',
  BIEN_CANH_BAO_AN_TOAN: 'Biển cảnh báo an toàn',
};


export async function importData(sheetData, mapDonVi, mapVitri) {
  const { rows } = sheetData;

  function convertToDB(row) {
    const thietBiInfo = thietBiCommonInfo(row, mapDonVi, mapVitri)
    return {
      ma_giao_cheo: row[Headers.MA_THIET_BI]?.trim(),
      ten_giao_cheo: trim(row[Headers.TEN_THIET_BI]),
      ...thietBiInfo,
      so_the_tscd: row[Headers.SO_TSCD],
      thu_tu: row[Headers.STT],
      ngay_van_hanh: convertDate(row[Headers.NGAY_VAN_HANH]),
      tinh_trang_van_hanh: row[Headers.TINH_TRANG_VAN_HANH],
      nuoc_san_xuat: row[Headers.NUOC_SAN_XUAT],
      nam_san_xuat: convertYear(row[Headers.NAM_SAN_XUAT]),
      so_huu: row[Headers.SO_HUU],
      ngay_lap_dat: convertDate(row[Headers.NGAY_LAP_DAT]),

      doi_tuong_giao_cheo: row[Headers.DOI_TUONG_GIAO_CHEO],
      khoang_cach_giao_cheo: row[Headers.KHOANG_CACH_GIAO_CHEO],
      khoang_cach_diem_giao_cheo_den_vi_tri: row[Headers.KHOANG_CACH_DIEM_GIAO_CHEO_DEN_VI_TRI],
      thong_tin_khac: row[Headers.THONG_TIN_KHAC],
      bien_bao_giao_cheo_duong_thuy: row[Headers.BIEN_BAO_GIAO_CHEO_DUONG_THUY],
      bien_bao_giao_cheo_duong_bo: row[Headers.BIEN_BAO_GIAO_CHEO_DUONG_BO],
      bien_canh_bao_an_toan: row[Headers.BIEN_CANH_BAO_AN_TOAN],
      is_deleted: false,
    };
  }

  const dataToDB = rows.map(row => convertToDB(row)).filter(element => element.ten_giao_cheo);
  const result = await GIAO_CHEO.bulkWrite(
    dataToDB.map((giaoCheo) =>
      ({
        updateOne: {
          filter: { ma_giao_cheo: giaoCheo.ma_giao_cheo },
          update: { $set: giaoCheo },
          upsert: true,
        },
      }),
    ),
  );
  return result;
}

export function checkImport(t, sheetData, mapDonVi, mapVitri) {
  if (!sheetData) return null;
  const { rows } = sheetData;

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row) {
    let errors = [];
    errors = [...errors, ...checkCommonInfo(t, row)];
    errors = [...errors, ...checkThietBiCommonInfo(t, row, mapVitri)];

    if (!row[Headers.DON_VI]?.trim()) {
      errors = [...errors, createError(Headers.DON_VI, t('missing_unit_construction'))];
    } else {
      if (!mapDonVi[row[Headers.DON_VI]?.trim()]) {
        errors = [...errors, createError(Headers.DON_VI, t('management_unit_incorrect_or_dont_create'))];
      }
    }

    // if (!checkNumber(row[Headers.KHOANG_CACH_GIAO_CHEO])) {
    //   errors = [...errors, createError(Headers.KHOANG_CACH_GIAO_CHEO, t('incorrect_or_not_available'))];
    // }
    //
    // if (!checkNumber(row[Headers.KHOANG_CACH_DIEM_GIAO_CHEO_DEN_VI_TRI])) {
    //   errors = [...errors, createError(Headers.KHOANG_CACH_DIEM_GIAO_CHEO_DEN_VI_TRI, t('incorrect_or_not_available'))];
    // }

    if (errors.length) {
      row['Lỗi'] = errors;
    } else {
      row['Lỗi'] = null;
    }
    return row;
  }

  sheetData.rows = rows.map(row => validateRow(t, row)).filter(element => element[Headers.TEN_THIET_BI]);
  return sheetData;
}

export function getAll(querry) {
  return GIAO_CHEO.find(querry)
    .lean();
}

export function count(querry) {
  return GIAO_CHEO.count(querry)
    .lean();
}

export async function updateAll(arrData) {
  await GIAO_CHEO.bulkWrite(
    arrData.map((row) =>
      ({
        updateOne: {
          filter: { _id: row._id },
          update: { $set: row },
          upsert: false,
        },
      }),
    ),
  );
}

const objSchema = Joi.object({
  ma_giao_cheo: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã vị trí')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function deleteAll(query) {
  return GIAO_CHEO.remove(query)
}
