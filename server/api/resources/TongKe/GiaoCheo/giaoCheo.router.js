import express from 'express';
import passport from 'passport';
import * as donviController from './giaoCheo.controller';

export const giaoCheoRouter = express.Router();
giaoCheoRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), donviController.getAll)
  .post(passport.authenticate('jwt', { session: false }), donviController.create);

giaoCheoRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), donviController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), donviController.remove)
  .put(passport.authenticate('jwt', { session: false }), donviController.update);
