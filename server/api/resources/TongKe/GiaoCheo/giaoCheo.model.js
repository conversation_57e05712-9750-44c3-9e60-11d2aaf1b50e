import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DON_VI, GIAO_CHEO, VI_TRI } from '../../../constant/dbCollections';
import { TINH_TRANG_VAN_HANH } from '../../DanhMuc/TinhTrangVanHanh';

const schema = new Schema({
  ma_giao_cheo: { type: String, required: true, unique: true, validate: /\S+/ },
  ma_thiet_bi_cha: { type: String, validate: /\S+/ },
  ma_tim_kiem: { type: String, validate: /\S+/ },
  ma_tim_kiem_cha: { type: String, validate: /\S+/ },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  ten_giao_cheo: { type: String },
  thu_tu: { type: Number },
  ngay_van_hanh: { type: Date },
  tinh_trang_van_hanh: {
    type: String,
    enum: Object.values(TINH_TRANG_VAN_HANH),
    default: TINH_TRANG_VAN_HANH.VAN_HANH,
  },
  so_huu: String,
  ngay_lap_dat: { type: Date },
  nam_san_xuat: { type: Date },
  ngay_sua_doi: { type: Date },
  ghi_chu: { type: String },
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  doi_tuong_giao_cheo: String,
  khoang_cach_giao_cheo: String,
  khoang_cach_diem_giao_cheo_den_vi_tri: String,
  thong_tin_khac: String,
  bien_bao_giao_cheo_duong_thuy: String,
  bien_bao_giao_cheo_duong_bo: String,
  bien_canh_bao_an_toan: String,

  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(GIAO_CHEO, schema, GIAO_CHEO);
