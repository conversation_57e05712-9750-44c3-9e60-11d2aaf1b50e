import express from 'express';
import passport from 'passport';
import * as Controller from './hanhLangTuyen.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import WorkPermission from '../../RBAC/permissions/WorkPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const hanhLangTuyenRouter = express.Router();

hanhLangTuyenRouter
  .route('/download')
  .get(Controller.downloadBieuMauTheoDoiHanhLangTuyen);

hanhLangTuyenRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
hanhLangTuyenRouter.post('*', authorizationMiddleware([WorkPermission.CREATE]));
hanhLangTuyenRouter.put('*', authorizationMiddleware([WorkPermission.UPDATE]));
hanhLangTuyenRouter.delete('*', authorizationMiddleware([WorkPermission.DELETE]));
hanhLangTuyenRouter.route('/')
  .get(Controller.getAll)
  .post(Controller.create);

hanhLangTuyenRouter
  .route('/createorupdate')
  .post(Controller.createOrUpdate);

hanhLangTuyenRouter
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
