import * as ValidatorHelper from '../../../helpers/validatorHelper';

import { VI_PHAM_QUY_DINH_HANH_LANG_TUYEN } from './index';

import HanhLangTuyenModel from './hanhLangTuyen.model';
import createBaseService from '../../../base/baseService';


const Joi = require('joi');

const objSchema = Joi.object({});

const baseService = createBaseService(HanhLangTuyenModel)

export const createMulti = baseService.createMulti;
export const getAll = baseService.getAll;
export const getById = baseService.getById;
export const getOne = baseService.getOne;
export const getByIdAndUpdate = baseService.getByIdAndUpdate;

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return HanhLangTuyenModel.create(value);
}

export async function createMany(dataList = []) {
  if (!Array.isArray(dataList)) {
    throw new Error('Input must be an array');
  }

  const validRecords = [];

  for (const data of dataList) {
    const { error, value } = validate(data);
    if (!error) {
      validRecords.push(value);
    }
  }

  return HanhLangTuyenModel.insertMany(validRecords);
}

export async function updateAll(dataUpdate, options = {}) {
  options.new = true;
  const results = [];
  for (const row of dataUpdate) {
    try {
      const { error, value } = validate(row);
      if (!error) {
        const updated = await HanhLangTuyenModel.findByIdAndUpdate(value._id, value, options);
        if (updated) results.push(updated);
      }
    } catch {
    }
  }
  return results;
}

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};

export async function bangTongHopHanhLangTuyen(id) {
  const allHanhLangTuyen = await getAll({ duong_day_id: id, is_deleted: false })
    .populate('duong_day_id khoang_cot_id')
    .sort({ thoi_gian_tao: -1 });

  function convertDataToRows(hanhLangTuyen, index) {
    return {
      stt: index + 1,
      duong_day_id: hanhLangTuyen.duong_day_id,
      khoang_cot_id: hanhLangTuyen.khoang_cot_id,
      hien_trang_hanh_lang_tuyen: hanhLangTuyen.hien_trang_hanh_lang_tuyen,
      chu_so_huu: hanhLangTuyen.chu_so_huu,
      thong_tin_lien_he: hanhLangTuyen.thong_tin_lien_he,
      dien_tich_khoi_luong: hanhLangTuyen.dien_tich_khoi_luong,
      vi_pham_quy_dinh: VI_PHAM_QUY_DINH_HANH_LANG_TUYEN[hanhLangTuyen?.vi_pham_quy_dinh]?.label,
      khoang_cach_gan_nhat: hanhLangTuyen.khoang_cach_gan_nhat,
      bien_phap_da_thuc_hien: hanhLangTuyen.bien_phap_da_thuc_hien,
      ghi_chu: hanhLangTuyen.ghi_chu,
    };
  }

  return allHanhLangTuyen.map(convertDataToRows);
}
