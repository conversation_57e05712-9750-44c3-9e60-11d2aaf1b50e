import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DUONG_DAY, HANH_LANG_TUYEN, KHOANG_COT, USER, VI_TRI } from '../../../constant/dbCollections';

const schema = new Schema({
  duong_day_id: { type: Schema.Types.ObjectId, ref: DUONG_DAY, required: true },
  khoang_cot_id: { type: Schema.Types.ObjectId, ref: KHOANG_COT },
  hien_trang_hanh_lang_tuyen: { type: String },
  chu_so_huu: { type: String },
  thong_tin_lien_he: { type: String },
  dien_tich_khoi_luong: { type: String },
  vi_pham_quy_dinh: { type: String },
  khoang_cach_gan_nhat: { type: String },
  bien_phap_da_thuc_hien: { type: String },
  ghi_chu: { type: String },
  nguoi_chinh_sua: { type: Schema.Types.ObjectId, ref: USER },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'thoi_gian_tao',
    updatedAt: 'thoi_gian_cap_nhat',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(HANH_LANG_TUYEN, schema, HANH_LANG_TUYEN);
