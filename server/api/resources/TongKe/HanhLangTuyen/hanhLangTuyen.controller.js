import * as Service from './hanhLangTuyen.service';
import Model from './hanhLangTuyen.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import queryHelper from '../../../helpers/queryHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import { Types } from 'mongoose';
import { generateDocument } from '../../Report/GenerateFile/generate.controller';
import { getFilePath } from '../../../utils/fileUtils';
import { TEMPLATES_DIRS } from '../../../constant/constant';
import i18next from 'i18next';

const searchLike = [];
const populateOpts = [
  { path: 'duong_day_id' },
  { path: 'khoang_cot_id' },
];

const sortOpts = { thoi_gian_tao: -1 };
const uniqueOpts = [];
export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, searchLike, populateOpts, sortOpts);
export const createOrUpdate = async function createOrUpdate(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    if (!Array.isArray(req.body)) {
      return responseHelper.error(res, null, 400);
    }
    const duongDayId = req.body[0].duong_day_id;
    req.body.forEach(row => {
      if (row._id) {
        row.nguoi_chinh_sua = req.user._id;
      } else {
        row._id = Types.ObjectId();
        row.nguoi_chinh_sua = req.user._id;
        row.is_deleted = false;
      }
    });
    const result = await Model.bulkWrite(
      req.body.map((row) =>
        ({
          updateOne: {
            filter: { _id: row._id },
            update: { $set: row },
            upsert: true,
          },
        }),
      ),
    );
    const updatedData = await Model.find({ duong_day_id: duongDayId, is_deleted: false })
      .populate(populateOpts)
      .populate({ path: 'nguoi_chinh_sua', select: 'full_name' })
      .lean();
    return responseHelper.success(res, updatedData);
  } catch (err) {
    console.log('err', err);
    return responseHelper.error(res, err, 500);
  }
};

export async function downloadBieuMauTheoDoiHanhLangTuyen(req, res) {
  try {
    const { t } = req;
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const hanhLangTuyen = await Service.bangTongHopHanhLangTuyen(criteria.id);
    let data = {};

    data.ten_duong_day = hanhLangTuyen[0].duong_day_id.ten_duong_day;

    data.hanh_lang_tuyen = hanhLangTuyen;

    const templateFilePath = getFilePath(`bieu_mau_hanh_lang_tuyen.xlsx`, TEMPLATES_DIRS.BIEU_MAU);
    const outputFileName = `${i18next.t('theo_doi_hanh_lang_tuyen')}.xlsx`;
    generateDocument(res, data, templateFilePath, outputFileName);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}
