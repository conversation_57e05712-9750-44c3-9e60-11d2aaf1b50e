import express from 'express';
import passport from 'passport';
import * as khoangNeoController from './khoangNeo.controller';
import { loggerMiddleware } from '../../../logs/middleware';

export const khoangNeoRouter = express.Router();

khoangNeoRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);


khoangNeoRouter
  .route('/')
  .get(khoangNeoController.getAll)
  .post(khoangNeoController.create);
khoangNeoRouter
  .route('/auto')
  .post(khoangNeoController.autoCreate);

khoangNeoRouter
  .route('/:id')
  .get(khoangNeoController.findOne)
  .delete(khoangNeoController.remove)
  .put(khoangNeoController.update);
