import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { KHOANG_NEO, VI_TRI } from '../../../constant/dbCollections';

const schema = new Schema({
  ten_khoang_neo: String,
  vi_tri_bat_dau_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  vi_tri_ket_thuc_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  so_cot: Number,
  // chieu_dai: Number,
  cong_don_tu_dau_day: Number,
  cong_don_tu_cuoi_day: Number,
  ghi_chu: String,
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export default mongoose.model(KHOANG_NEO, schema, KHOANG_NEO);
