import * as responseAction from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from './khoangNeo.service';
import Model from './khoangNeo.model';
import VI_TRI from '../ViTri/viTri.model';
import * as controllerHelper from '../../../helpers/controllerHelper';

const uniqueOpts = [];
const populateOpts = [
  { path: 'vi_tri_bat_dau_id', select: 'ten_vi_tri cong_trinh_id' },
  { path: 'vi_tri_ket_thuc_id', select: 'ten_vi_tri cong_trinh_id' },
];
export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);

export async function create(req, res) {

  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const vt1 = await VI_TRI.findById(value.vi_tri_bat_dau_id);
    const vt2 = await VI_TRI.findById(value.vi_tri_ket_thuc_id);
    const createOrUpdateData = Service.createData(vt1, vt2);
    const data = await Model.create(createOrUpdateData);
    const dataRtn = await data.populate(populateOpts).execPopulate();
    return responseAction.success(res, dataRtn);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function autoCreate(req, res) {
  try {
    await Service.createOrUpdateKhoangNeo();
    const data = await Service.getAll().count();
    return responseAction.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_duong_day', 'ma_duong_day']);
    const { criteria, options } = query;
    options.populate = populateOpts;
    options.sort = { thu_tu: 1 };
    const data = await Model.paginate(criteria, options);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}
