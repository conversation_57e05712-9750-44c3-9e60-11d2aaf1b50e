import * as ValidatorHelper from '../../../helpers/validatorHelper';
import * as CongTrinhService from '../CongTrinh/congTrinh.service';
import * as ViTriService from '../ViTri/viTri.service';
import * as CotDienService from '../CotDien/cotDien.service';
import KHOANG_NEO from './khoangNeo.model';
import { extractIds } from '../../../utils/dataconverter';

const Joi = require('joi');

const objSchema = Joi.object({
  ten_duong_day: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên đường dây')),
  ma_duong_day: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã đường dây')),
  loai_duong_day_id: Joi.string().messages(ValidatorHelper.messageDefine('<PERSON>ạ<PERSON> đường dây')),
});


export function createObj(cotNeoDau, cotNeoSau) {
  return {
    ten_khoang_neo: `${cotNeoDau.vi_tri_id?.ten_vi_tri}-${cotNeoSau.vi_tri_id?.ten_vi_tri}`,
    vi_tri_bat_dau_id: cotNeoDau.vi_tri_id?._id,
    vi_tri_ket_thuc_id: cotNeoSau.vi_tri_id?._id,
    is_deleted: false,
  };
}

export async function createData(listViTriIds) {
  let result = [];
  const listCotDien = (await CotDienService.getAll({ vi_tri_id: { $in: listViTriIds }, is_deleted: false })
    .populate({ path: 'vi_tri_id', select: 'ten_vi_tri khoang_neo thu_tu' }))
    .sort((cot1, cot2) => cot1.vi_tri_id?.thu_tu - cot2.vi_tri_id?.thu_tu);
  const listCotNeo = listCotDien.filter(cotDien => cotDien.cong_dung_cot?.trim()?.toLowerCase().includes('néo'));
  for (let i = 1; i < listCotNeo.length; i += 1) {
    const cotNeoDau = listCotNeo[i - 1];
    const cotNeoSau = listCotNeo[i];
    result.push(createObj(cotNeoDau, cotNeoSau));
  }
  return result;
}

export async function createOrUpdateKhoangNeo(listViTriIds) {
  let createOrUpdateData = [];
  if (listViTriIds) {
    createOrUpdateData = [...(await createData(listViTriIds))];
  } else {
    const allCongTrinh = await CongTrinhService.getAll({ is_deleted: false });
    for (let i = 0; i < allCongTrinh.length; i++) {
      const allViTri = await ViTriService.getAll({ cong_trinh_id: allCongTrinh[i]._id, is_deleted: false });
      const viTriIds = extractIds(allViTri);
      createOrUpdateData = [...createOrUpdateData, ...(await createData(viTriIds))];
    }
  }
  return KHOANG_NEO.bulkWrite(
    createOrUpdateData.map((khoangNeo) =>
      ({
        updateOne: {
          filter: { vi_tri_ket_thuc_id: khoangNeo.vi_tri_ket_thuc_id },
          update: { $set: khoangNeo },
          upsert: true,
        },
      }),
    ),
  );
}

export function getOne(query) {
  return KHOANG_NEO.findOne(query)
    .populate({ path: 'vi_tri_bat_dau_id', select: 'ten_vi_tri cong_trinh_id' })
    .populate({ path: 'vi_tri_ket_thuc_id', select: 'ten_vi_tri cong_trinh_id' })
    .lean();
}

export function getAll(query) {
  return KHOANG_NEO.find(query)
    .populate({ path: 'vi_tri_bat_dau_id', select: 'ten_vi_tri cong_trinh_id' })
    .populate({ path: 'vi_tri_ket_thuc_id', select: 'ten_vi_tri cong_trinh_id' })
    .lean();
}

export async function updateAll(arrData) {
  await KHOANG_NEO.bulkWrite(
    arrData.map((row) =>
      ({
        updateOne: {
          filter: { _id: row._id },
          update: { $set: row },
          upsert: false,
        },
      }),
    ),
  );
}

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
