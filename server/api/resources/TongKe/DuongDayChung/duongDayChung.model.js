import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DUONG_DAY_CHUNG, DON_VI } from '../../../constant/dbCollections';

const schema = new Schema({
  ten_duong_day_chung: { type: String, required: true, validate: /\S+/ },
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  thu_tu: { type: Number },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(DUONG_DAY_CHUNG, schema, DUONG_DAY_CHUNG);
