import express from 'express';
import passport from 'passport';
import * as duongDayChinhController from './duongDayChung.controller';

export const duongDayChungRouter = express.Router();
duongDayChungRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), duongDayChinhController.getAll)
  .post(passport.authenticate('jwt', { session: false }), duongDayChinhController.create);

duongDayChungRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), duongDayChinhController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), duongDayChinhController.remove)
  .put(passport.authenticate('jwt', { session: false }), duongDayChinhController.update);
