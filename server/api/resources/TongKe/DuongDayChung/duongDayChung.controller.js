import * as Service from './duongDayChung.service';
import Model from './duongDayChung.model';
import * as controllerHelper from '../../../helpers/controllerHelper';

const searchLike = ['ten_duong_day_chung'];
const populateOpts = ['don_vi_id'];
const uniqueOpts = [{ field: 'ten_duong_day_chung', message: 'Tên đường dây chung' }];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, searchLike, populateOpts);

