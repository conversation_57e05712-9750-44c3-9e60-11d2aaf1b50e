import fetch from 'node-fetch';

import { getAgent } from '../../../helpers/requestHelper';
import * as responseAction from '../../../helpers/responseHelper';


export async function getPmisProxy(req, res) {
  try {
    const url = 'http://10.3.1.10' + req.url;
    const options = { agent: getAgent(url) };
    const dataResponse = await fetch(url, options).then(response => response.json());
    return responseAction.success(res, dataResponse);
  } catch (err) {
    return responseAction.error(res, err);
  }
}
