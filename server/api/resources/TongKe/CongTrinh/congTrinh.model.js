import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CONG_TRINH, DON_VI, LOAI_DUONG_DAY } from '../../../constant/dbCollections';
import { TINH_TRANG_VAN_HANH } from '../../DanhMuc/TinhTrangVanHanh';

const schema = new Schema({
  ma_cong_trinh: { type: String, required: true, unique: true, validate: /\S+/ },
  ten_cong_trinh: { type: String, required: true, validate: /\S+/ },
  ma_thiet_bi_cha: { type: String, validate: /\S+/ },
  ma_tim_kiem: { type: String, validate: /\S+/ },
  ma_tim_kiem_cha: { type: String, validate: /\S+/ },
  thu_tu: { type: Number },
  ngay_van_hanh: { type: Date },
  tinh_trang_van_hanh: {
    type: String,
    enum: Object.values(TINH_TRANG_VAN_HANH),
    default: undefined,
  },
  so_huu: String,
  ngay_lap_dat: { type: Date },
  nam_san_xuat: Date,
  ngay_sua_doi: Date,
  ghi_chu: String,
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  cong_trinh_chinh_id: { type: Schema.Types.ObjectId, ref: CONG_TRINH },
  nam_xay_dung: Date,
  ngay_dong_dien: Date,
  don_vi_quan_ly_du_an: String,
  don_vi_quan_ly_van_hanh: String,
  ngay_ban_giao_tai_san: Date,
  nhom_cong_trinh_du_an: String,
  trang_thai_ban_giao: { type: Boolean, default: false },
  loai_duong_day_id: { type: Schema.Types.ObjectId, ref: LOAI_DUONG_DAY },
  so_mach: { type: Number },
  dong_bo_lan_cuoi: { type: Date },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.index({ ma_cong_trinh: 1 });
schema.index({ ten_cong_trinh: 1 });
schema.index({ ma_cong_trinh: 1, ten_cong_trinh:1 });
schema.plugin(mongoosePaginate);
export default mongoose.model(CONG_TRINH, schema, CONG_TRINH);
