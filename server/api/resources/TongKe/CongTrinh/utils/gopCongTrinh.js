import CommonError from '../../../../error/CommonError';
import { cloneObj } from '../../../../common/functionCommons';

import * as ViTriService from '../../ViTri/viTri.service';
import * as BienBanDongDienService from '../../../QuanLyTonTaiCongTrinhXayDung/BienBanDongDien/bienBan.service';
import * as PhuLucTonTaiService from '../../../QuanLyTonTaiCongTrinhXayDung/PhuLucTonTai/phuLucTonTai.service';
import * as TonTaiLamQuangService from '../../../QuanLyTonTaiCongTrinhXayDung/TonTaiLamQuang/tonTaiLamQuang.service';
import * as TonTaiThoiDiemBanGiaoService
  from '../../../QuanLyTonTaiCongTrinhXayDung/TonTaiThoiDiemBanGiao/tonTaiThoiDiemBanGiao.service';
import * as CongTrinhCongViecService from '../../../QuanLyVanHanh/CongTrinhCongViec/congTrinhCongViec.service';
import * as TaiLieuService from '../../../TaiLieu/taiLieu.service';
import * as FileHoSoService from '../../../QuanLyVanHanh/HoSo/LinkFile/linkFile.service';

async function gopCongTrinh(dataRequest) {
  if (!Array.isArray(dataRequest.cong_trinh_can_gop)
    || !dataRequest.cong_trinh_can_gop.includes(dataRequest.cong_trinh_hop_nhat)) {
    return { success: false, error: CommonError.NOT_FOUND };
  }
  const congTrinhDich = dataRequest.cong_trinh_hop_nhat;
  const congTrinhCanGop = dataRequest.cong_trinh_can_gop.filter(duongDayId => duongDayId !== congTrinhDich);

  const dataCurrent = await ViTriService.getAll({ cong_trinh_id: congTrinhCanGop }, { cong_trinh_id: 1 });

  const dataUpdate = cloneObj(dataCurrent)
    .map(currentItem => {
      currentItem.cong_trinh_id = congTrinhDich;
      return currentItem;
    });

  const queryRelate = { $or: congTrinhCanGop.map(ctId => ({ cong_trinh_id: ctId })) };
  const fileHoSoQuery = { $or: congTrinhCanGop.map(ctId => ({ doi_tuong_id: ctId })) };

  const [
    vi_tri,
    bien_ban_dong_dien,
    phu_luc_ton_tai,
    ton_tai_lam_quang,
    ton_tai_thoi_diem_ban_giao,
    cong_trinh_cong_viec,
    tai_lieu,
    file_ho_so,
  ] = await Promise.all([
    ViTriService.updateAll(dataUpdate, { timestamps: false }),
    BienBanDongDienService.updateByQuery(queryRelate, { cong_trinh_id: congTrinhDich }, { timestamps: false }),
    PhuLucTonTaiService.updateByQuery(queryRelate, { cong_trinh_id: congTrinhDich }, { timestamps: false }),
    TonTaiLamQuangService.updateByQuery(queryRelate, { cong_trinh_id: congTrinhDich }, { timestamps: false }),
    TonTaiThoiDiemBanGiaoService.updateByQuery(queryRelate, { cong_trinh_id: congTrinhDich }, { timestamps: false }),
    CongTrinhCongViecService.updateByQuery(queryRelate, { cong_trinh_id: congTrinhDich }, { timestamps: false }),
    TaiLieuService.updateByQuery(fileHoSoQuery, { doi_tuong_id: congTrinhDich }, { timestamps: false }),
    FileHoSoService.updateByQuery(fileHoSoQuery, { doi_tuong_id: congTrinhDich }, { timestamps: false }),
  ]);

  return {
    success: true,
    data: {
      vi_tri,
      bien_ban_dong_dien,
      phu_luc_ton_tai,
      ton_tai_lam_quang,
      ton_tai_thoi_diem_ban_giao,
      cong_trinh_cong_viec,
      tai_lieu,
      file_ho_so,
    },
  };
}


export default gopCongTrinh;
