import fs from 'fs';
import path from 'path';

import CommonError from '../../../../error/CommonError';
import { convertObject, extractIds, extractKeys } from '../../../../utils/dataconverter';
import { cloneObj } from '../../../../common/functionCommons';

import * as CongTrinhService from '../congTrinh.service';
import * as ViTriService from '../../ViTri/viTri.service';
import * as BienBanDongDienService from '../../../QuanLyTonTaiCongTrinhXayDung/BienBanDongDien/bienBan.service';
import * as PhuLucTonTaiService from '../../../QuanLyTonTaiCongTrinhXayDung/PhuLucTonTai/phuLucTonTai.service';
import * as TonTaiLamQuangService from '../../../QuanLyTonTaiCongTrinhXayDung/TonTaiLamQuang/tonTaiLamQuang.service';
import * as TonTaiThoiDiemBanGiaoService
  from '../../../QuanLyTonTaiCongTrinhXayDung/TonTaiThoiDiemBanGiao/tonTaiThoiDiemBanGiao.service';
import * as CongTrinhCongViecService from '../../../QuanLyVanHanh/CongTrinhCongViec/congTrinhCongViec.service';

import * as TaiLieuService from '../../../TaiLieu/taiLieu.service';

import * as FileHoSoService from '../../../QuanLyVanHanh/HoSo/LinkFile/linkFile.service';
import { deleteAll } from '../congTrinh.service';
import * as fileUtils from '../../../../utils/fileUtils';


async function tachCongTrinh(req) {
  const { t } = req;
  const { id } = req.params;

  const dataRequest = req.body;

  const congTrinhCurrent = await CongTrinhService.getOne({ _id: id, is_deleted: false });
  if (!congTrinhCurrent) {
    return { success: false, error: CommonError.NOT_FOUND };
  }

  const tenCongTrinh = [], maCongTrinh = [];
  dataRequest
    .filter(x => !!x.ma_cong_trinh?.toString()?.trim() && !!x.ten_cong_trinh?.toString()?.trim())
    .forEach((item) => {
      tenCongTrinh.push(item.ten_cong_trinh?.toString()?.trim());
      maCongTrinh.push(item.ma_cong_trinh?.toString()?.trim());
    });

  if (!tenCongTrinh.length || !maCongTrinh.length
    || tenCongTrinh.length !== maCongTrinh.length
    || tenCongTrinh.length !== dataRequest?.length
  ) {
    return { success: false, error: CommonError.NOT_FOUND };
  }


  const checkUnique = await CongTrinhService.getAll({ ma_cong_trinh: maCongTrinh });
  if (checkUnique.length) {
    const codeExist = extractKeys(checkUnique, 'ma_cong_trinh').join(', ');
    const message = t('CONSTRUCTION_CODE_EXIST').format(codeExist);
    return { success: false, error: { message } };
  }

  const dataCreate = cloneObj(req.body).map(row => {
    row.tinh_trang_van_hanh = congTrinhCurrent.tinh_trang_van_hanh;
    row.don_vi_id = congTrinhCurrent.don_vi_id;
    row.loai_duong_day_id = congTrinhCurrent.loai_duong_day_id;
    row.so_mach = congTrinhCurrent.so_mach;

    return row;
  });

  const congTrinhMoi = await CongTrinhService.createMany(dataCreate);
  const congTrinhGroupByCode = convertObject(congTrinhMoi, 'ma_cong_trinh');
  const dataUpdate = [];
  req.body.forEach(row => {
    row.vi_tri_id.filter(vtId => {
      dataUpdate.push({
        _id: vtId,
        cong_trinh_id: congTrinhGroupByCode[row.ma_cong_trinh]._id,
      });
    });
  });

  const congTrinhMoiId = extractIds(congTrinhMoi);

  const [
    data_updated,
    bien_ban_dong_dien,
    phu_luc_ton_tai,
    ton_tai_lam_quang,
    ton_tai_thoi_diem_ban_giao,
    cong_trinh_cong_viec,
    tai_lieu,
    file_ho_so,
  ] = await Promise.all([
    ViTriService.updateAll(dataUpdate),
    handleCloneRelateData(BienBanDongDienService, id, congTrinhMoiId),
    handleCloneRelateData(PhuLucTonTaiService, id, congTrinhMoiId),
    handleCloneRelateData(TonTaiLamQuangService, id, congTrinhMoiId),
    handleCloneRelateData(TonTaiThoiDiemBanGiaoService, id, congTrinhMoiId),
    handleCloneRelateData(CongTrinhCongViecService, id, congTrinhMoiId),
    handleCloneTaiLieuData(id, congTrinhMoiId),
    handleCloneHoSoData(id, congTrinhMoiId),
  ]);

  return {
    success: true,
    data: {
      data_updated,
      bien_ban_dong_dien,
      phu_luc_ton_tai,
      ton_tai_lam_quang,
      ton_tai_thoi_diem_ban_giao,
      cong_trinh_cong_viec,
      tai_lieu,
      file_ho_so,
    },
  };
}

async function handleCloneRelateData(service, congTrinhGocId, congTrinhMoiId) {
  const currentData = await service.getAll({ cong_trinh_id: congTrinhGocId });
  const dataClone = [];

  cloneObj(currentData).forEach(currentItem => {
    delete currentItem._id;

    congTrinhMoiId.forEach(ctId => {
      dataClone.push({
        ...currentItem,
        cong_trinh_id: ctId,
      });
    });
  });

  return service.createMulti(dataClone, { timestamps: false });
}

async function handleCloneHoSoData(congTrinhGocId, congTrinhMoiId) {
  const currentData = await FileHoSoService.getAll({ doi_tuong_id: congTrinhGocId });
  const dataClone = [];

  cloneObj(currentData).forEach(currentItem => {
    delete currentItem._id;

    congTrinhMoiId.forEach(ctId => {
      dataClone.push({
        ...currentItem,
        doi_tuong_id: ctId,
      });
    });
  });

  return FileHoSoService.createMulti(dataClone, { timestamps: false });
}

async function handleCloneTaiLieuData(congTrinhGocId, congTrinhMoiId) {
  const currentData = await TaiLieuService.getAll({ doi_tuong_id: congTrinhGocId });
  const dataClone = [];

  cloneObj(currentData).forEach(currentItem => {
    delete currentItem._id;
    const filePath = fileUtils.getFilePath(currentItem.file_id);
    congTrinhMoiId.forEach(ctId => {
      dataClone.push({
        ...currentItem,
        doi_tuong_id: ctId,
        file_id: cloneFileWithNewTimestamp(filePath),
      });
    });
  });

  return TaiLieuService.createMulti(dataClone, { timestamps: false });
}

function cloneFileWithNewTimestamp(filePath) {
  if (!fs.existsSync(filePath)) {
    return null;
  }

  const dir = path.dirname(filePath);
  const ext = path.extname(filePath);
  const fileName = path.basename(filePath, ext);

  // Regex nhận timestamp ở cuối, có hoặc không dấu "_"
  const timestampRegex = /_?\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}(?:[.-]\d+)?Z$/;

  // Loại bỏ timestamp cũ nếu có
  const baseName = fileName.replace(timestampRegex, '');

  // Tạo timestamp mới
  const now = new Date();
  const timestamp = now.toISOString().replace(/[:.]/g, '-');

  // Luôn gắn _ trước timestamp mới
  const newFileName = `${baseName}_${timestamp}${ext}`;
  const newFilePath = path.join(dir, newFileName);

  fs.copyFileSync(filePath, newFilePath);

  return newFileName;
}


export default tachCongTrinh;
