import { Types } from 'mongoose';

import { cloneObj } from '../../../common/functionCommons';
import { formatDate } from '../../../common/formatUTCDateToLocalDate';
import * as responseHelper from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';
import { generateDocument } from '../../Report/GenerateFile/generate.controller';
import { getFilePath } from '../../../utils/fileUtils';
import CommonError from '../../../error/CommonError';

import Model from './congTrinh.model';
import DON_VI from '../../DonVi/donVi.model';

import * as Service from './congTrinh.service';
import * as CongTrinhService from './congTrinh.service';
import * as ViTriService from '../ViTri/viTri.service';
import * as DonViService from '../../DonVi/donVi.service';
import * as TongKeService from '../TongKe.service';
import * as CotDienService from '../CotDien/cotDien.service';
import * as DayCapQuang from '../DayCapQuang/dayCapQuang.service';
import * as DayChongSet from '../DayChongSet/dayChongSet.service';
import * as GiaoCheo from '../GiaoCheo/giaoCheo.service';
import * as TiepDat from '../TiepDat/tiepDat.service';
import * as VanHanhService from '../VanHanh/vanHanh.service';
import * as CachDien from '../DayDan/dayDan.service';
import * as DayDan from '../CachDien/cachDien.service';


import { TINH_TRANG_VAN_HANH } from '../../DanhMuc/TinhTrangVanHanh';
import { CAP_DON_VI, TEMPLATES_DIRS } from '../../../constant/constant';
import { convertObject, extractIds, extractKeys } from '../../../utils/dataconverter';
import gopCongTrinh from './utils/gopCongTrinh';
import tachCongTrinh from './utils/tachCongTrinh';


export async function deleteAllThietBi(req, res) {
  try {
    const { id } = req.params;
    const checkCongTrinhChinh = await Service.getOne({ cong_trinh_chinh_id: id, is_deleted: false }, { _id: true });
    if (checkCongTrinhChinh) {
      return responseHelper.error(res, { message: 'Không thể xóa công trình chính' }, 400);
    }
    const congTrinh = await CongTrinhService.getOne({ _id: id });
    if (!congTrinh) return responseHelper.error(res, { message: 'Không tìm thấy công trình' }, 404);
    const viTris = await ViTriService.getAll({ cong_trinh_id: id });
    const viTrisId = extractIds(viTris);
    const cotDien = await CotDienService.deleteAll({ vi_tri_id: viTrisId });
    const dayCapQuang = await DayCapQuang.deleteAll({ vi_tri_id: viTrisId });
    const dayChongSet = await DayChongSet.deleteAll({ vi_tri_id: viTrisId });
    const giaoCheo = await GiaoCheo.deleteAll({ vi_tri_id: viTrisId });
    const tiepDat = await TiepDat.deleteAll({ vi_tri_id: viTrisId });

    const vanHanhs = await VanHanhService.getAll({ vi_tri_id: viTrisId });
    const vanHanhIds = extractIds(vanHanhs);
    const cachDien = await CachDien.deleteAll({ van_hanh_id: vanHanhIds });
    const dayDan = await DayDan.deleteAll({ van_hanh_id: vanHanhIds });
    const vanHanh = await VanHanhService.deleteAll({ vi_tri_id: viTrisId });
    return responseHelper.success(res, {
      congTrinh, cotDien, dayCapQuang, dayChongSet,
      giaoCheo, tiepDat, cachDien, dayDan, vanHanh,
    });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function deleteAllUnusedViTri(req, res) {
  try {
    const { id } = req.params;
    const checkCongTrinhChinh = await Service.getOne({ cong_trinh_chinh_id: id, is_deleted: false }, { _id: true });
    if (checkCongTrinhChinh) {
      return responseHelper.error(res, { message: 'Không thể xóa công trình chính' }, 400);
    }
    const congTrinh = await CongTrinhService.getOne({ _id: id });
    if (!congTrinh) return responseHelper.error(res, { message: 'Không tìm thấy công trình' }, 404);
    const viTris = await ViTriService.deleteAll({ cong_trinh_id: id, is_deleted: true });
    return responseHelper.success(res, { congTrinh, viTris });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


const populateOpts = [
  { path: 'cong_trinh_chinh_id' },
  { path: 'don_vi_id' },
  { path: 'ban_quan_ly_du_an_id' },
];

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id).populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, 404, '');
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const checkViTri = await ViTriService.getOne({ cong_trinh_id: id, is_deleted: false }, { _id: true });
    if (checkViTri) {
      return responseHelper.error(res, { message: 'Không thể xóa công trình đã có vị trí' }, 400);
    }
    const checkCongTrinhChinh = await Service.getOne({ cong_trinh_chinh_id: id, is_deleted: false }, { _id: true });
    if (checkCongTrinhChinh) {
      return responseHelper.error(res, { message: 'Không thể xóa công trình chính' }, 400);
    }

    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseHelper.error(res, 404, '');
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    const isUnique = await Model.findOne({
      ma_cong_trinh: value.ma_cong_trinh,
      is_deleted: false,
      _id: { $ne: value._id },
    }, { _id: 1 });
    if (isUnique) {
      return responseHelper.error(res, { message: 'Mã công trình đã tồn tại, vui lòng kiểm tra và thử lại' }, 400);
    }
    if (value.nam_xay_dung && value.ngay_van_hanh && value.nam_xay_dung > value.ngay_van_hanh) {
      return responseHelper.error(res, { message: 'Năm xây dựng phải nhỏ hơn năm vận hành, vui lòng kiểm tra và thử lại' }, 400);
    }
    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true }).populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function create(req, res) {

  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    const isUnique = await Model.findOne({ ma_cong_trinh: value.ma_cong_trinh, is_deleted: false }, { _id: 1 });
    if (isUnique) {
      return responseHelper.error(res, { message: 'Mã công trình đã tồn tại, vui lòng kiểm tra và thử lại' }, 400);
    }
    if (value.nam_xay_dung && value.ngay_van_hanh && value.nam_xay_dung > value.ngay_van_hanh) {
      return responseHelper.error(res, { message: 'Năm xây dựng phải nhỏ hơn năm vận hành, vui lòng kiểm tra và thử lại' }, 400);
    }
    let data = await Model.create(value);
    data = await Model.populate(data, populateOpts);
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseHelper.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ma_cong_trinh', 'ten_cong_trinh']);
    const { criteria, options } = query;
    if (criteria.not_include_child) {
      delete criteria.not_include_child;
    } else {
      criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);
    }
    if (!options.sort) {
      options.sort = { don_vi_id: 1, loai_duong_day_id: -1, thu_tu: 1, ten_cong_trinh: 1 };
    }
    options.populate = populateOpts;
    const data = await Model.paginate(criteria, options);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getCongTrinhDoiTruyenTai(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ma_cong_trinh', 'ten_cong_trinh']);
    const { criteria, options } = query;
    const donViId = await DON_VI.findById(
      criteria.don_vi_id || req.user?.don_vi_id,
      { cap_don_vi: 1, don_vi_cha_id: 1 }).lean();
    if (donViId.cap_don_vi !== CAP_DON_VI.DOI_TRUYEN_TAI_DIEN) {
      criteria.don_vi_id = await DonViService.getDonViInScope(donViId._id);
    } else {
      criteria.don_vi_id = [donViId.don_vi_cha_id?._id, donViId._id];
    }
    options.sort = { don_vi_id: 1, thu_tu: 1, ten_cong_trinh: 1 };
    options.populate = populateOpts;
    const data = await Model.paginate(criteria, options);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllChuaBanGiao(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ma_cong_trinh', 'ten_cong_trinh']);
    const { criteria, options } = query;
    if (criteria.not_include_child) {
      delete criteria.not_include_child;
    } else {
      criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);
    }
    criteria.trang_thai_ban_giao = { $ne: true };
    options.sort = { thu_tu: 1 };
    options.populate = populateOpts;
    const data = await Model.paginate(criteria, options);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllExactlyDonViId(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ma_cong_trinh', 'ten_cong_trinh']);
    const { criteria, options } = query;
    options.sort = { thu_tu: 1 };
    options.populate = populateOpts;
    const data = await Model.paginate(criteria, options);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function getAllCongTrinh(req, res) {
  try {
    let query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);
    options.populate = populateOpts;
    options.sort = { thu_tu: 1 };
    const data = await Model.paginate(criteria, options);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getAllCongTrinhXayDung(req, res) {
  try {
    let query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    criteria.tinh_trang_van_hanh = TINH_TRANG_VAN_HANH.DANG_XAY_DUNG;
    options.populate = populateOpts;
    options.sort = { created_at: 1 };
    const data = await Model.paginate(criteria, options);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function getAllViTri(req, res) {
  try {
    const { id } = req.params;
    const data = await Service.getViTriByCongTrinh(req, id);
    return responseHelper.success(res, data);
  } catch (err) {
    console.log(err);
    return responseHelper.error(res, err);
  }
}

export async function getAllCongTrinhChinh(req, res) {
  try {
    let query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const donVi = await DonViService.getById(criteria.don_vi_id, { don_vi_cha_id: 1, ten_don_vi: 1 });
    if (!donVi.don_vi_cha_id) {
      return responseHelper.success(res, []);
    }
    criteria.don_vi_id = donVi.don_vi_cha_id;
    const data = await Model.find(criteria)
      .populate(populateOpts)
      .sort({ thu_tu: 1, ten_cong_trinh: 1 })
      .lean();
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function exportTongKe(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const dataTongKe = await TongKeService.summaryDataTongKe(criteria);
    const dataToGenetate = await TongKeService.convertDataTongKe(dataTongKe);

    const listConstructionType = ['110', '220', '500'];
    let constructionType = '500';
    listConstructionType.forEach(type => {
      if (dataToGenetate.cong_trinh[0]?.ten_cong_trinh?.includes(type)) {
        constructionType = type;
      }
    });
    dataToGenetate.kv = constructionType;
    const templateFilePath = getFilePath('export_tong_ke_cong_trinh.xlsx', TEMPLATES_DIRS.TONG_KE);
    const outputFileName = 'Tổng kê công trình.xlsx';
    generateDocument(res, dataToGenetate, templateFilePath, outputFileName);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function exportDanhSachCongTrinh(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    delete criteria.danh_sach_cot;

    const selectedColumns = req.query.danh_sach_cot.split(',');

    const data = await Model.find(criteria)
      .populate(populateOpts)
      .sort({ 'loai_duong_day_id.ten_loai': -1, thu_tu: 1, ten_cong_trinh: 1 })
      .lean();

    data.forEach(item => {
      item.ngay_van_hanh = formatDate(item.ngay_van_hanh) || '';
      item.nam_xay_dung = new Date(item.ngay_van_hanh).getFullYear() || '';
    });
    const templateFilePath = getFilePath('export_danh_sach_cong_trinh.xlsx', TEMPLATES_DIRS.TONG_KE);

    await CongTrinhService.genExcelCongTrinhByColumns(res, data, templateFilePath, null, selectedColumns);

  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function findAllCongTrinh(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ma_cong_trinh', 'ten_cong_trinh']);
    const { criteria } = query;
    let field = '';
    if (criteria.ma_cong_trinh) field = 'ma_cong_trinh';
    if (criteria.ten_cong_trinh) field = 'ten_cong_trinh';
    let data = await Model.distinct(field, criteria).lean();
    if (data.length) {
      data = data.map((row) => {
        return { [field]: row };
      });
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function sortAndPaginateCongTrinh(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ma_cong_trinh', 'ten_cong_trinh']);
    const { criteria, options } = query;
    if (criteria.not_include_child) {
      delete criteria.not_include_child;
      if (criteria.don_vi_id) {
        criteria.don_vi_id = Types.ObjectId(criteria.don_vi_id);
      }
    }
    if (!options.sort) {
      options.sort = { 'loai_duong_day_id.ten_loai': -1, thu_tu: 1, ten_cong_trinh: 1 };
    } else {
      if (options.sort.loai_duong_day_id) {
        options.sort['loai_duong_day_id.ten_loai'] = options.sort.loai_duong_day_id;
        delete options.sort.loai_duong_day_id;
      }
      if (options.sort.cong_trinh_chinh_id) {
        options.sort['cong_trinh_chinh_id.ten_cong_trinh'] = options.sort.cong_trinh_chinh_id;
        delete options.sort.cong_trinh_chinh_id;
      }
    }
    let data = await CongTrinhService.sortAndPaginateCongTrinh(criteria, options);
    data = data[0];
    data.total_docs = data.extend[0]?.total_docs || 0;
    data.limit = options.limit;
    data.page = parseInt(options.page);
    delete data.extend;
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function gopDuLieuCongTrinh(req, res) {
  try {
    const dataUpdated = await gopCongTrinh(req.body);
    if (!dataUpdated.success) {
      return responseHelper.error(res, dataUpdated.error);
    }

    return responseHelper.success(res, dataUpdated.data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function tachDuLieuCongTrinh(req, res) {
  try {
    const dataUpdated = await tachCongTrinh(req);
    if (!dataUpdated.success) {
      return responseHelper.error(res, dataUpdated.error);
    }

    return responseHelper.success(res, dataUpdated.data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}
