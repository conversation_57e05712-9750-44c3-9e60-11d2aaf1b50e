import * as ValidatorHelper from '../../../helpers/validatorHelper';
import { convertDate } from '../../../helpers/checkDataHelper';
import { checkCommonInfo } from '../TongKe.service';
import CongTrinhModel from './congTrinh.model';
import * as ViTriService from '../ViTri/viTri.service';
import { extractIds, groupBy } from '../../../utils/dataconverter';
import * as KhoangNeoService from '../KhoangNeo/khoangNeo.service';
import { trim } from '../../../common/functionCommons';
import KHOANG_COT from '../KhoangCot/khoangCot.model';
import { CommonHeaders } from '../ThietBi/thietBi.controller';
import ExcelJs from 'exceljs';
import { renderDataToFileTemp } from '../../Report/GenerateFile/generate.common';
import createBaseService from '../../../base/baseService';

const Joi = require('joi');


const baseService = createBaseService(CongTrinhModel);

export const createMulti = baseService.createMulti;
export const getAll = baseService.getAll;
export const getOne = baseService.getOne;
export const getById = baseService.getById;
export const distinctId = baseService.distinctId;
export const updateByQuery = baseService.updateByQuery;
export const updateByRows = baseService.updateByRows;
export const deleteAll = baseService.deleteAll;
export const updateAll = baseService.updateAll;

export function findOneAndUpdate(filter, update = {}) {
  return CongTrinhModel.findOneAndUpdate(filter, update, { new: true }).populate('don_vi_id');
}

export function remove(query) {
  return CongTrinhModel.deleteMany(query).lean();
}


export async function createMany(dataList = []) {
  if (!Array.isArray(dataList)) {
    throw new Error('Input must be an array');
  }

  const validRecords = [];

  for (const data of dataList) {
    const { error, value } = validate(data);
    if (!error) {
      validRecords.push(value);
    }
  }

  return CongTrinhModel.insertMany(validRecords);
}

const objSchema = Joi.object({
  ten_cong_trinh: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên công trình')),
  ma_cong_trinh: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã công trình')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

const Headers = {
  MA_THIET_BI: 'Mã thiết bị',
  DUONG_DAY: 'Đường dây/TBA',
  THIET_BI_CHA: 'Thiết bị/Công trình cha',
  TEN_THIET_BI: 'Thiết bị/Công trình',
  STT: 'STT',
  SERIAL: 'Số chế tạo/Serial',
  NGAY_VAN_HANH: 'Ngày vận hành',
  SO_TSCD: 'Số TSCĐ',
  TINH_TRANG_VAN_HANH: 'Tình trạng vận hành',
  MA_LIEN_KET_KHAC: 'Mã liên kết khác',
  HANG_SAN_XUAN: 'Hãng sản xuất',
  NHA_CUNG_CAP: 'Nhà cung cấp',
  NUOC_SAN_XUAT: 'Nước sản xuất',
  SO_HUU: 'Sở hữu',
  NGAY_LAP_DAT: 'Ngày lắp đặt',
  NAM_SAN_XUAT: 'Năm sản xuất',
  NGAY_SUA_DOI: 'Ngày sửa đổi',
  GHI_CHU: 'Ghi chú',
  DON_VI: 'Đơn vị',
  CONG_TRINH_CHINH: 'Công trình chính',
  NAM_XAY_DUNG: 'Năm xây dựng',
};

export async function importData(sheetData, mapDonVi, mapCongTrinhCha) {
  const { rows } = sheetData;

  function convertToDB(row) {
    return {
      ten_cong_trinh: trim(row[CommonHeaders.TEN_THIET_BI]),
      ma_cong_trinh: trim(row[CommonHeaders.MA_THIET_BI]),
      ma_thiet_bi_cha: trim(row[CommonHeaders.MA_THIET_BI_CHA]),
      ma_tim_kiem: trim(row[CommonHeaders.MA_TIM_KIEM_THIET_BI]),
      ma_tim_kiem_cha: trim(row[CommonHeaders.MA_TIM_KIEM_CHA]),
      cong_trinh_chinh_id: mapCongTrinhCha[trim(row[Headers.MA_THIET_BI_CHA])],
      don_vi_id: mapDonVi[trim(row[Headers.DON_VI])],
      ngay_van_hanh: convertDate(row[Headers.NGAY_VAN_HANH]),
      tinh_trang_van_hanh: row[Headers.TINH_TRANG_VAN_HANH],
      so_huu: row[Headers.SO_HUU],
      nam_xay_dung: row[Headers.NAM_XAY_DUNG] ? new Date(parseInt(row[Headers.NAM_XAY_DUNG]), 1, 1, 0, 0, 0, 0) : null,
      thu_tu: row[Headers.STT],
      ghi_chu: row[Headers.GHI_CHU],
      is_deleted: false,
    };
  }

  const dataToDB = rows.map(row => convertToDB(row)).filter(element => element.ten_cong_trinh);
  return CongTrinhModel.bulkWrite(
    dataToDB.map((congTrinh) =>
      ({
        updateOne: {
          filter: { ma_cong_trinh: congTrinh.ma_cong_trinh },
          update: { $set: congTrinh },
          upsert: true,
        },
      }),
    ),
  );
}

export async function checkImport(t, sheetData, mapDonVi) {

  if (!sheetData) return null;
  const congTrinhChaList = sheetData.rows.map(row => row[CommonHeaders.MA_THIET_BI_CHA]);

  const allCongTrinhCha = await getAll({ is_deleted: false, ma_cong_trinh: { $in: congTrinhChaList } });
  const mapCongTrinhCha = {};
  allCongTrinhCha.forEach(congTrinh => {
    mapCongTrinhCha[congTrinh.ma_cong_trinh] = congTrinh._id;
  });
  const { rows } = sheetData;

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row) {
    let errors = [];
    errors = [...errors, ...checkCommonInfo(t, row)];

    if (!row[Headers.DON_VI]?.trim()) {
      errors = [...errors, createError(Headers.DON_VI, t('missing_unit_construction'))];
    } else {
      if (!mapDonVi[row[Headers.DON_VI]?.trim()]) {
        errors = [...errors, createError(Headers.DON_VI, t('management_unit_incorrect_or_dont_create'))];
      }
    }

    // if (row[CommonHeaders.MA_THIET_BI_CHA] && !mapCongTrinhCha[row[CommonHeaders.MA_THIET_BI_CHA]?.trim()]) {
    //   errors = [...errors, createError(Headers.CONG_TRINH_CHINH, 'Mã thiết bị cha không đúng hoặc chưa được tạo')];
    // }

    if (errors.length) {
      row['Lỗi'] = errors;
    } else {
      row['Lỗi'] = null;
    }
    return row;
  }

  sheetData.rows = rows.map(row => validateRow(t, row));
  return sheetData;
}


export async function getViTriByCongTrinh(req, congTrinhId) {
  const allViTri = await ViTriService.getAll({ cong_trinh_id: congTrinhId });
  const viTriIds = extractIds(allViTri);

  const khoangCotData = await KHOANG_COT.find({ vi_tri_id: viTriIds, is_deleted: false })
    .select('vi_tri_bat_dau_id vi_tri_ket_thuc_id name ten_khoang_cot vi_tri_id');

  const khoangNeoData = await KhoangNeoService.getAll({ vi_tri_ket_thuc_id: viTriIds, is_deleted: false })
    .populate({ path: 'vi_tri_bat_dau_id', select: 'ma_vi_tri ten_vi_tri thu_tu' })
    .populate({ path: 'vi_tri_ket_thuc_id', select: 'ma_vi_tri ten_vi_tri thu_tu' })
    .select('vi_tri_bat_dau_id vi_tri_ket_thuc_id ten_khoang_neo');

  const groupKhoangCotByViTriId = groupBy(khoangCotData, 'vi_tri_id');

  const mapKhoangNeo = {};
  khoangNeoData.forEach(khoangNeo => {
    mapKhoangNeo[khoangNeo.vi_tri_ket_thuc_id?._id] = khoangNeo;
  });

  allViTri.forEach(viTri => {
    viTri.khoang_cot_id = groupKhoangCotByViTriId[viTri._id];
    viTri.khoang_neo_id = mapKhoangNeo[viTri._id];
  });

  return allViTri;
}

export async function sortAndPaginateCongTrinh(criteria, options) {
  return CongTrinhModel.aggregate([
    {
      $facet: {
        docs: [
          {
            $match: criteria,
          },
          {
            $lookup: {
              from: 'LoaiDuongDay',
              localField: 'loai_duong_day_id',
              foreignField: '_id',
              as: 'loai_duong_day_id',
            },
          },
          {
            $unwind: { path: '$loai_duong_day_id', preserveNullAndEmptyArrays: true },
          },
          {
            $lookup: {
              from: 'CongTrinh',
              localField: 'cong_trinh_chinh_id',
              foreignField: '_id',
              as: 'cong_trinh_chinh_id',
            },
          },
          {
            $unwind: { path: '$cong_trinh_chinh_id', preserveNullAndEmptyArrays: true },
          },
          {
            $sort: options.sort,
          },
          {
            $skip: (options.page - 1) * 10,
          },
          {
            $limit: options.limit,
          },
          {
            $lookup: {
              from: 'DonVi',
              localField: 'don_vi_id',
              foreignField: '_id',
              as: 'don_vi_id',
            },
          },
          {
            $unwind: { path: '$don_vi_id', preserveNullAndEmptyArrays: true },
          },
        ],
        extend: [
          {
            $match: criteria,
          },
          {
            $count: 'total_docs',
          },
        ],
      },
    },
  ]);
};

export async function genExcelCongTrinhByColumns(res, data, templateFilePath, outputFileName = '', selectedColumns = null) {
  const fileTemplateFinal = await updateCongTrinhTempByColumns(templateFilePath, selectedColumns);
  const fileAfterRender = await renderDataToFileTemp(fileTemplateFinal, data);
  res.download(fileAfterRender, outputFileName);
}

async function updateCongTrinhTempByColumns(templateFilePath, selectedColumns = null) {
  try {
    // Load file Excel bằng ExcelJS
    const workbook = new ExcelJs.Workbook();
    await workbook.xlsx.readFile(templateFilePath);
    const worksheet = workbook.getWorksheet(1); // Lấy sheet đầu tiên

    // Định nghĩa cột và vị trí
    const columnHeaders = worksheet.getRow(1).values; // Lấy tiêu đề cột
    const columnIndexes = {}; // Lưu vị trí của từng cột

    columnHeaders.forEach((header, index) => {
      if (header) columnIndexes[header] = index; // Map tên cột với vị trí
    });

    // Xóa các cột không nằm trong selectedColumns
    Object.keys(columnIndexes)
      .reverse() // Xóa từ phải sang trái để tránh lệch index
      .forEach((col) => {
        if (!selectedColumns.includes(col)) {
          worksheet.spliceColumns(columnIndexes[col], 1);
        }
      });

    // Lưu lại file sau khi xóa cột
    const tempUpdatedTemplate = 'temp_updated_template.xlsx';
    await workbook.xlsx.writeFile(tempUpdatedTemplate);
    return tempUpdatedTemplate;
  } catch (error) {
    reject(error);
  }
}
