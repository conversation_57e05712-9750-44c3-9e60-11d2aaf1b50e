import express from 'express';
import passport from 'passport';
import * as congTrinhController from './congTrinh.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import TongKePermission from '../../RBAC/permissions/TongKePermission';
import { loggerMiddleware } from '../../../logs/middleware';
import { gopDuLieuCongTrinh, tachDuLieuCongTrinh } from './congTrinh.controller';

export const congTrinhRouter = express.Router();

congTrinhRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
congTrinhRouter.post('*', authorizationMiddleware([TongKePermission.CREATE]));
congTrinhRouter.put('*', authorizationMiddleware([TongKePermission.UPDATE]));
congTrinhRouter.delete('*', authorizationMiddleware([TongKePermission.DELETE]));

congTrinhRouter
  .route('/')
  .get(congTrinhController.getAll)
  .post(congTrinhController.create);

congTrinhRouter
  .route('/allcongtrinhchuabangiao')
  .get(congTrinhController.getAllChuaBanGiao);

congTrinhRouter
  .route('/allcongtrinh')
  .get(congTrinhController.getAllCongTrinh);

congTrinhRouter
  .route('/doitruyentai')
  .get(congTrinhController.getCongTrinhDoiTruyenTai);

congTrinhRouter
  .route('/congtrinhchinh')
  .get(congTrinhController.getAllCongTrinhChinh);

congTrinhRouter
  .route('/findallcongtrinh')
  .get(congTrinhController.findAllCongTrinh);

congTrinhRouter
  .route('/xaydung')
  .get(congTrinhController.getAllCongTrinhXayDung);

congTrinhRouter
  .route('/download/chitiet')
  .get(congTrinhController.exportTongKe);

congTrinhRouter
  .route('/download/danhsach')
  .get(congTrinhController.exportDanhSachCongTrinh);

congTrinhRouter
  .route('/sortpaginate')
  .get(congTrinhController.sortAndPaginateCongTrinh);

congTrinhRouter
  .route('/gop-cong-trinh')
  .post(congTrinhController.gopDuLieuCongTrinh);

congTrinhRouter
  .route('/:id/tach-cong-trinh')
  .post(authorizationMiddleware([TongKePermission.CREATE]), congTrinhController.tachDuLieuCongTrinh);

congTrinhRouter
  .route('/:id/vitri')
  .get(congTrinhController.getAllViTri);

congTrinhRouter
  .route('/:id/xoaThietBi')
  .delete(congTrinhController.deleteAllThietBi);

congTrinhRouter
  .route('/:id/xoaViTriThua')
  .delete(congTrinhController.deleteAllUnusedViTri);

congTrinhRouter
  .route('/:id')
  .get(congTrinhController.findOne)
  .delete(congTrinhController.remove)
  .put(congTrinhController.update);


