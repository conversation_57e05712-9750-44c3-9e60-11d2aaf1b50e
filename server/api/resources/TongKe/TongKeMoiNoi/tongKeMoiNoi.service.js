import * as ValidatorHelper from '../../../helpers/validatorHelper';
import TONG_KE_MOI_NOI from './tongKeMoiNoi.model';

const Joi = require('joi');

const objSchema = Joi.object({});


export function getAll(query, projection = {}) {
  return TONG_KE_MOI_NOI.find(query, projection).lean();
}

export async function createMany(dataList = []) {
  if (!Array.isArray(dataList)) {
    throw new Error('Input must be an array');
  }

  const validRecords = [];

  for (const data of dataList) {
    const { error, value } = validate(data);
    if (!error) {
      validRecords.push(value);
    }
  }

  return TONG_KE_MOI_NOI.insertMany(validRecords);
}

export async function updateAll(dataUpdate, options = {}) {
  options.new = true;
  const results = [];
  for (const row of dataUpdate) {
    try {
      const { error, value } = validate(row);
      if (!error) {
        const updated = await TONG_KE_MOI_NOI.findByIdAndUpdate(value._id, value, options);
        if (updated) results.push(updated);
      }
    } catch {
    }
  }
  return results;
}

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function bangTongHopTongKeMoiNoi(id) {
  const allTongKeMoiNoi = await getAll({
    duong_day_id: id,
    is_deleted: false,
  })
    .populate('khoang_cot_id')
    .populate({
      path: 'duong_day_id',
      populate: {
        path: 'don_vi_id',
        populate: 'don_vi_cha_id',
      },
    });

  const convertDataToRows = (tongKe, index) => ({
    stt: index + 1,
    duong_day_id: tongKe.duong_day_id,
    khoang_cot_id: tongKe.khoang_cot_id?.ten_khoang_cot || '',
    day_dan: tongKe.day_dan,
    day_chong_set: tongKe.day_chong_set,
    ghi_chu: tongKe.ghi_chu,
  });

  return allTongKeMoiNoi.map(convertDataToRows);
}
