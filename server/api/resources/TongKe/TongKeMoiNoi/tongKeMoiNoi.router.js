import express from 'express';
import passport from 'passport';
import * as tongKeMoiNoiController from './tongKeMoiNoi.controller';

export const tongKeMoiNoiRouter = express.Router();
tongKeMoiNoiRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), tongKeMoiNoiController.getAll)
  .post(passport.authenticate('jwt', { session: false }), tongKeMoiNoiController.create);

tongKeMoiNoiRouter
  .route('/download')
  .get(tongKeMoiNoiController.downloadBieuMauTongKeMoiNoi);

tongKeMoiNoiRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), tongKeMoiNoiController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), tongKeMoiNoiController.remove)
  .put(passport.authenticate('jwt', { session: false }), tongKeMoiNoiController.update);


