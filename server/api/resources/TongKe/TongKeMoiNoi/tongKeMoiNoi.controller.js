import * as Service from './tongKeMoiNoi.service';
import Model from './tongKeMoiNoi.model';
import * as controllerHelper from '../../../helpers/controllerHelper';
import queryHelper from '../../../helpers/queryHelper';
import { generateDocument } from '../../Report/GenerateFile/generate.controller';
import * as responseAction from '../../../helpers/responseHelper';
import { getFilePath } from '../../../utils/fileUtils';
import { TEMPLATES_DIRS } from '../../../constant/constant';
import i18next from 'i18next';

const searchLike = ['ma_giao_cheo', 'ten_giao_cheo'];
const populateOpts = [
  // {
  //   path: 'khoang_cot_id', populate: 'vi_tri_bat_dau_id vi_tri_ket_thuc_id',
  // },
  { path: 'duong_day_id' },
];
const uniqueOpts = [];
const sortGetALl = { created_at: -1 };
const pupulateOptsGetAll = [...populateOpts, { path: 'khoang_cot_id', select: 'ten_khoang_cot' }];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, searchLike, pupulateOptsGetAll, sortGetALl);

export async function downloadBieuMauTongKeMoiNoi(req, res) {
  try {
    const { t } = req;
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;

    const tongKeMoiNoi = await Service.bangTongHopTongKeMoiNoi(criteria.id);
    const data = {
      ten_duong_day: tongKeMoiNoi[0]?.duong_day_id?.ten_duong_day,
      don_vi_id: tongKeMoiNoi[0]?.duong_day_id?.don_vi_id,
      tong_ke_moi_noi: tongKeMoiNoi,
    };

    const templateFilePath = getFilePath('bieu_mau_tong_ke_moi_noi.docx', TEMPLATES_DIRS.BIEU_MAU);
    const outputFileName = `${i18next.t('tong_ke_moi_noi')}.docx`;

    generateDocument(res, data, templateFilePath, outputFileName);
  } catch (error) {
    responseAction.error(res, error);
  }
}

