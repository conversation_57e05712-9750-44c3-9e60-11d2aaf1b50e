import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DUONG_DAY, TONG_KE_MOI_NOI, KHOANG_COT } from '../../../constant/dbCollections';

const schema = new Schema({
  khoang_cot_id: { type: Schema.Types.ObjectId, ref: KHOANG_COT },
  duong_day_id: { type: Schema.Types.ObjectId, ref: DUONG_DAY },
  day_dan: {
    moi_noi_pha_a: Number,
    moi_noi_pha_b: Number,
    moi_noi_pha_c: Number,
    moi_va_pha_a: Number,
    moi_va_pha_b: Number,
    moi_va_pha_c: Number,
    quan_bao_duong_pha_a: Number,
    quan_bao_duong_pha_b: Number,
    quan_bao_duong_pha_c: Number,
  },
  day_chong_set: {
    moi_noi: Number,
    moi_va: Number,
    quan_bao_duong: Number,
  },
  ghi_chu: String,
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(TONG_KE_MOI_NOI, schema, TONG_KE_MOI_NOI);
