import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { COT_DIEN, DON_VI, VI_TRI } from '../../../constant/dbCollections';
import { TINH_TRANG_VAN_HANH } from '../../DanhMuc/TinhTrangVanHanh';

const schema = new Schema({
  ma_cot_dien: { type: String, required: true, unique: true, validate: /\S+/ },
  ma_thiet_bi_cha: { type: String, validate: /\S+/ },
  ma_tim_kiem: { type: String, validate: /\S+/ },
  ma_tim_kiem_cha: { type: String, validate: /\S+/ },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  ten_cot_dien: { type: String },
  thu_tu: { type: Number },
  so_the_tscd: String,
  ngay_van_hanh: { type: Date },
  tinh_trang_van_hanh: {
    type: String,
    enum: Object.values(TINH_TRANG_VAN_HANH),
    default: TINH_TRANG_VAN_HANH.VAN_HANH,
  },
  hang_san_xuat: String,
  nha_cung_cap: String,
  nuoc_san_xuat: String,
  so_huu: String,
  ngay_lap_dat: { type: Date },
  nam_san_xuat: String,
  ngay_sua_doi: Date,
  ghi_chu: String,
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  ma_hieu_cot: String,
  cong_dung_cot: String,
  chieu_cao: String,
  trong_luong: String,
  so_mach_day_dan: String,
  so_mach_dcs: String,
  do_rong_chan_cot: String,
  mong_cot: String,
  loai_bulong_neo_mong: String,
  loai_mong: String,
  ke_mong: String,
  tuong_chan: String,
  muong_thoat_nuoc: String,
  den_canh_bao_hang_khong: String,

  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
// Existing index
schema.index({ vi_tri_id: 1 });

// Optimized indexes for updateThongTinViTri function
// Index for vi_tri_id array queries with is_deleted filter
schema.index({ vi_tri_id: 1, is_deleted: 1 }, { background: true });
// Index for ma_cot_dien queries with is_deleted filter
schema.index({ ma_cot_dien: 1, is_deleted: 1 }, { background: true });
// Index for don_vi_id queries with is_deleted filter
schema.index({ don_vi_id: 1, is_deleted: 1 }, { background: true });
// Index for created_at with is_deleted for time-based queries
schema.index({ created_at: 1, is_deleted: 1 }, { background: true });

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(COT_DIEN, schema, COT_DIEN);
