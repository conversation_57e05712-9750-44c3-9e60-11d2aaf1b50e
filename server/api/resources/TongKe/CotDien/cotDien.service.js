import * as ValidatorHelper from '../../../helpers/validatorHelper';
import COT_DIEN from './cotDien.model';
import { checkNumber, convertDate, convertYear } from '../../../helpers/checkDataHelper';
import { checkCommonInfo, checkThietBiCommonInfo, thietBiCommonInfo } from '../TongKe.service';
import * as Service from '../KhoangNeo/khoangNeo.service';
import { trim } from '../../../common/functionCommons';
import { CommonHeaders } from '../ThietBi/thietBi.controller';

const Headers = {
  MA_THIET_BI: 'Mã thiết bị',
  DUONG_DAY: 'Đường dây/TBA',
  THIET_BI_CHA: 'Thiết bị/Công trình cha',
  TEN_THIET_BI: 'Thiết bị/Công trình',
  STT: 'STT',
  SERIAL: '<PERSON>ố chế tạo/Serial',
  NGAY_VAN_HANH: '<PERSON><PERSON>y vận hành',
  SO_TSCD: 'Số TSCĐ',
  TINH_TRANG_VAN_HANH: 'Tình trạng vận hành',
  MA_LIEN_KET_KHAC: 'Mã liên kết khác',
  HANG_SAN_XUAN: 'Hãng sản xuất',
  NHA_CUNG_CAP: 'Nhà cung cấp',
  NUOC_SAN_XUAT: 'Nước sản xuất',
  SO_HUU: 'Sở hữu',
  NGAY_LAP_DAT: 'Ngày lắp đặt',
  NAM_SAN_XUAT: 'Năm sản xuất',
  NGAY_SUA_DOI: 'Ngày sửa đổi',
  GHI_CHU: 'Ghi chú',
  DON_VI: 'Đơn vị',


  MA_HIEU_COT: 'Mã hiệu cột',
  CONG_DUNG_COT: 'Công dụng cột',
  CHIEU_CAO: 'Chiều cao',
  TRONG_LUONG: 'Trọng lượng',
  SO_MACH_DAY_DAN: 'Số mạch dây dẫn',
  SO_MACH_DAY_CHONG_SET: 'Số mạch DCS',
  DO_RONG_CHAN_COT: 'Độ rộng chân cột',
  MONG_COT: 'Móng cột',
  LOAI_BULONG_NEO_MONG: 'Loại bulông neo móng',
  KE_MONG: 'Kè móng',
  LOAI_MONG: 'Loại móng',
  TUONG_MONG: 'Tường chắn',
  MUONG_THOAT_NUOC: 'Mương thoát nước',
  DEN_CANH_BAO_BAO_HANG_KHONG: 'Đèn cảnh báo hàng không',
};

export async function importData(sheetData, mapDonVi, mapVitri) {
  const { rows } = sheetData;
  let listViTriIds = [];

  function convertToDB(row) {
    listViTriIds.push(mapVitri[row[Headers.THIET_BI_CHA]?.toString()?.trim()]);
    const thietBiInfo = thietBiCommonInfo(row, mapDonVi, mapVitri)
    return {
      ma_cot_dien: trim(row[Headers.MA_THIET_BI]),
      ten_cot_dien: trim(row[Headers.TEN_THIET_BI]),
      ...thietBiInfo,
      so_the_tscd: row[Headers.SO_TSCD],
      thu_tu: row[Headers.STT],
      ngay_van_hanh: convertDate(row[Headers.NGAY_VAN_HANH]),
      tinh_trang_van_hanh: row[Headers.TINH_TRANG_VAN_HANH],
      hang_san_xuat: row[Headers.HANG_SAN_XUAN],
      nuoc_san_xuat: row[Headers.NUOC_SAN_XUAT],
      nam_san_xuat: convertYear(row[Headers.NAM_SAN_XUAT]),
      so_huu: row[Headers.SO_HUU],
      ngay_lap_dat: convertDate(row[Headers.NGAY_LAP_DAT]),
      ma_hieu_cot: row[Headers.MA_HIEU_COT],
      cong_dung_cot: row[Headers.CONG_DUNG_COT],
      chieu_cao: row[Headers.CHIEU_CAO],
      trong_luong: row[Headers.TRONG_LUONG],
      so_mach_day_dan: row[Headers.SO_MACH_DAY_DAN],
      so_mach_dcs: row[Headers.SO_MACH_DAY_CHONG_SET],
      mong_cot: row[Headers.MONG_COT],
      do_rong_chan_cot: row[Headers.DO_RONG_CHAN_COT],
      loai_bulong_neo_mong: row[Headers.LOAI_BULONG_NEO_MONG],
      ke_mong: row[Headers.KE_MONG],
      loai_mong: row[Headers.LOAI_MONG],
      tuong_chan: row[Headers.TUONG_MONG],
      muong_thoat_nuoc: row[Headers.MUONG_THOAT_NUOC],
      den_canh_bao_hang_khong: row[Headers.DEN_CANH_BAO_BAO_HANG_KHONG],
      is_deleted: false,
    };
  }

  const dataToDB = rows.map(row => convertToDB(row)).filter(element => element.ten_cot_dien);
  const result = await COT_DIEN.bulkWrite(
    dataToDB.map((cotDien) =>
      ({
        updateOne: {
          filter: { ma_cot_dien: cotDien.ma_cot_dien },
          update: { $set: cotDien },
          upsert: true,
        },
      }),
    ),
  );
  await Service.createOrUpdateKhoangNeo(listViTriIds);
  return result;
}

export function checkImport(t, sheetData, mapDonVi, mapVitri) {
  if (!sheetData) return null;
  const { rows } = sheetData;

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row) {
    let errors = [];
    errors = [...errors, ...checkCommonInfo(t, row)];
    errors = [...errors, ...checkThietBiCommonInfo(t, row, mapVitri)];

    if (!row[Headers.DON_VI]?.trim()) {
      errors = [...errors, createError(Headers.DON_VI, t('missing_unit_construction'))];
    } else {
      if (!mapDonVi[row[Headers.DON_VI]?.trim()]) {
        errors = [...errors, createError(Headers.DON_VI, t('management_unit_incorrect_or_dont_create'))];
      }
    }

    if (!checkNumber(row[Headers.SO_TIA])) {
      errors = [...errors, createError(Headers.SO_TIA, t('incorrect_or_not_available'))];
    }
    // if (!checkNumber(row[Headers.CHIEU_CAO])) {
    //   errors = [...errors, createError(Headers.CHIEU_CAO, t('incorrect_or_not_available'))];
    // }
    // if (!checkNumber(row[Headers.TRONG_LUONG])) {
    //   errors = [...errors, createError(Headers.TRONG_LUONG, t('incorrect_or_not_available'))];
    // }
    if (!checkNumber(row[Headers.SO_MACH_DAY_DAN])) {
      errors = [...errors, createError(Headers.SO_MACH_DAY_DAN, t('incorrect_or_not_available'))];
    }
    if (!checkNumber(row[Headers.SO_MACH_DAY_CHONG_SET])) {
      errors = [...errors, createError(Headers.SO_MACH_DAY_CHONG_SET, t('incorrect_or_not_available'))];
    }

    if (errors.length) {
      row['Lỗi'] = errors;
    } else {
      row['Lỗi'] = null;
    }
    return row;
  }

  sheetData.rows = rows.map(row => validateRow(t, row));
  return sheetData;
}

export function getAll(query, projection = {}) {
  return COT_DIEN.find(query, projection).lean();
}

export function count(query) {
  return COT_DIEN.count(query);
}

export function getOne(query) {
  return COT_DIEN.findOne(query).lean();
}

export async function updateAll(arrData) {
  await COT_DIEN.bulkWrite(
    arrData.map((row) =>
      ({
        updateOne: {
          filter: { _id: row._id },
          update: { $set: row },
          upsert: false,
        },
      }),
    ),
  );
}

const Joi = require('joi');

const objSchema = Joi.object({
  ma_cot_dien: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã vị trí')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function deleteAll(query) {
  return COT_DIEN.remove(query)
}
