import express from 'express';
import passport from 'passport';
import * as donviController from './cotDien.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import TongKePermission from '../../RBAC/permissions/TongKePermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const cotDienRouter = express.Router();
cotDienRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
cotDienRouter.post('*', authorizationMiddleware([TongKePermission.CREATE]));
cotDienRouter.get('*', authorizationMiddleware([TongKePermission.READ]));
cotDienRouter.put('*', authorizationMiddleware([TongKePermission.UPDATE]));
cotDienRouter.delete('*', authorizationMiddleware([TongKePermission.DELETE]));
cotDienRouter
  .route('/')
  .get(donviController.getAll)
  .post(donviController.create);

cotDienRouter
  .route('/:id')
  .get(donviController.findOne)
  .delete(donviController.remove)
  .put(donviController.update);
