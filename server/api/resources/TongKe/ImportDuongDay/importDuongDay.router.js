import express from 'express';
import passport from 'passport';
import * as duongDayController from './importDuongDay.controller';
import { checkTempFolder, multipartMiddleware } from '../../../utils/fileUtils';
import { loggerMiddleware } from '../../../logs/middleware';
import { authorizationMiddleware } from '../../RBAC/middleware';
import TongKePermission from '../../RBAC/permissions/TongKePermission';

export const importDuongDayRouter = express.Router();
importDuongDayRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
importDuongDayRouter.post('*', authorizationMiddleware([TongKePermission.CREATE]));
importDuongDayRouter.put('*', authorizationMiddleware([TongKePermission.UPDATE]));
importDuongDayRouter.delete('*', authorizationMiddleware([TongKePermission.DELETE]));

importDuongDayRouter
  .route('/import')
  .post(passport.authenticate('jwt', { session: false }),
    checkTempFolder, multipartMiddleware, duongDayController.importThietBi);

importDuongDayRouter
  .route('/importone')
  .post(passport.authenticate('jwt', { session: false }),
    duongDayController.importOne);
importDuongDayRouter
  .route('/importmany')
  .post(passport.authenticate('jwt', { session: false }), duongDayController.importMany);

importDuongDayRouter
  .route('/checkimport')
  .post(passport.authenticate('jwt', { session: false }),
    checkTempFolder, multipartMiddleware, duongDayController.checkImport);

importDuongDayRouter
  .route('/checkimportbydata')
  .post(passport.authenticate('jwt', { session: false }), duongDayController.checkImportData);
