import * as ValidatorHelper from '../../../helpers/validatorHelper';
import { checkCommonInfo } from '../TongKe.service';
import DUONG_DAY from '../DuongDay/duongDay.model';

const Joi = require('joi');

const objSchema = Joi.object({
  ma_vi_tri: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã vị trí')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

const Headers = {
  MA_THIET_BI: 'Mã thiết bị',
  DUONG_DAY: 'Đường dây/TBA',
  THIET_BI_CHA: 'Thiết bị/Công trình cha',
  TEN_THIET_BI: 'Thiết bị/Công trình',
  STT: 'STT',
  SERIAL: 'Số chế tạo/Serial',
  NGAY_VAN_HANH: 'Ngày vận hành',
  MA_CMIS: 'Mã CMIS',
  SO_TSCD: 'Số TSCĐ',
  TINH_TRANG_VAN_HANH: 'Tình trạng vận hành',
  MA_LIEN_KET_KHAC: 'Mã liên kết khác',
  HANG_SAN_XUAN: 'Hãng sản xuất',
  NHA_CUNG_CAP: 'Nhà cung cấp',
  NUOC_SAN_XUAT: 'Nước sản xuất',
  SO_HUU: 'Sở hữu',
  NGAY_LAP_DAT: 'Ngày lắp đặt',
  NAM_SAN_XUAT: 'Năm sản xuất',
  NGAY_SUA_DOI: 'Ngày sửa đổi',
  GHI_CHU: 'Ghi chú',
  DON_VI: 'Đơn vị',

  MA_TIM_KIEM_CHA: 'Mã tìm kiếm cha',
  MA_TIM_KIEM_THIET_BI: 'Mã tìm kiếm thiết bị',
  DUONG_DAY_CHINH: 'Đường dây chính',
  DIEN_AP_THIET_KE: 'Điện áp thiết kế',
  TONG_CHIEU_DAI: 'Tổng chiều dài',
  TU_TRAM: 'Từ trạm',
  TU_NGAN_LO: 'Từ ngăn lộ',
  DEN_TRAM: 'Đến trạm',
  DEN_NGAN_LO: 'Đến ngăn lộ',
  SO_MACH: 'Số mạch',
  I_CHO_PHEP: 'I cho phép',
  CHIEU_DAI_THUC_TE: 'Chiều dài thực tế',
  DO_THONG_SO_DUONG_DAY: 'Đo thông số đường dây',
  NGAY_DO: 'Ngày đo',
  GIA_TRI_DO: 'Giá trị đo',
  DUONG_DAY_CHUNG_COT: 'Đường dây chung cột',
  CHUNG_COT_VOI_DUONG_DAY: 'Chung cột với đường dây',
  TU_VI_TRI_DEN_VI_TRI_CHUNG_COT: 'Từ vị trí đến vị trí chung cột',
  CHIEU_DAI_CHUNG_COT: 'Chiều dài chung cột',
  LOAI_DAY: 'Loại dây',
};

export function checkImport(t, sheetData, mapDonVi, mapDuongDay) {
  if (!sheetData) return null;

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row) {
    let errors = [];
    errors = [...errors, ...checkCommonInfo(t, row)];
    if (!row[Headers.DON_VI]?.trim()) {
      errors = [...errors, createError(Headers.DON_VI, t('missing_unit_construction'))];
    } else {
      if (!mapDonVi[row[Headers.DON_VI]?.trim()]) {
        errors = [...errors, createError(Headers.DON_VI, t('management_unit_incorrect_or_dont_create'))];
      }
    }
    if (!!row[Headers.DUONG_DAY_CHINH]?.trim() && !mapDuongDay[row[Headers.DUONG_DAY_CHINH]?.trim()]) {
      errors = [...errors, createError(Headers.DUONG_DAY_CHINH, t('parent_line_incorrect_or_dont_create'))];
    }
    if (errors.length) {
      row['Lỗi'] = errors;
    } else {
      row['Lỗi'] = null;
      row['Lỗi'] = null;
    }
    return row;
  }

  return sheetData?.map(e => {
    e.rows = e.rows.map(row => validateRow(t, row));
    return e;
  });
}

export async function importData(sheetData, mapDonVi, mapCongTrinhCha) {
  const { rows } = sheetData;

  function convertToDB(row) {
    return {
      ten_duong_day: row[Headers.TEN_THIET_BI]?.trim(),
      ma_duong_day: row[Headers.MA_THIET_BI]?.trim(),
      duong_day_chinh_id: mapCongTrinhCha[row[Headers.DUONG_DAY_CHINH]?.trim()],
      don_vi_id: mapDonVi[row[Headers.DON_VI]?.trim()],
      chieu_dai: row[Headers.TONG_CHIEU_DAI],
      thu_tu: row[Headers.STT],
      ghi_chu: row[Headers.GHI_CHU],
      is_deleted: false,
    };
  }

  const dataToDB = rows.map(row => convertToDB(row)).filter(element => element.ten_duong_day);
  return await DUONG_DAY.bulkWrite(
    dataToDB.map((duongDay) =>
      ({
        updateOne: {
          filter: { ma_duong_day: duongDay.ma_duong_day },
          update: { $set: duongDay },
          upsert: true,
        },
      }),
    ),
  );
}
