import * as responseAction from '../../../helpers/responseHelper';
import * as DuongDayService from '../DuongDay/duongDay.service';
import * as DonViService from '../../DonVi/donVi.service';
import * as Service from './importDuongDay.service';
import exelUtils from '../../../utils/exelUtils';

const SHEET_NAMES = {
  CONG_TRINH: 'Công trình',
  DUONG_DAY: 'Đường dây',
  VI_TRI: 'Vị trí',
  COT_DIEN: 'Cột điện',
  CAP_QUANG: '<PERSON><PERSON><PERSON> quang',
  GIAO_CHEO: 'Giao chéo',
  DAY_CHONG_SET: 'Dây chống sét',
  VAN_HANH: 'Vận hành',
  DAY_DAN: 'Dây dẫn',
  CACH_DIEN: '<PERSON><PERSON><PERSON> điện',
  TIEP_DAT: 'Tiế<PERSON> đất',
};

export async function importThietBi(req, res) {
  function getSheetByName(sheetData, name) {
    return sheetData.find((sheet) => sheet.name.toLowerCase().includes(name.toLowerCase()));
  }

  try {
    let filePath = req.files.file.path;
    const sheetData = await exelUtils.transformFile(filePath);
    const result = {};
    const allDonVi = await DonViService.getAll({ is_deleted: false });
    const mapDonVi = {};
    allDonVi.forEach(donVi => {
      mapDonVi[donVi.ten_don_vi] = donVi._id;
    });

    const allDuongDayCha = await DuongDayService.getAll({ is_deleted: false });
    const MapDuongDayCha = {};
    allDuongDayCha.forEach(duongDay => {
      MapDuongDayCha[duongDay.ten_duong_day] = duongDay._id;
    });

    result.duong_day = await Service.importData(getSheetByName(sheetData, 'Đường dây'), mapDonVi, MapDuongDayCha);

    responseAction.success(res, result);
  } catch (e) {
    responseAction.error(res, e);
  }
}

async function importData(ma_cong_trinh, name, sheetData) {
  sheetData.forEach(sheet => {
    trimData(sheet);
  });

  function getSheetByName(sheetData, sheetName) {
    return sheetData.find((sheet) => sheet.name.toLowerCase().includes(sheetName.toLowerCase()));
  }

  const result = {};
  const allDonVi = await DonViService.getAll({ is_deleted: false });
  const mapDonVi = {};
  allDonVi.forEach(donVi => {
    mapDonVi[donVi.ten_don_vi] = donVi._id;
  });

  const allDuongDayCha = await DuongDayService.getAll({ is_deleted: false });
  const mapDuongDayCha = {};
  allDuongDayCha.forEach(duongDay => {
    mapDuongDayCha[duongDay.ten_duong_day] = duongDay._id;
  });
  if (getSheetByName(sheetData, SHEET_NAMES.DUONG_DAY)) {
    result[name] = await Service.importData(getSheetByName(sheetData, SHEET_NAMES.DUONG_DAY), mapDonVi, mapDuongDayCha);
  }

  return result;
}

export async function importOne(req, res) {
  try {
    const sheetData = [trimData(req.body)];
    const result = await importData(req.body.ma_cong_trinh, req.body.name, sheetData);
    responseAction.success(res, result);
  } catch (e) {
    responseAction.error(res, e);
  }
}

export async function importMany(req, res) {
  try {
    let result = {};
    for (let sheet of req.body) {
      result = { ...result, ...await importData(sheet.ma_cong_trinh, sheet.name, [sheet]) };
    }
    responseAction.success(res, result);
  } catch (e) {
    responseAction.error(res, e);
  }
}

async function checkImportByData(t, sheetData) {
  function getSheetByName(sheetData, name) {
    return sheetData.filter((sheet) => sheet.name.toLowerCase().includes(name.toLowerCase()));
  }

  let resultArray = [];
  const allDonVi = await DonViService.getAll({ is_deleted: false });
  const mapDonVi = {};
  allDonVi.forEach(donVi => {
    mapDonVi[donVi.ten_don_vi] = donVi._id;
  });

  const allDuongDayCha = await DuongDayService.getAll({ is_deleted: false });
  const mapDuongDayCha = {};
  allDuongDayCha.forEach(duongDay => {
    mapDuongDayCha[duongDay.ten_duong_day] = duongDay._id;
  });

  resultArray = await Service.checkImport(t, getSheetByName(sheetData, 'Đường dây'), mapDonVi, mapDuongDayCha);

  return resultArray;
}

export async function checkImport(req, res) {

  try {
    const { t } = req;
    let filePath = req.files.file.path;
    const sheetData = await exelUtils.transformFile(filePath);
    const resultArray = await checkImportByData(t, sheetData);
    responseAction.success(res, resultArray);
  } catch (e) {
    responseAction.error(res, e);
  }
}

export async function checkImportData(req, res) {
  try {
    const { t } = req;
    const sheetData = req.body;
    const resultArray = await checkImportByData(t, sheetData);
    responseAction.success(res, resultArray);
  } catch (e) {
    responseAction.error(res, e);
  }
}

function trimData(dataInput) {
  if (!Array.isArray(dataInput?.rows)) return dataInput;
  dataInput.rows.forEach(row => {
    Object.entries(row).forEach(([key, value]) => {
      row[key] = (!!value && typeof value === 'string') ? value.trim() : value;
    });
  });
  return dataInput;
}
