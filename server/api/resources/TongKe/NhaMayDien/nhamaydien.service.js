import * as ValidatorHelper from '../../../helpers/validatorHelper';
import NhaMayDien from './nhamaydien.model';
import { checkNumber, convertDate, convertYear } from '../../../helpers/checkDataHelper';
import { checkCommonInfo, checkThietBiCommonInfo, thietBiCommonInfo } from '../TongKe.service';

export function getAll(query, projection = {}) {
  return NhaMayDien.find(query, projection).lean();
}

export function count(query) {
  return NhaMayDien.count(query);
}

export function getOne(query) {
  return NhaMayDien.findOne(query).lean();
}

export async function updateAll(arrData) {
  await NhaMayDien.bulkWrite(
    arrData.map((row) =>
      ({
        updateOne: {
          filter: { _id: row._id },
          update: { $set: row },
          upsert: false,
        },
      }),
    ),
  );
}

const Joi = require('joi');

const objSchema = Joi.object({
  ma_nmd: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã nhà máy điện')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function deleteAll(query) {
  return NhaMayDien.remove(query)
}
