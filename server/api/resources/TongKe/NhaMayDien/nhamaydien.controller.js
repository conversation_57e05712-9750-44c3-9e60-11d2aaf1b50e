import * as Service from './nhamaydien.service';
import * as controllerHelper from '../../../helpers/controllerHelper';
import Model from './nhamaydien.model';

const populateOpts = [{ path: 'don_vi_id', select: 'ten_don_vi' }];
const uniqueOpts = [{ field: 'ma_nmd', message: 'NMD_CODE', checkDeleted: true }];
const searchLike = ['ten_nmd', 'ma_nmd'];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, searchLike, populateOpts, null, 'don_vi_id');
