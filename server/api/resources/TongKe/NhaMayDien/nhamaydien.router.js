import express from 'express';
import passport from 'passport';
import * as nhaMayDienController from './nhamaydien.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import TongKePermission from '../../RBAC/permissions/TongKePermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const nhaMayDienRouter = express.Router();
nhaMayDienRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
nhaMayDienRouter.post('*', authorizationMiddleware([TongKePermission.CREATE]));
nhaMayDienRouter.get('*', authorizationMiddleware([TongKePermission.READ]));
nhaMayDienRouter.put('*', authorizationMiddleware([TongKePermission.UPDATE]));
nhaMayDienRouter.delete('*', authorizationMiddleware([TongKePermission.DELETE]));
nhaMayDienRouter
  .route('/')
  .get(nhaMayDienController.getAll)
  .post(nhaMayDienController.create);

nhaMayDienRouter
  .route('/:id')
  .get(nhaMayDienController.findOne)
  .delete(nhaMayDienController.remove)
  .put(nhaMayDienController.update);
