import express from 'express';
import passport from 'passport';
import * as donviController from './cachDien.controller';

export const cachDienRouter = express.Router();
cachDienRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), donviController.getAll)
  .post(passport.authenticate('jwt', { session: false }), donviController.create);

cachDienRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), donviController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), donviController.remove)
  .put(passport.authenticate('jwt', { session: false }), donviController.update);
