import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CACH_DIEN, DON_VI, VAN_HANH } from '../../../constant/dbCollections';
import { TINH_TRANG_VAN_HANH } from '../../DanhMuc/TinhTrangVanHanh';

const schema = new Schema({
  ma_cach_dien: { type: String, required: true, unique: true, validate: /\S+/ },
  ma_thiet_bi_cha: { type: String, validate: /\S+/ },
  ma_tim_kiem: { type: String, validate: /\S+/ },
  ma_tim_kiem_cha: { type: String, validate: /\S+/ },
  van_hanh_id: { type: Schema.Types.ObjectId, ref: VAN_HANH },
  ten_cach_dien: { type: String },
  thu_tu: { type: Number },
  so_the_tscd: String,
  ngay_van_hanh: { type: Date },
  tinh_trang_van_hanh: {
    type: String,
    enum: Object.values(TINH_TRANG_VAN_HANH),
    default: TINH_TRANG_VAN_HANH.VAN_HANH,
  },
  hang_san_xuat: String,
  nha_cung_cap: String,
  nuoc_san_xuat: String,
  so_huu: String,
  ngay_lap_dat: { type: Date },
  nam_san_xuat: String,
  ngay_sua_doi: Date,
  ghi_chu: String,
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  duong_day: String,
  pha: String,
  chuc_nang: String,
  loai_cach_dien: String,
  ma_hieu_cach_dien: String,
  huong_lap_dat: String,
  so_luong_chuoi: String,
  so_luong_bat_tren_chuoi: String,
  tai_trong_pha_huy: String,
  duong_kinh_ty_su: String,
  chieu_cao_bat_su: String,
  chieu_rong_tan_su: String,
  chieu_dai_duong_ro: String,
  trong_luong_bat_su: String,
  vong_vang_quang: String,
  mo_phong: String,
  ma_hieu_ta_bu: String,
  ma_hieu_chuoi: String,
  so_luong_ta_bu: String,
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.index({ van_hanh_id: 1 });
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(CACH_DIEN, schema, CACH_DIEN);
