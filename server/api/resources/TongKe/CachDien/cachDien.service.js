import * as ValidatorHelper from '../../../helpers/validatorHelper';
import CACH_DIEN from './cachDien.model';
import { convertDate, cutTail, trim } from '../../../common/functionCommons';
import { convertYear } from '../../../helpers/checkDataHelper';
import { checkCommonInfo, checkThietBiCommonInfo, thietBiVanHanhCommonInfo } from '../TongKe.service';
import TIEP_DAT from '../../../constant/dbCollections';

const Headers = {
  MA_THIET_BI: 'Mã thiết bị',
  DUONG_DAY: 'Đường dây/TBA',
  THIET_BI_CHA: 'Thiết bị/Công trình cha',
  TEN_THIET_BI: 'Thiết bị/Công trình',
  STT: 'STT',
  SERIAL: 'Số chế tạo/Serial',
  NGAY_VAN_HANH: '<PERSON><PERSON><PERSON> vận hành',
  SO_TSCD: 'Số TSCĐ',
  TINH_TRANG_VAN_HANH: 'Tình trạng vận hành',
  MA_LIEN_KET_KHAC: 'Mã liên kết khác',
  HANG_SAN_XUAN: 'Hãng sản xuất',
  NHA_CUNG_CAP: 'Nhà cung cấp',
  NUOC_SAN_XUAT: 'Nước sản xuất',
  SO_HUU: 'Sở hữu',
  NGAY_LAP_DAT: 'Ngày lắp đặt',
  NAM_SAN_XUAT: 'Năm sản xuất',
  NGAY_SUA_DOI: 'Ngày sửa đổi',
  GHI_CHU: 'Ghi chú',
  DON_VI: 'Đơn vị',
  MA_HIEU_CHUOI: 'Mã hiệu chuỗi',
  KY_HIEU_DUONG_DAY: 'Đường dây',
  PHA: 'Pha',
  CHUC_NANG: 'Chức năng',
  LOAI_CACH_DIEN: 'Loại cách điện',
  MA_HIEU_CACH_DIEN: 'Mã hiệu cách điện',
  HUONG_LAP_DAT: 'Hướng lắp đặt',
  SO_LUONG_CHUOI: 'Số lượng chuỗi',
  SO_LUONG_BAT_TREN_CHUOI: 'Số lượng bát\\chuỗi',
  TAI_TRONG_PHA_HUY: 'Tải trọng phá hủy',
  DUONG_KINH_TY_SU: 'Đường kính ty sứ',
  CHIEU_CAO_BAT_SU: 'Chiều cao bát sứ',
  CHIEU_RONG_TAN_SU: 'Chiều rộng tán sứ',
  CHIEU_DAI_DUONG_RO: 'Chiều dài đường rò',
  TRONG_LUONG_BAT_SU: 'Trọng lượng bát sứ',
  VONG_VANG_QUANG: 'Vòng vầng quang',
  MO_PHONG: 'Mỏ phóng',
  MA_HIEU_TA_BU: 'Mã hiệu tạ bù',
  SO_LUONG_TA_BU: 'Số lượng tạ bù',
};

export async function importData(sheetData, mapDonVi, mapVanHanh) {
  const { rows } = sheetData;

  function convertToDB(row) {
    const thietBiVanHanhInfo = thietBiVanHanhCommonInfo(row, mapDonVi, mapVanHanh);
    return {
      ma_cach_dien: row[Headers.MA_THIET_BI]?.trim(),
      ten_cach_dien: trim(row[Headers.TEN_THIET_BI]),
      ...thietBiVanHanhInfo,
      so_the_tscd: row[Headers.SO_TSCD],
      thu_tu: row[Headers.STT],
      ngay_van_hanh: convertDate(row[Headers.NGAY_VAN_HANH]),
      tinh_trang_van_hanh: row[Headers.TINH_TRANG_VAN_HANH],
      hang_san_xuat: row[Headers.HANG_SAN_XUAN],
      nuoc_san_xuat: row[Headers.NUOC_SAN_XUAT],
      nam_san_xuat: convertYear(row[Headers.NAM_SAN_XUAT]),
      so_huu: row[Headers.SO_HUU],
      ngay_lap_dat: convertDate(row[Headers.NGAY_LAP_DAT]),

      duong_day: row[Headers.KY_HIEU_DUONG_DAY],
      pha: row[Headers.PHA],
      chuc_nang: row[Headers.CHUC_NANG],
      loai_cach_dien: row[Headers.LOAI_CACH_DIEN],
      ma_hieu_cach_dien: row[Headers.MA_HIEU_CACH_DIEN],
      huong_lap_dat: row[Headers.HUONG_LAP_DAT],
      so_luong_chuoi: row[Headers.SO_LUONG_CHUOI],
      so_luong_bat_tren_chuoi: row[Headers.SO_LUONG_BAT_TREN_CHUOI],
      tai_trong_pha_huy: row[Headers.TAI_TRONG_PHA_HUY],
      duong_kinh_ty_su: row[Headers.DUONG_KINH_TY_SU],
      chieu_cao_bat_su: row[Headers.CHIEU_CAO_BAT_SU],
      chieu_rong_tan_su: row[Headers.CHIEU_RONG_TAN_SU],
      chieu_dai_duong_ro: row[Headers.CHIEU_DAI_DUONG_RO],
      trong_luong_bat_su: row[Headers.TRONG_LUONG_BAT_SU],
      vong_vang_quang: row[Headers.VONG_VANG_QUANG],
      ma_hieu_chuoi: row[Headers.MA_HIEU_CHUOI],
      mo_phong: row[Headers.MO_PHONG],
      ma_hieu_ta_bu: row[Headers.MA_HIEU_TA_BU],
      so_luong_ta_bu: row[Headers.SO_LUONG_TA_BU],
      is_deleted: false,
    };
  }

  const dataToDB = rows.map(row => convertToDB(row)).filter(element => element.ten_cach_dien);
  return await CACH_DIEN.bulkWrite(
    dataToDB.map((element) =>
      ({
        updateOne: {
          filter: { ma_cach_dien: element.ma_cach_dien },
          update: { $set: element },
          upsert: true,
        },
      }),
    ),
  );
}

export function checkImport(t, sheetData, mapDonVi, mapVanHanh) {
  if (!sheetData) return null;
  const { rows } = sheetData;

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row) {
    let errors = [];
    errors = [...errors, ...checkCommonInfo(t, row)];
    errors = [...errors, ...checkThietBiCommonInfo(t, row, mapVanHanh)];
    if (!row[Headers.DON_VI]?.trim()) {
      errors = [...errors, createError(Headers.DON_VI, t('missing_unit_management'))];
    } else {
      if (!mapDonVi[row[Headers.DON_VI]?.trim()]) {
        errors = [...errors, createError(Headers.DON_VI, t('management_unit_incorrect_or_dont_create'))];
      }
    }
    if (errors.length) {
      row['Lỗi'] = errors;
    } else {
      row['Lỗi'] = null;
    }
    return row;
  }

  sheetData.rows = rows.map(row => validateRow(t, row));
  return sheetData;
}

export function getAll(query, projection = {}) {
  return CACH_DIEN.find(query, projection).lean();
}

export function count(query) {
  return CACH_DIEN.count(query);
}

const Joi = require('joi');

const objSchema = Joi.object({
  ma_day_dan: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã tiếp địa')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function deleteAll(query) {
  return CACH_DIEN.remove(query);
}
