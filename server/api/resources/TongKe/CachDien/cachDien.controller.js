import * as Service from './cachDien.service';
import Model from './cachDien.model';
import * as controllerHelper from '../../../helpers/controllerHelper';

const searchLike = ['ma_cach_dien', 'ten_cach_dien'];
const populateOpts = [];
const uniqueOpts = [{ field: 'ma_cach_dien', message: '<PERSON><PERSON> cách điện' }];

export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, searchLike, populateOpts);

