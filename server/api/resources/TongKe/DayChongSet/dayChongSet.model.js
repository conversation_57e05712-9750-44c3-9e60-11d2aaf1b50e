import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DAY_CHONG_SET, DON_VI, VI_TRI } from '../../../constant/dbCollections';
import { TINH_TRANG_VAN_HANH } from '../../DanhMuc/TinhTrangVanHanh';

const schema = new Schema({
  ma_day_chong_set: { type: String, required: true, unique: true, validate: /\S+/ },
  ma_thiet_bi_cha: { type: String, validate: /\S+/ },
  ma_tim_kiem: { type: String, validate: /\S+/ },
  ma_tim_kiem_cha: { type: String, validate: /\S+/ },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  ten_day_chong_set: { type: String },
  thu_tu: { type: Number },
  seri: { type: String },
  so_the_tscd: String,
  ngay_van_hanh: { type: Date },
  tinh_trang_van_hanh: {
    type: String,
    enum: Object.values(TINH_TRANG_VAN_HANH),
    default: TINH_TRANG_VAN_HANH.VAN_HANH,
  },
  hang_san_xuat: String,
  nha_cung_cap: String,
  nuoc_san_xuat: String,
  so_huu: String,
  ngay_lap_dat: Date,
  nam_san_xuat: Date,
  ngay_sua_doi: Date,
  ghi_chu: Date,
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  ma_hieu_day_chong_set: String,
  tiet_dien: String,
  luc_keo_dut: String,
  he_so_dan_dai: String,
  trong_luong_day: String,
  dien_tro_mot_chieu: String,
  so_luong_moi_noi: String,
  ma_hieu_chong_rung: String,
  so_luong_chong_rung: String,
  ma_hieu_chuoi_cach_dien: String,
  so_luong_chuoi: String,
  so_luong_bat_tren_chuoi: String,
  ma_hieu_cach_dien: String,
  mo_phong_set: String,
  noi_dat: String,
  hop_noi_cap_quang: String,
  so_luong_canh_bao_hang_khong: String,
  vi_tri_dat: String,
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized indexes for updateThongTinViTri function
// Index for vi_tri_id array queries with is_deleted filter
schema.index({ vi_tri_id: 1, is_deleted: 1 }, { background: true });
// Index for ma_day_chong_set queries with is_deleted filter
schema.index({ ma_day_chong_set: 1, is_deleted: 1 }, { background: true });
// Index for don_vi_id queries with is_deleted filter
schema.index({ don_vi_id: 1, is_deleted: 1 }, { background: true });
// Index for created_at with is_deleted for time-based queries
schema.index({ created_at: 1, is_deleted: 1 }, { background: true });

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(DAY_CHONG_SET, schema, DAY_CHONG_SET);
