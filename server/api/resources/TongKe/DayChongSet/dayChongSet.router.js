import express from 'express';
import passport from 'passport';
import * as donviController from './dayChongSet.controller';

export const dayChongSetRouter = express.Router();
dayChongSetRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), donviController.getAll)
  .post(passport.authenticate('jwt', { session: false }), donviController.create);

dayChongSetRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), donviController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), donviController.remove)
  .put(passport.authenticate('jwt', { session: false }), donviController.update);
