import * as ValidatorHelper from '../../../helpers/validatorHelper';
import DAY_CHONG_SET from './dayChongSet.model';
import { convertDate, trim } from '../../../common/functionCommons';
import { checkCommonInfo, checkThietBiCommonInfo, thietBiCommonInfo } from '../TongKe.service';
import { checkNumber } from '../../../helpers/checkDataHelper';
import COT_DIEN from '../../../constant/dbCollections';

const Joi = require('joi');

const Headers = {
  MA_THIET_BI: 'Mã thiết bị',
  DUONG_DAY: 'Đường dây/TBA',
  THIET_BI_CHA: 'Thiết bị/Công trình cha',
  TEN_THIET_BI: 'Thiết bị/Công trình',
  STT: 'STT',
  SERIAL: '<PERSON><PERSON> chế tạo/Serial',
  NGAY_VAN_HANH: '<PERSON><PERSON><PERSON> vận hành',
  SO_TSCD: 'Số TSCĐ',
  TINH_TRANG_VAN_HANH: 'Tình trạng vận hành',
  MA_LIEN_KET_KHAC: 'Mã liên kết khác',
  HANG_SAN_XUAN: 'Hãng sản xuất',
  NHA_CUNG_CAP: 'Nhà cung cấp',
  NUOC_SAN_XUAT: 'Nước sản xuất',
  SO_HUU: 'Sở hữu',
  NGAY_LAP_DAT: 'Ngày lắp đặt',
  NAM_SAN_XUAT: 'Năm sản xuất',
  NGAY_SUA_DOI: 'Ngày sửa đổi',
  GHI_CHU: 'Ghi chú',
  DON_VI: 'Đơn vị',

  MA_HIEU_DAY_CHONG_SET_CAP_QUANG: 'Mã hiệu dây chống sét/ Cáp quang',
  TIET_DIEN: 'Tiết diện',
  LUC_KEO_DUT: 'Lực kéo đứt',
  HE_SO_DAN_DAI: 'Hệ số dãn dài',
  TRONG_LUONG_DAY: 'Trọng lượng dây',
  DIEN_TRO_MOT_CHIEU: 'Điện trở một chiều',
  VI_TRI_DAT: 'Vị trí đặt',
  SO_LUONG_MOI_NOI: 'Số lượng mối nối',
  MA_HIEU_CHONG_RUNG: 'Mã hiệu chống rung',
  SO_LUONG_CHONG_RUNG: 'Số lượng chống rung',
  MA_HIEU_CHUOI_CACH_DIEN: 'Mã hiệu chuỗi cách điện',
  SO_LUONG_CHUOI: 'Số lượng chuỗi',
  SO_LUONG_BAT_TREN_CHUOI: 'Số lượng bát/ chuỗi',
  MA_HIEU_CACH_DIEN: 'Mã hiệu cách điện',
  MO_PHONG_SET: 'Mỏ phóng sét',
  NOI_DAT: 'Nối đất',
  HOP_CAP_QUANG: 'Hộp nối cáp quang',
  SO_LUONG_BONG_CANH_BAO_HANG_KHONG: 'Số lượng bóng cảnh báo hàng không',
};

export async function importData(sheetData, mapDonVi, mapVitri) {
  let { rows } = sheetData;

  function convertToDB(row) {
    const thietBiInfo = thietBiCommonInfo(row, mapDonVi, mapVitri)

    return {
      ma_day_chong_set: row[Headers.MA_THIET_BI]?.trim(),
      ten_day_chong_set: trim(row[Headers.TEN_THIET_BI]),
      ...thietBiInfo,
      seri: row[Headers.SERIAL],
      so_the_tscd: row[Headers.SO_TSCD],
      thu_tu: parseInt(row[Headers.STT]) ? row[Headers.STT] : 0,
      ngay_van_hanh: convertDate(row[Headers.NGAY_VAN_HANH]),
      tinh_trang_van_hanh: row[Headers.TINH_TRANG_VAN_HANH],
      so_huu: row[Headers.SO_HUU],
      ngay_lap_dat: convertDate(row[Headers.NGAY_LAP_DAT]),

      ma_hieu_day_chong_set: row[Headers.MA_HIEU_DAY_CHONG_SET_CAP_QUANG],
      tiet_dien: row[Headers.TIET_DIEN],
      luc_keo_dut: row[Headers.LUC_KEO_DUT],
      he_so_dan_dai: row[Headers.HE_SO_DAN_DAI],
      trong_luong_day: row[Headers.TRONG_LUONG_DAY],
      dien_tro_mot_chieu: row[Headers.DIEN_TRO_MOT_CHIEU],
      so_luong_soi_quang: row[Headers.SO_LUONG_SOI_QUANG],
      vi_tri_dat: row[Headers.VI_TRI_DAT],

      so_luong_moi_noi: row[Headers.SO_LUONG_MOI_NOI],
      ma_hieu_chong_rung: row[Headers.MA_HIEU_CHONG_RUNG],
      so_luong_chong_rung: row[Headers.SO_LUONG_CHONG_RUNG],
      ma_hieu_chuoi_cach_dien: row[Headers.MA_HIEU_CHUOI_CACH_DIEN],
      so_luong_chuoi: row[Headers.SO_LUONG_CHUOI],
      so_luong_bat_tren_chuoi: row[Headers.SO_LUONG_BAT_TREN_CHUOI],
      ma_hieu_cach_dien: row[Headers.MA_HIEU_CACH_DIEN],
      mo_phong_set: row[Headers.MO_PHONG_SET],
      noi_dat: row[Headers.NOI_DAT],
      hop_noi_cap_quang: row[Headers.HOP_CAP_QUANG],
      so_luong_canh_bao_hang_khong: row[Headers.SO_LUONG_BONG_CANH_BAO_HANG_KHONG],
      is_deleted: false,
    };
  }

  rows = rows.filter(row => !(row[Headers.MA_HIEU_DAY_CHONG_SET_CAP_QUANG]?.toUpperCase()?.includes('OPGW')));

  const dataToDB = rows.map(row => convertToDB(row)).filter(element => element.ten_day_chong_set);
  const result = await DAY_CHONG_SET.bulkWrite(
    dataToDB.map((element) =>
      ({
        updateOne: {
          filter: { ma_day_chong_set: element.ma_day_chong_set },
          update: { $set: element },
          upsert: true,
        },
      }),
    ),
  );
  return result;
}


export function checkImport(t, sheetData, mapDonVi, mapVitri) {
  if (!sheetData) return null;
  const { rows } = sheetData;

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row) {
    let errors = [];
    errors = [...errors, ...checkCommonInfo(t, row)];
    errors = [...errors, ...checkThietBiCommonInfo(t, row, mapVitri)];

    if (!row[Headers.DON_VI]?.trim()) {
      errors = [...errors, createError(Headers.DON_VI, t('missing_unit_management'))];
    } else {
      if (!mapDonVi[row[Headers.DON_VI]?.trim()]) {
        errors = [...errors, createError(Headers.DON_VI, t('management_unit_incorrect_or_dont_create'))];
      }
    }

    // if (!checkNumber(row[Headers.TIET_DIEN])) {
    //   errors = [...errors, createError(Headers.TIET_DIEN, t('incorrect_or_not_available'))];
    // }

    // if (!checkNumber(row[Headers.LUC_KEO_DUT])) {
    //   errors = [...errors, createError(Headers.LUC_KEO_DUT, t('incorrect_or_not_available'))];
    // }
    // if (!checkNumber(row[Headers.HE_SO_DAN_DAI])) {
    //   errors = [...errors, createError(Headers.HE_SO_DAN_DAI, t('incorrect_or_not_available'))];
    // }

    // if (!checkNumber(row[Headers.TRONG_LUONG_DAY])) {
    //   errors = [...errors, createError(Headers.TRONG_LUONG_DAY, t('incorrect_or_not_available'))];
    // }
    // if (!checkNumber(row[Headers.DIEN_TRO_MOT_CHIEU])) {
    //   errors = [...errors, createError(Headers.DIEN_TRO_MOT_CHIEU, t('incorrect_or_not_available'))];
    // }


    // if (!checkNumber(row[Headers.SO_LUONG_MOI_NOI])) {
    //   errors = [...errors, createError(Headers.SO_LUONG_MOI_NOI, t('incorrect_or_not_available'))];
    // }
    //
    // if (!checkNumber(row[Headers.SO_LUONG_CHONG_RUNG])) {
    //   errors = [...errors, createError(Headers.SO_LUONG_CHONG_RUNG, t('incorrect_or_not_available'))];
    // }
    // if (!checkNumber(row[Headers.SO_LUONG_CHUOI])) {
    //   errors = [...errors, createError(Headers.SO_LUONG_CHUOI, t('incorrect_or_not_available'))];
    // }
    // if (!checkNumber(row[Headers.SO_LUONG_BAT_TREN_CHUOI])) {
    //   errors = [...errors, createError(Headers.SO_LUONG_BAT_TREN_CHUOI, t('incorrect_or_not_available'))];
    // }
    // if (!checkNumber(row[Headers.SO_LUONG_BONG_CANH_BAO_HANG_KHONG])) {
    //   errors = [...errors, createError(Headers.SO_LUONG_BONG_CANH_BAO_HANG_KHONG, t('incorrect_or_not_available'))];
    // }

    if (errors.length) {
      row['Lỗi'] = errors;
    } else {
      row['Lỗi'] = null;
    }
    return row;
  }

  sheetData.rows = rows.map(row => validateRow(t, row));
  return sheetData;
}

export function getAll(query, projection = {}) {
  return DAY_CHONG_SET.find(query, projection)
    .lean();
}

export function count(query) {
  return DAY_CHONG_SET.count(query);
}

const objSchema = Joi.object({
  ma_day_chong_set: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã vị trí')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function deleteAll(query) {
  return DAY_CHONG_SET.remove(query)
}

export async function updateAll(arrData) {
  await DAY_CHONG_SET.bulkWrite(
    arrData.map((row) =>
      ({
        updateOne: {
          filter: { _id: row._id },
          update: { $set: row },
          upsert: false,
        },
      }),
    ),
  );
}
