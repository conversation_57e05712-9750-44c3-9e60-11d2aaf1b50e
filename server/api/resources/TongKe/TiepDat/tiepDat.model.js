import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DON_VI, TIEP_DAT, VI_TRI } from '../../../constant/dbCollections';
import { TINH_TRANG_VAN_HANH } from '../../DanhMuc/TinhTrangVanHanh';

const schema = new Schema({
  ma_tiep_dat: { type: String, required: true, unique: true, validate: /\S+/ },
  ma_thiet_bi_cha: { type: String, validate: /\S+/ },
  ma_tim_kiem: { type: String, validate: /\S+/ },
  ma_tim_kiem_cha: { type: String, validate: /\S+/ },
  ten_tiep_dat: { type: String },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  thu_tu: { type: Number },
  so_the_tscd: String,
  ngay_van_hanh: { type: Date },
  tinh_trang_van_hanh: {
    type: String,
    enum: Object.values(TINH_TRANG_VAN_HANH),
    default: TINH_TRANG_VAN_HANH.VAN_HANH,
  },
  hang_san_xuat: String,
  nha_cung_cap: String,
  nuoc_san_xuat: String,
  so_huu: String,
  ngay_lap_dat: String,
  nam_san_xuat: Date,
  ghi_chu: String,
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  ma_hieu_noi_dat: String,
  so_tia: String,
  chieu_dai_tia: String,
  vat_lieu_tia: String,
  so_coc: String,
  chieu_dai_coc: String,
  khoang_cach_cac_coc: String,
  vat_lieu_coc: String,

  thoi_gian_chiu_ngan_mach: String,
  loai_noi_dat: String,
  dien_tro_tai_thoi_diem_ban_dau: String,
  so_tiep_dat: { type: Number, default: 1 },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized indexes for updateThongTinViTri function
// Index for vi_tri_id array queries with is_deleted filter
schema.index({ vi_tri_id: 1, is_deleted: 1 }, { background: true });
// Index for ma_tiep_dat queries with is_deleted filter
schema.index({ ma_tiep_dat: 1, is_deleted: 1 }, { background: true });
// Index for don_vi_id queries with is_deleted filter
schema.index({ don_vi_id: 1, is_deleted: 1 }, { background: true });
// Index for created_at with is_deleted for time-based queries
schema.index({ created_at: 1, is_deleted: 1 }, { background: true });

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(TIEP_DAT, schema, TIEP_DAT);
