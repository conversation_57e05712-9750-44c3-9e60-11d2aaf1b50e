import * as ValidatorHelper from '../../../helpers/validatorHelper';
import TIEP_DAT from './tiepDat.model';
import moment from 'moment';
import { convertDate, checkNumber, checkDate, checkYear, convertYear } from '../../../helpers/checkDataHelper';
import { checkCommonInfo, checkThietBiCommonInfo, thietBiCommonInfo } from '../TongKe.service';
import { trim } from '../../../common/functionCommons';
import COT_DIEN from '../../../constant/dbCollections';
import { CommonHeaders } from '../ThietBi/thietBi.controller';

const Joi = require('joi');
const Headers = {
  MA_THIET_BI: 'Mã thiết bị',
  DUONG_DAY: 'Đường dây/TBA',
  THIET_BI_CHA: 'Thiết bị/Công trình cha',
  TEN_THIET_BI: 'Thiết bị/Công trình',
  STT: 'STT',
  SERIAL: 'Số chế tạo/Serial',
  NGAY_VAN_HANH: 'Ngày vận hành',
  SO_TSCD: 'Số TSCĐ',
  TINH_TRANG_VAN_HANH: 'Tình trạng vận hành',
  MA_LIEN_KET_KHAC: 'Mã liên kết khác',
  HANG_SAN_XUAN: 'Hãng sản xuất',
  NHA_CUNG_CAP: 'Nhà cung cấp',
  NUOC_SAN_XUAT: 'Nước sản xuất',
  SO_HUU: 'Sở hữu',
  NGAY_LAP_DAT: 'Ngày lắp đặt',
  NAM_SAN_XUAT: 'Năm sản xuất',
  NGAY_SUA_DOI: 'Ngày sửa đổi',
  GHI_CHU: 'Ghi chú',
  DON_VI: 'Đơn vị',

  MA_HIEU_NOI_DAT: 'Mã hiệu nối đất',
  SO_TIA: 'Số tia',
  CHIEU_DAI_TIA: 'Chiều dài tia',
  CHIEU_DAI_COC: 'Chiều dài cọc',
  VAT_LIEU_TIA: 'Vật liệu tia',
  SO_COC: 'Số cọc',
  KHOANG_CACH_CAC_COC: 'Khoảng cách các cọc',
  VAT_LIEU_COC: 'Vật liệu cọc',
  THOI_GIAN_CHIU_DUNG_DONG_NGAN_MACH: 'Thời gian chịu đựng dòng ngắn mạch',
  LOAI_NOI_DAT: 'Loại nối đất',
  DIEN_TRO_TAI_THOI_DIEM_BAN_DAU: 'Điện trở tại thời điểm ban đầu',
};

export async function importData(sheetData, mapDonVi, mapVitri) {
  const { rows } = sheetData;

  function convertToDB(row) {
    const thietBiInfo = thietBiCommonInfo(row, mapDonVi, mapVitri)
    return {
      ma_tiep_dat: row[Headers.MA_THIET_BI]?.trim(),
      ten_tiep_dat: trim(row[Headers.TEN_THIET_BI]),
      ...thietBiInfo,
      so_the_tscd: row[Headers.SO_TSCD],
      thu_tu: row[Headers.STT],
      ngay_van_hanh: convertDate(row[Headers.NGAY_VAN_HANH]),
      tinh_trang_van_hanh: row[Headers.TINH_TRANG_VAN_HANH],
      nuoc_san_xuat: row[Headers.NUOC_SAN_XUAT],
      nam_san_xuat: convertYear(row[Headers.NAM_SAN_XUAT]),
      so_huu: row[Headers.SO_HUU],
      ngay_lap_dat: convertDate(row[Headers.NGAY_LAP_DAT]),

      ma_hieu_noi_dat: row[Headers.MA_HIEU_NOI_DAT],
      so_tia: row[Headers.SO_TIA],
      chieu_dai_tia: row[Headers.CHIEU_DAI_TIA],
      vat_lieu_tia: row[Headers.VAT_LIEU_TIA],
      so_coc: row[Headers.SO_COC],
      chieu_dai_coc: row[Headers.CHIEU_DAI_COC],
      khoang_cach_cac_coc: row[Headers.KHOANG_CACH_CAC_COC],
      vat_lieu_coc: row[Headers.VAT_LIEU_COC],
      thoi_gian_chiu_ngan_mach: row[Headers.THOI_GIAN_CHIU_DUNG_DONG_NGAN_MACH],
      dien_tro_tai_thoi_diem_ban_dau: row[Headers.DIEN_TRO_TAI_THOI_DIEM_BAN_DAU],
      loai_noi_dat: row[Headers.LOAI_NOI_DAT],
    };
  }

  const dataToDB = rows.map(row => convertToDB(row)).filter(element => element.ten_tiep_dat);
  const result = await TIEP_DAT.bulkWrite(
    dataToDB.map((tiepDat) =>
      ({
        updateOne: {
          filter: { ma_tiep_dat: tiepDat.ma_tiep_dat },
          update: { $set: tiepDat },
          upsert: true,
        },
      }),
    ),
  );
  return result;
}

export function checkImport(t, sheetData, mapDonVi, mapVitri) {
  if (!sheetData) return null;
  const { rows } = sheetData;

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row) {
    let errors = [];
    errors = [...errors, ...checkCommonInfo(t, row)];
    errors = [...errors, ...checkThietBiCommonInfo(t, row, mapVitri)];

    if (!row[Headers.DON_VI]?.trim()) {
      errors = [...errors, createError(Headers.DON_VI, t('missing_unit_construction'))];
    } else {
      if (!mapDonVi[row[Headers.DON_VI]?.trim()]) {
        errors = [...errors, createError(Headers.DON_VI, t('management_unit_incorrect_or_dont_create'))];
      }
    }

    if (!checkNumber(row[Headers.SO_TIA])) {
      errors = [...errors, createError(Headers.SO_TIA, t('incorrect_or_not_available'))];
    }
    // if (!checkNumber(row[Headers.CHIEU_DAI_TIA])) {
    //   errors = [...errors, createError(Headers.CHIEU_DAI_TIA, t('incorrect_or_not_available'))];
    // }
    // if (!checkNumber(row[Headers.KHOANG_CACH_CAC_COC])) {
    //   errors = [...errors, createError(Headers.KHOANG_CACH_CAC_COC, t('incorrect_or_not_available'))];
    // }
    // if (!checkNumber(row[Headers.DIEN_TRO_TAI_THOI_DIEM_BAN_DAU])) {
    //   errors = [...errors, createError(Headers.DIEN_TRO_TAI_THOI_DIEM_BAN_DAU, t('incorrect_or_not_available'))];
    // }

    if (errors.length) {
      row['Lỗi'] = errors;
    } else {
      row['Lỗi'] = null;
    }
    return row;
  }

  sheetData.rows = rows.map(row => validateRow(t, row));
  return sheetData;
}

export function getAll(query, projection = {}) {
  return TIEP_DAT.find(query, projection).lean();
}

export function count(query) {
  return TIEP_DAT.count(query);
}

export function getOne(query) {
  return TIEP_DAT.findOne(query).lean();
}

export async function updateAll(arrData) {
  await TIEP_DAT.bulkWrite(
    arrData.map((row) =>
      ({
        updateOne: {
          filter: { _id: row._id },
          update: { $set: row },
          upsert: false,
        },
      }),
    ),
  );
}

const objSchema = Joi.object({
  ma_tiep_dat: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã tiếp địa')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function deleteAll(query) {
  return TIEP_DAT.remove(query)
}

