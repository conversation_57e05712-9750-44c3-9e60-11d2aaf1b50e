import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DUONG_DAY, KHOANG_COT, VI_TRI } from '../../../constant/dbCollections';

const schema = new Schema({
  // _id: { type: String, unique: true },
  ten_khoang_cot: String,
  ma_khoang_cot: String,
  vi_tri_bat_dau_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  vi_tri_ket_thuc_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  duong_day_id: [{ type: Schema.Types.ObjectId, ref: DUONG_DAY }],
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  chieu_dai: Number,
  cong_don_tu_dau_day: Number,
  cong_don_tu_cuoi_day: Number,
  ghi_chu: String,
  auto_create: { type: Boolean, default: false },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

// Existing indexes
schema.index({ vi_tri_id: 1 });
schema.index({ duong_day_id: 1 });

// Optimized compound indexes for the specific query
// Index for the main query conditions: is_deleted + duong_day_id + vi_tri fields
schema.index({ 
  is_deleted: 1, 
  duong_day_id: 1, 
  vi_tri_bat_dau_id: 1, 
  vi_tri_ket_thuc_id: 1 
});

// Additional index for exists queries on vi_tri fields
schema.index({ 
  vi_tri_bat_dau_id: 1, 
  vi_tri_ket_thuc_id: 1, 
  is_deleted: 1 
});

// Sparse index for documents where both vi_tri fields exist
schema.index({ 
  vi_tri_bat_dau_id: 1, 
  vi_tri_ket_thuc_id: 1 
}, { sparse: true });
export default mongoose.model(KHOANG_COT, schema, KHOANG_COT);
