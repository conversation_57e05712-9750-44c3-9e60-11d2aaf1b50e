import * as responseHelper from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';
import CommonError from '../../../error/CommonError';
import Model from './khoangCot.model';
import VI_TRI from '../ViTri/viTri.model';

import * as Service from './khoangCot.service';
import * as DonViService from '../../DonVi/donVi.service';
import * as ViTriService from '../ViTri/viTri.service';

import { extractIds } from '../../../utils/dataconverter';
import { formatUnique } from '../../../common/functionCommons';

const selectViTri = {
  select: 'ten_vi_tri don_vi_id cong_trinh_id kinh_do vi_do thu_tu',
  populate: [
    { path: 'don_vi_id', select: 'ten_don_vi' },
    { path: 'cong_trinh_id', select: 'ten_cong_trinh' },
  ],
};

const populateOpts = [
  { path: 'vi_tri_bat_dau_id', ...selectViTri },
  { path: 'vi_tri_ket_thuc_id', ...selectViTri },
  {
    path: 'vi_tri_id', ...selectViTri,
    options: { sort: { 'vi_tri_id.cong_trinh_id': 1, 'vi_tri_id.thu_tu': 1 } },
  },
  {
    path: 'duong_day_id',
    select: 'ten_duong_day',
  },
];

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id)
      .populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, 404, '');
    }
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true, auto_create: false }, { new: true });
    if (!data) {
      return responseHelper.error(res, 404, '');
    }
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    value.auto_create = false;
    value.duong_day_id = formatUnique(value.duong_day_id);

    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true })
      .populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    data.ma_khoang_cot = Service.generateMaKhoangCot(data.vi_tri_bat_dau_id.ma_vi_tri, data.vi_tri_ket_thuc_id.ma_vi_tri);
    await data.save();
    return responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function create(req, res) {

  try {
    const { t } = req;
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    const vt1 = await VI_TRI.findById(value.vi_tri_bat_dau_id);
    const vt2 = await VI_TRI.findById(value.vi_tri_ket_thuc_id);
    const vtLienKet = await VI_TRI.findById(value.vi_tri_id);

    const checkUnique = await Service.getOne({
      vi_tri_bat_dau_id: vt1._id,
      vi_tri_ket_thuc_id: vt2._id,
      is_deleted: false,
    });
    if (checkUnique) {
      return responseHelper.error(res, { message: t('tower_distance_existed') });
    }

    const createData = {
      ten_khoang_cot: `${vt1.ten_vi_tri}-${vt2.ten_vi_tri}`,
      vi_tri_bat_dau_id: vt1._id,
      vi_tri_ket_thuc_id: vt2._id,
      ma_khoang_cot: Service.generateMaKhoangCot(vt1.ma_vi_tri, vt2.ma_vi_tri),
      vi_tri_id: vtLienKet._id,
      chieu_dai: value.chieu_dai,
      duong_day_id: formatUnique(value.duong_day_id),
      auto_create: false,
      is_deleted: false,
    };
    const data = await Model.create(createData);

    const dataRtn = await data.populate(populateOpts).execPopulate();
    return responseHelper.success(res, dataRtn);
  } catch (err) {
    console.error(err);
    return responseHelper.error(res, err, 500);
  }
}

export async function autoCreate(req, res) {
  try {
    await Service.createOrUpdateKhoangCot();
    const data = await Service.count();
    return responseHelper.success(res, data);
  } catch (err) {
    console.error(err);
    return responseHelper.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req, ['ten_khoang_cot']);
    const { criteria, options } = query;
    options.populate = populateOpts;
    const dontInScope = await DonViService.getArrayDonViQuery(req, criteria.don_vi_id);
    const queryViTri = {};
    delete criteria.don_vi_id;
    if (criteria.cong_trinh_id) {

      queryViTri.cong_trinh_id = criteria.cong_trinh_id;
      delete criteria.cong_trinh_id;
    }

    const viTriPromise = dontInScope.map(donVi => {
      return VI_TRI.find({ ...queryViTri, don_vi_id: donVi }, { _id: 1 }).lean();
    });
    const allViTri = (await Promise.all(viTriPromise)).flat();

    criteria.vi_tri_id = extractIds(allViTri);
    const data = await Model.paginate(criteria, options);
    responseHelper.success(res, data);
  } catch (err) {
    responseHelper.error(res, err);
  }
}

export async function updateSchema(req, res) {
  try {
    const res = {};
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function updateUniquePowerLine(req, res) {
  try {
    const donViId = req.params.id;

    if (!req?.user?.is_system_admin || !donViId) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    const viTriByDonVi = await ViTriService.getAll({ don_vi_id: req.body.don_vi_id, is_deleted: false });
    const viTriId = extractIds(viTriByDonVi);

    const khoangCotByDonVi = await Service.getAll({ vi_tri_id: viTriId, is_deleted: false }, { duong_day_id: 1 });

    khoangCotByDonVi.forEach(khoangCot => {
      khoangCot.duong_day_id = formatUnique(khoangCot.duong_day_id);
    });

    const results = await Service.updateAll(khoangCotByDonVi);
    return responseHelper.success(res, results);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function removeDeletedPowerLine(req, res) {
  try {
    const donViId = req.params.id;

    if (!req?.user?.is_system_admin || !donViId) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }

    const viTriByDonVi = await ViTriService.getAll({ don_vi_id: donViId, is_deleted: false });
    const viTriId = extractIds(viTriByDonVi);

    const khoangCotByDonVi = await Service.getAll(
      { vi_tri_id: viTriId, is_deleted: false },
      { duong_day_id: 1 })
      .populate({ path: 'duong_day_id', select: 'ten_duong_day is_deleted' });


    khoangCotByDonVi.forEach(khoangCot => {
      if (khoangCot.duong_day_id?.length) {
        const duongDayFiltered = khoangCot.duong_day_id.filter(duongDay => !duongDay.is_deleted);
        khoangCot.duong_day_id = extractIds(duongDayFiltered);
      }
    });

    const results = await Service.updateAll(khoangCotByDonVi);
    return responseHelper.success(res, results);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}
