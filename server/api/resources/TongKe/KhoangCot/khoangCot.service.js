import * as ValidatorHelper from '../../../helpers/validatorHelper';
import * as CongTrinhService from '../CongTrinh/congTrinh.service';
import * as ViTriService from '../ViTri/viTri.service';
import VI_TRI from '../ViTri/viTri.model';
import * as VanHanhService from '../VanHanh/vanHanh.service';

import KHOANG_COT from './khoangCot.model';
import { extractIds, groupBy } from '../../../utils/dataconverter';

export function createData(vt1, vt2) {
  const khoangCot = {
    ten_khoang_cot: `${vt1.ten_vi_tri}-${vt2.ten_vi_tri}`,
    ma_khoang_cot: generateMaKhoangCot(vt1.ma_vi_tri, vt2.ma_vi_tri),
    vi_tri_bat_dau_id: vt1._id,
    vi_tri_ket_thuc_id: vt2._id,
    vi_tri_id: vt2._id,
    chieu_dai: vt2.khoang_cot,
    auto_create: true,
    is_deleted: false,
  };
  if (vt2.duong_day_id) {
    khoangCot.duong_day_id = vt2.duong_day_id;
  }
  return khoangCot;
}


const Joi = require('joi');

const objSchema = Joi.object({
  ten_duong_day: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên đường dây')),
  ma_duong_day: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã đường dây')),
  loai_duong_day_id: Joi.string().messages(ValidatorHelper.messageDefine('Loại đường dây')),
});

export function generateMaKhoangCot(maViTri1, maViTri2) {
  return `${maViTri1}-${maViTri2}`;
}

export async function createOrUpdateKhoangCot(congTrinhIds) {
  let allCongTrinhIds;
  const createOrUpdateData = [];
  if (congTrinhIds) {
    allCongTrinhIds = [...new Set(congTrinhIds)];
  } else {
    const allCongTrinh = await CongTrinhService.getAll({ is_deleted: false });
    allCongTrinhIds = extractIds(allCongTrinh);
  }
  const allViTri = await ViTriService.getAll(
    { cong_trinh_id: allCongTrinhIds, is_deleted: false },
    { _id: 1, ten_vi_tri: 1, khoang_cot: 1, cong_trinh_id: 1, thu_tu: 1, ma_vi_tri: 1 },
  ).sort({ thu_tu: 1 });

  const allVanHanh = await VanHanhService.getAll(
    { vi_tri_id: extractIds(allViTri), is_deleted: false },
    { vi_tri_id: 1, duong_day_id: 1 },
  );
  const vanHanhGroupViTri = allVanHanh.reduce((prevValue, currentValue) => {
    if (currentValue.vi_tri_id && currentValue.duong_day_id) {
      prevValue[currentValue.vi_tri_id] ||= [];
      prevValue[currentValue.vi_tri_id].push(currentValue.duong_day_id);
    }
    return prevValue;
  }, {});

  const objViTri = groupBy(allViTri, 'cong_trinh_id');
  for (let i = 0; i < allCongTrinhIds.length; i++) {
    const viTriByCongTrinh = objViTri[allCongTrinhIds[i]];
    if (Array.isArray(viTriByCongTrinh)) {
      for (let i = 1; i < viTriByCongTrinh.length; i += 1) {
        const vt1 = viTriByCongTrinh[i - 1];
        const vt2 = { ...viTriByCongTrinh[i], duong_day_id: vanHanhGroupViTri[viTriByCongTrinh[i]?._id] };
        createOrUpdateData.push(createData(vt1, vt2));
      }
    }
  }

  /*console.log('createOrUpdateData', createOrUpdateData[0]);
  console.log('createOrUpdateData', createOrUpdateData.length);
  let khoangCot = [];
  for (let i = 0; i < createOrUpdateData.length / 10000; i += 1) {
    const max = (i + 1) * 10000 >= createOrUpdateData.length ? createOrUpdateData.length : (i + 1) * 10000;
    console.log('max', i * 10000, max);
    const pathCreateData = [];
    for (let j = i * 10000; j < max; j++) {
      pathCreateData.push({
        updateOne: {
          filter: { vi_tri_id: createOrUpdateData[j].vi_tri_id },
          update: { $set: createOrUpdateData[j] },
          upsert: true,
        },
      });
    }

    const resultCreated = await KHOANG_COT.bulkWrite(pathCreateData);
    khoangCot.push(resultCreated);
  }
  return khoangCot;*/

  const extractedIds = extractIds(allViTri);
  const needUpdateKhoangCots = await KHOANG_COT.find({
    vi_tri_id: { $in: extractedIds },
  }).populate('vi_tri_bat_dau_id vi_tri_ket_thuc_id');
  for (let i = 0; i < needUpdateKhoangCots.length; i++) {
    needUpdateKhoangCots[i].ma_khoang_cot = generateMaKhoangCot(needUpdateKhoangCots[i]?.vi_tri_bat_dau_id?.ma_vi_tri, needUpdateKhoangCots[i]?.vi_tri_ket_thuc_id?.ma_vi_tri);
    await needUpdateKhoangCots[i].save();
  }

  return KHOANG_COT.bulkWrite(
    createOrUpdateData.map((khoangCot) =>
      ({
        updateOne: {
          filter: { ma_khoang_cot: khoangCot.ma_khoang_cot },
          update: { $set: khoangCot },
          upsert: true,
        },
      }),
    ),
  );
}

export function getOne(query, projection = {}) {
  return KHOANG_COT.findOne(query, projection).lean();
}

export function getAll(query, projection = {}) {
  return KHOANG_COT.find(query, projection).lean();
}

export function count(query) {
  return KHOANG_COT.count(query);
}

export function updateMany(filter, update) {
  return KHOANG_COT.updateMany(filter, update);
}


export async function createMany(dataList = []) {
  if (!Array.isArray(dataList)) {
    throw new Error('Input must be an array');
  }

  const validRecords = [];

  for (const data of dataList) {
    const { error, value } = validate(data);
    if (!error) {
      validRecords.push(value);
    }
  }

  return KHOANG_COT.insertMany(validRecords);
}

export async function updateAll(dataUpdate, options = {}) {
  options.new = true;
  const results = [];
  for (const row of dataUpdate) {
    try {
      const { error, value } = validate(row);
      if (!error) {
        const updated = await KHOANG_COT.findByIdAndUpdate(value._id, value, options);
        if (updated) results.push(updated);
      }
    } catch {
    }
  }
  return results;
}

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function khoiLuongQuanLyTheoDonVi(queryAggregate) {
  const allDonVi = await KHOANG_COT.aggregate([
      {
        $match: queryAggregate,
      },
      {
        $unwind: { path: '$duong_day_id' },
      },
      {
        $group: {
          _id: {
            don_vi_id: '$don_vi_id',
          },
          chieu_dai: {
            $sum: '$chieu_dai',
          },
        },
      },
    ],
  );
  let khoiLuongQuanLy = 0;
  allDonVi.forEach(dv => {
    khoiLuongQuanLy += dv.chieu_dai / 1000;
  });
  return khoiLuongQuanLy;
}

export async function khoiLuongQuanLyCongTrinh(viTriIds) {
  let viTriQuanLy, chieuDaiQuanLy;

  async function getChieuDaiQuanLy() {
    chieuDaiQuanLy = await KHOANG_COT.aggregate([
        { $match: { vi_tri_id: { $in: viTriIds }, is_deleted: false } },
        {
          $lookup: {
            from: 'ViTri',
            localField: 'vi_tri_id',
            foreignField: '_id',
            as: 'vi_tri_id',
          },
        },
        { $unwind: '$vi_tri_id' },
        {
          $group: {
            _id: {
              don_vi_id: '$vi_tri_id.don_vi_id',
              cong_trinh_id: '$vi_tri_id.cong_trinh_id',
            },
            chieu_dai: { $sum: '$chieu_dai' },
          },
        },
        { $set: { don_vi_id: '$_id.don_vi_id', cong_trinh_id: '$_id.cong_trinh_id' } },
        { $project: { _id: 0 } },
      ],
    );
  }

  async function getViTriQuanLy() {
    viTriQuanLy = await VI_TRI.aggregate([
        { $match: { _id: { $in: viTriIds }, is_deleted: false } },
        {
          $lookup: {
            from: 'CotDien',
            localField: '_id',
            foreignField: 'vi_tri_id',
            as: 'cong_dung_cot',
          },
        },
        {
          $unwind: {
            path: '$cong_dung_cot', preserveNullAndEmptyArrays: true,
          },
        },
        {
          $set: {
            cong_dung_cot: { $split: ['$cong_dung_cot.cong_dung_cot', ' '] },
          },
        },
        {
          $project: {
            cong_dung_cot: { $arrayElemAt: ['$cong_dung_cot', 0] },
            cong_trinh_id: 1,
            don_vi_id: 1,
          },
        },
        {
          $group: {
            _id: {
              don_vi_id: '$don_vi_id',
              cong_trinh_id: '$cong_trinh_id',
            },
            so_vi_tri: { $sum: 1.0 },
            so_cot_neo: {
              $sum: {
                $cond: [
                  {
                    $eq: [
                      '$cong_dung_cot',
                      'Néo',
                    ],
                  },
                  1.0,
                  0.0,
                ],
              },
            },
            so_cot_do: {
              $sum: {
                $cond: [
                  {
                    $eq: [
                      '$cong_dung_cot',
                      'Đỡ',
                    ],
                  },
                  1.0,
                  0.0,
                ],
              },
            },
          },
        },
        {
          $lookup: {
            from: 'CongTrinh',
            localField: '_id.cong_trinh_id',
            foreignField: '_id',
            as: 'cong_trinh_id',
          },
        },
        { $unwind: { path: '$cong_trinh_id' } },
        { $sort: { 'cong_trinh_id.thu_tu': 1 } },
        {
          $set: {
            don_vi_id: '$_id.don_vi_id',
            cong_trinh_id: '$_id.cong_trinh_id',
            ten_cong_trinh: '$cong_trinh_id.ten_cong_trinh',
          },
        },
        { $project: { _id: 0 } },
      ],
    );
  }

  const allPromises = [
    getViTriQuanLy(),
    getChieuDaiQuanLy(),
  ];
  await Promise.all(allPromises);

  const mapDonVi = {};
  chieuDaiQuanLy.forEach(element => {
    mapDonVi[element.don_vi_id + element.cong_trinh_id] = parseFloat((element.chieu_dai / 1000).toFixed(2)) || 0;
  });

  viTriQuanLy.forEach(element => {
    element.chieu_dai = parseFloat(mapDonVi[element.don_vi_id + element.cong_trinh_id]) || 0;
  });
  const mapKhoiLuongQuanLy = {};
  viTriQuanLy.forEach(element => {
    if (!mapKhoiLuongQuanLy[element.don_vi_id]) {
      mapKhoiLuongQuanLy[element.don_vi_id] = {
        so_vi_tri: 0,
        so_cot_do: 0,
        so_cot_neo: 0,
        chieu_dai: 0,
        cong_trinhs: [],
      };
    }
    mapKhoiLuongQuanLy[element.don_vi_id].so_vi_tri += element.so_vi_tri;
    mapKhoiLuongQuanLy[element.don_vi_id].so_cot_do += element.so_cot_do;
    mapKhoiLuongQuanLy[element.don_vi_id].so_cot_neo += element.so_cot_neo;
    mapKhoiLuongQuanLy[element.don_vi_id].chieu_dai += parseFloat(element.chieu_dai);
    mapKhoiLuongQuanLy[element.don_vi_id].cong_trinhs.push(element);
  });

  Object.values(mapKhoiLuongQuanLy).forEach(element => {
    element.chieu_dai = parseFloat(element.chieu_dai.toFixed(2));
  });
  console.log(mapKhoiLuongQuanLy);
  return mapKhoiLuongQuanLy;
}
