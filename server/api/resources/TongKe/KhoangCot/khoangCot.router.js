import express from 'express';
import passport from 'passport';
import * as Controller from './khoangCot.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import Permission from '../../RBAC/permissions/KhoangCotPermission';
import { loggerMiddleware } from '../../../logs/middleware';
import { removeDeletedPowerLine, removeDuplicatePowerLines, updateUniquePowerLine } from './khoangCot.controller';

export const khoangCotRouter = express.Router();

khoangCotRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
khoangCotRouter.post('*', authorizationMiddleware([Permission.CREATE]));
khoangCotRouter.put('*', authorizationMiddleware([Permission.UPDATE]));
khoangCotRouter.delete('*', authorizationMiddleware([Permission.DELETE]));
khoangCotRouter.get('*', authorizationMiddleware([Permission.READ]));

khoangCotRouter.route('/')
  .get(Controller.getAll)
  .post(Controller.create);

khoangCotRouter.route('/auto')
  .post(Controller.autoCreate);

khoangCotRouter.route('/don-vi/:id/update-unique-powerline')
  .put(Controller.updateUniquePowerLine);

khoangCotRouter.route('/don-vi/:id/remove-deleted-powerline')
  .put(Controller.removeDeletedPowerLine);

khoangCotRouter.route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
