import express from 'express';
import passport from 'passport';
import * as thietBiController from './thietBi.controller';
import { checkTempFolder, multipartMiddleware } from '../../../utils/fileUtils';
import { loggerMiddleware } from '../../../logs/middleware';
import { authorizationMiddleware } from '../../RBAC/middleware';
import TongKePermission from '../../RBAC/permissions/TongKePermission';

export const thietBiRouter = express.Router();
thietBiRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
thietBiRouter.post('*', authorizationMiddleware([TongKePermission.CREATE, TongKePermission.UPDATE]));
thietBiRouter.put('*', authorizationMiddleware([TongKePermission.UPDATE]));
thietBiRouter.delete('*', authorizationMiddleware([TongKePermission.DELETE]));

thietBiRouter
  .route('/import')
  .post(passport.authenticate('jwt', { session: false }),
    checkTempFolder, multipartMiddleware, thietBiController.importThietBi);

thietBiRouter
  .route('/importone')
  .post(passport.authenticate('jwt', { session: false }),
    thietBiController.importOne);
thietBiRouter
  .route('/importmany')
  .post(passport.authenticate('jwt', { session: false }), thietBiController.importMany);

thietBiRouter
  .route('/checkimport')
  .post(passport.authenticate('jwt', { session: false }),
    checkTempFolder, multipartMiddleware, thietBiController.checkImport);

thietBiRouter
  .route('/checkimportbydata')
  .post(passport.authenticate('jwt', { session: false }), thietBiController.checkImportData);

thietBiRouter
  .route('/completed-delete')
  .delete(thietBiController.deleteByDonVi);
