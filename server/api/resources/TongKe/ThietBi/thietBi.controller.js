import * as responseAction from '../../../helpers/responseHelper';
import * as CongTrinhService from '../CongTrinh/congTrinh.service';
import * as ViTriService from '../ViTri/viTri.service';
import * as CotDienService from '../CotDien/cotDien.service';
import * as TiepDatService from '../TiepDat/tiepDat.service';
import * as DayChongSetService from '../DayChongSet/dayChongSet.service';
import * as DayCapQuangService from '../DayCapQuang/dayCapQuang.service';
import * as GiaoCheoService from '../GiaoCheo/giaoCheo.service';
import * as VanHanhService from '../VanHanh/vanHanh.service';
import * as DonViService from '../../DonVi/donVi.service';
import * as DayDanService from '../DayDan/dayDan.service';
import * as CachDienService from '../CachDien/cachDien.service';
import exelUtils from '../../../utils/exelUtils';
import * as DuongDayService from '../DuongDay/duongDay.service';
import CONG_TRINH from '../CongTrinh/congTrinh.model';
import { cutTail } from '../../../common/functionCommons';
import CommonError from '../../../error/CommonError';
import DUONG_DAY from '../DuongDay/duongDay.model';
import VI_TRI from '../ViTri/viTri.model';
import CACH_DIEN from '../CachDien/cachDien.model';
import COT_DIEN from '../CotDien/cotDien.model';
import DAY_CAP_QUANG from '../DayCapQuang/dayCapQuang.model';
import DAY_CHONG_SET from '../DayChongSet/dayChongSet.model';
import GIAO_CHEO from '../GiaoCheo/giaoCheo.model';
import KHOANG_COT from '../KhoangCot/khoangCot.model';
import KHOANG_NEO from '../KhoangNeo/khoangNeo.model';
import TIEP_DAT from '../TiepDat/tiepDat.model';
import VAN_HANH from '../VanHanh/vanHanh.model';
import DAY_DAN from '../DayDan/dayDan.model';

const SHEET_NAMES = {
  CONG_TRINH: 'Công trình',
  VI_TRI: 'Vị trí',
  COT_DIEN: 'Cột điện',
  CAP_QUANG: 'Cáp quang',
  GIAO_CHEO: 'Giao chéo',
  DAY_CHONG_SET: 'Dây chống sét',
  VAN_HANH: 'Vận hành',
  DAY_DAN: 'Dây dẫn',
  CACH_DIEN: 'Cách điện',
  TIEP_DAT: 'Tiếp đất',
};

export const CommonHeaders = {
  MA_THIET_BI: 'Mã thiết bị',
  MA_THIET_BI_CHA: 'Mã thiết bị cha',
  MA_TIM_KIEM_THIET_BI: 'Mã tìm kiếm thiết bị',
  MA_TIM_KIEM_CHA: 'Mã tìm kiếm cha',
  DUONG_DAY: 'Đường dây/TBA',
  THIET_BI_CHA: 'Thiết bị/Công trình cha',
  TEN_THIET_BI: 'Thiết bị/Công trình',
  STT: 'STT',
  SERIAL: 'Số chế tạo/Serial',
  NGAY_VAN_HANH: 'Ngày vận hành',
  SO_TSCD: 'Số TSCĐ',
  TINH_TRANG_VAN_HANH: 'Tình trạng vận hành',
  MA_LIEN_KET_KHAC: 'Mã liên kết khác',
  HANG_SAN_XUAN: 'Hãng sản xuất',
  NHA_CUNG_CAP: 'Nhà cung cấp',
  NUOC_SAN_XUAT: 'Nước sản xuất',
  SO_HUU: 'Sở hữu',
  NGAY_LAP_DAT: 'Ngày lắp đặt',
  NAM_SAN_XUAT: 'Năm sản xuất',
  NGAY_SUA_DOI: 'Ngày sửa đổi',
  GHI_CHU: 'Ghi chú',
  DON_VI: 'Đơn vị',


  MA_HIEU_COT: 'Mã hiệu cột',
  CONG_DUNG_COT: 'Công dụng cột',
  CHIEU_CAO: 'Chiều cao',
  TRONG_LUONG: 'Trọng lượng',
  SO_MACH_DAY_DAN: 'Số mạch dây dẫn',
  SO_MACH_DAY_CHONG_SET: 'Số mạch DCS',
  DO_RONG_CHAN_COT: 'Độ rộng chân cột',
  MONG_COT: 'Móng cột',
  LOAI_BULONG_NEO_MONG: 'Loại bulông neo móng',
  KE_MONG: 'Kè móng',
  LOAI_MONG: 'Loại móng',
  TUONG_MONG: 'Tường chắn',
  MUONG_THOAT_NUOC: 'Mương thoát nước',
  DEN_CANH_BAO_BAO_HANG_KHONG: 'Đèn cảnh báo hàng không',
};


export async function importThietBi(req, res) {
  function getSheetByName(sheetData, name) {
    return sheetData.find((sheet) => sheet.name.toLowerCase().includes(name.toLowerCase()));
  }

  try {
    let filePath = req.files.file.path;
    const sheetData = await exelUtils.transformFile(filePath);
    const result = {};
    const allDonVi = await DonViService.getAll({ is_deleted: false });
    const mapDonVi = {};
    allDonVi.forEach(donVi => {
      mapDonVi[donVi.ten_don_vi] = donVi._id;
    });

    const allCongTrinhCha = await CongTrinhService.getAll({ is_deleted: false });
    const mapCongTrinhCha = {};
    allCongTrinhCha.forEach(congTrinh => {
      mapCongTrinhCha[congTrinh.ten_cong_trinh] = congTrinh._id;
    });

    result.cong_trinh = await CongTrinhService.importData(getSheetByName(sheetData, 'Công trình'), mapDonVi, mapCongTrinhCha);

    const allCongTrinh = await CongTrinhService.getAll({ is_deleted: false });
    const mapCongTrinh = {};
    allCongTrinh.forEach(congTrinh => {
      mapCongTrinh[congTrinh.ma_cong_trinh] = congTrinh._id;
    });

    result.vi_tri = await ViTriService.importData(getSheetByName(sheetData, 'Vị trí'), mapDonVi, mapCongTrinh);
    const congTrinh = await CONG_TRINH.findOne({ ma_cong_trinh: result.cong_trinh.ma_cong_trinh });
    const mapVitri = await ViTriService.getAllIdMap({ cong_trinh_id: congTrinh._id, is_deleted: false });

    result.cot_dien = await CotDienService.importData(getSheetByName(sheetData, 'Cột điện'), mapDonVi, mapVitri);
    result.tiep_dat = await TiepDatService.importData(getSheetByName(sheetData, 'Tiếp đất'), mapDonVi, mapVitri);
    result.giao_cheo = await GiaoCheoService.importData(getSheetByName(sheetData, 'Giao chéo'), mapDonVi, mapVitri);
    result.day_cap_quang = await DayCapQuangService.importData(getSheetByName(sheetData, 'Cáp quang'), mapDonVi, mapVitri);
    result.day_chong_set = await DayChongSetService.importData(getSheetByName(sheetData, 'Dây chống sét'), mapDonVi, mapVitri);
    const allDuongDay = await DuongDayService.getAll({ is_deleted: false });
    const mapDuongDay = {};
    allDuongDay.forEach(duongDay => {
      mapDuongDay[duongDay.ten_duong_day] = duongDay._id;
    });
    result.van_hanh = await VanHanhService.importData(getSheetByName(sheetData, 'Vận hành'), mapDonVi, mapVitri, mapDuongDay);
    const allVanHanh = await VanHanhService.getAll({ is_deleted: false }).populate('duong_day_id');
    // await ViTriService.updateDuongDay(allVanHanh);
    const mapVanHanh = {};
    allVanHanh.forEach(vanHanh => {
      mapVanHanh[vanHanh.ma_van_hanh] = vanHanh._id;
    });

    result.day_dan = await DayDanService.importData(getSheetByName(sheetData, 'Dây dẫn'), mapDonVi, mapVanHanh);
    result.cach_dien = await CachDienService.importData(getSheetByName(sheetData, 'Cách điện'), mapDonVi, mapVanHanh);
    responseAction.success(res, result);
  } catch (e) {
    console.log(e);
    responseAction.error(res, e);
  }
}

async function importData(ma_cong_trinh, name, sheetData) {
  sheetData.forEach(sheet => {
    trimData(sheet);
  });

  function getSheetByName(sheetData, sheetName) {
    return sheetData.find((sheet) => sheet.name.toLowerCase().includes(sheetName.toLowerCase()));
  }

  const result = {};
  const allDonVi = await DonViService.getAll({ is_deleted: false });
  const mapDonVi = {};
  allDonVi.forEach(donVi => {
    mapDonVi[donVi.ten_don_vi] = donVi._id;
  });

  const allCongTrinhCha = await CongTrinhService.getAll({ is_deleted: false });
  const mapCongTrinhCha = {};
  allCongTrinhCha.forEach(congTrinh => {
    mapCongTrinhCha[congTrinh.ma_cong_trinh] = congTrinh._id;
  });
  if (getSheetByName(sheetData, SHEET_NAMES.CONG_TRINH)) {
    result[name] = await CongTrinhService.importData(getSheetByName(sheetData, SHEET_NAMES.CONG_TRINH), mapDonVi, mapCongTrinhCha);
  }

  const allCongTrinh = await CongTrinhService.getAll({ is_deleted: false });
  const mapCongTrinh = {};
  allCongTrinh.forEach(congTrinh => {
    mapCongTrinh[congTrinh.ma_cong_trinh] = congTrinh._id;
  });

  if (getSheetByName(sheetData, SHEET_NAMES.VI_TRI)) {
    result[name] = await ViTriService.importData(getSheetByName(sheetData, SHEET_NAMES.VI_TRI), mapDonVi, mapCongTrinh);
  }
  const congTrinh = await CONG_TRINH.findOne({ ma_cong_trinh });
  const mapVitri = await ViTriService.getAllIdMap({ cong_trinh_id: congTrinh._id, is_deleted: false });
  if (getSheetByName(sheetData, SHEET_NAMES.COT_DIEN)) {
    result[name] = await CotDienService.importData(getSheetByName(sheetData, SHEET_NAMES.COT_DIEN), mapDonVi, mapVitri);
  }
  if (getSheetByName(sheetData, SHEET_NAMES.TIEP_DAT)) {
    result[name] = await TiepDatService.importData(getSheetByName(sheetData, SHEET_NAMES.TIEP_DAT), mapDonVi, mapVitri);
  }
  if (getSheetByName(sheetData, SHEET_NAMES.GIAO_CHEO)) {
    result[name] = await GiaoCheoService.importData(getSheetByName(sheetData, SHEET_NAMES.GIAO_CHEO), mapDonVi, mapVitri);
  }
  if (getSheetByName(sheetData, SHEET_NAMES.CAP_QUANG)) {
    result[name] = await DayCapQuangService.importData(getSheetByName(sheetData, SHEET_NAMES.CAP_QUANG), mapDonVi, mapVitri);
  }
  if (getSheetByName(sheetData, SHEET_NAMES.DAY_CHONG_SET)) {
    result[name] = await DayChongSetService.importData(getSheetByName(sheetData, SHEET_NAMES.DAY_CHONG_SET), mapDonVi, mapVitri);
  }
  const allDuongDay = await DuongDayService.getAll({ is_deleted: false });
  const mapDuongDay = {};
  allDuongDay.forEach(duongDay => {
    mapDuongDay[duongDay.ten_duong_day] = duongDay._id;
  });
  if (getSheetByName(sheetData, SHEET_NAMES.VAN_HANH)) {
    result[name] = await VanHanhService.importData(getSheetByName(sheetData, SHEET_NAMES.VAN_HANH), mapDonVi, mapVitri, mapDuongDay);
  }
  const allVanHanh = await VanHanhService.getAll({ is_deleted: false }).populate('duong_day_id');
  // await ViTriService.updateDuongDay(allVanHanh);
  const mapVanHanh = {};
  allVanHanh.forEach(vanHanh => {
    mapVanHanh[vanHanh.ma_van_hanh] = vanHanh._id;
  });
  if (getSheetByName(sheetData, SHEET_NAMES.DAY_DAN)) {
    result[name] = await DayDanService.importData(getSheetByName(sheetData, SHEET_NAMES.DAY_DAN), mapDonVi, mapVanHanh);
  }
  if (getSheetByName(sheetData, SHEET_NAMES.CACH_DIEN)) {
    result[name] = await CachDienService.importData(getSheetByName(sheetData, SHEET_NAMES.CACH_DIEN), mapDonVi, mapVanHanh);
  }
  return result;
}

export async function importOne(req, res) {

  try {
    const sheetData = [trimData(req.body)];
    const result = await importData(req.body.ma_cong_trinh, req.body.name, sheetData);
    responseAction.success(res, result);
  } catch (e) {
    console.log(e);
    responseAction.error(res, e);
  }
}

export async function importMany(req, res) {
  try {
    let result = {};
    for (let sheet of req.body) {
      result = { ...result, ...await importData(sheet.ma_cong_trinh, sheet.name, [sheet]) };
    }
    responseAction.success(res, result);
  } catch (e) {
    console.log(e);
    responseAction.error(res, e);
  }
}

async function checkImportByData(t, sheetData) {

  function getSheetByName(sheetData, name) {
    return sheetData.find((sheet) => sheet.name.toLowerCase().includes(name.toLowerCase()));
  }

  let resultArray = [];
  const allDonVi = await DonViService.getAll({ is_deleted: false });
  const mapDonVi = {};
  allDonVi.forEach(donVi => {
    mapDonVi[donVi.ten_don_vi] = donVi._id;
  });
  const congtrinhSheetChecked = await CongTrinhService.checkImport(t, getSheetByName(sheetData, SHEET_NAMES.CONG_TRINH), mapDonVi);
  resultArray = [...resultArray, congtrinhSheetChecked];
  const viTriSheetChecked = await ViTriService.checkImport(t, getSheetByName(sheetData, SHEET_NAMES.VI_TRI), mapDonVi, congtrinhSheetChecked);
  resultArray = [...resultArray, viTriSheetChecked];
  const maViTriList = viTriSheetChecked?.rows.map(row => row[CommonHeaders.MA_THIET_BI]);
  const mapVitri = await ViTriService.getAllIdMap({ is_deleted: false, ma_vi_tri: { $in: maViTriList } });

  resultArray = [...resultArray, CotDienService.checkImport(t, getSheetByName(sheetData, SHEET_NAMES.COT_DIEN), mapDonVi, mapVitri)];
  resultArray = [...resultArray, TiepDatService.checkImport(t, getSheetByName(sheetData, SHEET_NAMES.TIEP_DAT), mapDonVi, mapVitri)];
  resultArray = [...resultArray, GiaoCheoService.checkImport(t, getSheetByName(sheetData, SHEET_NAMES.GIAO_CHEO), mapDonVi, mapVitri)];
  // resultArray = [...resultArray, getSheetByName(sheetData, SHEET_NAMES.CAP_QUANG)];
  resultArray = [...resultArray, DayChongSetService.checkImport(t, getSheetByName(sheetData, SHEET_NAMES.DAY_CHONG_SET), mapDonVi, mapVitri)];
  const allDuongDay = await DuongDayService.getAll({ is_deleted: false });
  const mapDuongDay = {};
  allDuongDay.forEach(duongDay => {
    mapDuongDay[duongDay.ten_duong_day] = duongDay._id;
  });
  const vanHanhListChecked = await VanHanhService.checkImport(t, getSheetByName(sheetData, SHEET_NAMES.VAN_HANH), mapDonVi, mapVitri, mapDuongDay);
  resultArray = [...resultArray, vanHanhListChecked];
  const maVanHanhChecked = vanHanhListChecked?.rows.map(row => row[CommonHeaders.MA_THIET_BI]);
  const allVanHanh = await VanHanhService.getAll({
    is_deleted: false,
    ma_van_hanh: { $in: maVanHanhChecked },
  }).populate('duong_day_id');
  const mapVanHanh = {};
  allVanHanh.forEach(vanHanh => {
    mapVanHanh[vanHanh.ma_van_hanh] = vanHanh._id;
  });
  resultArray = [...resultArray, DayDanService.checkImport(t, getSheetByName(sheetData, SHEET_NAMES.DAY_DAN), mapDonVi, mapVanHanh)];
  resultArray = [...resultArray, CachDienService.checkImport(t, getSheetByName(sheetData, SHEET_NAMES.CACH_DIEN), mapDonVi, mapVanHanh)];
  return resultArray;
}

export async function checkImport(req, res) {

  try {
    const { t } = req;
    let filePath = req.files.file.path;
    const sheetData = await exelUtils.transformFile(filePath);
    const resultArray = await checkImportByData(t, sheetData);
    responseAction.success(res, resultArray);
  } catch (e) {
    console.log(e);
    responseAction.error(res, e);
  }
}

export async function checkImportData(req, res) {

  try {
    const { t } = req;
    const sheetData = req.body;
    const resultArray = await checkImportByData(t, sheetData);
    responseAction.success(res, resultArray);
  } catch (e) {
    console.log(e);
    responseAction.error(res, e);
  }
}

function trimData(dataInput) {
  if (!Array.isArray(dataInput?.rows)) return dataInput;
  dataInput.rows.forEach(row => {
    Object.entries(row).forEach(([key, value]) => {
      row[key] = (!!value && typeof value === 'string') ? value.trim() : value;
    });
  });
  return dataInput;
}

export async function deleteByDonVi(req, res) {
  try {
    const { donViId } = req.query;
    if (!donViId) {
      return responseAction.error(res, CommonError.NOT_FOUND);
    }
    const donViQuery = { don_vi_id: await DonViService.getDonViQuery(req, donViId) };
    const viTriToDeleted = await ViTriService.getAll(donViQuery);
    const viTriIdsToDeleted = viTriToDeleted.map(viTri => viTri._id);
    const vanHanhToDeleted = await VanHanhService.getAll({ vi_tri_id: { $in: viTriIdsToDeleted } });
    const vanHanhIdsToDeleted = vanHanhToDeleted.map(vanHanh => vanHanh._id);

    let removeResult = {};
    removeResult['CONG_TRINH'] = await CONG_TRINH.remove(donViQuery);
    removeResult['DUONG_DAY'] = await DUONG_DAY.remove(donViQuery);
    removeResult['VI_TRI'] = await VI_TRI.remove(donViQuery);
    removeResult['CACH_DIEN'] = await CACH_DIEN.remove(donViQuery);
    removeResult['CACH_DIEN2'] = await CACH_DIEN.remove({ van_hanh_id: { $in: vanHanhIdsToDeleted } });
    removeResult['DAY_DAN'] = await DAY_DAN.remove(donViQuery);
    removeResult['DAY_DAN2'] = await DAY_DAN.remove({ van_hanh_id: { $in: vanHanhIdsToDeleted } });
    removeResult['COT_DIEN'] = await COT_DIEN.remove(donViQuery);
    removeResult['COT_DIEN2'] = await COT_DIEN.remove({ vi_tri_id: { $in: viTriIdsToDeleted } });
    removeResult['DAY_CAP_QUANG'] = await DAY_CAP_QUANG.remove(donViQuery);
    removeResult['DAY_CAP_QUANG2'] = await DAY_CAP_QUANG.remove({ vi_tri_id: { $in: viTriIdsToDeleted } });
    removeResult['DAY_CHONG_SET'] = await DAY_CHONG_SET.remove(donViQuery);
    removeResult['DAY_CHONG_SET2'] = await DAY_CHONG_SET.remove({ vi_tri_id: { $in: viTriIdsToDeleted } });
    removeResult['GIAO_CHEO'] = await GIAO_CHEO.remove(donViQuery);
    removeResult['GIAO_CHEO2'] = await GIAO_CHEO.remove({ vi_tri_id: { $in: viTriIdsToDeleted } });
    removeResult['KHOANG_COT'] = await KHOANG_COT.remove({ vi_tri_ket_thuc_id: { $in: viTriIdsToDeleted } });
    removeResult['KHOANG_NEO'] = await KHOANG_NEO.remove({ vi_tri_ket_thuc_id: { $in: viTriIdsToDeleted } });
    removeResult['TIEP_DAT'] = await TIEP_DAT.remove(donViQuery);
    removeResult['TIEP_DAT2'] = await TIEP_DAT.remove({ vi_tri_id: { $in: viTriIdsToDeleted } });
    removeResult['VAN_HANH'] = await VAN_HANH.remove(donViQuery);
    removeResult['VAN_HANH2'] = await VAN_HANH.remove({ vi_tri_id: { $in: viTriIdsToDeleted } });
    responseAction.success(res, removeResult);
  } catch (e) {
    console.log(e);
    responseAction.error(res, e);
  }
}
