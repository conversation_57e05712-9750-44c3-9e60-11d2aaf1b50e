import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { DAY_CAP_QUANG, DON_VI, VI_TRI } from '../../../constant/dbCollections';
import { TINH_TRANG_VAN_HANH } from '../../DanhMuc/TinhTrangVanHanh';

const schema = new Schema({
  ma_day_cap_quang: { type: String, required: true, unique: true, validate: /\S+/ },
  ma_thiet_bi_cha: { type: String, validate: /\S+/ },
  ma_tim_kiem: { type: String, validate: /\S+/ },
  ma_tim_kiem_cha: { type: String, validate: /\S+/ },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  ten_day_cap_quang: { type: String },
  thu_tu: { type: Number },
  seri: { type: String },
  so_the_tscd: String,
  ngay_van_hanh: { type: Date },
  tinh_trang_van_hanh: {
    type: String,
    enum: Object.values(TINH_TRANG_VAN_HANH),
    default: TINH_TRANG_VAN_HANH.VAN_HANH,
  },
  hang_san_xuat: String,
  nha_cung_cap: String,
  nuoc_san_xuat: String,
  so_huu: String,
  ngay_lap_dat: { type: Date },
  nam_san_xuat: Date,
  ngay_sua_doi: Date,
  ghi_chu: String,
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  ma_hieu_day_cap_quang: String,
  tiet_dien: String,
  so_luong_soi_quang: String,
  luc_keo_dut: String,
  he_so_dan_dai: String,
  trong_luong_day: String,
  dien_tro_mot_chieu: String,
  vi_tri_dat: String,
  so_luong_moi_noi: String,
  ma_hieu_chong_rung: String,
  so_luong_chong_rung: String,
  ma_hieu_chuoi_cach_dien: String,
  so_luong_chuoi: String,
  so_luong_bat_tren_chuoi: String,
  ma_hieu_cach_dien: String,
  mo_phong_set: String,
  noi_dat: String,
  hop_noi_cap_quang: String,
  so_luong_canh_bao_hang_khong: String,
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(DAY_CAP_QUANG, schema, DAY_CAP_QUANG);
