import express from 'express';
import passport from 'passport';
import * as donviController from './dayCapQuang.controller';

export const dayCapQuangRouter = express.Router();
dayCapQuangRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), donviController.getAll)
  .post(passport.authenticate('jwt', { session: false }), donviController.create);

dayCapQuangRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), donviController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), donviController.remove)
  .put(passport.authenticate('jwt', { session: false }), donviController.update);
