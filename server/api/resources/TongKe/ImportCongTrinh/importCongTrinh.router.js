import express from 'express';
import passport from 'passport';
import * as congTrinhController from './importCongTrinh.controller';
import { checkTempFolder, multipartMiddleware } from '../../../utils/fileUtils';
import { loggerMiddleware } from '../../../logs/middleware';
import { authorizationMiddleware } from '../../RBAC/middleware';
import TongKePermission from '../../RBAC/permissions/TongKePermission';

export const importCongTrinhRouter = express.Router();
importCongTrinhRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
importCongTrinhRouter.post('*', authorizationMiddleware([TongKePermission.CREATE]));
importCongTrinhRouter.put('*', authorizationMiddleware([TongKePermission.UPDATE]));
importCongTrinhRouter.delete('*', authorizationMiddleware([TongKePermission.DELETE]));

importCongTrinhRouter
  .route('/import')
  .post(passport.authenticate('jwt', { session: false }),
    checkTempFolder, multipartMiddleware, congTrinhController.importThietBi);

importCongTrinhRouter
  .route('/importone')
  .post(passport.authenticate('jwt', { session: false }),
    congTrinhController.importOne);
importCongTrinhRouter
  .route('/importmany')
  .post(passport.authenticate('jwt', { session: false }), congTrinhController.importMany);

importCongTrinhRouter
  .route('/checkimport')
  .post(passport.authenticate('jwt', { session: false }),
    checkTempFolder, multipartMiddleware, congTrinhController.checkImport);

importCongTrinhRouter
  .route('/checkimportbydata')
  .post(passport.authenticate('jwt', { session: false }), congTrinhController.checkImportData);
