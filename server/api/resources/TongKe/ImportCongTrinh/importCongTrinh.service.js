import * as ValidatorHelper from '../../../helpers/validatorHelper';
import { checkCommonInfo } from '../TongKe.service';

const Joi = require('joi');

const objSchema = Joi.object({
  ma_vi_tri: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã vị trí')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

const Headers = {
  MA_THIET_BI: 'Mã thiết bị',
  DUONG_DAY: 'Đường dây/TBA',
  THIET_BI_CHA: 'Thiết bị/Công trình cha',
  TEN_THIET_BI: 'Thiết bị/Công trình',
  STT: 'STT',
  SERIAL: 'Số chế tạo/Serial',
  NGAY_VAN_HANH: 'Ngày vận hành',
  SO_TSCD: 'Số TSCĐ',
  TINH_TRANG_VAN_HANH: 'Tình trạng vận hành',
  MA_LIEN_KET_KHAC: 'Mã liên kết khác',
  HANG_SAN_XUAN: 'Hãng sản xuất',
  NHA_CUNG_CAP: 'Nhà cung cấp',
  NUOC_SAN_XUAT: 'Nước sản xuất',
  SO_HUU: 'Sở hữu',
  NGAY_LAP_DAT: 'Ngày lắp đặt',
  NAM_SAN_XUAT: 'Năm sản xuất',
  NGAY_SUA_DOI: 'Ngày sửa đổi',
  GHI_CHU: 'Ghi chú',
  DON_VI: 'Đơn vị',
  CONG_TRINH_CHINH: 'Công trình chính',
  NAM_XAY_DUNG: 'Năm xây dựng',
};

export function checkImport(t, sheetData, mapDonVi, mapCongTrinhCha) {
  if (!sheetData) return null;

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(t, row) {
    let errors = [];
    errors = [...errors, ...checkCommonInfo(t, row)];
    if (!row[Headers.DON_VI]?.trim()) {
      errors = [...errors, createError(Headers.DON_VI, t('missing_unit_construction'))];
    } else {
      if (!mapDonVi[row[Headers.DON_VI]?.trim()]) {
        errors = [...errors, createError(Headers.DON_VI, t('management_unit_incorrect_or_dont_create'))];
      }
    }
    if (!!row[Headers.CONG_TRINH_CHINH]?.trim() && !mapCongTrinhCha[row[Headers.CONG_TRINH_CHINH]?.trim()]) {
      errors = [...errors, createError(Headers.CONG_TRINH_CHINH, t('parent_construction_incorrect_or_dont_create'))];
    }
    if (errors.length) {
      row['Lỗi'] = errors;
    } else {
      row['Lỗi'] = null;
      row['Lỗi'] = null;
    }
    return row;
  }

  return sheetData?.map(e => {
    e.rows = e.rows.map(row => validateRow(t, row));
    return e;
  });
}

