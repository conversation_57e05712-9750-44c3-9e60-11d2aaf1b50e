import * as responseAction from '../../../helpers/responseHelper';
import * as CongTrinhService from '../CongTrinh/congTrinh.service';
import * as DonViService from '../../DonVi/donVi.service';
import * as Services from './importCongTrinh.service';
import exelUtils from '../../../utils/exelUtils';

const SHEET_NAMES = {
  CONG_TRINH: 'Công trình',
  DUONG_DAY: 'Đường dây',
  VI_TRI: 'Vị trí',
  COT_DIEN: 'Cột điện',
  CAP_QUANG: '<PERSON><PERSON><PERSON> quang',
  GIAO_CHEO: 'Giao chéo',
  DAY_CHONG_SET: 'Dây chống sét',
  VAN_HANH: 'Vận hành',
  DAY_DAN: 'Dây dẫn',
  CACH_DIEN: '<PERSON><PERSON><PERSON> điện',
  TIEP_DAT: 'Tiế<PERSON> đất',
};

export async function importThietBi(req, res) {
  function getSheetByName(sheetData, name) {
    return sheetData.find((sheet) => sheet.name.toLowerCase().includes(name.toLowerCase()));
  }

  try {
    let filePath = req.files.file.path;
    const sheetData = await exelUtils.transformFile(filePath);
    const result = {};
    const allDonVi = await DonViService.getAll({ is_deleted: false });
    const mapDonVi = {};
    allDonVi.forEach(donVi => {
      mapDonVi[donVi.ten_don_vi] = donVi._id;
    });
    const allCongTrinhCha = await CongTrinhService.getAll({ is_deleted: false });
    const mapCongTrinhCha = {};
    allCongTrinhCha.forEach(congTrinh => {
      mapCongTrinhCha[congTrinh.ten_cong_trinh] = congTrinh._id;
    });

    result.cong_trinh = await CongTrinhService.importData(getSheetByName(sheetData, 'Công trình'), mapDonVi, mapCongTrinhCha);

    const allCongTrinh = await CongTrinhService.getAll({ is_deleted: false });
    const mapCongTrinh = {};
    allCongTrinh.forEach(congTrinh => {
      mapCongTrinh[congTrinh.ten_cong_trinh] = congTrinh._id;
    });

    responseAction.success(res, result);
  } catch (e) {
    responseAction.error(res, e);
  }
}

async function importData(ma_cong_trinh, name, sheetData) {
  sheetData.forEach(sheet => {
    trimData(sheet);
  });

  function getSheetByName(sheetData, sheetName) {
    return sheetData.find((sheet) => sheet.name.toLowerCase().includes(sheetName.toLowerCase()));
  }

  const result = {};
  const allDonVi = await DonViService.getAll({ is_deleted: false });
  const mapDonVi = {};
  allDonVi.forEach(donVi => {
    mapDonVi[donVi.ten_don_vi] = donVi._id;
  });

  const allCongTrinhCha = await CongTrinhService.getAll({ is_deleted: false });
  const mapCongTrinhCha = {};
  allCongTrinhCha.forEach(congTrinh => {
    mapCongTrinhCha[congTrinh.ten_cong_trinh] = congTrinh._id;
  });
  if (getSheetByName(sheetData, SHEET_NAMES.CONG_TRINH)) {
    result[name] = await CongTrinhService.importData(getSheetByName(sheetData, SHEET_NAMES.CONG_TRINH), mapDonVi, mapCongTrinhCha);
  }

  const allCongTrinh = await CongTrinhService.getAll({ is_deleted: false });
  const mapCongTrinh = {};
  allCongTrinh.forEach(congTrinh => {
    mapCongTrinh[congTrinh.ten_cong_trinh] = congTrinh._id;
  });

  return result;
}

export async function importOne(req, res) {

  try {
    const sheetData = [trimData(req.body)];
    const result = await importData(req.body.ma_cong_trinh, req.body.name, sheetData);
    responseAction.success(res, result);
  } catch (e) {
    responseAction.error(res, e);
  }
}

export async function importMany(req, res) {
  try {
    let result = {};
    for (let sheet of req.body) {
      result = { ...result, ...await importData(sheet.ma_cong_trinh, sheet.name, [sheet]) };
    }
    responseAction.success(res, result);
  } catch (e) {
    responseAction.error(res, e);
  }
}

async function checkImportByData(t, sheetData) {
  function getSheetByName(sheetData, name) {
    return sheetData.filter((sheet) => sheet.name.toLowerCase().includes(name.toLowerCase()));
  }

  let resultArray = [];
  const allDonVi = await DonViService.getAll({ is_deleted: false });
  const mapDonVi = {};
  allDonVi.forEach(donVi => {
    mapDonVi[donVi.ten_don_vi] = donVi._id;
  });

  const allCongTrinhCha = await CongTrinhService.getAll({ is_deleted: false });
  const mapCongTrinhCha = {};
  allCongTrinhCha.forEach(congTrinh => {
    mapCongTrinhCha[congTrinh.ten_cong_trinh] = congTrinh._id;
  });

  resultArray = [...resultArray, Services.checkImport(t, getSheetByName(sheetData, SHEET_NAMES.CONG_TRINH), mapDonVi, mapCongTrinhCha)];
  return resultArray;
}

export async function checkImport(req, res) {

  try {
    const { t } = req;
    let filePath = req.files.file.path;
    const sheetData = await exelUtils.transformFile(filePath);
    const resultArray = await checkImportByData(t, sheetData);
    responseAction.success(res, resultArray);
  } catch (e) {
    responseAction.error(res, e);
  }
}

export async function checkImportData(req, res) {

  try {
    const sheetData = req.body;
    const { t } = req;
    const resultArray = await checkImportByData(t, sheetData);
    responseAction.success(res, resultArray);
  } catch (e) {
    responseAction.error(res, e);
  }
}

function trimData(dataInput) {
  if (!Array.isArray(dataInput?.rows)) return dataInput;
  dataInput.rows.forEach(row => {
    Object.entries(row).forEach(([key, value]) => {
      row[key] = (!!value && typeof value === 'string') ? value.trim() : value;
    });
  });
  return dataInput;
}
