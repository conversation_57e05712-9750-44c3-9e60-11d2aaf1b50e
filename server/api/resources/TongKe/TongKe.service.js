import { checkDate, checkNumber, checkYear } from '../../helpers/checkDataHelper';
import { cutTail, trim } from '../../common/functionCommons';
import { LOAI_DAY } from '../../constant/constant';
import * as CongTrinhService from './CongTrinh/congTrinh.service';
import * as ViTriService from './ViTri/viTri.service';
import * as CotDienService from './CotDien/cotDien.service';
import * as CachDienService from './CachDien/cachDien.service';
import * as DayDanService from './DayDan/dayDan.service';
import * as DayCapQuangService from './DayCapQuang/dayCapQuang.service';
import * as DayChongSetService from './DayChongSet/dayChongSet.service';
import * as VanHanhService from './VanHanh/vanHanh.service';
import * as TiepDatService from './TiepDat/tiepDat.service';
import * as GiaoCheoService from './GiaoCheo/giaoCheo.service';
import { extractIds } from '../../utils/dataconverter';
import { formatDate, formatToDateDetail } from '../../common/formatUTCDateToLocalDate';
import { CommonHeaders } from './ThietBi/thietBi.controller';


const Headers = {
  MA_THIET_BI: 'Mã thiết bị',
  DUONG_DAY: 'Đường dây/TBA',
  THIET_BI_CHA: 'Thiết bị/Công trình cha',
  TEN_THIET_BI: 'Thiết bị/Công trình',
  STT: 'STT',
  SERIAL: 'Số chế tạo/Serial',
  NGAY_VAN_HANH: 'Ngày vận hành',
  SO_TSCD: 'Số TSCĐ',
  TINH_TRANG_VAN_HANH: 'Tình trạng vận hành',
  MA_LIEN_KET_KHAC: 'Mã liên kết khác',
  HANG_SAN_XUAN: 'Hãng sản xuất',
  NHA_CUNG_CAP: 'Nhà cung cấp',
  NUOC_SAN_XUAT: 'Nước sản xuất',
  SO_HUU: 'Sở hữu',
  NGAY_LAP_DAT: 'Ngày lắp đặt',
  NAM_SAN_XUAT: 'Năm sản xuất',
  NGAY_SUA_DOI: 'Ngày sửa đổi',
  GHI_CHU: 'Ghi chú',
  DON_VI: 'Đơn vị',

  KY_HIEU_DUONG_DAY: 'Đường dây',
  PHA: 'Pha',
  CHUC_NANG: 'Chức năng',
  LOAI_CACH_DIEN: 'Loại cách điện',
  MA_HIEU_CACH_DIEN: 'Mã hiệu cách điện',
  HUONG_LAP_DAT: 'Hướng lắp đặt',
  SO_LUONG_CHUOI: 'Số lượng chuỗi',
  SO_LUONG_BAT_TREN_CHUOI: 'Số lượng bát\\chuỗi',
  TAI_TRONG_PHA_HUY: 'Tải trọng phá huỷ',
  DUONG_KINH_TY_SU: 'Đường kính ty sứ',
  CHIEU_CAO_BAT_SU: 'Chiều cao bát sứ',
  CHIEU_RONG_TAN_SU: 'Chiều rộng tán sứ',
  CHIEU_DAI_DUONG_RO: 'Chiều dài đường rò',
  TRONG_LUONG_BAT_SU: 'Trọng lượng bát sứ',
  VONG_VANG_QUANG: 'Vòng vầng quang',
  MO_PHONG: 'Mỏ phóng',
};

function createError(col, error) {
  return { col, error };
}

function checkExistValue(value) {
  return !!value?.toString().trim();
}

export function checkCommonInfo(t, row) {
  let errors = [];
  if (!row[Headers.TEN_THIET_BI]) {
    errors = [...errors, createError(Headers.TEN_THIET_BI, t('nothing'))];
  }
  if (!row[Headers.MA_THIET_BI]) {
    errors = [...errors, createError(Headers.MA_THIET_BI, t('nothing'))];
  }

  if (checkExistValue(row[Headers.NGAY_VAN_HANH]) && !checkDate(row[Headers.NGAY_VAN_HANH])) {
    errors = [...errors, createError(Headers.NGAY_VAN_HANH, t('format_wrong'))];
  }

  if (checkExistValue(row[Headers.NGAY_LAP_DAT]) && !checkDate(row[Headers.NGAY_LAP_DAT])) {
    errors = [...errors, createError(Headers.NGAY_LAP_DAT, t('format_wrong'))];
  }
  if (checkExistValue(row[Headers.NAM_SAN_XUAT]) && !checkYear(row[Headers.NAM_SAN_XUAT])) {
    errors = [...errors, createError(Headers.NAM_SAN_XUAT, t('format_incorrect'))];
  }

  if (checkExistValue(row[Headers.NAM_XAY_DUNG]) && !checkYear(row[Headers.NAM_XAY_DUNG])) {
    errors = [...errors, createError(Headers.NAM_XAY_DUNG, t('incorrect_or_not_available'))];
  }

  if (!checkNumber(row[Headers.STT])) {
    errors = [...errors, createError(Headers.STT, t('incorrect_or_not_available'))];
  }
  return errors;
}

export function checkThietBiCommonInfo(t, row, mapVitri) {
  let errors = [];
  if (!row[CommonHeaders.MA_THIET_BI_CHA] || !mapVitri[trim(row[CommonHeaders.MA_THIET_BI_CHA])]) {
    errors = [...errors, createError(CommonHeaders.MA_THIET_BI_CHA, t('incorrect_or_dont_create'))];
  }
  return errors;
}

export function thietBiCommonInfo(row, mapDonVi, mapVitri) {
  return {
    ma_thiet_bi_cha: trim(row[CommonHeaders.MA_THIET_BI_CHA]),
    ma_tim_kiem: trim(row[CommonHeaders.MA_TIM_KIEM_THIET_BI]),
    ma_tim_kiem_cha: trim(row[CommonHeaders.MA_TIM_KIEM_CHA]),
    vi_tri_id: mapVitri[trim(row[CommonHeaders.MA_THIET_BI_CHA])],
    don_vi_id: mapDonVi[trim(row[Headers.DON_VI])],
    is_deleted: false,
  };
}

export function thietBiVanHanhCommonInfo(row, mapDonVi, mapVanHanh) {
  return {
    ma_thiet_bi_cha: trim(row[CommonHeaders.MA_THIET_BI_CHA]),
    ma_tim_kiem: trim(row[CommonHeaders.MA_TIM_KIEM_THIET_BI]),
    ma_tim_kiem_cha: trim(row[CommonHeaders.MA_TIM_KIEM_CHA]),
    van_hanh_id: mapVanHanh[trim(row[CommonHeaders.MA_THIET_BI_CHA])],
    don_vi_id: mapDonVi[trim(row[Headers.DON_VI])],
    is_deleted: false,
  };
}

export async function summaryDataTongKe(criteria) {

  const dataTongKe = {};
  const congTrinhData = await CongTrinhService.getOne({ _id: criteria.cong_trinh_id, is_deleted: false })
    .populate([
      { path: 'cong_trinh_chinh_id', select: 'ten_cong_trinh ma_cong_trinh' },
      { path: 'don_vi_id', select: 'ten_don_vi' },
    ]);
  dataTongKe.cong_trinh = [congTrinhData];

  const viTriData = await ViTriService.getAll(criteria)
    .populate([
      { path: 'cong_trinh_id', select: 'ten_cong_trinh ma_cong_trinh' },
      { path: 'don_vi_id', select: 'ten_don_vi' },
    ]);
  dataTongKe.vi_tri = viTriData;
  const viTriIds = extractIds(viTriData);

  async function getDataByViTriIds(DataService, listViTriID = [], populateOpts = []) {
    return (await DataService.getAll({ vi_tri_id: listViTriID, is_deleted: false })
      .populate([
        { path: 'vi_tri_id', select: 'ten_vi_tri ma_vi_tri' },
        { path: 'don_vi_id', select: 'ten_don_vi' },
        ...populateOpts,
      ]));
  }

  async function getGiaoCheo() {
    dataTongKe.giao_cheo = await getDataByViTriIds(GiaoCheoService, viTriIds);
  }

  async function getVanHanh() {
    const populateOpts = [
      { path: 'duong_day_id', select: 'ten_duong_day' },
      { path: 'duong_day_chinh', select: 'ten_duong_day' },
    ];
    dataTongKe.van_hanh = await getDataByViTriIds(VanHanhService, viTriIds, populateOpts);
  }

  async function getDayCapQuangChongSet() {
    const dayChongSetData = await getDataByViTriIds(DayChongSetService, viTriIds);
    const dayCapQuangData = await getDataByViTriIds(DayCapQuangService, viTriIds);
    dataTongKe.day_cap_quang = [...dayCapQuangData, ...dayChongSetData].sort((a, b) => a?.thu_tu - b?.thu_tu);
  }

  async function getTiepDat() {
    dataTongKe.tiep_dat = await getDataByViTriIds(TiepDatService, viTriIds);
  }

  async function getCotDien() {
    dataTongKe.cot_dien = await getDataByViTriIds(CotDienService, viTriIds);
  }

  const allPromise = [
    getGiaoCheo(),
    getVanHanh(),
    getDayCapQuangChongSet(),
    getTiepDat(),
    getCotDien(),
  ];
  await Promise.all(allPromise);

  const vanHanhIds = extractIds(dataTongKe.van_hanh);
  dataTongKe.cach_dien = await CachDienService.getAll({ van_hanh_id: vanHanhIds, is_deleted: false })
    .populate([
      { path: 'van_hanh_id', select: 'ten_van_hanh ma_van_hanh' },
      { path: 'don_vi_id', select: 'ten_don_vi' },
    ]);

  dataTongKe.day_dan = await DayDanService.getAll({ van_hanh_id: vanHanhIds, is_deleted: false })
    .populate([
      { path: 'van_hanh_id', select: 'ten_van_hanh ma_van_hanh' },
      { path: 'don_vi_id', select: 'ten_don_vi' },
    ]);

  return dataTongKe;
}

export function convertDataTongKe(dataTongKe = {}) {
  function convertDayCapQuang(row) {
    trim(row.don_vi_id?.ten_don_vi);
    row.ma_thiet_bi = row.ma_day_cap_quang || row.ma_day_chong_set;
    row.ten_thiet_bi = row.ten_day_cap_quang || row.ten_day_chong_set;
    row.ma_hieu_day = row.ma_hieu_day_cap_quang || row.ma_hieu_day_chong_set;
    row.loai_day = row.ma_day_cap_quang ? LOAI_DAY.DAY_CAP_QUANG.label : LOAI_DAY.DAY_CHONG_SET.label;
    row.ngay_van_hanh = formatDate(row.ngay_van_hanh);
    row.ngay_lap_dat = formatDate(row.ngay_lap_dat);
    row.nam_san_xuat = formatToDateDetail(row.nam_san_xuat).nam;
    return row;
  }

  function convertDateData(row) {
    trim(row.don_vi_id?.ten_don_vi);
    row.ngay_van_hanh = formatDate(row?.ngay_van_hanh);
    row.ngay_lap_dat = formatDate(row?.ngay_lap_dat);
    row.nam_san_xuat = formatToDateDetail(row?.nam_san_xuat)?.nam;
    row.nam_xay_dung = formatToDateDetail(row?.nam_xay_dung)?.nam;
    return row;
  }

  for (const [key, value] of Object.entries(dataTongKe)) {
    key === LOAI_DAY.DAY_CAP_QUANG.code.toLowerCase() ? value.map(convertDayCapQuang) : value.map(convertDateData);
  }

  return dataTongKe;
}
