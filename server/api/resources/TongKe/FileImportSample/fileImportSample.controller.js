import * as responseAction from '../../../helpers/responseHelper';
import queryHelper from '../../../helpers/queryHelper';
import { getDirPath, getFilePath } from '../../../utils/fileUtils';
import { LOAI_CONG_VIEC } from '../../DanhMuc/LoaiCongViec/index';

export async function getFileTemples(req, res) {
  const { t } = req;
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    let templateFileName, outputFileName;
    switch (criteria.type.toUpperCase()) {
      case '110KV':
        templateFileName = 'cong_trinh_110kV.xlsx';
        outputFileName = 'Mẫu file công trình 110kV.xlsx';
        break;
      case '220KV':
        templateFileName = 'cong_trinh_220kV.xlsx';
        outputFileName = 'Mẫu file công trình 220kV.xlsx';
        break;
      case '500KV':
        templateFileName = 'cong_trinh_500kV.xlsx';
        outputFileName = 'Mẫu file công trình 500kV.xlsx';
        break;
      case LOAI_CONG_VIEC.DO_KHOANG_CACH_PHA_DAT.code:
        templateFileName = 'do_khoang_cach_pha_dat.xlsx';
        outputFileName = `${LOAI_CONG_VIEC.DO_KHOANG_CACH_PHA_DAT.name}.xlsx`;
        break;
      case LOAI_CONG_VIEC.DO_DIEN_TRO_TIEP_DIA.code:
        templateFileName = 'do_dien_tro_tiep_dia.xlsx';
        outputFileName = `${LOAI_CONG_VIEC.DO_DIEN_TRO_TIEP_DIA.name}.xlsx`;
        break;
      case LOAI_CONG_VIEC.DO_NHIET_DO_TIEP_XUC.code:
        templateFileName = 'do_nhiet_do_tiep_xuc.xlsx';
        outputFileName = `${LOAI_CONG_VIEC.DO_NHIET_DO_TIEP_XUC.name}.xlsx`;
        break;
      default:
        templateFileName = 'cong_trinh_500kV.xlsx';
        outputFileName = 'Mẫu file công trình 500kV.xlsx';
        break;
    }
    const templateFilePath = getFilePath(templateFileName, getDirPath('fileImportSample', './server/templates'));
    res.download(templateFilePath, outputFileName);
  } catch (err) {
    console.log(err);
    responseAction.error(res, { message: t('file_cant_download') }, 400);
  }
}

export async function getFileSampleCongTrinh(req, res) {
  const { t } = req;
  try {
    const  templateFileName = 'mau_file_import_cong_trinh.xlsx';
    const  outputFileName = 'Mẫu tập tin nhập công trình.xlsx';
    const templateFilePath = getFilePath(templateFileName, getDirPath('fileImportSample', './server/templates'));
    res.download(templateFilePath, outputFileName);
  } catch (err) {
    console.log(err);
    responseAction.error(res, { message: t('file_cant_download') }, 400);
  }
}

export async function getFileSampleDuongDay(req, res) {
  const { t } = req;
  try {
    const  templateFileName = 'mau_file_import_duong_day.xlsx';
    const  outputFileName = 'Mẫu tập tin nhập đường dây.xlsx';
    const templateFilePath = getFilePath(templateFileName, getDirPath('fileImportSample', './server/templates'));
    res.download(templateFilePath, outputFileName);
  } catch (err) {
    console.log(err);
    responseAction.error(res, { message: t('file_cant_download') }, 400);
  }
}

export async function getFileSamplePhieuGiaoViec(req, res) {
  const { t } = req;
  try {
    const templateFileName = 'mau_file_import_phieu_giao_viec.xlsx';
    const outputFileName = 'Mẫu tập tin nhập phiếu giao việc.xlsx';
    const templateFilePath = getFilePath(templateFileName, getDirPath('fileImportSample', './server/templates'));
    res.download(templateFilePath, outputFileName);
  } catch (err) {
    console.log(err);
    responseAction.error(res, { message: t('file_cant_download') }, 400);
  }
}

export async function getFileSampleNghiemThuHanhLangTuyen(req, res) {
  const { t } = req;
  try {
    const templateFileName = 'mau_file_import_nghiem_thu_hanh_lang_tuyen.xlsx';
    const outputFileName = 'Mẫu tập tin nhập nghiệm thu hành lang tuyến.xlsx';
    const templateFilePath = getFilePath(templateFileName, getDirPath('fileImportSample', './server/templates'));
    res.download(templateFilePath, outputFileName);
  } catch (err) {
    console.log(err);
    responseAction.error(res, { message: t('file_cant_download') }, 400);
  }
}
