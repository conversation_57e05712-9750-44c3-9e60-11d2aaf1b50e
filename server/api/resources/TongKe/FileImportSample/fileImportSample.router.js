import express from 'express';
import * as Controller from './fileImportSample.controller';

export const fileImportSampleRouter = express.Router();

fileImportSampleRouter
  .route('/vitri')
  .get(Controller.getFileTemples);

fileImportSampleRouter
  .route('/congtrinh')
  .get(Controller.getFileSampleCongTrinh);

fileImportSampleRouter
  .route('/duongday')
  .get(Controller.getFileSampleDuongDay);

  fileImportSampleRouter
  .route('/phieugiaoviec')
  .get(Controller.getFileSamplePhieuGiaoViec);
  
fileImportSampleRouter
  .route('/nghiemthuhanhlangtuyen')
  .get(Controller.getFileSampleNghiemThuHanhLangTuyen);

