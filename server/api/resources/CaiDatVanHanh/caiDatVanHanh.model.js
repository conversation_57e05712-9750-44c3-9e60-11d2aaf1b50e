import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CAI_DAT_VAN_HANH, ROLE, TIEU_CHI } from '../../constant/dbCollections';


const schema = new Schema({
  ngay_bat_dau: { type: Number },
  ngay_ket_thuc: { type: Number },
  gio_bat_dau: { type: Number },
  gio_ket_thuc: { type: Number },
  ngay_thong_bao_ton_tai: { type: Number },
  vai_tro_nhan_thong_bao_ton_tai_id: [{ type: Schema.Types.ObjectId, ref: ROLE }],
  vai_tro_nhan_thong_bao_su_co_id: [{ type: Schema.Types.ObjectId, ref: ROLE }],
  tieu_chi_do_tiep_dia: { type: Schema.Types.ObjectId, ref: TIEU_CHI },
  tieu_chi_do_khoang_cach_pha: { type: Schema.Types.ObjectId, ref: TIEU_CHI },
  tieu_chi_do_nhiet_do: { type: Schema.Types.ObjectId, ref: TIEU_CHI },
  don_vi_tao_phieu: { type: Array, default: ['DOI_TRUYEN_TAI_DIEN'] },
  kich_hoat_auto_huy_phieu: { type: Boolean, default: false },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(CAI_DAT_VAN_HANH, schema, CAI_DAT_VAN_HANH);
