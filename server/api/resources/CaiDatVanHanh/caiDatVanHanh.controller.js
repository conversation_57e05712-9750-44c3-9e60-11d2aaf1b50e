import * as Service from './caiDatVanHanh.service';
import Model from './caiDatVanHanh.model';
import * as controllerHelper from '../../helpers/controllerHelper';
import * as responseHelper from '../../helpers/responseHelper';

const populateOpts = [
  {
    path: 'tieu_chi_do_tiep_dia',
    populate: { path: 'tieu_chi_cha_id' },
  },
  {
    path: 'tieu_chi_do_khoang_cach_pha',
    populate: { path: 'tieu_chi_cha_id' },
  },
  {
    path: 'tieu_chi_do_nhiet_do',
    populate: { path: 'tieu_chi_cha_id' },
  },
];

export async function findOne(req, res) {
  try {
    const data = await Model.findOne({}, { _id: 0, created_at: 0, updated_at: 0 })
      .populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, 404, '');
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    const setting = await Model.findOne().lean();
    const data = await Model.findOneAndUpdate({ _id: setting._id }, value, { new: true })
      .populate(populateOpts);
    if (!data) {
      return responseHelper.error(res, null, 404);
    }
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export const getAll = controllerHelper.createGetAllFunction(Model);
