import * as ValidatorHelper from '../../helpers/validatorHelper';
import CaiDatVanHanhModel from './caiDatVanHanh.model';

export function getAll(query) {
  return CaiDatVanHanhModel.find(query).lean();
}

export function getOne(query, projection = {}) {
  return CaiDatVanHanhModel.findOne(query, projection).lean();
}

export function getConfig() {
  return CaiDatVanHanhModel.findOne({ is_deleted: false }).lean();
}

const Joi = require('joi');

const objSchema = Joi.object({
  tu_ngay: Joi.number(),
  den_ngay: Joi.number(),
  tu_gio: Joi.date(),
  den_gio: Joi.date(),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
