import express from 'express';
import passport from 'passport';
import * as caiDatVanHanhController from './caiDatVanHanh.controller';
import { authorizationMiddleware } from '../RBAC/middleware';
import SettingPermission from '../RBAC/permissions/SettingPermission';
import { loggerMiddleware } from '../../logs/middleware';

export const caiDatVanHanhRouter = express.Router();
caiDatVanHanhRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
caiDatVanHanhRouter.post('*', authorizationMiddleware([SettingPermission.CREATE]));
caiDatVanHanhRouter.put('*', authorizationMiddleware([SettingPermission.UPDATE]));
caiDatVanHanhRouter.delete('*', authorizationMiddleware([SettingPermission.DELETE]));
caiDatVanHanhRouter
  .route('/')
  .get(caiDatVanHanhController.findOne)
  .put(caiDatVanHanhController.update);

caiDatVanHanhRouter
  .route('/:id')
  .get(caiDatVanHanhController.findOne);
