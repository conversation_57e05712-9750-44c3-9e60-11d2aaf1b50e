function createResources(code, description) {
  return {
    code: code,
    description: description,
  };
}

export default {
  ALL: createResources('ALL', 'Tất cả'),
  USER: createResources('USER', 'Người dùng'),
  VAI_TRO: createResources('VAI_TRO', 'Vai trò'),
  ORG_UNIT: createResources('ORG_UNIT', 'Đơn vị'),
  REPORT: createResources('REPORT', 'Báo cáo, dashboard'),
  GIAO_VIEC: createResources('GIAO_VIEC', 'Giao việc'),
  HO_SO: createResources('HO_SO', '<PERSON><PERSON> sơ'),
  SETTING: createResources('SETTING', 'Cài đặt'),
  TONG_KE: createResources('TONG_KE', 'Tổng kê'),
  KHOANG_COT: createResources('KHOANG_COT', 'Khoảng cột'),
  DANH_MUC: createResources('DANH_MUC', '<PERSON>h mục'),
  WORK_RESULT: createResources('WORK_RESULT', '<PERSON><PERSON><PERSON> quả công việc'),
  KE_HOACH_XLTT: createResources('KE_HOACH_XLTT', 'Lập kế hoạch XLTT'),
  EXTRA_DATA: createResources('EXTRA_DATA', 'Dữ liệu bổ sung'),
  SU_CO_DUONG_DAY: createResources('SU_CO_DUONG_DAY', 'Sự cố dường dây'),
  CONSTRUCTION: createResources('CONSTRUCTION', 'Công trình xây dựng'),
  TON_TAI_THIET_BI: createResources('TON_TAI_THIET_BI', 'Tồn tại thiết bị'),
  XAC_NHAN_LAM_QUANG: createResources('XAC_NHAN_LAM_QUANG', 'Xác nhận biên bản Lâm Quảng (Đơn vị QLVH)'),
  BAN_QUAN_LY_DU_AN: createResources('BAN_QUAN_LY_DU_AN', 'Xác nhận biên bản Lâm Quảng (Ban QLDA)'),
  QUAN_LY_THIET_BI: createResources('QUAN_LY_THIET_BI', 'Quản lý thiết bị'),
  TON_TAI_CAP_TREN: createResources('TON_TAI_CAP_TREN', 'Tồn tại cấp trên'),
  IMPORT_DO_THONG_SO: createResources('IMPORT_DO_THONG_SO', 'Nhập kết quả đo thông số'),
  NHAC_NHO_DO_THONG_SO: createResources('NHAC_NHO_DO_THONG_SO', 'Nhắc nhở đo thông số định kỳ'),
  NHAC_NHO_TON_TAI_TRONG_XLVH: createResources('NHAC_NHO_TON_TAI_TRONG_XLVH', 'Nhắc nhở tồn tại trong xử lý vận hành'),
  NHAC_NHO_TON_TAI_DA_BAO_CAO_TTD: createResources('NHAC_NHO_TON_TAI_DA_BAO_CAO_TTD', 'Nhắc nhở tồn tại đã báo cáo TTĐ'),
};
