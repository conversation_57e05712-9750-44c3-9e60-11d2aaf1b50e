import express from 'express';
import passport from 'passport';
import * as mayDoController from './phanBoMayDo.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import Permission from '../../RBAC/permissions/QuanLyThietBiPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const phanBoMayDoRouter = express.Router();
phanBoMayDoRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
phanBoMayDoRouter.post('*', authorizationMiddleware([Permission.CREATE]));
phanBoMayDoRouter.put('*', authorizationMiddleware([Permission.UPDATE]));
phanBoMayDoRouter.delete('*', authorizationMiddleware([Permission.DELETE]));
phanBoMayDoRouter.route('/')
  .get(mayDoController.getAll)
  .post(mayDoController.create);

phanBoMayDoRouter
  .route('/:id')
  .get(mayDoController.findOne)
  .delete(mayDoController.remove)
  .put(mayDoController.update);
