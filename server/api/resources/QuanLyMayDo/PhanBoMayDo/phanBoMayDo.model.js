import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { MAY_DO, DON_VI, PHAN_BO_MAY_DO } from '../../../constant/dbCollections';

const schema = new Schema({
  may_do_id: { type: Schema.Types.ObjectId, ref: MAY_DO, required: true },
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI, required: true },
  serial: { type: String },
  ghi_chu: { type: String },
  ngay_nhap: { type: Date },
  ngay_phan_bo: { type: Date },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized indexes for PHAN_BO_MAY_DO queries
// Using background: true for better performance during index creation

// Index for may_do_id queries with soft delete
schema.index({ 
  may_do_id: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_may_do_deleted'
});

// Index for don_vi_id queries with soft delete
schema.index({ 
  don_vi_id: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_don_vi_deleted'
});

// Compound index for may_do and don_vi filtering
schema.index({ 
  may_do_id: 1, 
  don_vi_id: 1,
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_may_do_don_vi_deleted'
});

// Index for serial queries
schema.index({ 
  serial: 1, 
  is_deleted: 1
}, { 
  background: true,
  sparse: true,
  name: 'idx_serial_deleted'
});

// Index for time-based queries
schema.index({ 
  created_at: -1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_created_deleted'
});

schema.plugin(mongoosePaginate);

export default mongoose.model(PHAN_BO_MAY_DO, schema, PHAN_BO_MAY_DO);
