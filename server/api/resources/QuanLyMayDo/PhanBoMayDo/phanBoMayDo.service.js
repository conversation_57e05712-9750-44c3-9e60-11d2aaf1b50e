import * as ValidatorHelper from '../../../helpers/validatorHelper';
import PHAN_BO_MAY_DO from './phanBoMayDo.model';

const Joi = require('joi');

const objSchema = Joi.object({
  may_do_id: Joi.string().required(),
  don_vi_id: Joi.string().required(),
  ghi_chu: Joi.string(),
  ngay_nhap: Joi.string(),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function getAll(query) {
  return PHAN_BO_MAY_DO.find(query)
    .lean();
}
