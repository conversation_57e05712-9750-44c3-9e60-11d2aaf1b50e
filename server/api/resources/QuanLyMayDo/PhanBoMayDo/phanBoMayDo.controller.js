import * as Service from './phanBoMayDo.service';
import Model from './phanBoMayDo.model';
import * as controllerHelper from '../../../helpers/controllerHelper';

const populateOpts = [
  { path: 'may_do_id', select: 'ten_may loai_may' },
  { path: 'don_vi_id', select: 'ten_don_vi' },
];

const sortOpts = { 'don_vi_id': 1, 'may_do_id': 1 };

export const remove = controllerHelper.createRemoveFunction(Model);
export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, [], populateOpts, sortOpts, 'don_vi_id');
