import express from 'express';
import passport from 'passport';
import * as dmMayDoController from './mayDo.controller';
import { authorizationMiddleware } from '../../RBAC/middleware';
import Permission from '../../RBAC/permissions/DanhMucPermission';
import { loggerMiddleware } from '../../../logs/middleware';

export const mayDoRouter = express.Router();
mayDoRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
mayDoRouter.post('*', authorizationMiddleware([Permission.CREATE]));
mayDoRouter.put('*', authorizationMiddleware([Permission.UPDATE]));
mayDoRouter.delete('*', authorizationMiddleware([Permission.DELETE]));
mayDoRouter.route('/')
  .get(dmMayDoController.getAll)
  .post(dmMayDoController.create);

mayDoRouter
  .route('/:id')
  .get(dmMayDoController.findOne)
  .delete(dmMayDoController.remove)
  .put(dmMayDoController.update);
