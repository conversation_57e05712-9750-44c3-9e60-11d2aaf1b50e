import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { MAY_DO } from '../../../constant/dbCollections';

const schema = new Schema({
  ten_may: { type: String, required: true, validate: /\S+/ },
  loai_may: { type: String },
  ma_may: { type: String },
  do_chinh_xac: { type: Number },
  thoi_gian_hoi_dap: { type: Number },
  ghi_chu: { type: String },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized indexes for MAY_DO queries
// Using background: true for better performance during index creation

// Index for ten_may queries with soft delete
schema.index({ 
  ten_may: 1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_ten_may_deleted'
});

// Index for ma_may queries with soft delete
schema.index({ 
  ma_may: 1, 
  is_deleted: 1
}, { 
  background: true,
  sparse: true,
  name: 'idx_ma_may_deleted'
});

// Index for loai_may filtering
schema.index({ 
  loai_may: 1, 
  is_deleted: 1
}, { 
  background: true,
  sparse: true,
  name: 'idx_loai_may_deleted'
});

// Index for time-based queries
schema.index({ 
  created_at: -1, 
  is_deleted: 1
}, { 
  background: true,
  name: 'idx_created_deleted'
});

schema.plugin(mongoosePaginate);

export default mongoose.model(MAY_DO, schema, MAY_DO);
