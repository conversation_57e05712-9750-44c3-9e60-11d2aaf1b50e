import * as ValidatorHelper from '../../helpers/validatorHelper';
import KHOANG_COT from '../TongKe/KhoangCot/khoangCot.model';
import { LOAI_VI_TRI } from '../TongKe/ViTri/viTri.model';

import * as LoaiDuongDayService from '../DanhMuc/LoaiDuongDay/loaiDuongDay.service';
import * as DonViService from '../DonVi/donVi.service';
import * as ViTriService from '../TongKe/ViTri/viTri.service';
import * as KhoangCotService from '../TongKe/KhoangCot/khoangCot.service';
import * as CotDienService from '../TongKe/CotDien/cotDien.service';

import { changeObjKey, extractIds, removeSpace } from '../../utils/dataconverter';
import { CAP_DON_VI } from '../../constant/constant';
import { TINH_TRANG_VAN_HANH } from '../DanhMuc/TinhTrangVanHanh';
import { cloneObj } from '../../common/functionCommons';
import { DUONG_DAY } from '../../constant/dbCollections';

const Joi = require('joi');

const objSchema = Joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function getKhoiLuongQuanLy(donviQuery) {
  const allLoaiDuongDay = await LoaiDuongDayService.getAll({ is_deleted: false });
  const mapThuTuCapDienAp = {};
  allLoaiDuongDay.forEach(element => mapThuTuCapDienAp[element._id] = element.thu_tu);

  function initDataDonVi(allDonVi = []) {
    let initLoaiDuongDay = {};
    allLoaiDuongDay.forEach(loai => {
      initLoaiDuongDay[loai.ten_loai] = {
        ten_duong_day: null,
        loai_duong_day_id: null,
        chieu_dai: 0,
        thu_tu: null,
        cot_neo: new Set(),
        cot_do: new Set(),
        vi_tri: new Set(),
      };
    });
    return allDonVi.map(donVi => ({ ...donVi, ...initLoaiDuongDay }));
  }

  let cotDiens, khoangCots, mapCotDien = {}, mapViTriByDuongDay = {};

  async function getCotDienData() {
    cotDiens = await CotDienService.getAll({
      vi_tri_id: extractIds(viTris), is_deleted: false, cong_dung_cot: { $exists: true },
    }).select('vi_tri_id cong_dung_cot');
    cotDiens.forEach(cot => {
      mapCotDien[cot.vi_tri_id] = cot.cong_dung_cot?.toUpperCase();
    });
  }

  async function getKhoangCotData() {
    khoangCots = await KhoangCotService.getAll({ vi_tri_id: extractIds(viTris), is_deleted: false })
      .populate({ path: 'vi_tri_id', select: 'don_vi_id' })
      .populate({ path: 'duong_day_id', select: 'loai_duong_day_id ten_duong_day tinh_trang_van_hanh is_deleted' })
      .select('duong_day_id vi_tri_bat_dau_id vi_tri_ket_thuc_id vi_tri_id chieu_dai');

    // filter duong day
    khoangCots.forEach(khoangCot => {
      khoangCot.duong_day_id = khoangCot.duong_day_id
        ?.filter(duongDay => duongDay.tinh_trang_van_hanh === TINH_TRANG_VAN_HANH.VAN_HANH && !duongDay.is_deleted);
    });
  }

  async function getVanHanhData() {
    mapViTriByDuongDay = await ViTriService.getHashMapViTriByDuongDay(extractIds(viTris));
  }

  function countLocationAndLineDistance(allKhoangCot = [], mapDonViId) {
    const mapKhoiLuongQuanLy = {};

    function checkAndAddViTriToSummary(loaiViTri, keyMap, donVi, duongDayID) {
      if (mapDonViId[loaiViTri]?.toString() === donVi && mapViTriByDuongDay[duongDayID]?.includes(loaiViTri)) {
        if (!mapKhoiLuongQuanLy[keyMap]?.vi_tri.has(loaiViTri)) {
          mapKhoiLuongQuanLy[keyMap]?.vi_tri.add(loaiViTri);
        }
        if (!mapKhoiLuongQuanLy[keyMap]?.cot_neo.has(loaiViTri) && mapCotDien[loaiViTri]?.includes('NÉO')) {
          mapKhoiLuongQuanLy[keyMap]?.cot_neo.add(loaiViTri);
        }
        if (!mapKhoiLuongQuanLy[keyMap]?.cot_do.has(loaiViTri) && mapCotDien[loaiViTri]?.includes('ĐỠ')) {
          mapKhoiLuongQuanLy[keyMap]?.cot_do.add(loaiViTri);
        }
      }
    }

    allKhoangCot.filter(x => (x.vi_tri_bat_dau_id && x.vi_tri_ket_thuc_id))
      .forEach(khoangCot => {
        khoangCot.duong_day_id?.forEach(loai => {
          const keyMap = khoangCot.vi_tri_id?.don_vi_id + loai._id;
          const vtBatDau = khoangCot.vi_tri_bat_dau_id?.toString();
          const vtKetThuc = khoangCot.vi_tri_ket_thuc_id?.toString();
          const donVi = khoangCot.vi_tri_id?.don_vi_id?.toString();

          mapKhoiLuongQuanLy[keyMap] ||= {
            ten_duong_day: null,
            loai_duong_day_id: null,
            chieu_dai: 0,
            thu_tu: null,
            cot_neo: new Set(),
            cot_do: new Set(),
            vi_tri: new Set(),
          };

          mapKhoiLuongQuanLy[keyMap].ten_duong_day = loai.ten_duong_day;
          mapKhoiLuongQuanLy[keyMap].loai_duong_day_id = loai.loai_duong_day_id;
          mapKhoiLuongQuanLy[keyMap].thu_tu = mapThuTuCapDienAp[loai.loai_duong_day_id];
          mapKhoiLuongQuanLy[keyMap].chieu_dai += khoangCot.chieu_dai / 1000; // m -> km
          checkAndAddViTriToSummary(vtBatDau, keyMap, donVi, loai._id);
          checkAndAddViTriToSummary(vtKetThuc, keyMap, donVi, loai._id);
        });
      });

    return { mapKhoiLuongQuanLy };
  }

  let allDonVi = await DonViService.getAll({ _id: donviQuery, is_deleted: false });
  allDonVi = initDataDonVi(allDonVi);

  const viTris = await ViTriService.getAll(
    { don_vi_id: donviQuery, is_deleted: false, loai_vi_tri: { $ne: LOAI_VI_TRI.XA_POOCTIC } },
    { don_vi_id: 1 });

  const mapDonViId = {};
  viTris.forEach(vt => mapDonViId[vt._id] = vt.don_vi_id);

  const allPromises = [
    getCotDienData(),
    getKhoangCotData(),
    getVanHanhData(),
  ];
  await Promise.all(allPromises);

  const { mapKhoiLuongQuanLy } = countLocationAndLineDistance(khoangCots, mapDonViId);

  return handleDataDonVi(allDonVi, 'duong_days', mapKhoiLuongQuanLy, allLoaiDuongDay);
}

export async function getKhoiLuongCongTrinh(donviQuery) {

  const allLoaiDuongDay = await LoaiDuongDayService.getAll({ is_deleted: false });
  const mapThuTuCapDienAp = {};
  allLoaiDuongDay.forEach(element => mapThuTuCapDienAp[element._id] = element.thu_tu);

  function initDataDonVi(allDonVi = []) {
    let initLoaiDuongDay = {};
    allLoaiDuongDay.forEach(loai => {
      initLoaiDuongDay[loai.ten_loai] = {
        ten_cong_trinh: null,
        loai_duong_day_id: null,
        chieu_dai: 0,
        thu_tu: null,
        cot_neo: new Set(),
        cot_do: new Set(),
        vi_tri: new Set(),
      };
    });
    return allDonVi.map(donVi => ({ ...donVi, ...initLoaiDuongDay }));
  }

  let cotDiens, khoangCots, mapCotDien = {};
  const mapDonViId = {}, mapChieuDaiQuanLy = {}, mapViTriQuanLy = {};

  async function getCotDienData() {
    cotDiens = await CotDienService.getAll({
      vi_tri_id: extractIds(viTris), is_deleted: false, cong_dung_cot: { $exists: true },
    }).select('vi_tri_id cong_dung_cot');
    cotDiens.forEach(cot => {
      mapCotDien[cot.vi_tri_id] = cot.cong_dung_cot?.toUpperCase();
    });
  }

  async function getKhoangCotData() {
    khoangCots = await KhoangCotService.getAll({ vi_tri_id: extractIds(viTris), is_deleted: false })
      .populate({
        path: 'vi_tri_id', select: 'don_vi_id cong_trinh_id',
        populate: {
          path: 'cong_trinh_id',
          select: 'ten_cong_trinh loai_duong_day_id so_mach tinh_trang_van_hanh is_deleted',
        },
      }).select('vi_tri_bat_dau_id  vi_tri_ket_thuc_id vi_tri_id chieu_dai');

    khoangCots.forEach(khoangCot => {
      khoangCot.duong_day_id = khoangCot.duong_day_id
        ?.filter(duongDay => duongDay.tinh_trang_van_hanh === TINH_TRANG_VAN_HANH.VAN_HANH && !duongDay.is_deleted);
    });
  }

  function calcLineDistance(allKhoangCot = []) {
    allKhoangCot.filter(x => (x.vi_tri_bat_dau_id && x.vi_tri_ket_thuc_id))
      .forEach(khoangCot => {
        if (!!khoangCot.vi_tri_id?.cong_trinh_id?._id) {
          const keyMap = khoangCot.vi_tri_id?.don_vi_id + khoangCot.vi_tri_id?.cong_trinh_id?._id;
          mapChieuDaiQuanLy[keyMap] ||= {
            ten_cong_trinh: null,
            loai_duong_day_id: null,
            chieu_dai: 0,
            thu_tu: null,
            so_mach: null,
          };

          mapChieuDaiQuanLy[keyMap].ten_cong_trinh = khoangCot.vi_tri_id?.cong_trinh_id?.ten_cong_trinh;
          mapChieuDaiQuanLy[keyMap].loai_duong_day_id = khoangCot.vi_tri_id?.cong_trinh_id?.loai_duong_day_id;
          mapChieuDaiQuanLy[keyMap].thu_tu = mapThuTuCapDienAp[khoangCot.vi_tri_id?.cong_trinh_id?.loai_duong_day_id];
          mapChieuDaiQuanLy[keyMap].chieu_dai += khoangCot.chieu_dai / 1000; // m -> km
          mapChieuDaiQuanLy[keyMap].so_mach = khoangCot.vi_tri_id?.cong_trinh_id?.so_mach;
        }
      });


    Object.values(mapChieuDaiQuanLy).forEach(item => {
      item.chieu_dai = item.chieu_dai.fixedFloat(2);
    });
  }

  function countLocation(allViTri = []) {
    allViTri.forEach(viTri => {
      const keyMap = viTri?.don_vi_id + viTri?.cong_trinh_id;
      const viTriID = viTri._id.toString();
      if (!!viTri?.cong_trinh_id) {
        !mapViTriQuanLy[keyMap] && (mapViTriQuanLy[keyMap] = {
          cot_neo: new Set(),
          cot_do: new Set(),
          vi_tri: new Set(),
        });
        if (!mapViTriQuanLy[keyMap]?.vi_tri.has(viTriID)) {
          mapViTriQuanLy[keyMap]?.vi_tri.add(viTriID);
        }
        if (mapCotDien[viTriID]?.includes('NÉO') && !mapViTriQuanLy[keyMap]?.cot_neo.has(viTriID)) {
          mapViTriQuanLy[keyMap]?.cot_neo.add(viTriID);
        }
        if (mapCotDien[viTriID]?.includes('ĐỠ') && !mapViTriQuanLy[keyMap]?.cot_do.has(viTriID)) {
          mapViTriQuanLy[keyMap]?.cot_do.add(viTriID);
        }
      }
    });
  }

  let allDonVi = await DonViService.getAll({ _id: donviQuery, is_deleted: false });
  allDonVi = initDataDonVi(allDonVi);

  let viTris = await ViTriService.getAll(
    { don_vi_id: donviQuery, is_deleted: false, loai_vi_tri: { $ne: LOAI_VI_TRI.XA_POOCTIC } },
    { don_vi_id: 1, cong_trinh_id: 1 })
    .populate({ path: 'cong_trinh_id', select: 'tinh_trang_van_hanh is_deleted' });

  viTris = viTris
    .filter(viTri => viTri.cong_trinh_id?.tinh_trang_van_hanh === TINH_TRANG_VAN_HANH.VAN_HANH && !viTri.cong_trinh_id?.is_deleted)
    .map(viTri => {
      viTri.cong_trinh_id = viTri.cong_trinh_id?._id;
      return viTri;
    });

  viTris.forEach(vt => {
    mapDonViId[vt._id] = vt.don_vi_id;
  });

  const allGetDataPromises = [
    getCotDienData(),
    getKhoangCotData(),
  ];
  await Promise.all(allGetDataPromises);

  const allCalcPromises = [
    calcLineDistance(khoangCots),
    countLocation(viTris),
  ];
  await Promise.all(allCalcPromises);

  for (const [key, value] of Object.entries(mapViTriQuanLy)) {
    if (!!mapChieuDaiQuanLy[key]) {
      mapChieuDaiQuanLy[key] = { ...mapChieuDaiQuanLy[key], ...value };
    }
  }
  return handleDataDonVi(allDonVi, 'cong_trinhs', mapChieuDaiQuanLy, allLoaiDuongDay);
}

function handleDataDonVi(allDonVi = [], childrenKey, mapKhoiLuongQuanLy = {}, allLoaiDuongDay) {

  function updateLoaiDuongDay(donviCha, donviCon) {
    allLoaiDuongDay.forEach(loai => {
      donviCha[loai.ten_loai].chieu_dai += donviCon[loai.ten_loai]?.chieu_dai;
      donviCha[loai.ten_loai].vi_tri += donviCon[loai.ten_loai].vi_tri;
      donviCha[loai.ten_loai].cot_do += donviCon[loai.ten_loai].cot_do;
      donviCha[loai.ten_loai].cot_neo += donviCon[loai.ten_loai].cot_neo;
    });
  }

  function updateDataForParent(donVi, allDonVi) {
    const allChild = allDonVi.filter(dv => dv.don_vi_cha_id?.toString() === donVi._id?.toString());
    allChild.forEach(child => updateLoaiDuongDay(donVi, child));
  }

  function updateDataForOrgUnit(capDonVi, allDonVi) {
    allDonVi.forEach(donVi => {
      donVi.cap_don_vi === capDonVi && updateDataForParent(donVi, allDonVi);
    });
  }

  allDonVi.forEach(donVi => {
    donVi[childrenKey] = [];
    for (const [key, value] of Object.entries(mapKhoiLuongQuanLy)) {
      value.so_vi_tri = value.vi_tri ? value.vi_tri.size : 0;
      value.so_cot_do = value.cot_do ? value.cot_do.size : 0;
      value.so_cot_neo = value.cot_neo ? value.cot_neo.size : 0;
      if (key.includes(donVi._id.toString())) {
        donVi[childrenKey].push(value);
      }
    }
    donVi[childrenKey].sort(function(a, b) {
      return a.thu_tu - b.thu_tu;
    });
  });

  allDonVi.forEach(donVi => {
    const loaiDuongDays = allLoaiDuongDay.map(loaiDuongDay => ({ ...loaiDuongDay }));
    loaiDuongDays.forEach(loai => {
      let chieuDai = 0, soViTri = 0, soCotDo = 0, soCotNeo = 0;
      !!donVi[childrenKey].length && donVi[childrenKey].forEach(congTrinh => {
        if (loai?._id?.toString() === congTrinh?.loai_duong_day_id?.toString()) {
          chieuDai += congTrinh.chieu_dai;
          soViTri += congTrinh.so_vi_tri;
          soCotDo += congTrinh.so_cot_do;
          soCotNeo += congTrinh.so_cot_neo;
        }
      });
      donVi[loai.ten_loai] = {
        chieu_dai: chieuDai,
        vi_tri: soViTri,
        cot_do: soCotDo,
        cot_neo: soCotNeo,
      };
    });
  });

  [CAP_DON_VI.TRUYEN_TAI_DIEN, CAP_DON_VI.CONG_TY, CAP_DON_VI.TONG_CONG_TY]
    .forEach(item => updateDataForOrgUnit(item, allDonVi));


  allDonVi.forEach(donVi => {
    allLoaiDuongDay.forEach(loai => {
      donVi[loai.ten_loai].chieu_dai = donVi[loai.ten_loai].chieu_dai.fixedFloat(2);
    });
  });

  return allDonVi;
}

export async function handeDataToReport(allDonVi = [], childrenKey) {
  const allLoaiDuongDay = await LoaiDuongDayService.getAll({ is_deleted: false });

  allDonVi.forEach(donVi => {
    donVi.chieu_dai = 0;

    allLoaiDuongDay.forEach(loai => {
      donVi.chieu_dai += donVi[loai.ten_loai]?.chieu_dai || 0;
      changeObjKey(donVi, loai.ten_loai, removeSpace(loai.ten_loai));
    });

    donVi.chieu_dai = donVi.chieu_dai.fixedFloat(2);
    donVi.duong_days?.forEach(duongDay => duongDay.chieu_dai = duongDay.chieu_dai.fixedFloat(2));

    donVi[childrenKey]?.forEach(element => {
      allLoaiDuongDay.forEach(loai => {
        if (element.loai_duong_day_id?.toString() === loai._id.toString()) {
          element[removeSpace(loai.ten_loai)] = cloneObj(element);
        } else {
          element[removeSpace(loai.ten_loai)] = {
            chieu_dai: 0, so_cot_do: 0, so_cot_neo: 0, so_vi_tri: 0,
          };
        }
      });
    });
  });
  return allDonVi;
}

export async function khoiLuongQuanLyByMotDonVi(queryAggregate) {
  return KHOANG_COT.aggregate([
    {
      $match: queryAggregate,
    },
    {
      $lookup: {
        from: DUONG_DAY,
        localField: 'duong_day_id',
        foreignField: '_id',
        as: 'duong_day_info',
      },
    },
    {
      $unwind: '$duong_day_info',
    },
    {
      $match: {
        'duong_day_info.tinh_trang_van_hanh': TINH_TRANG_VAN_HANH.VAN_HANH,
        'duong_day_info.is_deleted': false,
      },
    },
    {
      $group: {
        _id: null,
        khoi_luong_quan_ly: { $sum: '$chieu_dai' },
      },
    },
    {
      $project: {
        _id: 0,
        khoi_luong_quan_ly: 1,
      },
    },
  ]);

}
