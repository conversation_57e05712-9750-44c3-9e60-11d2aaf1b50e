import * as responseHelper from '../../helpers/responseHelper';

import * as DonViService from '../DonVi/donVi.service';
import * as Service from './thongTinQuanLy.service';

import queryHelper from '../../helpers/queryHelper';
import { TEMPLATES_DIRS } from '../../constant/constant';
import { buildTree } from '../DonVi/donVi.service';
import { getFilePath } from '../../utils/fileUtils';
import { generateDocument } from '../Report/GenerateFile/generate.controller';

export async function khoiLuongQuanLy(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const donviQuery = await DonViService.getDonViQuery(req, criteria.don_vi_id);
    const allDonVi = await Service.getKhoiLuongQuanLy(donviQuery);
    const congTyTreeForAdmin = buildTree(allDonVi);
    return responseHelper.success(res, congTyTreeForAdmin);

  } catch (e) {
    console.log(e);
    return responseHelper.error(res, e);
  }
}

export async function khoiLuongQuanLyTheoCongTrinh(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const donviQuery = await DonViService.getDonViQuery(req, criteria.don_vi_id);
    const allDonVi = await Service.getKhoiLuongCongTrinh(donviQuery);
    const congTyTreeForAdmin = buildTree(allDonVi);
    return responseHelper.success(res, congTyTreeForAdmin);

  } catch (e) {
    console.log(e);
    return responseHelper.error(res, e);
  }
}

export async function downloadKhoiLuongQuanLy(req, res) {
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  const donViQuery = await DonViService.getDonViQuery(req, criteria.don_vi_id);

  let allDonVi = await Service.getKhoiLuongQuanLy(donViQuery);
  allDonVi = await Service.handeDataToReport(allDonVi, 'duong_days');
  const dataKhoiLuongQuanLy = buildTree(allDonVi);
  const donViData = await DonViService.getById(req.query.don_vi_id || req.user.don_vi_id);
  const fileName = `khoi_luong_quan_ly_cap_${donViData.cap_don_vi.toLowerCase()}.xlsx`;
  const templateFilePath = getFilePath(fileName, TEMPLATES_DIRS.KHOILUONGQUANLY);

  await generateDocument(res, dataKhoiLuongQuanLy, templateFilePath);
}


export async function downloadKhoiLuongCongTrinh(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const donviQuery = await DonViService.getDonViQuery(req, criteria.don_vi_id);
    let allDonVi = await Service.getKhoiLuongCongTrinh(donviQuery);
    allDonVi = await Service.handeDataToReport(allDonVi, 'cong_trinhs');
    const congTyTreeForAdmin = buildTree(allDonVi);
    const donViQuery = await DonViService.getById(req.query.don_vi_id || req.user.don_vi_id);
    const fileName = `khoi_luong_quan_ly_cong_trinh_cap_${donViQuery.cap_don_vi.toLowerCase()}.xlsx`;
    const templateFilePath = getFilePath(fileName, TEMPLATES_DIRS.KHOILUONGQUANLY);

    await generateDocument(res, congTyTreeForAdmin, templateFilePath);

  } catch (e) {
    console.log(e);
    return responseHelper.error(res, e);
  }

}


