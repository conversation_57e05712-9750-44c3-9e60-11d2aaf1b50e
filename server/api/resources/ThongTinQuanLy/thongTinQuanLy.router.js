import express from 'express';
import passport from 'passport';
import * as thongTinQuanLyController from './thongTinQuanLy.controller';
import { loggerMiddleware } from '../../logs/middleware';

export const thongTinQuanLyRouter = express.Router();
thongTinQuanLyRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

thongTinQuanLyRouter.route('/')
  .get(thongTinQuanLyController.khoiLuongQuanLy);

thongTinQuanLyRouter.route('/download')
  .get(thongTinQuanLyController.downloadKhoiLuongQuanLy);

thongTinQuanLyRouter.route('/congtrinh')
  .get(thongTinQuanLyController.khoiLuongQuanLyTheoCongTrinh);

thongTinQuanLyRouter.route('/congtrinh/download')
  .get(thongTinQuanLyController.downloadKhoiLuongCongTrinh);
