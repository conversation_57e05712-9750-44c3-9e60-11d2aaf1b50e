import * as ValidatorHelper from '../../helpers/validatorHelper';
import GPS_USER from './gpsUser.model';

const Joi = require('joi');

const objSchema = Joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function getAll(query, projection = {}) {
  return GPS_USER.find(query, projection).lean();
}

export function getOne(query, projection) {
  return GPS_USER.findOne(query, projection).lean();
}
