import { Router } from 'express';
import * as Controller from './gpsUser.controller';
import passport from 'passport';

export const gpsUserRouter = Router();
gpsUserRouter.use(passport.authenticate('jwt', { session: false }));

gpsUserRouter.route('/')
  .post(Controller.create)
  .get(Controller.getAll);

gpsUserRouter.route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);

