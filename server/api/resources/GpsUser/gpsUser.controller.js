import * as controllerHelper from '../../helpers/controllerHelper';
import Model from './gpsUser.model';
import * as Service from './gpsUser.service';


const populateOpts = [
  { path: 'user_id', select: 'full_name' },
];
const uniqueOpts = [];
const sortOpts = { thoi_gian_check_in: 1 };

export const remove = controllerHelper.createRemoveFunction(Model);
export const findOne = controllerHelper.createFindOneFunction(Model);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts, uniqueOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts, uniqueOpts);
export const getAll = controllerHelper.createGetAllFunction(Model, null, populateOpts, sortOpts);


