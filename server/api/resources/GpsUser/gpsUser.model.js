import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import { GPS_USER, USER, PHIEU_GIAO_VIEC } from '../../constant/dbCollections';

const schema = new Schema({
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  user_id: { type: Schema.Types.ObjectId, ref: USER },
  thoi_gian_check_in: Date,
  vi_do: {
    type: Number,
    required: true,
  },
  kinh_do: {
    type: Number,
    required: true,
  },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  collation: { locale: 'vi' },
  versionKey: false,
});
schema.plugin(mongoosePaginate);

export default mongoose.model(GPS_USER, schema, GPS_USER);
