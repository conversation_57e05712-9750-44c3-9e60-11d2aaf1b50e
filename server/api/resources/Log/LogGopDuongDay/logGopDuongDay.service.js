import * as ValidatorHelper from '../../../helpers/validatorHelper';
import LOG_GOP_DUONG_DAY from './logGopDuongDay.model';

const Joi = require('joi');

const objSchema = Joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function getAll(query, projection = {}) {
  return LOG_GOP_DUONG_DAY.find(query, projection).lean();
}

export function create(doc = {}) {
  return LOG_GOP_DUONG_DAY.create(doc);
}


