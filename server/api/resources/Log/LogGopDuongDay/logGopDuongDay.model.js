import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { LOG_GOP_DUONG_DAY, USER, DUONG_DAY } from '../../../constant/dbCollections';

const schema = new Schema({
  time_log: { type: Date, default: Date.now },
  user_id: { type: Schema.Types.ObjectId, ref: USER },
  duong_day_can_gop_id: [{ type: Schema.Types.ObjectId, ref: DUONG_DAY }],
  duong_day_hop_nhat_id: { type: Schema.Types.ObjectId, ref: DUONG_DAY },
  status: {
    type: String,
    enum: ['SUCCESS', 'ERROR'],
  },
  result: Object,
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(LOG_GOP_DUONG_DAY, schema, LOG_GOP_DUONG_DAY);
