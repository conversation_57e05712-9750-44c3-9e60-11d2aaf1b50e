import moment from 'moment';

import Model from './logGopDuongDay.model';
import queryHelper from '../../../helpers/queryHelper';
import * as responseHelper from '../../../helpers/responseHelper';
import { getLogPath } from '../../../utils/fileUtils';
import * as controllerHelper from '../../../helpers/controllerHelper';

const populateOpts = [];
const sortOpts = { created_at: -1 };
const searchLike = [];

export const getAll = controllerHelper.createGetAllFunction(Model, searchLike, populateOpts, sortOpts);

export async function downloadFileLog(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria } = query;
    const fileName = `${moment(criteria.date_log).format('YYYY-MM-DD')}.txt`;
    const filePath = getLogPath(fileName);
    res.download(filePath, fileName);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}
