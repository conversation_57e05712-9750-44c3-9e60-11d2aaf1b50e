import { Router } from 'express';
import passport from 'passport';

import { sysadminAuthorizationMiddleware } from '../../RBAC/middleware';

import * as ServerLogsController from './logGopDuongDay.controller';

export const logGopDuongDayRouter = Router();
logGopDuongDayRouter.use(passport.authenticate('jwt', { session: false }));
logGopDuongDayRouter.get('*', sysadminAuthorizationMiddleware());

logGopDuongDayRouter.route('/')
  .get(ServerLogsController.getAll);

logGopDuongDayRouter.route('/downloadlog')
  .get(ServerLogsController.downloadFileLog);

