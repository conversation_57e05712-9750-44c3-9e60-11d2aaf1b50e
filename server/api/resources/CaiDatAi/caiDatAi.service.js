import * as ValidatorHelper from '../../helpers/validatorHelper';
import CAI_DAT_AI from './caiDatAi.model';

export function getAll(query) {
  return CAI_DAT_AI.find(query).lean();
}

export function getOne() {
  return CAI_DAT_AI.findOne().lean();
}

const Joi = require('joi');

const objSchema = Joi.object({
  api_phan_tich_hinh_anh: Joi.string(),
  api_dataset: Joi.string(),

});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
