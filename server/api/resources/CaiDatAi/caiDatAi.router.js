import express from 'express';
import passport from 'passport';
import * as caiDatAiController from './caiDatAi.controller';
import { authorizationMiddleware } from '../RBAC/middleware';
import SettingPermission from '../RBAC/permissions/SettingPermission';
import { loggerMiddleware } from '../../logs/middleware';

export const caiDatAiRouter = express.Router();
caiDatAiRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
caiDatAiRouter.post('*', authorizationMiddleware([SettingPermission.CREATE]));
caiDatAiRouter.put('*', authorizationMiddleware([SettingPermission.UPDATE]));
caiDatAiRouter.delete('*', authorizationMiddleware([SettingPermission.DELETE]));
caiDatAiRouter.route('/')
  .get(caiDatAiController.findOne)
  .put(caiDatAiController.update);

caiDatAiRouter
  .route('/:id')
  .get(caiDatAiController.findOne);

