import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CAI_DAT_AI } from '../../constant/dbCollections';

const schema = new Schema({
  api_phan_tich_hinh_anh: { type: String, required: true, validate: /\S+/ },
  api_dataset: String,

  automatic_sync: { type: Boolean, default: false },
  domain: { type: String },
  api_sync_image: { type: String },
  sync_dataset_id: { type: String },
  token: { type: String },

  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

export default mongoose.model(CAI_DAT_AI, schema, CAI_DAT_AI);
