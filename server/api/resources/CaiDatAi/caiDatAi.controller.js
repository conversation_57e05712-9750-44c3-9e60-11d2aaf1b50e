import Model from './caiDatAi.model';

import * as Service from './caiDatAi.service';

import * as controllerHelper from '../../helpers/controllerHelper';
import * as responseAction from '../../helpers/responseHelper';
import * as responseHelper from '../../helpers/responseHelper';

import { maskToken } from '../../common/functionCommons';
import CommonError from '../../error/CommonError';


export async function findOne(req, res) {
  try {
    const data = await Model.findOne({}, { _id: 0, password: 0 });
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    data.token = maskToken(data.token);

    return responseAction.success(res, data);
  } catch (err) {
    return responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseHelper.error(res, error, 400);
    const setting = await Model.findOne().lean();
    const data = await Model.findOneAndUpdate({ _id: setting._id }, value, { new: true }).lean();
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    data.token = maskToken(data.token);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export const getAll = controllerHelper.createGetAllFunction(Model);
