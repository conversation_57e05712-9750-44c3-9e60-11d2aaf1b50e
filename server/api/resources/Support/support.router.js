import express from 'express';
import passport from 'passport';

import * as Controller from './support.controller';
import { loggerMiddleware } from '../../logs/middleware';
import { getTonTaiSaiPhieu } from './support.controller';

export const supportRouter = express.Router();
supportRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

supportRouter.route('/clean-vitri')
  .get(Controller.cleanViTri);

supportRouter.route('/count-vitri-in-trash')
  .get(Controller.countViTriInTrash);

supportRouter.route('/clean-vanhanh')
  .get(Controller.cleanVanHanh);

supportRouter.route('/remove-by-ma-van-hanh')
  .get(Controller.removeByMaVanHanh);


supportRouter.route('/count-vanhanh-in-trash')
  .get(Controller.countVanHanhInTrash);

supportRouter.route('/clean-congtrinh')
  .get(Controller.cleanCongTrinh);

supportRouter.route('/count-congtrinh-in-trash')
  .get(Controller.countCongTrinhInTrash);

supportRouter.route('/clean-duongday')
  .get(Controller.cleanDuongDay);

supportRouter.route('/count-duongday-in-trash')
  .get(Controller.countDuongDayInTrash);

supportRouter.route('/clean-khoangcot')
  .get(Controller.cleanKhoangCot);

supportRouter.route('/count-khoangcot-in-trash')
  .get(Controller.countKhoangCotInTrash);

supportRouter.route('/clean-thietbi')
  .get(Controller.cleanThietBi);

supportRouter.route('/count-thietbi-in-trash')
  .get(Controller.countThietBiInTrash);

supportRouter.route('/update-ky-kiem-tra')
  .get(Controller.updateKyKiemTra);

supportRouter.route('/update-pham-vi-bay-da-thuc-hien')
  .put(Controller.updatePhamViBayDaThucHien);

supportRouter.route('/:id/remove-all-data-by-cong-trinh')
  .delete(Controller.removeAllDataByCongTrinh);

supportRouter.route('/:id/update-khoi-luong-cong-viec')
  .put(Controller.updateKhoiLuongCongViec);

supportRouter.route('/set-default-loai-vi-tri')
  .put(Controller.setDefaultLoaiViTri);

supportRouter.route('/duong-day/default-van-hanh')
  .put(Controller.setDefaultVanHanhDuongDay);

supportRouter.route('/khoang-cot/update-duplicate-duong-day')
  .put(Controller.updateKhoangCotDuplicateDuongDay);

supportRouter.route('/ton-tai-sai-phieu')
  .get(Controller.getTonTaiSaiPhieu);

supportRouter.route('/xoa-ton-tai-loi/:id')
  .put(Controller.xoaTonTaiLoi);
