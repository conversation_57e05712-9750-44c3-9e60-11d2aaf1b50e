import * as responseHelper from '../../helpers/responseHelper';
import ViTriModel, { LOAI_VI_TRI } from '../TongKe/ViTri/viTri.model';
import { extractIds, extractKeys } from '../../utils/dataconverter';
import VanHanhModel from '../TongKe/VanHanh/vanHanh.model';
import CongTrinhModel from '../TongKe/CongTrinh/congTrinh.model';
import KhoangCotModel from '../TongKe/KhoangCot/khoangCot.model';
import DuongDayModel from '../TongKe/DuongDay/duongDay.model';
import DayDanModel from '../TongKe/DayDan/dayDan.model';
import CachDienModel from '../TongKe/CachDien/cachDien.model';
import CotDienModel from '../TongKe/CotDien/cotDien.model';
import DayCapQuangModel from '../TongKe/DayCapQuang/dayCapQuang.model';
import DayChongSetModel from '../TongKe/DayChongSet/dayChongSet.model';
import GiaoCheoModel from '../TongKe/GiaoCheo/giaoCheo.model';
import PhieuGiaoViecModel from '../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.model';
import KetQuaKiemTraModel from '../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.model';

import { getQuarterYear } from '../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.controller';
import * as CongTrinhService from '../TongKe/CongTrinh/congTrinh.service';
import * as VanHanhService from '../TongKe/VanHanh/vanHanh.service';
import * as DayDanService from '../TongKe/DayDan/dayDan.service';
import * as CachDienService from '../TongKe/CachDien/cachDien.service';
import * as DuongDayService from '../TongKe/DuongDay/duongDay.service';
import * as KhoangCotService from '../TongKe/KhoangCot/khoangCot.service';
import * as ViTriService from '../TongKe/ViTri/viTri.service';
import * as KetQuaKiemTraService from '../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.service';

import { MODEL_REF_PHIEU_GIAO_VIEC, SERVICE_DELETE_BY_VI_TRI } from './support.constants';
import VI_TRI_CONG_VIEC from '../QuanLyVanHanh/ViTriCongViec/viTriCongViec.model';
import { TRANG_THAI_HOAN_THANH } from '../DanhMuc/TrangThaiHoanThanh';
import CommonError from '../../error/CommonError';
import { TINH_TRANG_VAN_HANH } from '../DanhMuc/TinhTrangVanHanh';
import { formatUnique } from '../../common/functionCommons';
import { KIEM_TRA, LOAI_CONG_VIEC } from '../DanhMuc/LoaiCongViec';
import { PHIEU_GIAO_VIEC } from '../../constant/dbCollections';

export async function countViTriInTrash(req, res) {
  try {
    const { don_vi_id } = req.query;
    const numberOfViTriInTrash = await ViTriModel.count({ don_vi_id: don_vi_id, is_deleted: true });
    return responseHelper.success(res, { so_vi_tri: numberOfViTriInTrash });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function cleanViTri(req, res) {
  try {
    const { don_vi_id } = req.query;
    const allViTriInTrash = await ViTriService.getAll({ don_vi_id: don_vi_id, is_deleted: true }, { ten_vi_tri: 1 });
    const data = await ViTriModel.deleteMany({ _id: { $in: extractIds(allViTriInTrash) } });
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function countVanHanhInTrash(req, res) {
  try {
    const numberInTrash = await VanHanhModel.count({ is_deleted: true });
    return responseHelper.success(res, { so_luong: numberInTrash });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function cleanVanHanh(req, res) {
  try {
    const data = await VanHanhModel.deleteMany({ is_deleted: true });
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function removeByMaVanHanh(req, res) {
  try {
    const { ma_van_hanh } = req.query;
    const data = await VanHanhModel.deleteMany({ ma_van_hanh });
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function countCongTrinhInTrash(req, res) {
  try {
    const numberInTrash = await CongTrinhModel.count({ is_deleted: true });
    return responseHelper.success(res, { so_luong: numberInTrash });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function cleanCongTrinh(req, res) {
  try {
    const data = await CongTrinhModel.deleteMany({ is_deleted: true });
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function countKhoangCotInTrash(req, res) {
  try {
    const numberInTrash = await KhoangCotModel.count({ is_deleted: true });
    return responseHelper.success(res, { so_luong: numberInTrash });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function cleanKhoangCot(req, res) {
  try {
    const data = await KhoangCotModel.deleteMany({ is_deleted: true });
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function countDuongDayInTrash(req, res) {
  try {
    const numberInTrash = await DuongDayModel.count({ is_deleted: true });
    return responseHelper.success(res, { so_luong: numberInTrash });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function cleanDuongDay(req, res) {
  try {
    const data = await DuongDayModel.deleteMany({ is_deleted: true });
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function countThietBiInTrash(req, res) {
  try {
    const dayDan = await DayDanModel.count({ is_deleted: true });
    const cachDien = await CachDienModel.count({ is_deleted: true });
    const cotDien = await CotDienModel.count({ is_deleted: true });
    const dayCapQuang = await DayCapQuangModel.count({ is_deleted: true });
    const dayChongSet = await DayChongSetModel.count({ is_deleted: true });
    const giaoCheo = await GiaoCheoModel.count({ is_deleted: true });

    return responseHelper.success(res, {
      day_dan: dayDan,
      cach_dien: cachDien,
      cot_dien: cotDien,
      day_cap_quang: dayCapQuang,
      day_chong_set: dayChongSet,
      giao_cheo: giaoCheo,
    });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function cleanThietBi(req, res) {
  try {
    const dayDan = await DayDanModel.deleteMany({ is_deleted: true });
    const cachDien = await CachDienModel.deleteMany({ is_deleted: true });
    const cotDien = await CotDienModel.deleteMany({ is_deleted: true });
    const dayCapQuang = await DayCapQuangModel.deleteMany({ is_deleted: true });
    const dayChongSet = await DayChongSetModel.deleteMany({ is_deleted: true });
    const giaoCheo = await GiaoCheoModel.deleteMany({ is_deleted: true });
    return responseHelper.success(res, {
      day_dan: dayDan,
      cach_dien: cachDien,
      cot_dien: cotDien,
      day_cap_quang: dayCapQuang,
      day_chong_set: dayChongSet,
      giao_cheo: giaoCheo,
    });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function updateKyKiemTra(req, res) {
  try {
    const allPhieuGiaoViec = await PhieuGiaoViecModel.find({
      thang_kiem_tra: { $exists: true },
      quy_kiem_tra: { $exists: false },
      nam_kiem_tra: { $exists: false },
    }).select('thang_kiem_tra thoi_gian_cong_tac_bat_dau')
      .lean();

    allPhieuGiaoViec.map(phieu => {
      phieu = getQuarterYear(phieu);
      return phieu;
    });

    await PhieuGiaoViecModel.bulkWrite(
      allPhieuGiaoViec.map((row) =>
        ({
          updateOne: {
            filter: { _id: row._id },
            update: { $set: { quy_kiem_tra: row.quy_kiem_tra, nam_kiem_tra: row.nam_kiem_tra } },
            upsert: true,
          },
        }),
      ),
    );
    const dataAfterUpdate = await PhieuGiaoViecModel.find({ thang_kiem_tra: { $exists: true } });

    return responseHelper.success(res, dataAfterUpdate);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function updatePhamViBayDaThucHien(req, res) {
  try {

    await PhieuGiaoViecModel.findByIdAndUpdate(req.body.phieu_giao_viec_id,
      { pham_vi_bay_da_thuc_hien: req.body.pham_vi_bay_da_thuc_hien });

    const data = await PhieuGiaoViecModel.findById(req.body.phieu_giao_viec_id);
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function removeAllDataByCongTrinh(req, res) {
  try {
    const { id } = req.params;
    // return responseHelper.success(res, id);
    // Delete công trình con
    await CongTrinhModel.remove({ cong_trinh_chinh_id: id });

    const congTrinh = await CongTrinhService.getOne({ _id: id });
    if (!congTrinh) return responseHelper.error(res, { message: 'Không tìm thấy công trình' }, 404);
    const viTris = await ViTriService.getAll({ cong_trinh_id: id });
    const viTrisId = extractIds(viTris);


    const vanHanhs = await VanHanhService.getAll({ vi_tri_id: viTrisId });
    const vanHanhIds = extractIds(vanHanhs);
    const duongDayIds = extractKeys(vanHanhs, 'duong_day_id');

    // Remove data liên quan đến vị trí
    const promisesViTri = SERVICE_DELETE_BY_VI_TRI.map(service => {
      return service.deleteAll({ vi_tri_id: viTrisId });
    });
    const dataVTri = await Promise.all(promisesViTri);

    // Remove data liên quan đến vận hành
    const dayDan = await DayDanService.deleteAll({ van_hanh_id: vanHanhIds });
    const cachDien = await CachDienService.deleteAll({ van_hanh_id: vanHanhIds });

    const viTri = await ViTriService.deleteAll({ cong_trinh_id: id });
    const duongDay = await DuongDayService.deleteAll({ _id: duongDayIds });
    const congTrinhDelete = await CongTrinhService.remove({ _id: id });

    const phieuGiaoViec = await PhieuGiaoViecModel.find({ duong_day_ids: { $in: duongDayIds } }).lean();

    // Remove liên quan đến phiếu giao việc
    const promisesPhieuGiaoViec = MODEL_REF_PHIEU_GIAO_VIEC.map(model => {
      return model.remove({ phieu_giao_viec_id: { $in: extractIds(phieuGiaoViec) } });
    });
    const dataPhieuGiaoViec = await Promise.all(promisesPhieuGiaoViec);
    const phieuGiaoViecRemove = await PhieuGiaoViecModel.remove({ duong_day_ids: { $in: duongDayIds } });
    return responseHelper.success(res, {
      congTrinhDelete, viTri, dataPhieuGiaoViec, dataVTri,
      cachDien, dayDan, duongDay, phieuGiaoViecRemove,
    });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function updateKhoiLuongCongViec(req, res) {
  try {
    const { id } = req.params;
    const data = await VI_TRI_CONG_VIEC.updateMany(
      { phieu_giao_viec_id: id, is_deleted: false },
      { trang_thai_hoan_thanh: TRANG_THAI_HOAN_THANH.DA_HOAN_THANH },
    );
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function setDefaultLoaiViTri(req, res) {
  try {
    if (!req.user?.is_system_admin) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    const data = await ViTriModel.updateMany(
      { loai_vi_tri: { $nin: Object.values(LOAI_VI_TRI) }, is_deleted: false },
      { loai_vi_tri: LOAI_VI_TRI.COT_DIEN },
    );
    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function setDefaultVanHanhDuongDay(req, res) {
  try {
    if (!req.user?.is_system_admin) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    const allDuongDay = await DuongDayService.getAll(
      {
        $and: [
          { tinh_trang_van_hanh: { $ne: TINH_TRANG_VAN_HANH.VAN_HANH } },
          { tinh_trang_van_hanh: { $ne: TINH_TRANG_VAN_HANH.KHONG_VAN_HANH } },
        ],
      },
      { _id: 1 });

    const dataUpdate = allDuongDay.map(duongDay => {
      duongDay.tinh_trang_van_hanh = TINH_TRANG_VAN_HANH.VAN_HANH;
      return duongDay;
    });

    const dataUpdated = await DuongDayService.updateAll(dataUpdate);

    return responseHelper.success(res, dataUpdated);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}


export async function updateKhoangCotDuplicateDuongDay(req, res) {
  try {
    const docs = await KhoangCotService.getAll({ is_deleted: false }, { duong_day_id: 1 });

    const dataUpdate = docs
      .filter(doc => doc.duong_day_id?.length)
      .filter(doc => {
        const originalIds = extractIds(doc.duong_day_id);
        const uniqueIds = formatUnique(originalIds);
        doc.duong_day_id = formatUnique(originalIds);
        return originalIds.length !== uniqueIds.length;
      });
    const dataUpdated = await KhoangCotService.updateAll(dataUpdate);
    return responseHelper.success(res, dataUpdated);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function getTonTaiSaiPhieu(req, res) {
  try {
    if (!req.user?.is_system_admin) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    const loaiCongViec = Object.values(LOAI_CONG_VIEC).filter(congViec => congViec.type === 'KIEM_TRA');
    const loaiCongViecCodes = extractKeys(loaiCongViec, 'code');

    const tonTai = await KetQuaKiemTraModel.aggregate([
      { $match: { is_deleted: false } },
      {
        $lookup: {
          from: PHIEU_GIAO_VIEC,
          localField: 'phieu_giao_viec_id',
          foreignField: '_id',
          as: 'phieu',
        },
      },
      { $unwind: '$phieu' },
      {
        $match: {
          'phieu.loai_cong_viec': { $nin: loaiCongViecCodes },
        },
      },
      {
        $project: {
          _id: 1,
          vi_tri_id: 1,
          khoang_cot_id: 1,
          duong_day_id: 1,
          phieu_giao_viec_id: 1,
        },
      },
    ]);


    return responseHelper.success(res, tonTai);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function xoaTonTaiLoi(req, res) {
  try {
    if (!req.user?.is_system_admin) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    const { id } = req.params;

    const loaiCv = Object.values(LOAI_CONG_VIEC).filter(congViec => congViec.type === KIEM_TRA);
    const cvKiemTraCode = extractKeys(loaiCv, 'code');

    const tonTaiCurrent = await KetQuaKiemTraService.getOne(
      { _id: id, is_deleted: false },
      { phieu_giao_viec_id: 1 },
    )
      .populate({ path: 'phieu_giao_viec_id', select: 'loai_cong_viec' });

    if (!tonTaiCurrent) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }


    if (cvKiemTraCode.includes(tonTaiCurrent?.phieu_giao_viec_id?.loai_cong_viec)) {
      return responseHelper.error(res, { message: 'Không lỗi' });
    }

    const tonTaiDeleted = await KetQuaKiemTraService.getByIdAndUpdate(id, { is_deleted: true }, { new: true });


    return responseHelper.success(res, { id, tonTaiCurrent, tonTaiDeleted });
  } catch (err) {
    return responseHelper.error(res, err);
  }
}
