import * as CotDienService from '../TongKe/CotDien/cotDien.service';
import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../TongKe/DayCapQuang/dayCapQuang.service';
import * as DayChongSet from '../TongKe/DayChongSet/dayChongSet.service';
import * as GiaoCheo from '../TongKe/GiaoCheo/giaoCheo.service';
import * as TiepDat from '../TongKe/TiepDat/tiepDat.service';
import * as VanHanhService from '../TongKe/VanHanh/vanHanh.service';
import AnhViTri from '../AnhViTri/anhViTri.model';
import CauHinhBay from '../CauHinhBay/cau_hinh_bay.model';
import KetQuaDoNhietDo from '../KetQuaDoNhietDo/ketQuaDoNhietDo.model';
import KetQuaKiemTra from '../QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.model';
import KetQuaSuaChuaCoKeHoach from '../QuanLyVanHanh/KetQuaSuaChuaCoKeHoach/ketQuaSuaChuaCoKeHoach.model';
import KetQuaSuaChuaKhongKeHoach from '../QuanLyVanHanh/KetQuaSuaChuaKhongKeHoach/ketQuaSuaChuaKhongKeHoach.model';
import ReportDoNhietDo from '../Report/ReportDoNhietDo/doNhietDo.model';
import ReportDoDienTro from '../Report/ReportDoDienTro/doDienTro.model';
import ReportDoPhaDat from '../Report/ReportDoPhaDat/doPhaDat.model';
import NoiDungCongViec from '../QuanLyVanHanh/NoiDungCongViec/noiDungCongViec.model';
import NguoiCongTac from '../QuanLyVanHanh/NguoiCongTac/nguoiCongTac.model';
import TapTin from '../QuanLyVanHanh/TapTin/tapTin.model';
import PhieuCongTac from '../QuanLyVanHanh/PhieuCongTac/phieuCongTac.model';
import BienPhapAnToanCongViec from '../QuanLyVanHanh/BienPhapAnToanCongViec/bienPhapAnToanCongViec.model';
import CongViecPhuTro from '../QuanLyVanHanh/CongViecPhuTro/congViecPhuTro.model';
import CongTrinhCongViec from '../QuanLyVanHanh/CongTrinhCongViec/congTrinhCongViec.model';
import DieuKienAnToanCongViec from '../QuanLyVanHanh/DieuKienAnToanCongViec/dieuKienAnToanCongViec.model';
import CongViecPhatSinh from '../QuanLyVanHanh/CongViecPhatSinh/congViecPhatSinh.model';
import ViTriCongViec from '../QuanLyVanHanh/ViTriCongViec/viTriCongViec.model';

export const SERVICE_DELETE_BY_VI_TRI = [
  CotDienService,
  DayCapQuang,
  DayChongSet,
  GiaoCheo,
  TiepDat,
  VanHanhService,
];

export const MODEL_REF_PHIEU_GIAO_VIEC = [
  ViTriCongViec,
  AnhViTri,
  CauHinhBay,
  KetQuaDoNhietDo,
  KetQuaKiemTra,
  KetQuaSuaChuaCoKeHoach,
  KetQuaSuaChuaKhongKeHoach,
  ReportDoNhietDo,
  ReportDoDienTro,
  ReportDoPhaDat,
  NoiDungCongViec,
  NguoiCongTac,
  TapTin,
  PhieuCongTac,
  BienPhapAnToanCongViec,
  CongViecPhuTro,
  CongTrinhCongViec,
  DieuKienAnToanCongViec,
  CongViecPhatSinh,
];
