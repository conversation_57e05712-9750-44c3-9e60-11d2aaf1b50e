import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

import { CAU_HINH_BAY, PHIEU_GIAO_VIEC, VI_TRI } from '../../constant/dbCollections';

const schema = new Schema({
  _id: String,
  ten_cau_hinh: String,
  phieu_giao_viec_id: { type: Schema.Types.ObjectId, ref: PHIEU_GIAO_VIEC },
  vi_tri_id: { type: Schema.Types.ObjectId, ref: VI_TRI },
  vi_tri_range: [{ type: Schema.Types.ObjectId, ref: VI_TRI }],
  drone_serial: String,
  drone_model: String,
  start_latitude: Number,
  start_longitude: Number,
  start_absolute_altitude: { type: Number, default: null },
  target_record_height: Number,
  target_record_absolute_altitude: { type: Number, default: null },
  target_record_heading: { type: Number, default: 0 },
  go_home_height: Number,
  target_latitude: Number,
  target_longitude: Number,
  is_deleted: { type: Boolean, default: false, select: true },
  use_rtk: { type: Boolean, default: false },
  can_fly_absolute: { type: Boolean, default: false },
  base_station_latitude: Number,
  base_station_longitude: Number,
  base_station_altitude: Number,
  type_source: String,

  sync_time: String,
  sync_status: String,
  created_time: String,
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

// Optimized indexes for updateThongTinViTri function
// Index for vi_tri_id queries with is_deleted filter
schema.index({ vi_tri_id: 1, is_deleted: 1 }, { background: true });
// Index for phieu_giao_viec_id queries with is_deleted filter
schema.index({ phieu_giao_viec_id: 1, is_deleted: 1 }, { background: true });
// Index for created_at with is_deleted for time-based queries
schema.index({ created_at: 1, is_deleted: 1 }, { background: true });

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(CAU_HINH_BAY, schema, CAU_HINH_BAY);
