import * as ValidatorHelper from '../../helpers/validatorHelper';
import CAU_HINH_BAY from './cau_hinh_bay.model';
import { extractIds, groupBy } from '../../utils/dataconverter';
import MISSION_POINT from '../MissionPoint/mission_point.model';

const Joi = require('joi');

export async function getAll(query) {
  const cauHinhBays = await CAU_HINH_BAY.find(query).lean();
  const cauHinhBayIds = extractIds(cauHinhBays);
  const missionPoints = await MISSION_POINT.find({ cau_hinh_bay_id: { $in: cauHinhBayIds } }).lean();
  const missionPointGroupByCauHinhBayId = groupBy(missionPoints, 'cau_hinh_bay_id');
  cauHinhBays.forEach(cauHinhBay => {
    cauHinhBay.mission_points = missionPointGroupByCauHinhBayId[cauHinhBay._id];
  });
  return cauHinhBays;
}

export async function updateAll(arrData) {
  await CAU_HINH_BAY.bulkWrite(
    arrData.map((row) =>
      ({
        updateOne: {
          filter: { _id: row._id },
          update: { $set: row },
          upsert: false,
        },
      }),
    ),
  );
}

const objSchema = Joi.object({
  vi_tri_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Vị trí id')),
  phieu_giao_viec_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Phiếu giao việc id')),
});

export const validate = (data, method) => {
  return ValidatorHelper.validate(objSchema, data, method);
};
