import express from 'express';
import passport from 'passport';
import * as donviController from './cau_hinh_bay.controller';
import { authorizationMiddleware } from '../RBAC/middleware';
import TongKePermission from '../RBAC/permissions/TongKePermission';
import { loggerMiddleware } from '../../logs/middleware';

export const cauHinhBayRouter = express.Router();
cauHinhBayRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);
cauHinhBayRouter.post('*', authorizationMiddleware([TongKePermission.READ]));
cauHinhBayRouter.get('*', authorizationMiddleware([TongKePermission.READ]));
cauHinhBayRouter.put('*', authorizationMiddleware([TongKePermission.READ]));
cauHinhBayRouter.delete('*', authorizationMiddleware([TongKePermission.READ]));
cauHinhBayRouter
  .route('/')
  .get(donviController.getAll)
  .post(donviController.create);

cauHinhBayRouter
  .route('/:id')
  .get(donviController.findOne)
  .delete(donviController.remove)
  .put(donviController.update);
