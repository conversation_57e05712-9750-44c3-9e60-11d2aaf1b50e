import express from 'express';
import * as Controller from './file.controller';
import { downloadFileWithoutId, downloadFlightControlApp, downloadMultiAsZip } from './file.controller';
import SettingPermission from '../RBAC/permissions/SettingPermission';
import { authorizationMiddleware } from '../RBAC/middleware';
import passport from 'passport';
import { loggerMiddleware } from '../../logs/middleware';

export const fileRouter = express.Router();

fileRouter
  .route('/')
  .get(Controller.downloadWithFileName);

fileRouter
  .route('/preview')
  .get(Controller.previewFile);

fileRouter
  .route('/iosapp')
  .get(Controller.downloadIosApp);

fileRouter
  .route('/tailieu')
  .get(Controller.downloadMultiAsZip);

fileRouter
  .route('/androidapp')
  .get(Controller.downloadAndroidApp);

fileRouter.route('/flightcontrolapp').get(Controller.downloadFlightControlApp);

fileRouter
  .route('/:id')
  .get(Controller.downloadFile);

fileRouter
  .route('/image/:id')
  .get(Controller.downloadAnhViTri);

fileRouter
  .route('/images/:ids')
  .get(Controller.downloadMultiAnhViTri);


fileRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

fileRouter.route('/dichuyenanh/chukys')
  .get(authorizationMiddleware([SettingPermission.UPDATE]), Controller.getCountMoveSignature)
  .put(authorizationMiddleware([SettingPermission.UPDATE]), Controller.moveSignature)

fileRouter.route('/dichuyenanh/avatar')
  .get(authorizationMiddleware([SettingPermission.UPDATE]), Controller.getCountMoveAvatar)
  .put(authorizationMiddleware([SettingPermission.UPDATE]), Controller.moveAvatar)

fileRouter.route('/dichuyenanh/anhvitri')
  .get(authorizationMiddleware([SettingPermission.UPDATE]), Controller.getMoveAnhViTri)
  .put(authorizationMiddleware([SettingPermission.UPDATE]), Controller.moveAnhViTri)

fileRouter.route('/dichuyenanh/anhcongtac')
  .get(authorizationMiddleware([SettingPermission.UPDATE]), Controller.getMoveAnhCongTac)
  .put(authorizationMiddleware([SettingPermission.UPDATE]), Controller.moveAnhCongTac)

fileRouter.route('/anhvitri/addfolderpath')
  .get(authorizationMiddleware([SettingPermission.UPDATE]), Controller.countNeedAddFolderPath)
  .put(authorizationMiddleware([SettingPermission.UPDATE]), Controller.additionalFolderPath)



