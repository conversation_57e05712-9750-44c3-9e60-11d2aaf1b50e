import fs from 'fs';
import path from 'path';

import { getFilePath, moveFile } from '../../utils/fileUtils';
import * as responseHelper from '../../helpers/responseHelper';
import CommonError from '../../error/CommonError';
import Model from '../CaiDatHeThong/caiDatHeThong.model';
import { STORE_DIRS } from '../../constant/constant';
import queryHelper from '../../helpers/queryHelper';
import zip from 'adm-zip';
import { buildChunks } from '../../common/DataStructureHelper';
import AnhViTriModel from '../AnhViTri/anhViTri.model';
import TaiLieuModel from '../TaiLieu/taiLieu.model';
import AnhCongTacModel from '../QuanLyVanHanh/PhieuCongTac/AnhCongTac/anhCongTac.model';
import * as fileServices from './file.services';


let mime = {
  html: 'text/html',
  txt: 'text/plain',
  css: 'text/css',
  gif: 'image/gif',
  jpg: 'image/jpeg',
  png: 'image/png',
  svg: 'image/svg+xml',
  js: 'application/javascript',
};

export function previewFile(req, res) {
  const { id, file_dir } = req.query;
  const file = file_dir ? getFilePath(id, file_dir) : getFilePath(id);
  if ((file_dir && !file_dir.startsWith('./storage') && !file_dir.startsWith('storage')) || !file) {
    res.set('Content-Type', 'text/plain');
    res.status(404).end('Not found');
  } else {
    const type = mime[path.extname(file).slice(1)] || 'text/plain';
    const s = fs.createReadStream(file);
    s.on('open', function() {
      res.set('Content-Type', type);
      s.pipe(res);
    });
    s.on('error', function() {
      //Check trường hợp dữ liệu chưa chuyển sang folder tương ứng (dữ liệu cũ) thì tìm trong folder './storage'
      if (file_dir) {
        req.query.file_dir = null;
        previewFile(req, res);
      } else {
        res.set('Content-Type', 'text/plain');
        res.status(404).end('Not found');
      }
    });
  }
}


export function downloadFile(req, res) {
  const filePath = getFilePath(req.params.id);
  if (fs.existsSync(filePath)) {
    return res.download(filePath, req.query.fileName);
  }
  return responseHelper.error(res, null, 404, 'messagemessagemessage');
}

export function downloadWithFileName(req, res) {
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  const filePath = getFilePath(criteria.id);
  if (fs.existsSync(filePath)) {
    return res.download(filePath, criteria.file_name);
  }
  return res.download(path.join('./server', './assets', './images', 'no-image.png'));
}

export async function downloadIosApp(req, res) {
  const data = await Model.findOne().lean();
  if (!data) {
    return responseHelper.error(res, CommonError.NOT_FOUND);
  }
  const filePath = `${STORE_DIRS.MOBILE_APP}/${data.ios_app}`;
  if (fs.existsSync(filePath)) {
    return res.download(filePath, data.ios_app);
  }
  return responseHelper.error(res, CommonError.NOT_FOUND);
}

export async function downloadAndroidApp(req, res) {
  const data = await Model.findOne().lean();
  if (!data) {
    return responseHelper.error(res, CommonError.NOT_FOUND);
  }
  const filePath = `${STORE_DIRS.MOBILE_APP}/${data.android_app}`;
  if (fs.existsSync(filePath)) {
    return res.download(filePath, data.android_app);
  }
  return responseHelper.error(res, CommonError.NOT_FOUND);
}

export async function downloadFlightControlApp(req, res) {
  const data = await Model.findOne().lean();
  if (!data) {
    return responseHelper.error(res, CommonError.NOT_FOUND);
  }
  const filePath = `${STORE_DIRS.MOBILE_APP}/${data.flight_control_app}`;
  if (fs.existsSync(filePath)) {
    return res.download(filePath, data.flight_control_app);
  }
  return responseHelper.error(res, CommonError.NOT_FOUND);
}

export async function downloadAnhViTri(req, res) {
  const { id } = req.params;
  const data = await AnhViTriModel.findOne({ _id: id, is_deleted: false }, { image_id: 1, folder_path: 1 });
  if (data) {
    const filePath = getFilePath(data.image_id, data.folder_path);
    if (fs.existsSync(filePath)) {
      return res.download(filePath);
    }
  }
  return responseHelper.error(res, CommonError.NOT_FOUND, 404);
}

export async function downloadMultiAnhViTri(req, res) {
  let { ids } = req.params;
  ids = ids.split(',');
  let notFound = '';
  if (ids.length) {
    const listImages = await AnhViTriModel.find({ _id: ids, is_deleted: false }, { image_id: 1, folder_path: 1 });
    var zipFile = new zip();
    listImages.forEach((image) => {
      const filePath = getFilePath(image.image_id, image.folder_path);
      if (fs.existsSync(filePath)) {
        zipFile.addLocalFile(filePath);
      } else {
        notFound = file;
      }
    });
    if (notFound) return responseHelper.error(res, CommonError.NOT_FOUND, 404);
    else {
      const data = zipFile.toBuffer();
      const file_after_download = 'images.zip';
      res.set('Content-Type', 'application/octet-stream');
      res.set('Content-Disposition', `attachment; filename=${file_after_download}`);
      res.send(data);
    }
  }
}

export async function getCountMoveSignature(req, res) {
  try {
    const listImage = await fileServices.getListImageUser('chu_ky_id', STORE_DIRS.CHU_KY);
    responseHelper.success(res, listImage.length);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function moveSignature(req, res) {
  try {
    const { chunksSize } = req.body;
    const listImage = await fileServices.getListImageUser('chu_ky_id', STORE_DIRS.CHU_KY);

    if (listImage.length) {
      const chunks = buildChunks(listImage, 100);
      for (let i = 0; i < (chunksSize || chunks.length); i++) {
        chunks[i].forEach((image) => moveFile(image.filePath, image.newPath));
      }
    }
    responseHelper.success(res, listImage.length);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function getCountMoveAvatar(req, res) {
  try {
    const listImage = await fileServices.getListImageUser('avatar', STORE_DIRS.AVATAR);
    responseHelper.success(res, listImage.length);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function moveAvatar(req, res) {
  try {
    const { chunksSize } = req.body;
    const listImage = await fileServices.getListImageUser('avatar', STORE_DIRS.AVATAR);

    if (listImage.length) {
      const chunks = buildChunks(listImage, 100);
      for (let i = 0; i < (chunksSize || chunks.length); i++) {
        chunks[i].forEach((image) => moveFile(image.filePath, image.newPath));
      }
    }
    responseHelper.success(res, listImage.length);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function getMoveAnhViTri(req, res) {
  try {
    const dataReturn = await fileServices.getOldListImageByModel(AnhViTriModel);
    responseHelper.success(res, dataReturn.length);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function moveAnhViTri(req, res) {
  try {
    const { chunksSize } = req.body;
    const listImage = await fileServices.getOldListImageByModel(AnhViTriModel);
    if (listImage.length) {
      const chunks = buildChunks(listImage, 100);
      for (let i = 0; i < (chunksSize || chunks.length); i++) {
        chunks[i].forEach((image) => fileServices.moveImageAndUpdateFolderPath(AnhViTriModel, image.path, image.newPath, image.folderPath, image.id));
      }
    }
    responseHelper.success(res, listImage.length);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function getMoveAnhCongTac(req, res) {
  try {
    const dataReturn = await fileServices.getOldListImageByModel(AnhCongTacModel);
    responseHelper.success(res, dataReturn.length);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function moveAnhCongTac(req, res) {
  try {
    const { chunksSize } = req.body;
    const listImage = await fileServices.getOldListImageByModel(AnhCongTacModel);

    if (listImage.length) {
      const chunks = buildChunks(listImage, 100);
      for (let i = 0; i < (chunksSize || chunks.length); i++) {
        chunks[i].forEach((image) => fileServices.moveImageAndUpdateFolderPath(AnhCongTacModel, image.path, image.newPath, image.folderPath, image.id));
      }
    }
    responseHelper.success(res, listImage.length);
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function countNeedAddFolderPath(req, res) {
  try {
    const recordSNeedAddFolderPath = await fileServices.getRecordsNeedAddFolderPath();
    responseHelper.success(res, { countNeedAdd: recordSNeedAddFolderPath.length });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function additionalFolderPath(req, res) {
  try {
    const { chunksSize } = req.body;
    const recordSNeedAddFolderPath = await fileServices.getRecordsNeedAddFolderPath();
    let countAdded = 0;
    if (recordSNeedAddFolderPath.length) {
      const chunks = buildChunks(recordSNeedAddFolderPath, 100);
      for (let i = 0; i < (chunksSize || chunks.length); i++) {
        const executeList = chunks[i].map((record) => {
          return AnhViTriModel.findOneAndUpdate(
            { _id: record._id },
            { $set: { folder_path: record.folderPath } },
            { new: true },
          );
        });
        const values = await Promise.all(executeList);
        countAdded += values.length;
      }
    }
    return responseHelper.success(res, { countAdded });
  } catch (err) {
    return responseHelper.error(res, err, 500);
  }
}

export async function downloadMultiAsZip(req, res) {
  const { taiLieuIds } = req.query;
  const documentIds = taiLieuIds.split(',');
  let notFound = false;

  if (documentIds.length > 0) {
    const documents = await TaiLieuModel.find({ _id: documentIds, is_deleted: false }, { file_id: 1, file_name: 1 });
    const zipFile = new zip();

    for (const document of documents) {
      const filePath = getFilePath(document.file_id);

      if (fs.existsSync(filePath)) {
        zipFile.addLocalFile(filePath, null, document.file_name);
      } else {
        notFound = true;
        break;
      }
    }

    if (notFound) {
      return responseHelper.error(res, CommonError.NOT_FOUND, 404);
    }

    const data = zipFile.toBuffer();
    res.set('Content-Type', 'application/octet-stream');
    res.set('Content-Disposition', `attachment; filename=tai_lieu.zip`);
    res.send(data);
  }
}
