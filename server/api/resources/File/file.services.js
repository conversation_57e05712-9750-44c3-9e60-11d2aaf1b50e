import path from 'path';
import fs from 'fs'
import moment from 'moment';
import { createFolderIfNotExist, getFilePath, moveFile } from '../../utils/fileUtils';
import { STORE_DIRS } from '../../constant/constant';
import UserModel from '../User/user.model';
import anhViTriModel from '../AnhViTri/anhViTri.model';

export function moveImageAndUpdateFolderPath(Model, oldPath = '', newPath = '', folderPath = '', id) {
  fs.rename(oldPath, newPath, async (err) => {
    if (err) {
      console.log("Error: Couldn't move", oldPath);
    } else if (!folderPath) {
      //Nếu field folder_path trong dữ liệu ảnh vị trí chưa có thì update.
      const newFolderPath = path.dirname(newPath);
      await Model.findOneAndUpdate({ _id: id }, { folder_path: newFolderPath });
    }
  });
}

export function getNewFolderPath(createTime) {
  const folderNameByMonth = moment(createTime).format('MM-YYYY').toString();
  const fileDirByMonth = path.join(STORE_DIRS.IMAGE, folderNameByMonth);
  createFolderIfNotExist(fileDirByMonth);
  return (fileDirByMonth);
}

export async function getOldListImageByModel(ModalImage) {
  const projection = {
    image_id: 1,
    thumbnail_id: 1,
    thoi_gian_tao: 1,
    folder_path: 1
  }
  const query = {
    // folder_path: null,
    thoi_gian_tao: { $gte: moment('2022-03-01').toISOString() },
    $or: [
      { image_id: { $ne: null } },
      { thumbnail_id: { $ne: null } }
    ]
  }
  const listImage = await ModalImage.find(query, projection);
  const listImageInStorage = [];
  listImage.forEach((anhViTri) => {
    const imagePath = getFilePath(anhViTri.image_id);
    const thumbnailPath = getFilePath(anhViTri.thumbnail_id);
    const newFolderPath = getNewFolderPath(anhViTri.thoi_gian_tao);
    if (fs.existsSync(imagePath)) {
      const newPath = getFilePath(anhViTri.image_id, newFolderPath);
      listImageInStorage.push({ id: anhViTri._id, folderPath: anhViTri.folder_path, path: imagePath, newPath });
    }
    if (fs.existsSync(thumbnailPath)) {
      const newPath = getFilePath(anhViTri.thumbnail_id, newFolderPath);
      listImageInStorage.push({ id: anhViTri._id, folderPath: anhViTri.folder_path, path: thumbnailPath, newPath });
    }
  });
  return listImageInStorage;
}

export async function getListImageUser(field, dir) {
  const listUser = await UserModel.find({ [field]: { $ne: null } }, { [field]: 1 });
  const listImageInStorage = [];

  listUser.forEach((user) => {
    const filePath = getFilePath(user[field]);
    if (fs.existsSync(filePath)) {
      const newPath = getFilePath(user[field], dir);
      listImageInStorage.push({ filePath, newPath })
    }
  });
  return listImageInStorage;
}

export async function getRecordsNeedAddFolderPath() {
  const projection = {
    image_id: 1,
    thumbnail_id: 1,
    thoi_gian_tao: 1,
    folder_path: 1
  }
  const query = {
    $and: [
      { thoi_gian_tao: { $gte: moment('2022-03-01').toISOString() }, },
      {
        $or: [
          { folder_path: { $exists: false } },
          { folder_path: { $in: [null, ""] } }
        ]
      },
      {
        $or: [
          { image_id: { $ne: null } },
          { thumbnail_id: { $ne: null } }
        ],
      }
    ]
  }
  const recordsMissingFolderPath = await anhViTriModel.find(query, projection);
  let recordsNeedAddFolderPath = [];
  recordsMissingFolderPath.forEach((record) => {
    const folderPath = getNewFolderPath(record.thoi_gian_tao);
    const imagePath = getFilePath(record.image_id, folderPath);
    const thumbnailPath = getFilePath(record.thumbnail_id, folderPath);
    if (fs.existsSync(imagePath) || fs.existsSync(thumbnailPath)) {
      recordsNeedAddFolderPath.push({ _id: record._id, folderPath });
    }
  });
  return recordsNeedAddFolderPath;
}
