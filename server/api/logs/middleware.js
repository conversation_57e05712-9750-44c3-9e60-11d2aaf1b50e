import { config, createLogger, format, transports } from 'winston';
import { getFilePath, getLogPath } from '../utils/fileUtils';
import { STORE_DIRS } from '../constant/constant';
import 'winston-daily-rotate-file';
import 'winston-mongodb';
import moment from 'moment-timezone';
import ServerLogModel from '../resources/Log/ServerLog/serverLog.model';

const { combine, timestamp, printf } = format;

export const usersLogger = createLogger({
  levels: config.syslog.levels,
  format: combine(
    format.errors({ stack: true }),
    // timestamp({ format: 'DD-MM-YYYY HH:mm:ss' }),
    printf(info => formatDataLog(info)),
    format.metadata(),
    format.json(),
  ),

  transports: [
    new transports.DailyRotateFile({
      filename: getLogPath(`%DATE%.txt`),
      format: format.combine(
        format.colorize({
          all: false,
        }),
      ),
    }),
  ],
});

const formatDataLog = (info) => {
  let strRes = '';
  for (let [key, value] of Object.entries(info)) {
    strRes += `${key}: ${value} ,`;
  }
  return strRes;
};

const loggerInfo = (req) => {
  usersLogger.info('Log Request',
    {
      req_user_id: `${req.user?._id}`,
      user_full_name: `${req.user?.full_name}`,
      req_don_vi_id: `${req.user?.don_vi_id}`,
      method: `${req.method}`,
      time_log: moment(),
      user_ip: `${req.ip}`,
      req_ref: `${req.headers?.referer}`,
      req_api: `${req.headers?.origin + req.originalUrl}`,
    },
  );
};

export function loggerMiddleware(req, res, next) {
  loggerInfo(req);
  next();
}

export async function loggerResponse(req) {
  try {
    const dataLog = {
      req_user_id: req.user?._id,
      req_don_vi_id: req.user?.don_vi_id,
      req_body: req.body,
      req_ref: req.headers.referer,
      req_api: req.headers.origin + req.originalUrl,
      user_ip: req?.headers['x-forwarded-for'] || req?.headers['x-real-ip'],
      time_log: moment(),
      method: req.method,
      res_status_code: req.res.statusCode,
      res_status_message: req.res.statusMessage,
      client: req.headers['Client'],
    };
    await ServerLogModel.create(dataLog);
  } catch (e) {
    console.log(e);
  }
}

export async function loggerError(req, messageErr) {
  try {
    const dataLog = {
      req_user_id: req.user?._id,
      req_don_vi_id: req.user?.don_vi_id,
      req_url: req.headers.referer,
      time_log: moment(),
      user_ip: req?.headers['x-forwarded-for'] || req?.headers['x-real-ip'],
      method: req.method,
      res_status_code: req.res.statusCode,
      res_status_message: messageErr,
    };
    await ServerLogModel.create(dataLog);
  } catch (e) {
    console.log(e);
  }
}



