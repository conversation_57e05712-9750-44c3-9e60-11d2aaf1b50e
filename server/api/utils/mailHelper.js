import nodemailer from 'nodemailer';
import { getConfig } from '../../config/config';

const config = getConfig(process.env.NODE_ENV);

export function sendEmail(mailOptions) {
  return new Promise((resolve, reject) => {
    try {
      const transporter = nodemailer.createTransport(config.mail);

      transporter.sendMail(mailOptions, (error, info) => {
        if (error) {
          console.error('Gửi mail thất bại:', error);
          return reject(error);
        } else {
          console.log('Mail gửi thành công:', info.response);
          return resolve(info);
        }
      });
    } catch (e) {
      console.error('Lỗi tạo transporter:', e);
      return reject(e);
    }
  });
}

