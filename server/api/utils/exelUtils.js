import * as XLSX from 'xlsx';
import moment from 'moment-timezone';

export default {
  transformFile: async (filePath) => {
    let rowObj = [];
    let sheetData = [];
    const wb = XLSX.readFile(filePath, { cellDates: true, raw: true });
    wb.SheetNames.forEach(function(sheetName) {
      const rows = XLSX.utils.sheet_to_json(wb.Sheets[sheetName]);

      rows.map((row => {
        for (const key of Object.keys(row)) {
          if (row[key] instanceof Date) {
            row[key] = moment.utc(row[key]).tz('Asia/Ho_Chi_Minh', true);
          }
        }
      }));

      let sheet = {
        name: sheetName,
        rows: rows,
      };
      sheetData.push(sheet);
    });
    return sheetData;
  },
  transformFileNghiemThu: async (filePath) => {
    let sheetData = [];
    const wb = XLSX.readFile(filePath, { cellDates: true, raw: true });
    wb.SheetNames.forEach(function(sheetName) {
      const data = XLSX.utils.sheet_to_json(wb.Sheets[sheetName], { header: 1 })
        .filter(row => row.length > 0);

      const duongDay = data?.[1]?.[1]
        ?.split(/\r?\n/)
        ?.map(line => line.trim().replace(/,$/, ''))
        ?.filter(line => line !== '');

      if (!Array.isArray(duongDay)) return [];

      const [headers, subHeaders] = [data[2], data[3]];
      const rows = data.slice(4).map(row => {
        const formattedRow = {};
        Object.keys(headers).forEach(key => {
          if (!headers[key] || typeof headers[key] !== 'string') return;
          const headerName = headers[key].replace(/\r?\n/g, '').replace(/\s+/g, ' ').trim();
          const subHeaderName1 = subHeaders[key] || null;
          const subHeaderName2 = subHeaders[parseInt(key) + 1] || null;

          if (!subHeaderName1 || !subHeaderName2) {
            formattedRow[headerName] = row[key] || '';
          } else {
            formattedRow[headerName] = formattedRow[headerName] || {};
            if (subHeaderName1) formattedRow[headerName][subHeaderName1] = row[key] || '';
            if (subHeaderName2) formattedRow[headerName][subHeaderName2] = row[parseInt(key) + 1] || '';
          }
        });
        return formattedRow;
      });

      sheetData.push({
        name: sheetName,
        duongDay: duongDay,
        rows: rows,
      });
    });
    return sheetData;
  },
};
