import { authenticate } from 'ldap-authentication';
import { configLDAP } from '../constant/ldapConstants';

export async function findUserLD<PERSON>(username) {
  try {
    // Using admin authentication to search for user
    const options = {
      ldapOpts: {
        url: configLDAP.url,
        timeout: configLDAP.timeout,
      },
      adminDn: configLDAP.username,
      adminPassword: configLDAP.password,
      userSearchBase: configLDAP.baseDN,
      usernameAttribute: 'sAMAccountName',
      username: username,
      attributes: ['dn', 'sn', 'cn', 'mail', 'userPrincipalName', 'sAMAccountName', 'displayName', 'givenName', 'telephoneNumber'],
      verifyUserExists: true, // Only verify user exists without password check
    };

    const user = await authenticate(options);
    console.log('LDAP Find User:', user);
    return { success: true, user };
  } catch (e) {
    console.error('LDAP Find User Exception:', e);
    return { success: false, error: e };
  }
}

export async function ldapAuthenticateAPI(userDn, password) {
  try {
    // Direct user authentication using DN and password
    const options = {
      ldapOpts: {
        url: configLDAP.url,
        timeout: configLDAP.timeout,
      },
      userDn: userDn,
      userPassword: password,
      userSearchBase: configLDAP.baseDN,
      usernameAttribute: 'distinguishedName',
      username: userDn,
    };

    const user = await authenticate(options);
    console.log('LDAP Authentication:', user);
    return { success: true, user }; // Returns true if authentication successful, false otherwise
  } catch (e) {
    console.error('LDAP Authentication Exception:', e);
    return { success: false, error: e };
  }
}

export async function testUserLDAP(username) {
  try {
    // Using admin authentication to search for user with detailed attributes
    const options = {
      ldapOpts: {
        url: configLDAP.url,
        timeout: configLDAP.timeout,
      },
      adminDn: configLDAP.username,
      adminPassword: configLDAP.password,
      userSearchBase: configLDAP.baseDN,
      usernameAttribute: 'sAMAccountName',
      username: username,
      attributes: ['*'], // Get all attributes
      verifyUserExists: true,
    };

    const results = await authenticate(options);
    return results ? { success: true, ...results } : { success: false };
  } catch (e) {
    console.error('LDAP Test User Exception:', e);
    return { success: false };
  }
} 
