export async function getInfo() {
  try {
    const si = require('systeminformation');
    const cpu = await si.cpu();
    const disk = (await si.diskLayout())[0];
    const os = await si.osInfo();
    const versions = await si.versions();
    const ram = await si.mem();

    // console.log(info);
    const info = {
      cpu: {
        manufacturer: cpu.manufacturer,
        brand: cpu.brand,
        speedGHz: cpu.speed,
        cores: cpu.cores,
        physicalCores: cpu.physicalCores,
      },
      ram: {
        totalGB: ram.total / 1024 / 1024 / 1024,
      },
      disk: {
        vendor: disk.vendor,
        name: disk.name,
        sizeGB: disk.size / 1024 / 1024 / 1024,
        type: disk.type,
        interfaceType: disk.interfaceType,
      },
      os: {
        distro: os.distro,
        codename: os.codename,
        platform: os.platform,
        kernel: os.kernel,
        arch: os.arch,
      },
      node: {
        version: `v${versions.node}`,
        v8: versions.v8,
      },
    };

    return roundNumbersInObject(info);
  } catch (e) {
    console.log(e);
    return {
      error: e,
    };
  }
}

export async function getStatus() {
  try {
    const si = require('systeminformation');
    const ram = await si.mem();
    const fileSize = await si.fsSize();
    const status = {
      cpu: {
        cpuCurrentSpeed: await si.cpuCurrentSpeed(),
        cpuTemperature: await si.cpuTemperature(),
        currentLoad: await si.currentLoad(),
      },
      mem: {
        total: ram.total / 1024 / 1024 / 1024,
        free: ram.free / 1024 / 1024 / 1024,
        used: ram.used / 1024 / 1024 / 1024,
        available: ram.available / 1024 / 1024 / 1024,
        usedInPercent: (ram.used / ram.total) * 100,
      },
      fileSize: fileSize.map(file => {
        return {
          ...file,
          size: file.size / 1024 / 1024 / 1024,
          used: file.used / 1024 / 1024 / 1024,
          available: file.available / 1024 / 1024 / 1024,
        };
      }),
    };

    return roundNumbersInObject(status);
  } catch (e) {
    console.log(e);
    return {
      error: e,
    };
  }
}


function roundNumbersInObject(obj) {
  if (Array.isArray(obj)) {
    return obj.map(item => roundNumbersInObject(item));
  } else if (typeof obj === 'object' && obj !== null) {
    const newObj = {};
    for (const key in obj) {
      newObj[key] = roundNumbersInObject(obj[key]);
    }
    return newObj;
  } else if (typeof obj === 'number') {
    return obj.fixedFloat(2);
  } else {
    return obj; // giữ nguyên string, boolean, null, v.v.
  }
}
