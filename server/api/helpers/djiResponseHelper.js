import { getMessageError } from '../constant/messageError';
import CommonError from '../error/CommonError';
import { loggerResponse, loggerError } from '../logs/middleware';

export async function success(res, docs) {
  try {
    res.status(200).json({
      code: 0,
      message: 'success',
      data: docs,
    });
    if (res.req.method !== 'GET') {
      loggerResponse(res.req);
    }
  } catch (e) {
    console.log(e);
  }
}

export async function error(res, err = CommonError.UNEXPECTED_ERROR, message) {
  err = typeof err === 'function' ? err() : err;
  message = err.message || err;
  let messageErr = getMessageError(message, err, res.lang_id || 'vi');
  let response = {
    code: -1,
    message: messageErr,
  };
  try {
    res.status(200).json(response);
    if (res.req.method !== 'GET') {
      loggerError(res.req, messageErr);
    }
  } catch (e) {
    console.log(e);
  }
}
