import jwt from 'jsonwebtoken';
import { getConfig } from '../../config/config';

const config = getConfig(process.env.NODE_ENV);

export default {
  issue(payload, expiresIn) {
    return jwt.sign(payload, config.secret, {
      expiresIn,
    });
  },
  issueRefresh(payload, expiresIn) {
    return jwt.sign(payload, config.secretRefresh, {
      expiresIn,
    });
  },
  async verifyRefreshToken(token) {
    if (token) {
      try {
        return await jwt.verify(token, config.secretRefresh);
      } catch (error) {
        return null;
      }
    }
  },
  async verifyToken(token) {
    if (token) {
      try {
        return await jwt.verify(token, config.secret);
      } catch (error) {
        return null;
      }
    }
  },
};
