import Role from '../resources/Role/role.model';
import User from '../resources/User/user.model';
import Setting from '../resources/CaiDatHeThong/caiDatHeThong.model';
import SettingPmis from '../resources/CaiDatPmis/caiDatPmis.model';
import CaiDatAi from '../resources/CaiDatAi/caiDatAi.model';
import CaiDatVanHanh from '../resources/CaiDatVanHanh/caiDatVanHanh.model';
import DonVi from '../resources/DonVi/donVi.model';
import DeviceDictionary from '../resources/DJICloud/DeviceDictionary/deviceDictionary.model';

import actions from '../resources/RBAC/Actions';
import resources from '../resources/RBAC/Resources';
import CommonSetting from './initdata/CommonSetting';
import RootUnit from './initdata/RootUnit';
import DevicedictionaryData from './initdata/DeviceDictionary';

import { job } from './job';
import { CAP_DON_VI, ROLE_CODES, USER_CODES } from '../constant/constant';
import { TINH_TRANG_VAN_HANH } from '../resources/DanhMuc/TinhTrangVanHanh';
import { updateSchemaModel } from '../resources/TruongDuLieu/truongDuLieu.service';

import * as ModelNames from '../constant/dbCollections';
import * as permission from '../resources/RBAC/permissionHelper';
import { getAllBanDoLuoi, setCacheBanDoLuoi } from '../resources/Dashboard/dashboard.service';

async function initData() {

  async function initSetting() {
    const count = await Setting.countDocuments();
    if (count) return;
    await Setting.create(CommonSetting);
  }

  await initSetting();

  async function initPmisSetting() {
    console.log('initPmisSetting');
    const count = await SettingPmis.countDocuments();
    if (count) return;
    await SettingPmis.create({ allow_pmis_sync: false });
  }

  await initPmisSetting();

  async function initAISetting() {
    const count = await CaiDatAi.countDocuments();
    if (count) return;
    const caiDatAi = {
      api_phan_tich_hinh_anh: '',
      api_dataset: '',
    };
    await CaiDatAi.create(caiDatAi);
  }

  await initAISetting();

  async function initCaiDatVanHanh() {
    const count = await CaiDatVanHanh.countDocuments();
    if (count) return;
    const caiDatVanHanh = {
      ngay_bat_dau: 1,
      ngay_ket_thuc: 10,
      gio_bat_dau: new Date(new Date().getFullYear(), 1, 1, 0, 0, 0),
      gio_ket_thuc: new Date(new Date().getFullYear(), 1, 1, 0, 0, 0),
    };
    await CaiDatVanHanh.create(caiDatVanHanh);
  }

  await initCaiDatVanHanh();

  async function initRoles() {
    const countRole = await Role.countDocuments({ code: { $in: [ROLE_CODES.CODE_SYSTEM_ADMIN] } });
    if (countRole) return;
    const roleSystemAdmin = {
      code: ROLE_CODES.CODE_SYSTEM_ADMIN,
      name: 'System Admin',
      order: 0,
      is_system_admin: true,
      permissions: [
        permission.create(resources.ALL, actions.ALL),
      ],
    };
    await Role.create(roleSystemAdmin);
  }

  await initRoles();

  async function initRootUnit() {
    const count = await DonVi.countDocuments({ cap_don_vi: CAP_DON_VI.TONG_CONG_TY });
    if (count) return;
    await DonVi.create(RootUnit);
  }

  await initRootUnit();

  async function initSystemAdmin() {
    const rootUnit = await DonVi.findOne({ cap_don_vi: CAP_DON_VI.TONG_CONG_TY });
    const systemAdminRole = await Role.findOne({ code: ROLE_CODES.CODE_SYSTEM_ADMIN });
    const count = await User.countDocuments({ username: 'sysadmin' });
    if (count) return;
    const systemAdmin = {
      code: USER_CODES.SYSTEM_ADMIN,
      email: '<EMAIL>',
      username: 'sysadmin',
      password: 'thinklabs@36',
      full_name: 'System Admin',
      is_system_admin: true,
      don_vi_id: rootUnit?._id,
      role_id: [systemAdminRole?._id],
    };
    await User.create(systemAdmin);
  }

  await initSystemAdmin();

  async function initDeviceDictionary() {
    const count = await DeviceDictionary.countDocuments();
    if (count) return;
    await DeviceDictionary.insertMany(DevicedictionaryData);
  }

  await initDeviceDictionary();
  console.log('Init data done');
}

async function migrateTinhTrangVanHanhDonVi() {
  try {
    console.log('Bắt đầu migrate tình trạng vận hành cho Đơn vị...');
    const result = await DonVi.updateMany(
      {
        tinh_trang_van_hanh: { $exists: false },
      },
      {
        $set: {
          tinh_trang_van_hanh: TINH_TRANG_VAN_HANH.VAN_HANH,
        },
      },
    );
    console.log(`Migrate tình trạng vận hành cho Đơn vị hoàn tất. ${result.nModified} bản ghi đã được cập nhật.`);
  } catch (e) {
    console.log('Lỗi khi migrate tình trạng vận hành cho Đơn vị:', e);
  }
}

async function initTruongDuLieu() {
  try {
    for (let modelName of Object.values(ModelNames)) {
      await updateSchemaModel(modelName);
    }
  } catch (e) {
    console.log(e);
  }
}

export async function initBanDoLuoi() {
  try {
    const banDoLuoi = await getAllBanDoLuoi();
    await setCacheBanDoLuoi(banDoLuoi);
  } catch (e) {
    console.log(e);
  }
}

export async function allWorkerInitData() {
  try {
    await initBanDoLuoi();
  } catch (error) {
    console.log(error);
  }  
}

export async function onlyOneWorkerInitData() {
  try {
    await initData();
    await initTruongDuLieu();
    await migrateTinhTrangVanHanhDonVi();
    await initBanDoLuoi();
  } catch (e) {
    console.log(e);
  }
}
