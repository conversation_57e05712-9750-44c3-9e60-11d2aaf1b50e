import cron from 'node-cron';

import PhieuGiaoViecModel from '../resources/QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.model';

import * as KetQuaKiemTraService from '../resources/QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.service';
import * as PhieuMauService from '../resources/QuanLyVanHanh/PhieuMau/phieuMau.service';
import * as RemindService from '../resources/Remind/remind.service';
import * as SuCoDuongDayService from '../resources/TongKe/SuCoDuongDay/suCoDuongDay.service';
import * as AnhViTriService from '../resources/AnhViTri/anhViTri.service';
import * as ServerLogService from '../resources/Log/ServerLog/serverLog.service';
import * as CaiDatHeThongService from '../resources/CaiDatHeThong/caiDatHeThong.service';
import * as RefreshTokenService from '../resources/RefreshToken/refreshToken.service';
import * as SyncPmisService from '../resources/TongKe/SyncPmis/syncPmis.service';
import * as CongSuatPmisService from '../resources/QuanLyVanHanh/TraoLuuCongSuat/CongSuatPmiss/congSuatPmiss.service';
import * as PhieuGiaoViecService from '../resources/QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';

import { TRANG_THAI_PHIEU } from '../resources/DanhMuc/TrangThaiCongViec';
import { initBanDoLuoi } from './index';
import { extractIds } from '../utils/dataconverter';

// import * as SDKManager from '../common/SDKManager';

async function jobPhieuGiaoViec() {
  const cronTime = '*/1 * * * *'; // every 1m

  async function autoRejectPhieuGiaoViec() {
    const configSystem = await CaiDatHeThongService.getAll();
    const kichHoatAutoHuyPhieu = configSystem[0].kich_hoat_auto_huy_phieu;
    if (!kichHoatAutoHuyPhieu) return;
    const phieuQuaHanTiepNhan = await PhieuGiaoViecModel.find({
        trang_thai_cong_viec: TRANG_THAI_PHIEU.GIAO_PHIEU.code,
        is_deleted: false,
        thoi_gian_cong_tac_ket_thuc: { '$lte': new Date() },
      },
      { _id: 1 },
    ).lean();
    if (phieuQuaHanTiepNhan.length) {
      try {
        const phieuQuaHanTiepNhanId = extractIds(phieuQuaHanTiepNhan);
        await PhieuGiaoViecModel.updateMany(
          { _id: phieuQuaHanTiepNhanId },
          {
            trang_thai_cong_viec: TRANG_THAI_PHIEU.TU_CHOI_NHAN.code,
            qua_thoi_gian_tiep_nhan: true,
          });
        await PhieuGiaoViecService.restoreTrangThaiXuLyTonTai(phieuQuaHanTiepNhanId);
      } catch (e) {
        console.log(e);
      }
    }
  }

  cron.schedule(cronTime, autoRejectPhieuGiaoViec, { timezone: 'Asia/Ho_Chi_Minh' });
}

async function jobThongBaoTonTai() {
  const cronTime = `0 0 * * *`; // every day 0h
  cron.schedule(cronTime, KetQuaKiemTraService.checkTonTai, { timezone: 'Asia/Ho_Chi_Minh' });
}

async function jobDeleteImage() {
  const cronTime = '30 1 * * *'; // every 1h 30 am
  // const cronTime = '*/1 * * * *'; // every 1m : use to test

  cron.schedule(cronTime, AnhViTriService.autoDeleteImage, { timezone: 'Asia/Ho_Chi_Minh' });
}

async function jobAutoLapLich() {
  const cronTime = '15 0 * * *'; // every 0h 15 am
  cron.schedule(cronTime, PhieuMauService.autoLapLich, { timezone: 'Asia/Ho_Chi_Minh' });
}

async function jobAutoDeleteRefreshToken() {
  const cronTime = '20 0 * * *'; // every 0h 20 am
  // const cronTime = '*/1 * * * *'; // every 1m : use to test

  async function autoDeleteRefreshToken() {
    await RefreshTokenService.deleteMany({ expires_date: { $lte: new Date() } });
  }

  cron.schedule(cronTime, autoDeleteRefreshToken, { timezone: 'Asia/Ho_Chi_Minh' });
}

async function jobAutoHuyPhieu() {
  const cronTime = '*/1 * * * *'; // every 1m

  async function autoHuyPhieu() {
    await PhieuGiaoViecService.huyPhieu();
  }

  cron.schedule(cronTime, autoHuyPhieu, { timezone: 'Asia/Ho_Chi_Minh' });
}

async function jobAutoRemind() {
  // const cronTime = '*/1 * * * *'; // every 1m
  const cronTime = '35 0 * * *'; // every 0h 35 am

  cron.schedule(cronTime, async () => {
    await RemindService.remindToTaiXLVH();
    await RemindService.remindToTaiBaoCaoTTD();
    await RemindService.remindDoThongSo('DO_DIEN_TRO_TIEP_DIA');
    await RemindService.remindDoThongSo('DO_NHIET_DO_TIEP_XUC');
  }, { timezone: 'Asia/Ho_Chi_Minh' });
}

async function jobSuCoKinkei() {
  const cronTime = '*/5 * * * *'; // every 5m
  cron.schedule(cronTime, SuCoDuongDayService.autoSyncSuCo, { timezone: 'Asia/Ho_Chi_Minh' });
}

async function jobSuCoPmis() {
  const cronTime = '*/5 * * * *'; // every 5m
  cron.schedule(cronTime, SyncPmisService.syncSuCoPmis, { timezone: 'Asia/Ho_Chi_Minh' });
}

async function jobSyncImageAi() {
  const cronTime = '0 2 * * *'; // every 2h am
  cron.schedule(cronTime, AnhViTriService.syncImageAi, { timezone: 'Asia/Ho_Chi_Minh' });
}

async function jobSyncThongSoDuongDay() {
  const cronTime = '*/15 * * * *'; // every 15m
  // cron.schedule(cronTime, await SyncPmisService.syncThongSoDuongDayPmis, { timezone: 'Asia/Ho_Chi_Minh' });
  cron.schedule(cronTime, await CongSuatPmisService.syncThongSoDuongDayPmis, { timezone: 'Asia/Ho_Chi_Minh' });
}

async function jobDeleteLog() {
  const cronTime = '0 1 * * *'; // every 0h am
  cron.schedule(cronTime, await ServerLogService.deleteLogData, { timezone: 'Asia/Ho_Chi_Minh' });
  cron.schedule(cronTime, await ServerLogService.deleteFileLog, { timezone: 'Asia/Ho_Chi_Minh' });
}

async function jobUpdateCacheBanDoLuoi() {
  const cronTime = `${process.pid % 60} */12 * * *`; // every 12h
  cron.schedule(cronTime, initBanDoLuoi, { timezone: 'Asia/Ho_Chi_Minh' });
}

async function jobDeleteSyncPmisLog() {
  const cronTime = '0 3 * * *'; // every 0h am
  cron.schedule(cronTime, await SyncPmisService.deleteSyncPmisLog, { timezone: 'Asia/Ho_Chi_Minh' });
}

export async function onlyOneWorkerJobs() { // chỉ 1 Worker run Job này
                                            // await jobPhieuGiaoViec();
  await jobThongBaoTonTai();
  await jobSuCoKinkei();
  await jobSuCoPmis();
  await jobSyncImageAi();
  await jobSyncThongSoDuongDay();
  // await jobSyncDataPmis();
  await jobDeleteLog();
  await jobDeleteImage();
  await jobAutoLapLich();
  await jobAutoDeleteRefreshToken();
  await jobAutoHuyPhieu();
  await jobAutoRemind();
  // await jobUpdateCacheBanDoLuoi();
  await jobDeleteSyncPmisLog();
}

export async function allWorkerJobs() { // Mọi Worder đều run job này
  jobUpdateCacheBanDoLuoi();
}
