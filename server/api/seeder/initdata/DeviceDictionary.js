export default [
  {
    device_name: 'Matrice 300 RTK',
    type: 60,
    domain: 0,
    sub_type: 0,
    code: 'M300'
  },
  {
    device_name: 'Matrice 30',
    type: 67,
    domain: 0,
    sub_type: 0,
    code: 'M30',
  },
  {
    device_name: 'Matrice 30T',
    type: 67,
    domain: 0,
    sub_type: 1,
    code: 'M30T'
  },
  {
    device_name: 'Z30',
    type: 20,
    domain: 1,
    sub_type: 0,
    code: 'Z30'
  },
  {
    device_name: 'XT2',
    type: 26,
    domain: 1,
    sub_type: 0,
    code: 'XT2'
  },
  {
    device_name: 'FPV',
    type: 39,
    domain: 1,
    sub_type: 0,
    code: 'FPV'
  },
  {
    device_name: 'XTS',
    type: 41,
    domain: 1,
    sub_type: 0,
    code: 'XTS'
  },
  {
    device_name: 'H20',
    type: 42,
    domain: 1,
    sub_type: 0,
    code: 'H20'
  },
  {
    device_name: 'H20T',
    type: 43,
    domain: 1,
    sub_type: 0,
    code: 'H20T'
  },
  {
    device_name: 'P1',
    device_desc: 'include 24 and 35 and 50mm',
    type: 50,
    domain: 1,
    sub_type: 65535,
    code: 'P1'
  },
  {
    device_name: 'M30 Camera',
    type: 52,
    domain: 1,
    sub_type: 0,
    code: 'M30_CAMERA'
  },
  {
    device_name: 'M30T Camera',
    type: 53,
    domain: 1,
    sub_type: 0,
    code: 'M30T_CAMERA'
  },
  {
    device_name: 'H20N',
    type: 61,
    domain: 1,
    sub_type: 0,
    code: 'H20N'
  },
  {
    device_name: 'DJI Dock Camera',
    type: 165,
    domain: 1,
    sub_type: 0,
    code: 'DOCK_CAMERA'
  },
  {
    device_name: 'L1',
    type: 90742,
    domain: 1,
    sub_type: 0,
    code: 'L1'
  },
  {
    device_name: 'DJI Smart Controller',
    device_desc: 'Remote control for M300',
    type: 56,
    domain: 2,
    sub_type: 0,
    code: 'RC'
  },
  {
    device_name: 'DJI RC Plus',
    device_desc: 'Remote control for M30',
    type: 119,
    domain: 2,
    sub_type: 0,
    code: 'RC_PLUS'
  },
  {
    device_name: 'DJI Dock',
    device_desc: '',
    type: 1,
    domain: 3,
    sub_type: 0,
    code: 'DOCK'
  },
  {
    device_name: 'Mavic 3E',
    type: 77,
    domain: 0,
    sub_type: 0,
    code: 'M3E',
  },
  {
    device_name: 'Mavic 3T',
    type: 77,
    domain: 0,
    sub_type: 1,
    code: 'M3T',
  },
  {
    device_name: 'Mavic 3E Camera',
    type: 66,
    domain: 1,
    sub_type: 0,
    code: 'M3E_CAMERA'
  },
  {
    device_name: 'Mavic 3T Camera',
    type: 67,
    domain: 1,
    sub_type: 0,
    code: 'M3T_CAMERA'
  },
  {
    device_name: 'DJI RC Pro',
    device_desc: 'Remote control for Mavic 3E/T and Mavic 3M',
    type: 144,
    domain: 2,
    sub_type: 0,
    code: 'RC_PRO'
  },
  {
    device_name: 'Mavic 3M',
    type: 77,
    domain: 0,
    sub_type: 2,
    code: 'M3M',
  },
  {
    device_name: 'Mavic 3M Camera',
    type: 68,
    domain: 1,
    sub_type: 0,
    code: 'M3M_CAMERA'
  },
  {
    device_name: 'Matrice 350 RTK',
    type: 89,
    domain: 0,
    sub_type: 0,
    code: 'M350',
  },
  {
    device_name: 'DJI Dock2',
    type: 2,
    domain: 3,
    sub_type: 0,
    code: 'DOCK2'
  },
  {
    device_name: 'M3D',
    type: 91,
    domain: 0,
    sub_type: 0,
    code: 'M3D'
  },
  {
    device_name: 'M3TD',
    type: 91,
    domain: 0,
    sub_type: 1,
    code: 'M3TD'
  },
  {
    device_name: 'M3D Camera',
    type: 80,
    domain: 1,
    sub_type: 0,
    code: 'M3D_CAMERA'
  },
  {
    device_name: 'M3TD Camera',
    type: 81,
    domain: 1,
    sub_type: 0,
    code: 'M3TD_CAMERA'
  },
];
