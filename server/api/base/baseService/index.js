import { count } from '../../resources/TongKe/DayDan/dayDan.service';

export default function createBaseService(Model) {
  return {
    createMulti(dataList = [], options = {}) {
      try {
        return Model.insertMany(dataList, {
          ordered: false,
          ...options,
        });
      } catch (err) {
        console.error('❌ createMulti error:', err);
        throw err;
      }
    },

    // Get all documents with optional filter and projection
    getAll(query = {}, projection = {}, options = {}) {
      return Model.find(query, projection, options).lean();
    },

    getById(id, projection = {}, options = {}) {
      return Model.findById(id, projection, options).lean();
    },

    getByIdAndUpdate(id, update = {}, options = {}) {
      return Model.findByIdAndUpdate(id, update, options);
    },

    count(query = {}) {
      return Model.count(query).lean();
    },

    // Get one document
    getOne(query = {}, projection = {}) {
      return Model.findOne(query, projection).lean();
    },

    // Remove one document (soft delete or hard delete)
    remove(query = {}) {
      return Model.deleteOne(query);
    },

    distinctId(query = {}) {
      return Model.distinct('_id', query);
    },

    distinctField(field, filter) {
      return Model.distinct(field, filter);
    },

    /**
     * Update multiple records using a common query
     */
    async updateByQuery(query = {}, update = {}, options = {}) {
      try {
        await Model.updateMany(query, update, options);
        return Model.find(query).lean();
      } catch (err) {
        console.error('❌ updateByQuery error:', err);
        throw err;
      }
    },

    /**
     * Update multiple records by list of rows { _id, ...data }
     */
    async updateByRows(rows = [], options = {}) {
      options.new = true;

      const tasks = rows.map(async (row) => {
        try {
          if (!row._id) return null;
          return await Model.findByIdAndUpdate(row._id, row, options);
        } catch (err) {
          console.error(`❌ updateByRows failed for _id=${row._id}:`, err);
          return null;
        }
      });

      const results = await Promise.all(tasks);
      return results.filter(Boolean); // Remove failed updates from the result list
    },

    // Delete many documents (hard delete)
    deleteAll(query = {}) {
      return Model.deleteMany(query);
    },

    // Update many documents
    updateAll(rows = []) {
      return Model.bulkWrite(
        rows.map((row) => ({
            updateOne: {
              filter: { _id: row._id },
              update: { $set: row },
              upsert: false,
            },
          }),
        ),
      );
    },
  };
}
