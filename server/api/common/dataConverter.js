import moment from 'moment';
import { snakeCase, camelCase } from 'lodash';

import { cloneObj } from './functionCommons';

//--------------------------------------------------------------------
export function convertToCamelCase(dataInput) {
  if (typeof dataInput === 'object') {
    if (Array.isArray(dataInput)) {
      let objOutput = [];
      dataInput.forEach(item => {
        objOutput = [...objOutput, convertToCamelCase(item)];
      });
      return objOutput;
    } else {
      return convertObjectToCamelCase(dataInput);
    }
  }
  return dataInput;
}

export function convertObjectToCamelCase(objInput) {
  if (!objInput) return objInput;
  const objOutput = {};
  Object.entries(objInput).forEach(([key, value]) => {
    if (key === 'extra') {
      objOutput[key] = value;
    } else {
      if (typeof value === 'object') {
        if (Array.isArray(value)) {
          // array
          objOutput[camelCase(key)] = convertToCamelCase(value);
        } else {
          // object
          objOutput[camelCase(key)] = convertObjectToCamelCase(value);
        }
      } else {
        if (key === '_id') {
          objOutput._id = value;
          objOutput.key = value;
        } else {
          objOutput[camelCase(key)] = value;
        }
      }
    }
  });
  return objOutput;
}

//--------------------------------------------------------------------
export function convertToSnakeCase(dataInput) {
  dataInput = cloneObj(dataInput);
  if (typeof dataInput === 'object') {
    if (Array.isArray(dataInput)) {
      let objOutput = [];
      dataInput.forEach(item => {
        objOutput = [...objOutput, convertToSnakeCase(item)];
      });
      return objOutput;
    } else {
      return convertObjectToSnakeCase(dataInput);
    }
  }
  return dataInput;
}

export function convertObjectToSnakeCase(objInput) {
  if (!objInput) return objInput;
  objInput = cloneObj(objInput);
  const objOutput = {};
  Object.entries(objInput).forEach(([key, value]) => {
    if (key === 'extra' || key === '_id') {
      objOutput[key] = value;
    } else {
      if (typeof value === 'object') {
        if (moment.isMoment(value)) {
          objOutput[snakeCase(key)] = value;
        } else if (Array.isArray(value)) {
          // array
          objOutput[snakeCase(key)] = convertToSnakeCase(value);
        } else {
          // object
          objOutput[snakeCase(key)] = convertObjectToSnakeCase(value);
        }
      } else {
        if (key === '_id') {
          objOutput._id = value;
        } else {
          objOutput[snakeCase(key)] = value !== undefined ? value : null;
        }
      }
    }
  });
  return objOutput;
}
