export function createDataTree(dataset, idProperty, parentIdProperty, sortFunction = sortThuTu) {
  const hashTable = Object.create(null);
  const dataTree = [];

  // Tạo map các node
  dataset.forEach(item => {
    hashTable[item[idProperty]] = { ...item, children: [] };
  });

  // Ghép cha con
  dataset.forEach(item => {
    const node = hashTable[item[idProperty]];
    const parentId = item[parentIdProperty];

    if (parentId && hashTable[parentId]) {
      hashTable[parentId].children.push(node);
    } else {
      dataTree.push(node);
    }
  });

  // Sắp xếp toàn bộ cây
  const sortedTree = sortTree(dataTree, sortFunction);

  // Gán index theo đệ quy
  function assignIndex(nodes, prefix = '') {
    nodes.forEach((node, idx) => {
      const currentIndex = idx + 1;
      node.index = prefix ? `${prefix}.${currentIndex}` : `${currentIndex}`;
      if (node.children?.length) {
        assignIndex(node.children, node.index);
      }
    });
  }

  assignIndex(sortedTree);

  return sortedTree;
}

export function sortThuTu(a, b) {
  return a.thu_tu - b.thu_tu;
}

export function sortTree(tree, sortFunction = sortThuTu) {
  const orgTree = tree.sort(sortFunction);
  let stackNodes = [...tree];
  while (stackNodes.length > 0) {
    const last = stackNodes.pop();
    if (last.children && last.children.length > 0) {
      last.children = last.children.sort(sortFunction);
      stackNodes.push(...last.children);
    }
  }
  return orgTree;
}


export function buildChunks(array, chunkSize = 1) {
  if (chunkSize <= 0) { // chunkSize = 0 lặp vô hạn
    chunkSize = 1;
  }
  const chunks = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    const chunk = array.slice(i, i + chunkSize);
    chunks.push(chunk);
  }
  return chunks;
}
