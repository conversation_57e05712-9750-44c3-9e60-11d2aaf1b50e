import CACHE from './cache.model';
import moment from 'moment';


export async function getCache(hash) {
  const cached = await CACHE.findOne({ hash: hash, expiredAt: { $gte: new Date() } }).lean();
  return cached?.data;
}

export async function setCache(hash, data, keepTime) {
  const current = new Date()
  const expiredAt = moment().add(keepTime, 'minute').toDate()
  console.log(current, expiredAt);
  const cache = {
    data:data,
    hash:hash,
    expiredAt: moment().add(keepTime, 'minute').toDate()
  }
  await CACHE.findOneAndReplace({ hash: hash }, cache, { upsert: true });
}
