import mongoose, { Schema } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

const CACHE = 'CACHE';

const schema = new Schema({
  hash: { type: String, required: true, validate: /\S+/},
  data: Object,
  expriedAt: Date,
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
schema.index({ hash: 1 });
export default mongoose.model(CACHE, schema, CACHE);
