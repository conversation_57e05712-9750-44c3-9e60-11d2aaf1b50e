// Memory cache storage
const memoryCache = new Map();

// Optional: Cache with TTL (Time To Live) support
const cacheWithTTL = new Map();

export async function getCache(hash) {
  console.log("GET CACHE", hash);

  try {
    // Check if cache exists and is not expired
    if (cacheWithTTL.has(hash)) {
      const cacheEntry = cacheWithTTL.get(hash);
      const now = Date.now();
      
      // Check if cache has expired
      if (cacheEntry.expiry && now > cacheEntry.expiry) {
        cacheWithTTL.delete(hash);
        console.log("CACHE EXPIRED", hash);
        return null;
      }
      console.log("CACHE HIT", hash);
      return cacheEntry.data;
    }
    
    return null;
  } catch (e) {
    console.log(e);
    return null;
  }
}

export async function setCache(hash, data, keepTime) {
  console.log("SET CACHE", hash, keepTime);

  try {
    const cacheEntry = {
      data: data,
      timestamp: Date.now(),
      expiry: keepTime ? Date.now() + (keepTime * 1000) : null // keepTime in seconds
    };
    
    cacheWithTTL.set(hash, cacheEntry);
  } catch (e) {
    console.log(e);
  }
}

// Optional: Function to clear expired cache entries
export function clearExpiredCache() {
  const now = Date.now();
  for (const [hash, entry] of cacheWithTTL.entries()) {
    if (entry.expiry && now > entry.expiry) {
      cacheWithTTL.delete(hash);
    }
  }
}

// Optional: Function to clear all cache
export function clearAllCache() {
  cacheWithTTL.clear();
}

// Optional: Function to get cache size
export function getCacheSize() {
  return cacheWithTTL.size;
}
