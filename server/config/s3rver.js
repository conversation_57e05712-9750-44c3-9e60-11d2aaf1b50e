const S3rver = require('../api/resources/DJICloud/S3Server/s3rver');
import { STORE_DIRS } from '../api/constant/constant';
import { getDirPath } from '../api/utils/fileUtils';
import { getConfig } from './config';

const config = getConfig(process.env.NODE_ENV);
const s3rverConfig = config.oss.s3rver;

export const runS3rver = () => {
  getDirPath(config.oss.bucket, STORE_DIRS.S3_SERVER_DATA);

  const s3rver = new S3rver({
    port: s3rverConfig.port,
    address: '0.0.0.0',
    silent: false,
    vhostBuckets: false,
    directory: STORE_DIRS.S3_SERVER_DATA,
    accessKey: s3rverConfig.access_key,
    secretKey: s3rverConfig.secret_key,
    configureBuckets: [{
      name: config.oss.bucket,
      CORSConfiguration: {
        CORSRules: [{
          AllowedHeaders: ['*'],
          AllowedMethods: ['GET', 'PUT', 'POST', 'DELETE', 'HEAD'],
          AllowedOrigins: ['*'],
          MaxAgeSeconds: 3600,
        }]
      }
    }],
  });
  s3rver.run((err) => {
    if (err) console.error('Lỗi khởi động s3rver:', err);
  });
};
