const { pathToRegexp } = require('path-to-regexp');
const responseHelper = require('./api/helpers/responseHelper');
const CaiDatHeThongService = require('./api/resources/CaiDatHeThong/caiDatHeThong.service');

const PLATFORM_OPTIONS = ['WEB', 'IOS', 'ANDROID'];

const API_ROUTER = {
  KHOI_LUONG_CONG_VIEC: '/api/phieugiaoviec/:id/khoiluongcongviec',
};

module.exports = function() {
  return async function(req, res, next) {
    const { t } = req;
    const regexKhoiLuongCOngViec = pathToRegexp(API_ROUTER.KHOI_LUONG_CONG_VIEC);
    if (regexKhoiLuongCOngViec.test(req.url)) {
      const { version } = req.headers;
      const platform = req.headers.platform?.toUpperCase();
      const configSystem = await CaiDatHeThongService.getAll();
      const mobileAppVer = configSystem[0].version_quan_ly_duong_day;

      if (!PLATFORM_OPTIONS.includes(platform) || version && Number(version) < mobileAppVer) {
        const errMessage = t('VUI_LONG_CAP_NHAT_APP') + '. ' + t('PHIEN_BAN_APP').format(version, mobileAppVer);
        return responseHelper.error(res, { message: errMessage }, 400);
      }
    }
    // Implement the middleware function based on the options object
    next();
  };
};
