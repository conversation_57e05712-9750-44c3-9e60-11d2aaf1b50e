diff --git a/node_modules/activedirectory/lib/activedirectory.js b/node_modules/activedirectory/lib/activedirectory.js
index 316dd10..580ad3d 100644
--- a/node_modules/activedirectory/lib/activedirectory.js
+++ b/node_modules/activedirectory/lib/activedirectory.js
@@ -411,24 +411,28 @@ function search(baseDN, opts, callback) {
    * @param {Object} entry The entry received.
    */
   function onSearchEntry(entry) {
-    log.trace('onSearchEntry(%j)', entry);
-    var result = entry.object;
-    delete result.controls; // Remove the controls array returned as part of the SearchEntry
-
-    // Some attributes can have range attributes (paging). Execute the query
-    // again to get additional items.
-    pendingRangeRetrievals++;
-    parseRangeAttributes.call(self, result, opts, function(err, item) {
-      pendingRangeRetrievals--;
-
-      if (err) item = entry.object;
-      entryParser(item, entry.raw, function(item) {
-        if (item) results.push(item);
-        if ((! pendingRangeRetrievals) && (isDone)) {
-          onSearchEnd();
-        }
+    try {
+      log.trace('onSearchEntry(%j)', entry);
+      var result = entry.object;
+      delete result.controls; // Remove the controls array returned as part of the SearchEntry
+  
+      // Some attributes can have range attributes (paging). Execute the query
+      // again to get additional items.
+      pendingRangeRetrievals++;
+      parseRangeAttributes.call(self, result, opts, function(err, item) {
+        pendingRangeRetrievals--;
+  
+        if (err) item = entry.object;
+        entryParser(item, entry.raw, function(item) {
+          if (item) results.push(item);
+          if ((! pendingRangeRetrievals) && (isDone)) {
+            onSearchEnd();
+          }
+        });
       });
-    });
+    } catch (error) {
+      console.log('Error in onSearchEntry', entry, error);
+    }
   }
 
   /**
