version: "3.7"

services:
  api:
    image: evnptc2/tradarprodapi2:latest
    extra_hosts:
      - "qldd-app01:**********"
      - "qldd-app02:**********"
      - "QLDD-AI:**********"
    deploy:
      placement:
        constraints: [node.hostname==qldd-app02]
      replicas: 1
      restart_policy:
        condition: any
    ports:
      #- 3000:3000
      - target: 3000
        published: 3000
        mode: host
    environment:
      MONGO_URI : "***********************************************************************************************************************************************************************************************************"
    #networks:
    #  - ai_net
    volumes:
      - tradarprodapi:/usr/src/app/storage
      - /etc/localtime:/etc/localtime:ro

#networks:
#  ai_net:

volumes:
  tradarprodapi:
    driver: local
