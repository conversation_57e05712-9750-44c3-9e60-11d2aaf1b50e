pipeline {
    agent any
    stages {
        stage('Init') {
            steps {
                echo 'Testing..'
            }
        }
        stage ('Deployments') {
            steps {
                echo 'Deploying to Production environment...'
                echo 'Copy project over SSH...'
                sshPublisher(publishers: [
                    sshPublisherDesc(
                        configName: 'aws_evn',
                        transfers:
                            [sshTransfer(
                                cleanRemote: false,
                                excludes: '',
                                execCommand: "docker build -t tradarprodapi ./thinklabsdev/tradarprodapiCI/ \
                                    && docker service rm tradarprod_api || true \
                                    && docker stack deploy -c ./thinklabsdev/tradarprodapiCI/docker-compose.yml tradarprod \
                                    && rm -rf ./thinklabsdev/tradarprodapiCIB \
                                    && mv ./thinklabsdev/tradarprodapiCI/ ./thinklabsdev/tradarprodapiCIB",
                                execTimeout: 600000,
                                flatten: false,
                                makeEmptyDirs: false,
                                noDefaultExcludes: false,
                                patternSeparator: '[, ]+',
                                remoteDirectory: './thinklabsdev/tradarprodapiCI',
                                remoteDirectorySDF: false,
                                removePrefix: '',
                                sourceFiles: '*, server/'
                            )],
                        usePromotionTimestamp: false,
                        useWorkspaceInPromotion: false,
                        verbose: false
                    )
                ])
            }
        }
    }
}