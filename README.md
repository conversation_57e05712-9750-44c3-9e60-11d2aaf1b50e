# tradar-evn-backend

## Description
A brief description of your project. For example:
> This is a [Node.js](https://nodejs.org/) project built with **Node.js 18.20.6**.

## System Requirements
- **Node.js**: Version **18.20.6**
- **npm**: Included with Node.js

## Installation
1. Clone this repository:
    ```bash
    git clone https://github.com/ThinklabsDev/tradar-evn-backend
    cd tradar-evn-backend
    ```
2. Make sure you have the correct Node.js version installed:
    ```bash
    node -v
    ```
   The output should be `v18.20.6`. If not, use [nvm](https://github.com/nvm-sh/nvm) to switch versions:
    ```bash
    nvm install 18.20.6
    nvm use 18.20.6
    ```

3. Install dependencies:
    ```bash
    yarn install
    ```

## Running the Project
To start the application, run:
```bash
yarn start

