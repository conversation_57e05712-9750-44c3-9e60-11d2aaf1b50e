name: Auto Merge Master to Dev

on:
  push:
    branches:
      - master

jobs:
  merge-and-create-pr:
    runs-on: ubuntu-latest
    steps:
      # Bước 1: Checkout code
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Important for merging

      # Bước 2: <PERSON><PERSON><PERSON> hình git
      - name: Setup Git
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "hungphandinh"

      # Bước 3: Merge nhánh master vào dev
      - name: Merge master into evnnpt/dev
        run: |
          git fetch origin evnnpt/dev
          git checkout evnnpt/dev
          git merge --no-ff origin/master -m "Merge main into evnnpt/dev [skip ci]"
          git push origin evnnpt/dev
