name: Merge back after release

on:
  push:
    branches:
      - prod

jobs:
  merge-and-create-pr:
    runs-on: ubuntu-latest
    steps:
      # Bước 1: Checkout code
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Important for merging

      # Bước 2: <PERSON><PERSON><PERSON> hình git
      - name: Setup Git
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "hungphandinh"

      # Bước 3: Merge nhánh release vào staging
      - name: Merge release into staging
        run: |
          git fetch origin staging
          git checkout staging
          git merge --no-ff origin/release -m "Merge release into staging [skip ci]"
          git push origin staging
      - name: Merge release into master
        run: |
          git fetch origin master
          git checkout master
          git merge --no-ff origin/release -m "Merge release into master [skip ci]"
          git push origin master

      # Bước 4: Tạo tag trên nhánh master với tên dạng "v1.0.x"
      - name: Create tag on master branch
        id: tag_master
        run: |
          git checkout master
          latest_tag=$(git describe --abbrev=0 --tags)
          # Extract the last number from the latest tag
          last_number=$(echo "$latest_tag" | grep -oE '[0-9]+$')
          # Increment the last number by 1
          next_number=$((last_number + 1))
          new_tag="v1.1.$next_number"
          git tag -a "$new_tag" -m "Release $new_tag"
          git push origin --tags
