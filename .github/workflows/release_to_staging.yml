name: Auto Merge Staging

on:
  push:
    branches:
      - release

jobs:
  merge-and-create-pr:
    runs-on: ubuntu-latest
    steps:
      # Bước 1: Checkout code
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Important for merging

      # Bước 2: <PERSON><PERSON><PERSON> hình git
      - name: Setup Git
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "hungphandinh"

      # Bước 3: Merge nhánh release vào staging
      - name: Merge release into staging
        run: |
          git fetch origin staging
          git checkout staging
          git merge --no-ff origin/release -m "Merge release into staging [skip ci]"
          git push origin staging
